package com.mohamedrady.v2hoor.model

import com.google.firebase.database.PropertyName
import java.text.SimpleDateFormat
import java.util.*

/**
 * User role enumeration
 */
enum class UserRole(val value: String, val displayName: String) {
    ADMIN("admin", "مدير"),
    USER("user", "مستخدم");
    
    companion object {
        fun fromString(value: String?): UserRole {
            return when (value?.lowercase()) {
                "admin" -> ADMIN
                "user" -> USER
                else -> USER // Default to user role
            }
        }
    }
    
    fun isAdmin(): Boolean = this == ADMIN
    fun isUser(): Boolean = this == USER
}

/**
 * User profile model for Firebase Realtime Database
 */
data class UserProfile(
    @PropertyName("uid")
    var uid: String = "",
    
    @PropertyName("email")
    var email: String = "",
    
    @PropertyName("name")
    var name: String = "",
    
    @PropertyName("role")
    var role: String = UserRole.USER.value,
    
    @PropertyName("subscription_end")
    var subscriptionEnd: String = "",
    
    @PropertyName("is_active")
    var isActive: Boolean = true,
    
    @PropertyName("created_at")
    var createdAt: Long = 0,
    
    @PropertyName("updated_at")
    var updatedAt: Long = 0,
    
    @PropertyName("last_login")
    var lastLogin: Long = 0,
    
    @PropertyName("subscription_type")
    var subscriptionType: String = "basic",
    
    @PropertyName("max_devices")
    var maxDevices: Int = 1,
    
    @PropertyName("data_limit_gb")
    var dataLimitGb: Long = 0, // 0 means unlimited
    
    @PropertyName("data_used_gb")
    var dataUsedGb: Long = 0,
    
    @PropertyName("notes")
    var notes: String = "",
    
    @PropertyName("created_by")
    var createdBy: String = "",
    
    @PropertyName("avatar_url")
    var avatarUrl: String = ""
) {
    constructor() : this(
        uid = "",
        email = "",
        name = "",
        role = UserRole.USER.value,
        subscriptionEnd = "",
        isActive = true,
        createdAt = 0,
        updatedAt = 0,
        lastLogin = 0,
        subscriptionType = "basic",
        maxDevices = 1,
        dataLimitGb = 0,
        dataUsedGb = 0,
        notes = "",
        createdBy = "",
        avatarUrl = ""
    )
    
    /**
     * Get user role enum
     */
    fun getUserRole(): UserRole {
        return UserRole.fromString(role)
    }
    
    /**
     * Check if user is admin
     */
    fun isAdmin(): Boolean {
        return getUserRole().isAdmin()
    }
    
    /**
     * Check if user is regular user
     */
    fun isUser(): Boolean {
        return getUserRole().isUser()
    }
    
    /**
     * Check if subscription is active
     */
    fun isSubscriptionActive(): Boolean {
        if (subscriptionEnd.isBlank()) return true // No expiry set
        
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val endDate = dateFormat.parse(subscriptionEnd)
            val currentDate = Date()
            endDate?.after(currentDate) ?: true
        } catch (e: Exception) {
            true // If parsing fails, assume active
        }
    }
    
    /**
     * Get subscription end date as Date object
     */
    fun getSubscriptionEndDate(): Date? {
        if (subscriptionEnd.isBlank()) return null
        
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            dateFormat.parse(subscriptionEnd)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Get days until subscription expires
     */
    fun getDaysUntilExpiry(): Long {
        val endDate = getSubscriptionEndDate() ?: return Long.MAX_VALUE
        val currentDate = Date()
        val diffInMillis = endDate.time - currentDate.time
        return diffInMillis / (24 * 60 * 60 * 1000)
    }
    
    /**
     * Check if subscription is expiring soon (within 7 days)
     */
    fun isSubscriptionExpiringSoon(): Boolean {
        val daysUntilExpiry = getDaysUntilExpiry()
        return daysUntilExpiry in 0..7
    }
    
    /**
     * Get formatted subscription end date
     */
    fun getFormattedSubscriptionEnd(): String {
        if (subscriptionEnd.isBlank()) return "غير محدد"
        
        return try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(subscriptionEnd)
            date?.let { outputFormat.format(it) } ?: subscriptionEnd
        } catch (e: Exception) {
            subscriptionEnd
        }
    }
    
    /**
     * Get formatted creation date
     */
    fun getFormattedCreatedAt(): String {
        return if (createdAt > 0) {
            val date = Date(createdAt)
            SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(date)
        } else {
            "غير محدد"
        }
    }
    
    /**
     * Get formatted last login
     */
    fun getFormattedLastLogin(): String {
        return if (lastLogin > 0) {
            val date = Date(lastLogin)
            SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(date)
        } else {
            "لم يسجل دخول من قبل"
        }
    }
    
    /**
     * Get data usage percentage
     */
    fun getDataUsagePercentage(): Float {
        return if (dataLimitGb > 0) {
            (dataUsedGb.toFloat() / dataLimitGb.toFloat()) * 100f
        } else {
            0f
        }
    }
    
    /**
     * Check if data limit is exceeded
     */
    fun isDataLimitExceeded(): Boolean {
        return dataLimitGb > 0 && dataUsedGb >= dataLimitGb
    }
    
    /**
     * Get status summary
     */
    fun getStatusSummary(): String {
        val status = mutableListOf<String>()
        
        if (!isActive) status.add("غير نشط")
        if (!isSubscriptionActive()) status.add("منتهي الصلاحية")
        if (isSubscriptionExpiringSoon()) status.add("ينتهي قريباً")
        if (isDataLimitExceeded()) status.add("تجاوز الحد المسموح")
        
        return if (status.isEmpty()) "نشط" else status.joinToString(", ")
    }
    
    /**
     * Validate user profile data
     */
    fun isValid(): Boolean {
        return uid.isNotBlank() && 
               email.isNotBlank() && 
               name.isNotBlank() && 
               role.isNotBlank()
    }
    
    /**
     * Update last login timestamp
     */
    fun updateLastLogin() {
        lastLogin = System.currentTimeMillis()
        updatedAt = System.currentTimeMillis()
    }
    
    /**
     * Update data usage
     */
    fun updateDataUsage(additionalGb: Long) {
        dataUsedGb += additionalGb
        updatedAt = System.currentTimeMillis()
    }
    
    /**
     * Reset data usage
     */
    fun resetDataUsage() {
        dataUsedGb = 0
        updatedAt = System.currentTimeMillis()
    }
}
