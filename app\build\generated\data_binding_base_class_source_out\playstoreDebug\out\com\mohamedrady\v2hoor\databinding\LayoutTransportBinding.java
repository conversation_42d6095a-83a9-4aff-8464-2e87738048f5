// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutTransportBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText etExtra;

  @NonNull
  public final EditText etPath;

  @NonNull
  public final EditText etRequestHost;

  @NonNull
  public final LinearLayout layoutExtra;

  @NonNull
  public final Spinner spHeaderType;

  @NonNull
  public final TextView spHeaderTypeTitle;

  @NonNull
  public final Spinner spNetwork;

  @NonNull
  public final TextView tvPath;

  @NonNull
  public final TextView tvRequestHost;

  private LayoutTransportBinding(@NonNull LinearLayout rootView, @NonNull EditText etExtra,
      @NonNull EditText etPath, @NonNull EditText etRequestHost, @NonNull LinearLayout layoutExtra,
      @NonNull Spinner spHeaderType, @NonNull TextView spHeaderTypeTitle,
      @NonNull Spinner spNetwork, @NonNull TextView tvPath, @NonNull TextView tvRequestHost) {
    this.rootView = rootView;
    this.etExtra = etExtra;
    this.etPath = etPath;
    this.etRequestHost = etRequestHost;
    this.layoutExtra = layoutExtra;
    this.spHeaderType = spHeaderType;
    this.spHeaderTypeTitle = spHeaderTypeTitle;
    this.spNetwork = spNetwork;
    this.tvPath = tvPath;
    this.tvRequestHost = tvRequestHost;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutTransportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutTransportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_transport, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutTransportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_extra;
      EditText etExtra = ViewBindings.findChildViewById(rootView, id);
      if (etExtra == null) {
        break missingId;
      }

      id = R.id.et_path;
      EditText etPath = ViewBindings.findChildViewById(rootView, id);
      if (etPath == null) {
        break missingId;
      }

      id = R.id.et_request_host;
      EditText etRequestHost = ViewBindings.findChildViewById(rootView, id);
      if (etRequestHost == null) {
        break missingId;
      }

      id = R.id.layout_extra;
      LinearLayout layoutExtra = ViewBindings.findChildViewById(rootView, id);
      if (layoutExtra == null) {
        break missingId;
      }

      id = R.id.sp_header_type;
      Spinner spHeaderType = ViewBindings.findChildViewById(rootView, id);
      if (spHeaderType == null) {
        break missingId;
      }

      id = R.id.sp_header_type_title;
      TextView spHeaderTypeTitle = ViewBindings.findChildViewById(rootView, id);
      if (spHeaderTypeTitle == null) {
        break missingId;
      }

      id = R.id.sp_network;
      Spinner spNetwork = ViewBindings.findChildViewById(rootView, id);
      if (spNetwork == null) {
        break missingId;
      }

      id = R.id.tv_path;
      TextView tvPath = ViewBindings.findChildViewById(rootView, id);
      if (tvPath == null) {
        break missingId;
      }

      id = R.id.tv_request_host;
      TextView tvRequestHost = ViewBindings.findChildViewById(rootView, id);
      if (tvRequestHost == null) {
        break missingId;
      }

      return new LayoutTransportBinding((LinearLayout) rootView, etExtra, etPath, etRequestHost,
          layoutExtra, spHeaderType, spHeaderTypeTitle, spNetwork, tvPath, tvRequestHost);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
