@echo off
echo ========================================
echo   V2Hoor Subscription Management System
echo   Deployment Script
echo ========================================
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Firebase CLI not found!
    echo Please install Firebase CLI first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo Firebase CLI found. Proceeding with deployment...
echo.

REM Login to Firebase (if not already logged in)
echo Checking Firebase authentication...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo Please login to Firebase:
    firebase login
    if %errorlevel% neq 0 (
        echo Failed to login to Firebase
        pause
        exit /b 1
    )
)

echo.
echo Setting Firebase project...
firebase use mrelfeky-209615
if %errorlevel% neq 0 (
    echo Failed to set Firebase project
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Deploying Subscription System
echo ========================================
echo.

echo Step 1: Deploying Firebase Realtime Database rules...
firebase deploy --only database
if %errorlevel% neq 0 (
    echo Failed to deploy database rules
    pause
    exit /b 1
)

echo.
echo Step 2: Deploying Firestore rules (for backward compatibility)...
firebase deploy --only firestore:rules
if %errorlevel% neq 0 (
    echo Warning: Failed to deploy Firestore rules (may not exist)
)

echo.
echo ========================================
echo   Subscription System Features
echo ========================================
echo.

echo ✅ Subscription Status Validation:
echo    - Active: Current date ≤ subscription_end
echo    - Expiring Soon: 1-3 days before expiration
echo    - Expired: Current date > subscription_end
echo    - Unlimited: No expiration date
echo.

echo ✅ Access Control:
echo    - Expired users blocked from app access
echo    - Data limit exceeded blocks server connections
echo    - Admin users have unlimited access
echo    - Real-time subscription monitoring
echo.

echo ✅ User Interface:
echo    - Dynamic subscription status banner
echo    - Color-coded status indicators
echo    - Data usage progress bars
echo    - Action buttons (Renew, Contact Support)
echo.

echo ✅ Admin Features:
echo    - Extend user subscriptions
echo    - Monitor expiring subscriptions
echo    - Manage data limits and usage
echo    - Bulk subscription operations
echo.

echo ========================================
echo   Subscription Data Structure
echo ========================================
echo.

echo Firebase Structure:
echo /users/{uid}/
echo   - subscription_end: "2025-12-31" (yyyy-MM-dd)
echo   - subscription_type: "basic" | "premium" | "unlimited"
echo   - is_active: true
echo   - data_limit_gb: 100
echo   - data_used_gb: 25
echo   - max_devices: 3
echo.

echo Subscription Types:
echo - Basic: Limited data, single device, basic servers
echo - Premium: High data limit, multiple devices, premium servers
echo - Unlimited: No limits, all features, admin-level access
echo - Trial: 30-day trial with basic features
echo.

echo ========================================
echo   Testing Instructions
echo ========================================
echo.

echo Admin Testing:
echo 1. Login with admin account (<EMAIL>)
echo 2. Verify unlimited access regardless of subscription
echo 3. Test subscription management features
echo 4. Extend user subscriptions
echo 5. Monitor expiring users
echo.

echo User Testing:
echo 1. Create test users with different subscription states:
echo    - Active subscription (future date)
echo    - Expiring subscription (1-2 days)
echo    - Expired subscription (past date)
echo    - Data limit exceeded
echo.

echo 2. Test access restrictions:
echo    - Expired users should be blocked from app
echo    - Data exceeded users can't connect to servers
echo    - Expiring users see warning banners
echo    - Active users have full access
echo.

echo 3. Test UI components:
echo    - Subscription banner shows correct status
echo    - Colors match subscription state
echo    - Action buttons work properly
echo    - Data usage progress updates
echo.

echo ========================================
echo   Sample Subscription Data
echo ========================================
echo.

echo Active User:
echo {
echo   "subscription_end": "2025-12-31",
echo   "subscription_type": "premium",
echo   "is_active": true,
echo   "data_limit_gb": 100,
echo   "data_used_gb": 25,
echo   "max_devices": 3
echo }
echo.

echo Expiring User:
echo {
echo   "subscription_end": "2024-12-03",
echo   "subscription_type": "basic",
echo   "is_active": true,
echo   "data_limit_gb": 10,
echo   "data_used_gb": 8,
echo   "max_devices": 1
echo }
echo.

echo Expired User:
echo {
echo   "subscription_end": "2024-11-01",
echo   "subscription_type": "basic",
echo   "is_active": false,
echo   "data_limit_gb": 10,
echo   "data_used_gb": 12,
echo   "max_devices": 1
echo }
echo.

echo ========================================
echo   Configuration Steps
echo ========================================
echo.

echo 1. Set up subscription data in Firebase Console:
echo    - Go to: https://console.firebase.google.com/project/mrelfeky-209615/database
echo    - Navigate to /users/{uid}/
echo    - Add subscription fields for each user
echo.

echo 2. Configure contact support:
echo    - Update WhatsApp number in SubscriptionStatusBanner.kt
echo    - Update support email in MainActivity.kt
echo    - Test contact support functionality
echo.

echo 3. Customize subscription types:
echo    - Modify subscription features in SubscriptionInfo.kt
echo    - Update data limits and device counts
echo    - Configure trial period duration
echo.

echo ========================================
echo   Monitoring and Maintenance
echo ========================================
echo.

echo Daily Tasks:
echo - Monitor expiring subscriptions
echo - Check data usage statistics
echo - Review subscription renewal requests
echo - Update user subscription dates
echo.

echo Weekly Tasks:
echo - Analyze subscription revenue
echo - Review user activity patterns
echo - Update subscription pricing
echo - Optimize data usage limits
echo.

echo Monthly Tasks:
echo - Generate subscription reports
echo - Review system performance
echo - Update subscription features
echo - Plan new subscription tiers
echo.

echo ========================================
echo   Troubleshooting
echo ========================================
echo.

echo Common Issues:
echo.
echo 1. Subscription not loading:
echo    - Check Firebase connection
echo    - Verify user authentication
echo    - Check subscription data format
echo.

echo 2. Access not blocked for expired users:
echo    - Verify Firebase rules deployment
echo    - Check subscription validation logic
echo    - Review date format (yyyy-MM-dd)
echo.

echo 3. Banner not showing:
echo    - Check subscription status calculation
echo    - Verify UI component integration
echo    - Review layout inclusion
echo.

echo 4. Admin bypass not working:
echo    - Check user role assignment
echo    - Verify admin permission logic
echo    - Review role-based access control
echo.

echo ========================================
echo   Subscription System Deployment Complete!
echo ========================================
echo.

echo Next Steps:
echo 1. Configure user subscriptions in Firebase Console
echo 2. Test all subscription states and access restrictions
echo 3. Set up contact support channels (WhatsApp/Email)
echo 4. Monitor subscription system performance
echo 5. Train admin users on subscription management
echo.

echo Database URL: https://mrelfeky-209615-default-rtdb.firebaseio.com/
echo Firebase Console: https://console.firebase.google.com/project/mrelfeky-209615
echo.

echo The V2Hoor Subscription Management System is now ready for production use!
echo.

pause
