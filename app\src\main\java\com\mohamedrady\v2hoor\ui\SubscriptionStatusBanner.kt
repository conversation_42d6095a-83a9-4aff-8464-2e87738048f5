package com.mohamedrady.v2hoor.ui

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.model.SubscriptionInfo
import com.mohamedrady.v2hoor.model.SubscriptionStatus

/**
 * Subscription Status Banner Component
 * Displays subscription status, expiration warnings, and action buttons
 */
class SubscriptionStatusBanner @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    
    private val subscriptionBanner: LinearLayout
    private val ivSubscriptionIcon: ImageView
    private val tvSubscriptionStatus: TextView
    private val tvSubscriptionDetails: TextView
    private val layoutDataUsage: LinearLayout
    private val progressDataUsage: ProgressBar
    private val tvDataUsage: TextView
    private val btnSubscriptionAction: Button
    private val btnCloseBanner: ImageButton
    
    private var onActionClickListener: (() -> Unit)? = null
    private var onCloseClickListener: (() -> Unit)? = null
    
    init {
        LayoutInflater.from(context).inflate(R.layout.subscription_status_banner, this, true)
        
        subscriptionBanner = findViewById(R.id.subscription_banner)
        ivSubscriptionIcon = findViewById(R.id.iv_subscription_icon)
        tvSubscriptionStatus = findViewById(R.id.tv_subscription_status)
        tvSubscriptionDetails = findViewById(R.id.tv_subscription_details)
        layoutDataUsage = findViewById(R.id.layout_data_usage)
        progressDataUsage = findViewById(R.id.progress_data_usage)
        tvDataUsage = findViewById(R.id.tv_data_usage)
        btnSubscriptionAction = findViewById(R.id.btn_subscription_action)
        btnCloseBanner = findViewById(R.id.btn_close_banner)
        
        setupClickListeners()
    }
    
    private fun setupClickListeners() {
        btnSubscriptionAction.setOnClickListener {
            onActionClickListener?.invoke()
        }
        
        btnCloseBanner.setOnClickListener {
            onCloseClickListener?.invoke()
            hide()
        }
    }
    
    /**
     * Update banner with subscription information
     */
    fun updateSubscription(subscription: SubscriptionInfo?) {
        if (subscription == null) {
            hide()
            return
        }
        
        val status = subscription.getStatus()
        
        when (status) {
            SubscriptionStatus.ACTIVE -> {
                showActiveBanner(subscription)
            }
            SubscriptionStatus.EXPIRING_SOON -> {
                showExpiringSoonBanner(subscription)
            }
            SubscriptionStatus.EXPIRED -> {
                showExpiredBanner(subscription)
            }
            SubscriptionStatus.UNLIMITED -> {
                showUnlimitedBanner(subscription)
            }
            SubscriptionStatus.UNKNOWN -> {
                hide()
            }
        }
    }
    
    private fun showActiveBanner(subscription: SubscriptionInfo) {
        show()
        
        // Set icon and colors
        ivSubscriptionIcon.setImageResource(R.drawable.ic_subscription_active)
        subscriptionBanner.isSelected = true
        subscriptionBanner.isEnabled = true
        subscriptionBanner.isPressed = false
        
        // Set text
        tvSubscriptionStatus.text = context.getString(R.string.subscription_active)
        tvSubscriptionStatus.setTextColor(ContextCompat.getColor(context, R.color.subscription_text_color))
        
        tvSubscriptionDetails.text = subscription.getStatusMessage()
        tvSubscriptionDetails.setTextColor(ContextCompat.getColor(context, R.color.subscription_details_color))
        
        // Show data usage if limited
        if (subscription.dataLimitGb > 0) {
            showDataUsage(subscription)
        } else {
            hideDataUsage()
        }
        
        // Hide action button for active subscriptions
        btnSubscriptionAction.visibility = View.GONE
        btnCloseBanner.visibility = View.VISIBLE
    }
    
    private fun showExpiringSoonBanner(subscription: SubscriptionInfo) {
        show()
        
        // Set icon and colors
        ivSubscriptionIcon.setImageResource(R.drawable.ic_subscription_warning)
        subscriptionBanner.isSelected = false
        subscriptionBanner.isEnabled = true
        subscriptionBanner.isPressed = true
        
        // Set text
        tvSubscriptionStatus.text = context.getString(R.string.subscription_expiring_soon)
        tvSubscriptionStatus.setTextColor(ContextCompat.getColor(context, R.color.subscription_text_color))
        
        val days = subscription.getDaysUntilExpiry()
        tvSubscriptionDetails.text = context.getString(R.string.subscription_expiring_message, days)
        tvSubscriptionDetails.setTextColor(ContextCompat.getColor(context, R.color.subscription_details_color))
        
        // Show data usage if limited
        if (subscription.dataLimitGb > 0) {
            showDataUsage(subscription)
        } else {
            hideDataUsage()
        }
        
        // Show renew button
        btnSubscriptionAction.text = context.getString(R.string.renew)
        btnSubscriptionAction.visibility = View.VISIBLE
        btnCloseBanner.visibility = View.VISIBLE
    }
    
    private fun showExpiredBanner(subscription: SubscriptionInfo) {
        show()
        
        // Set icon and colors
        ivSubscriptionIcon.setImageResource(R.drawable.ic_subscription_expired)
        subscriptionBanner.isSelected = false
        subscriptionBanner.isEnabled = false
        subscriptionBanner.isPressed = false
        
        // Set text
        tvSubscriptionStatus.text = context.getString(R.string.subscription_expired)
        tvSubscriptionStatus.setTextColor(ContextCompat.getColor(context, R.color.subscription_text_color))
        
        tvSubscriptionDetails.text = context.getString(R.string.subscription_expired_message)
        tvSubscriptionDetails.setTextColor(ContextCompat.getColor(context, R.color.subscription_details_color))
        
        // Hide data usage for expired subscriptions
        hideDataUsage()
        
        // Show contact support button
        btnSubscriptionAction.text = context.getString(R.string.contact_support)
        btnSubscriptionAction.visibility = View.VISIBLE
        btnCloseBanner.visibility = View.GONE // Don't allow closing expired banner
    }
    
    private fun showUnlimitedBanner(subscription: SubscriptionInfo) {
        show()
        
        // Set icon and colors
        ivSubscriptionIcon.setImageResource(R.drawable.ic_subscription_active)
        subscriptionBanner.isSelected = true
        subscriptionBanner.isEnabled = true
        subscriptionBanner.isPressed = false
        
        // Set text
        tvSubscriptionStatus.text = context.getString(R.string.subscription_unlimited)
        tvSubscriptionStatus.setTextColor(ContextCompat.getColor(context, R.color.subscription_text_color))
        
        tvSubscriptionDetails.text = subscription.getStatusMessage()
        tvSubscriptionDetails.setTextColor(ContextCompat.getColor(context, R.color.subscription_details_color))
        
        // Hide data usage for unlimited subscriptions
        hideDataUsage()
        
        // Hide action button for unlimited subscriptions
        btnSubscriptionAction.visibility = View.GONE
        btnCloseBanner.visibility = View.VISIBLE
    }
    
    private fun showDataUsage(subscription: SubscriptionInfo) {
        layoutDataUsage.visibility = View.VISIBLE
        
        val usagePercentage = subscription.getDataUsagePercentage()
        progressDataUsage.progress = usagePercentage.toInt()
        
        val usageText = "${subscription.dataUsedGb}/${subscription.dataLimitGb} GB"
        tvDataUsage.text = usageText
        
        // Change progress bar color based on usage
        val progressColor = when {
            usagePercentage >= 100f -> ContextCompat.getColor(context, R.color.subscription_icon_expired)
            usagePercentage >= 80f -> ContextCompat.getColor(context, R.color.subscription_icon_warning)
            else -> ContextCompat.getColor(context, R.color.subscription_icon_active)
        }
        
        progressDataUsage.progressTintList = ContextCompat.getColorStateList(context, progressColor)
    }
    
    private fun hideDataUsage() {
        layoutDataUsage.visibility = View.GONE
    }
    
    /**
     * Show the banner
     */
    fun show() {
        subscriptionBanner.visibility = View.VISIBLE
    }
    
    /**
     * Hide the banner
     */
    fun hide() {
        subscriptionBanner.visibility = View.GONE
    }
    
    /**
     * Check if banner is visible
     */
    fun isVisible(): Boolean {
        return subscriptionBanner.visibility == View.VISIBLE
    }
    
    /**
     * Set action button click listener
     */
    fun setOnActionClickListener(listener: () -> Unit) {
        onActionClickListener = listener
    }
    
    /**
     * Set close button click listener
     */
    fun setOnCloseClickListener(listener: () -> Unit) {
        onCloseClickListener = listener
    }
    
    /**
     * Handle default action based on subscription status
     */
    fun handleDefaultAction(subscription: SubscriptionInfo?) {
        if (subscription == null) return
        
        when (subscription.getStatus()) {
            SubscriptionStatus.EXPIRING_SOON -> {
                // Open renewal/contact support
                openContactSupport()
            }
            SubscriptionStatus.EXPIRED -> {
                // Open contact support
                openContactSupport()
            }
            else -> {
                // No default action for active/unlimited
            }
        }
    }
    
    private fun openContactSupport() {
        try {
            // Try to open WhatsApp first
            val whatsappIntent = Intent(Intent.ACTION_VIEW)
            whatsappIntent.data = Uri.parse("https://wa.me/1234567890") // Replace with actual WhatsApp number
            context.startActivity(whatsappIntent)
        } catch (e: Exception) {
            // Fallback to email
            try {
                val emailIntent = Intent(Intent.ACTION_SENDTO)
                emailIntent.data = Uri.parse("mailto:<EMAIL>") // Replace with actual email
                emailIntent.putExtra(Intent.EXTRA_SUBJECT, "Subscription Renewal Request")
                context.startActivity(emailIntent)
            } catch (e: Exception) {
                // Show toast if no email app available
                Toast.makeText(context, "يرجى التواصل مع الدعم الفني", Toast.LENGTH_LONG).show()
            }
        }
    }
}
