<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recycler_main" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_recycler_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/item_bg"><Targets><Target id="@+id/item_bg" tag="layout/item_recycler_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="218" endOffset="14"/></Target><Target id="@+id/info_container" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="216" endOffset="18"/></Target><Target id="@+id/layout_indicator" view="LinearLayout"><Expressions/><location startLine="25" startOffset="8" endLine="30" endOffset="44"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="54" startOffset="24" endLine="60" endOffset="94"/></Target><Target id="@+id/layout_subscription" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="68" startOffset="28" endLine="86" endOffset="79"/></Target><Target id="@+id/tv_subscription" view="TextView"><Expressions/><location startLine="76" startOffset="32" endLine="85" endOffset="79"/></Target><Target id="@+id/tv_statistics" view="TextView"><Expressions/><location startLine="88" startOffset="28" endLine="93" endOffset="96"/></Target><Target id="@+id/layout_share" view="LinearLayout"><Expressions/><location startLine="107" startOffset="20" endLine="124" endOffset="34"/></Target><Target id="@+id/layout_edit" view="LinearLayout"><Expressions/><location startLine="126" startOffset="20" endLine="142" endOffset="34"/></Target><Target id="@+id/layout_remove" view="LinearLayout"><Expressions/><location startLine="144" startOffset="20" endLine="160" endOffset="34"/></Target><Target id="@+id/layout_more" view="LinearLayout"><Expressions/><location startLine="162" startOffset="20" endLine="178" endOffset="34"/></Target><Target id="@+id/tv_type" view="TextView"><Expressions/><location startLine="191" startOffset="16" endLine="200" endOffset="40"/></Target><Target id="@+id/tv_test_result" view="TextView"><Expressions/><location startLine="202" startOffset="16" endLine="210" endOffset="40"/></Target></Targets></Layout>