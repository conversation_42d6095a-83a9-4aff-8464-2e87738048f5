// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBypassListBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout containerBypassApps;

  @NonNull
  public final LinearLayout containerPerAppProxy;

  @NonNull
  public final LinearLayout headerView;

  @NonNull
  public final LinearLayout layoutSwitchBypassAppsTips;

  @NonNull
  public final LinearProgressIndicator pbWaiting;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final SwitchCompat switchBypassApps;

  @NonNull
  public final SwitchCompat switchPerAppProxy;

  private ActivityBypassListBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout containerBypassApps, @NonNull LinearLayout containerPerAppProxy,
      @NonNull LinearLayout headerView, @NonNull LinearLayout layoutSwitchBypassAppsTips,
      @NonNull LinearProgressIndicator pbWaiting, @NonNull RecyclerView recyclerView,
      @NonNull SwitchCompat switchBypassApps, @NonNull SwitchCompat switchPerAppProxy) {
    this.rootView = rootView;
    this.containerBypassApps = containerBypassApps;
    this.containerPerAppProxy = containerPerAppProxy;
    this.headerView = headerView;
    this.layoutSwitchBypassAppsTips = layoutSwitchBypassAppsTips;
    this.pbWaiting = pbWaiting;
    this.recyclerView = recyclerView;
    this.switchBypassApps = switchBypassApps;
    this.switchPerAppProxy = switchPerAppProxy;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBypassListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBypassListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_bypass_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBypassListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.container_bypass_apps;
      LinearLayout containerBypassApps = ViewBindings.findChildViewById(rootView, id);
      if (containerBypassApps == null) {
        break missingId;
      }

      id = R.id.container_per_app_proxy;
      LinearLayout containerPerAppProxy = ViewBindings.findChildViewById(rootView, id);
      if (containerPerAppProxy == null) {
        break missingId;
      }

      id = R.id.header_view;
      LinearLayout headerView = ViewBindings.findChildViewById(rootView, id);
      if (headerView == null) {
        break missingId;
      }

      id = R.id.layout_switch_bypass_apps_tips;
      LinearLayout layoutSwitchBypassAppsTips = ViewBindings.findChildViewById(rootView, id);
      if (layoutSwitchBypassAppsTips == null) {
        break missingId;
      }

      id = R.id.pb_waiting;
      LinearProgressIndicator pbWaiting = ViewBindings.findChildViewById(rootView, id);
      if (pbWaiting == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.switch_bypass_apps;
      SwitchCompat switchBypassApps = ViewBindings.findChildViewById(rootView, id);
      if (switchBypassApps == null) {
        break missingId;
      }

      id = R.id.switch_per_app_proxy;
      SwitchCompat switchPerAppProxy = ViewBindings.findChildViewById(rootView, id);
      if (switchPerAppProxy == null) {
        break missingId;
      }

      return new ActivityBypassListBinding((LinearLayout) rootView, containerBypassApps,
          containerPerAppProxy, headerView, layoutSwitchBypassAppsTips, pbWaiting, recyclerView,
          switchBypassApps, switchPerAppProxy);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
