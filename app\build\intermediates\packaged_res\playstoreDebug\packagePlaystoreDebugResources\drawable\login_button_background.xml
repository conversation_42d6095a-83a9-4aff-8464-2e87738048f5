<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/primary_dark_color"
                android:endColor="@color/primary_color"
                android:angle="45" />
            <corners android:radius="28dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/primary_color"
                android:endColor="@color/primary_dark_color"
                android:angle="45" />
            <corners android:radius="28dp" />
        </shape>
    </item>
</selector>
