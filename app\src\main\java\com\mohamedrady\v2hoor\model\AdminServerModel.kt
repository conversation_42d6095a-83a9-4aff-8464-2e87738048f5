package com.mohamedrady.v2hoor.model

import com.google.firebase.database.PropertyName
import java.text.SimpleDateFormat
import java.util.*

/**
 * Admin Server Model for server management
 * Extended server model with admin-specific functionality
 */
data class AdminServerModel(
    @PropertyName("id")
    var id: String = "",
    
    @PropertyName("name")
    var name: String = "",
    
    @PropertyName("remarks")
    var remarks: String = "",
    
    @PropertyName("server")
    var server: String = "",
    
    @PropertyName("server_port")
    var serverPort: Int = 443,
    
    @PropertyName("protocol")
    var protocol: String = "vmess",
    
    @PropertyName("uuid")
    var uuid: String = "",
    
    @PropertyName("alter_id")
    var alterId: Int = 0,
    
    @PropertyName("security")
    var security: String = "auto",
    
    @PropertyName("network")
    var network: String = "tcp",
    
    @PropertyName("header_type")
    var headerType: String = "none",
    
    @PropertyName("request_host")
    var requestHost: String = "",
    
    @PropertyName("path")
    var path: String = "",
    
    @PropertyName("tls")
    var tls: String = "",
    
    @PropertyName("sni")
    var sni: String = "",
    
    @PropertyName("alpn")
    var alpn: String = "",
    
    @PropertyName("fingerprint")
    var fingerprint: String = "",
    
    @PropertyName("public_key")
    var publicKey: String = "",
    
    @PropertyName("short_id")
    var shortId: String = "",
    
    @PropertyName("spider_x")
    var spiderX: String = "",
    
    @PropertyName("flow")
    var flow: String = "",
    
    @PropertyName("encryption")
    var encryption: String = "none",
    
    @PropertyName("country")
    var country: String = "",
    
    @PropertyName("city")
    var city: String = "",
    
    @PropertyName("flag")
    var flag: String = "",
    
    @PropertyName("is_active")
    var isActive: Boolean = true,
    
    @PropertyName("priority")
    var priority: Int = 1,
    
    @PropertyName("created_at")
    var createdAt: Long = 0,
    
    @PropertyName("updated_at")
    var updatedAt: Long = 0,
    
    @PropertyName("expires_at")
    var expiresAt: Long = 0,
    
    @PropertyName("valid_until")
    var validUntil: Long = 0,
    
    @PropertyName("last_sync")
    var lastSync: Long = 0,
    
    @PropertyName("config_version")
    var configVersion: Int = 1,
    
    @PropertyName("subscription_id")
    var subscriptionId: String = "",
    
    @PropertyName("user_id")
    var userId: String = "",
    
    @PropertyName("tags")
    var tags: List<String> = emptyList(),
    
    @PropertyName("custom_config")
    var customConfig: String = "",
    
    @PropertyName("test_result")
    var testResult: ServerTestResult = ServerTestResult()
) {
    constructor() : this(
        id = "",
        name = "",
        remarks = "",
        server = "",
        serverPort = 443,
        protocol = "vmess",
        uuid = "",
        alterId = 0,
        security = "auto",
        network = "tcp",
        headerType = "none",
        requestHost = "",
        path = "",
        tls = "",
        sni = "",
        alpn = "",
        fingerprint = "",
        publicKey = "",
        shortId = "",
        spiderX = "",
        flow = "",
        encryption = "none",
        country = "",
        city = "",
        flag = "",
        isActive = true,
        priority = 1,
        createdAt = 0,
        updatedAt = 0,
        expiresAt = 0,
        validUntil = 0,
        lastSync = 0,
        configVersion = 1,
        subscriptionId = "",
        userId = "",
        tags = emptyList(),
        customConfig = "",
        testResult = ServerTestResult()
    )
    
    /**
     * Get protocol display name
     */
    fun getProtocolDisplayName(): String {
        return when (protocol.lowercase()) {
            "vmess" -> "VMess"
            "vless" -> "VLESS"
            "trojan" -> "Trojan"
            "shadowsocks" -> "Shadowsocks"
            "socks" -> "SOCKS"
            "http" -> "HTTP"
            else -> protocol.uppercase()
        }
    }
    
    /**
     * Get server address display
     */
    fun getServerAddressDisplay(): String {
        return "$server:$serverPort"
    }
    
    /**
     * Get location display
     */
    fun getLocationDisplay(): String {
        return when {
            city.isNotBlank() && country.isNotBlank() -> "$city, $country"
            country.isNotBlank() -> country
            city.isNotBlank() -> city
            else -> "غير محدد"
        }
    }
    
    /**
     * Get status display
     */
    fun getStatusDisplay(): String {
        return when {
            !isActive -> "غير نشط"
            isExpired() -> "منتهي الصلاحية"
            else -> "نشط"
        }
    }
    
    /**
     * Get status color
     */
    fun getStatusColor(): Int {
        return when {
            !isActive -> android.R.color.darker_gray
            isExpired() -> android.R.color.holo_red_dark
            else -> android.R.color.holo_green_dark
        }
    }
    
    /**
     * Check if server is expired
     */
    fun isExpired(): Boolean {
        val currentTime = System.currentTimeMillis()
        return (expiresAt > 0 && currentTime > expiresAt) ||
               (validUntil > 0 && currentTime > validUntil)
    }
    
    /**
     * Get formatted creation date
     */
    fun getFormattedCreatedAt(): String {
        return if (createdAt > 0) {
            val date = Date(createdAt)
            SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(date)
        } else {
            "غير محدد"
        }
    }
    
    /**
     * Get formatted expiration date
     */
    fun getFormattedExpirationDate(): String {
        val expirationTime = when {
            expiresAt > 0 -> expiresAt
            validUntil > 0 -> validUntil
            else -> 0
        }
        
        return if (expirationTime > 0) {
            val date = Date(expirationTime)
            SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(date)
        } else {
            "غير محدود"
        }
    }
    
    /**
     * Get tags display
     */
    fun getTagsDisplay(): String {
        return if (tags.isNotEmpty()) {
            tags.joinToString(", ")
        } else {
            "لا توجد علامات"
        }
    }
    
    /**
     * Validate server configuration
     */
    fun isValid(): Boolean {
        return id.isNotBlank() &&
               name.isNotBlank() &&
               server.isNotBlank() &&
               serverPort > 0 &&
               serverPort <= 65535 &&
               protocol.isNotBlank() &&
               uuid.isNotBlank()
    }
    
    /**
     * Get validation errors
     */
    fun getValidationErrors(): List<String> {
        val errors = mutableListOf<String>()
        
        if (id.isBlank()) errors.add("معرف السيرفر مطلوب")
        if (name.isBlank()) errors.add("اسم السيرفر مطلوب")
        if (server.isBlank()) errors.add("عنوان السيرفر مطلوب")
        if (serverPort <= 0 || serverPort > 65535) errors.add("رقم المنفذ غير صحيح")
        if (protocol.isBlank()) errors.add("البروتوكول مطلوب")
        if (uuid.isBlank()) errors.add("UUID مطلوب")
        
        return errors
    }
    
    /**
     * Get server summary
     */
    fun getSummary(): String {
        val parts = mutableListOf<String>()
        parts.add(getProtocolDisplayName())
        parts.add(getServerAddressDisplay())
        if (getLocationDisplay() != "غير محدد") parts.add(getLocationDisplay())
        return parts.joinToString(" • ")
    }
    
    /**
     * Convert to FirebaseServerModel
     */
    fun toFirebaseServerModel(): FirebaseServerModel {
        return FirebaseServerModel(
            id = id,
            name = name,
            remarks = remarks,
            server = server,
            serverPort = serverPort,
            protocol = protocol,
            uuid = uuid,
            alterId = alterId,
            security = security,
            network = network,
            headerType = headerType,
            requestHost = requestHost,
            path = path,
            tls = tls,
            sni = sni,
            alpn = alpn,
            fingerprint = fingerprint,
            publicKey = publicKey,
            shortId = shortId,
            spiderX = spiderX,
            flow = flow,
            encryption = encryption,
            country = country,
            city = city,
            flag = flag,
            isActive = isActive,
            priority = priority,
            createdAt = createdAt,
            updatedAt = updatedAt,
            expiresAt = expiresAt,
            validUntil = validUntil,
            lastSync = lastSync,
            configVersion = configVersion,
            subscriptionId = subscriptionId,
            userId = userId,
            tags = tags,
            customConfig = customConfig,
            testResult = testResult
        )
    }
    
    companion object {
        /**
         * Create from FirebaseServerModel
         */
        fun fromFirebaseServerModel(firebaseServer: FirebaseServerModel): AdminServerModel {
            return AdminServerModel(
                id = firebaseServer.id,
                name = firebaseServer.name,
                remarks = firebaseServer.remarks,
                server = firebaseServer.server,
                serverPort = firebaseServer.serverPort,
                protocol = firebaseServer.protocol,
                uuid = firebaseServer.uuid,
                alterId = firebaseServer.alterId,
                security = firebaseServer.security,
                network = firebaseServer.network,
                headerType = firebaseServer.headerType,
                requestHost = firebaseServer.requestHost,
                path = firebaseServer.path,
                tls = firebaseServer.tls,
                sni = firebaseServer.sni,
                alpn = firebaseServer.alpn,
                fingerprint = firebaseServer.fingerprint,
                publicKey = firebaseServer.publicKey,
                shortId = firebaseServer.shortId,
                spiderX = firebaseServer.spiderX,
                flow = firebaseServer.flow,
                encryption = firebaseServer.encryption,
                country = firebaseServer.country,
                city = firebaseServer.city,
                flag = firebaseServer.flag,
                isActive = firebaseServer.isActive,
                priority = firebaseServer.priority,
                createdAt = firebaseServer.createdAt,
                updatedAt = firebaseServer.updatedAt,
                expiresAt = firebaseServer.expiresAt,
                validUntil = firebaseServer.validUntil,
                lastSync = firebaseServer.lastSync,
                configVersion = firebaseServer.configVersion,
                subscriptionId = firebaseServer.subscriptionId,
                userId = firebaseServer.userId,
                tags = firebaseServer.tags,
                customConfig = firebaseServer.customConfig,
                testResult = firebaseServer.testResult
            )
        }
        
        /**
         * Create new server template
         */
        fun createNewServer(userId: String, createdBy: String): AdminServerModel {
            val currentTime = System.currentTimeMillis()
            return AdminServerModel(
                id = UUID.randomUUID().toString(),
                name = "سيرفر جديد",
                remarks = "تم إنشاؤه بواسطة المدير",
                server = "",
                serverPort = 443,
                protocol = "vmess",
                uuid = UUID.randomUUID().toString(),
                alterId = 0,
                security = "auto",
                network = "tcp",
                headerType = "none",
                requestHost = "",
                path = "",
                tls = "",
                sni = "",
                alpn = "",
                fingerprint = "",
                publicKey = "",
                shortId = "",
                spiderX = "",
                flow = "",
                encryption = "none",
                country = "",
                city = "",
                flag = "",
                isActive = true,
                priority = 1,
                createdAt = currentTime,
                updatedAt = currentTime,
                expiresAt = 0,
                validUntil = 0,
                lastSync = 0,
                configVersion = 1,
                subscriptionId = "",
                userId = userId,
                tags = listOf("admin-created"),
                customConfig = "",
                testResult = ServerTestResult()
            )
        }
    }
}
