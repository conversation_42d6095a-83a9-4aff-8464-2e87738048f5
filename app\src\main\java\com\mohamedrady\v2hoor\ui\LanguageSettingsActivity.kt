package com.mohamedrady.v2hoor.ui

import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import android.widget.ArrayAdapter
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityLanguageSettingsBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.service.LanguageManager
import kotlinx.coroutines.launch

/**
 * Language Settings Activity
 * Allows users to change app language and locale settings
 */
class LanguageSettingsActivity : BaseActivity() {
    
    private lateinit var binding: ActivityLanguageSettingsBinding
    private lateinit var languageManager: LanguageManager
    
    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase?.let { 
            LanguageManager.getInstance(it).applyLanguage(it, 
                LanguageManager.getInstance(it).getCurrentLanguageCode()
            ) 
        })
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("LanguageSettings", "🌍 Starting LanguageSettingsActivity")
        
        binding = ActivityLanguageSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Initialize language manager
        languageManager = LanguageManager.getInstance(this)
        
        setupToolbar()
        setupLanguageOptions()
        setupObservers()
        setupClickListeners()
        loadCurrentSettings()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.language_settings)
        }
    }
    
    private fun setupLanguageOptions() {
        // Setup language spinner
        val languages = languageManager.getAvailableLanguages()
        val languageNames = languages.map { languageManager.getLanguageDisplayName(it) }
        
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, languageNames)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerLanguage.adapter = adapter
        
        // Set current language selection
        val currentLanguage = languageManager.getCurrentLanguage()
        val currentIndex = languages.indexOf(currentLanguage)
        if (currentIndex >= 0) {
            binding.spinnerLanguage.setSelection(currentIndex)
        }
    }
    
    private fun setupObservers() {
        // Observe language changes
        languageManager.currentLanguage.observe(this) { language ->
            updateLanguageDisplay(language)
        }
        
        // Observe RTL status
        languageManager.isRTL.observe(this) { isRTL ->
            updateRTLIndicator(isRTL)
        }
    }
    
    private fun setupClickListeners() {
        // Language selection
        binding.spinnerLanguage.setOnItemSelectedListener(object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                val selectedLanguage = languageManager.getAvailableLanguages()[position]
                if (selectedLanguage.code != languageManager.getCurrentLanguageCode()) {
                    changeLanguage(selectedLanguage.code)
                }
            }
            
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        })
        
        // Auto detection toggle
        binding.switchAutoDetection.setOnCheckedChangeListener { _, isChecked ->
            languageManager.setAutoLanguageDetection(isChecked)
            if (isChecked) {
                languageManager.detectAndApplySystemLanguage()
                toast("تم تفعيل الكشف التلقائي للغة")
            } else {
                toast("تم إيقاف الكشف التلقائي للغة")
            }
        }
        
        // RTL support toggle
        binding.switchRTLSupport.setOnCheckedChangeListener { _, isChecked ->
            languageManager.setRTLSupport(isChecked)
            toast(if (isChecked) "تم تفعيل دعم RTL" else "تم إيقاف دعم RTL")
        }
        
        // Apply language button
        binding.btnApplyLanguage.setOnClickListener {
            applyLanguageChanges()
        }
        
        // Reset to default
        binding.btnResetLanguage.setOnClickListener {
            resetToDefault()
        }
        
        // Language preview buttons
        binding.btnPreviewArabic.setOnClickListener {
            previewLanguage(LanguageManager.LANGUAGE_ARABIC)
        }
        
        binding.btnPreviewEnglish.setOnClickListener {
            previewLanguage(LanguageManager.LANGUAGE_ENGLISH)
        }
    }
    
    private fun loadCurrentSettings() {
        // Load auto detection setting
        binding.switchAutoDetection.isChecked = languageManager.isAutoDetectionEnabled()
        
        // Load RTL support setting
        binding.switchRTLSupport.isChecked = languageManager.isRTLSupportEnabled()
        
        // Load language stats
        updateLanguageStats()
    }
    
    private fun changeLanguage(languageCode: String) {
        lifecycleScope.launch {
            try {
                languageManager.changeLanguage(languageCode)
                val language = languageManager.getLanguageByCode(languageCode)
                toast("تم تغيير اللغة إلى: ${language.nativeName}")
                
                // Show restart dialog
                showRestartDialog()
                
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("LanguageSettings", "Language changed to: $languageCode")
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("LanguageSettings", "Failed to change language", e)
                toast("فشل في تغيير اللغة")
            }
        }
    }
    
    private fun applyLanguageChanges() {
        try {
            // Apply current language to this activity
            val newContext = languageManager.applyLanguage(this, languageManager.getCurrentLanguageCode())
            
            // Restart activity to apply changes
            languageManager.restartActivity(this)
            
            toast("تم تطبيق تغييرات اللغة")
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("LanguageSettings", "Failed to apply language changes", e)
            toast("فشل في تطبيق تغييرات اللغة")
        }
    }
    
    private fun resetToDefault() {
        AlertDialog.Builder(this)
            .setTitle("إعادة تعيين اللغة")
            .setMessage("هل تريد إعادة تعيين إعدادات اللغة إلى الافتراضية؟")
            .setPositiveButton("نعم") { _, _ ->
                languageManager.resetToDefault()
                loadCurrentSettings()
                setupLanguageOptions()
                toast("تم إعادة تعيين إعدادات اللغة")
            }
            .setNegativeButton("لا", null)
            .show()
    }
    
    private fun previewLanguage(languageCode: String) {
        val language = languageManager.getLanguageByCode(languageCode)
        
        AlertDialog.Builder(this)
            .setTitle("معاينة اللغة")
            .setMessage("هذه معاينة للغة ${language.nativeName}. هل تريد تطبيق هذه اللغة؟")
            .setPositiveButton("تطبيق") { _, _ ->
                changeLanguage(languageCode)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showRestartDialog() {
        AlertDialog.Builder(this)
            .setTitle("إعادة تشغيل مطلوبة")
            .setMessage("لتطبيق تغييرات اللغة بالكامل، يجب إعادة تشغيل التطبيق. هل تريد إعادة التشغيل الآن؟")
            .setPositiveButton("إعادة التشغيل") { _, _ ->
                languageManager.restartActivity(this)
            }
            .setNegativeButton("لاحقاً", null)
            .setCancelable(false)
            .show()
    }
    
    private fun updateLanguageDisplay(language: LanguageManager.Language) {
        binding.tvCurrentLanguage.text = "اللغة الحالية: ${language.nativeName}"
        binding.tvLanguageCode.text = "رمز اللغة: ${language.code}"
    }
    
    private fun updateRTLIndicator(isRTL: Boolean) {
        binding.ivRTLIndicator.setImageResource(
            if (isRTL) R.drawable.ic_format_textdirection_r_to_l_24dp 
            else R.drawable.ic_format_textdirection_l_to_r_24dp
        )
        
        binding.tvRTLStatus.text = if (isRTL) "اتجاه النص: من اليمين لليسار" else "اتجاه النص: من اليسار لليمين"
    }
    
    private fun updateLanguageStats() {
        val stats = languageManager.getLanguageStats()
        
        binding.tvStatsCurrentLanguage.text = "اللغة: ${stats["current_language"]}"
        binding.tvStatsLanguageCode.text = "الرمز: ${stats["language_code"]}"
        binding.tvStatsRTL.text = "RTL: ${stats["is_rtl"]}"
        binding.tvStatsAutoDetection.text = "الكشف التلقائي: ${stats["auto_detection"]}"
        binding.tvStatsRTLSupport.text = "دعم RTL: ${stats["rtl_support"]}"
        binding.tvStatsSystemLanguage.text = "لغة النظام: ${stats["system_language"]}"
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Update stats when activity resumes
        updateLanguageStats()
    }
}
