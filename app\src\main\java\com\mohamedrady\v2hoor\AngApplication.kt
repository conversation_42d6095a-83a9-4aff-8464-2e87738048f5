package com.mohamedrady.v2hoor

import android.content.Context
import androidx.multidex.MultiDexApplication
import androidx.work.Configuration
import androidx.work.WorkManager
import com.tencent.mmkv.MMKV
import com.mohamedrady.v2hoor.AppConfig.ANG_PACKAGE
import com.mohamedrady.v2hoor.handler.SettingsManager
import com.mohamedrady.v2hoor.service.ThemeManager

class AngApplication : MultiDexApplication() {
    companion object {
        lateinit var application: AngApplication
    }

    /**
     * Attaches the base context to the application.
     * @param base The base context.
     */
    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        application = this
    }

    private val workManagerConfiguration: Configuration = Configuration.Builder()
        .setDefaultProcessName("${ANG_PACKAGE}:bg")
        .build()

    /**
     * Initializes the application.
     */
    override fun onCreate() {
        super.onCreate()

        // Initialize crash handler first
        com.mohamedrady.v2hoor.util.CrashHandler.init(this)

        // Start error monitoring
        com.mohamedrady.v2hoor.util.ErrorMonitor.startMonitoring(this)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "🚀 V2HoorVPN Application starting")

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "💾 Initializing MMKV")
        MMKV.initialize(this)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "🌙 Initializing theme manager")
        try {
            val themeManager = ThemeManager.getInstance(this)
            // Apply saved theme on app start
            themeManager.checkAndApplyScheduledTheme()
            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "✅ Theme manager initialized successfully")
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("Application", "Failed to initialize theme manager", e)
            // Fallback to legacy night mode setting
            SettingsManager.setNightMode()
        }

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "⚙️ Initializing WorkManager")
        WorkManager.initialize(this, workManagerConfiguration)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "🛣️ Initializing routing rulesets")
        SettingsManager.initRoutingRulesets(this)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "🍞 Configuring Toasty")
        es.dmoral.toasty.Toasty.Config.getInstance()
            .setGravity(android.view.Gravity.BOTTOM, 0, 200)
            .apply()

        // Initialize admin permissions for super admin
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "👑 Initializing admin permissions")
        try {
            val adminService = com.mohamedrady.v2hoor.service.AdminPermissionService.getInstance()
            // Set super admin permissions if email matches
            val currentUser = com.google.firebase.auth.FirebaseAuth.getInstance().currentUser
            if (currentUser?.email == "<EMAIL>") {
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "👑 Setting super admin permissions for ${currentUser.email}")
                com.mohamedrady.v2hoor.handler.MmkvManager.encodeSettings("is_super_admin", true)
                com.mohamedrady.v2hoor.handler.MmkvManager.encodeSettings("is_admin", true)
            }
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("Application", "Failed to initialize admin permissions", e)
        }

        // Initialize log listener service
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "🎧 Initializing log listener service")
        try {
            com.mohamedrady.v2hoor.service.LogListenerService.startListening()
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("Application", "Failed to initialize log listener", e)
        }

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("Application", "✅ V2HoorVPN Application initialized successfully")
    }
}
