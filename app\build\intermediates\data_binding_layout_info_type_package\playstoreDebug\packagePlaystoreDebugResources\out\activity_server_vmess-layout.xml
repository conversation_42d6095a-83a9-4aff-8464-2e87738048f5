<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_server_vmess" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_server_vmess.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_server_vmess_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="67" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="66" endOffset="18"/></Target><Target tag="binding_1" include="layout_address_port"><Expressions/><location startLine="14" startOffset="8" endLine="14" endOffset="55"/></Target><Target tag="binding_1" include="layout_transport"><Expressions/><location startLine="55" startOffset="8" endLine="55" endOffset="52"/></Target><Target tag="binding_1" include="layout_tls"><Expressions/><location startLine="57" startOffset="8" endLine="57" endOffset="46"/></Target><Target id="@+id/et_id" view="EditText"><Expressions/><location startLine="27" startOffset="12" endLine="31" endOffset="42"/></Target><Target id="@+id/sp_security" view="Spinner"><Expressions/><location startLine="46" startOffset="12" endLine="52" endOffset="52"/></Target></Targets></Layout>