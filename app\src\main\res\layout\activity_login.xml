<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background"
    android:fitsSystemWindows="true"
    tools:context=".ui.LoginActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:fitsSystemWindows="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp">

            <!-- Login Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="32dp">

                    <!-- App Logo -->
                    <ImageView
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="24dp"
                        android:src="@mipmap/ic_launcher"
                        android:contentDescription="@string/app_name" />

                    <!-- Welcome Text -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:gravity="center"
                        android:text="@string/welcome_to_app"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_color" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="32dp"
                        android:gravity="center"
                        android:text="@string/login_subtitle"
                        android:textSize="14sp"
                        android:textColor="?android:attr/textColorSecondary" />

                    <!-- Email Input -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:orientation="horizontal"
                        android:background="@drawable/input_background"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="12dp"
                            android:src="@android:drawable/ic_dialog_email"
                            android:contentDescription="@string/email" />

                        <EditText
                            android:id="@+id/et_email"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@android:color/transparent"
                            android:hint="@string/email"
                            android:inputType="textEmailAddress"
                            android:maxLines="1"
                            android:textSize="16sp"
                            android:textColorHint="@color/secondary_text_color" />

                    </LinearLayout>

                    <!-- Password Input -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:orientation="horizontal"
                        android:background="@drawable/input_background"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="12dp"
                            android:src="@android:drawable/ic_lock_lock"
                            android:contentDescription="@string/password" />

                        <EditText
                            android:id="@+id/et_password"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@android:color/transparent"
                            android:hint="@string/password"
                            android:inputType="textPassword"
                            android:maxLines="1"
                            android:textSize="16sp"
                            android:textColorHint="@color/secondary_text_color" />

                        <ImageView
                            android:id="@+id/iv_toggle_password"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="8dp"
                            android:src="@drawable/ic_visibility_off"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:padding="4dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:contentDescription="@string/toggle_password_visibility" />

                    </LinearLayout>

                    <!-- Forgot Password -->
                    <TextView
                        android:id="@+id/tv_forgot_password"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginBottom="32dp"
                        android:text="@string/forgot_password"
                        android:textColor="@color/primary_color"
                        android:textSize="14sp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp" />

                    <!-- Login Button with Progress -->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp">

                        <Button
                            android:id="@+id/btn_login"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:text="@string/login"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:background="@drawable/login_button_background"
                            android:textColor="@android:color/white"
                            android:elevation="4dp" />

                        <ProgressBar
                            android:id="@+id/progress_bar"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerInParent="true"
                            android:indeterminateTint="@android:color/white"
                            android:visibility="gone" />

                    </RelativeLayout>

                    <!-- Google Sign In Button -->
                    <Button
                        android:id="@+id/btn_google_sign_in"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:layout_marginBottom="20dp"
                        android:text="@string/sign_in_with_google"
                        android:textSize="16sp"
                        android:drawableStart="@drawable/ic_google"
                        android:drawablePadding="12dp"
                        android:background="@drawable/google_button_background"
                        android:textColor="@color/google_text_color"
                        android:elevation="2dp" />



                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
