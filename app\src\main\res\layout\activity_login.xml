<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.LoginActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- App Logo -->
            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="32dp"
                android:src="@mipmap/ic_launcher"
                android:contentDescription="@string/app_name" />

            <!-- Welcome Text -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="@string/welcome_to_app"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:gravity="center"
                android:text="@string/login_subtitle"
                android:textSize="16sp"
                android:textColor="?android:attr/textColorSecondary" />

            <!-- Email Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/email"
                app:startIconDrawable="@android:drawable/ic_dialog_email">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="@string/password"
                app:startIconDrawable="@android:drawable/ic_lock_lock"
                app:endIconMode="password_toggle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Forgot Password -->
            <TextView
                android:id="@+id/tv_forgot_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginBottom="24dp"
                android:text="@string/forgot_password"
                android:textColor="?attr/colorPrimary"
                android:textSize="14sp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp" />

            <!-- Login Button -->
            <Button
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="12dp"
                android:text="@string/login"
                android:textSize="16sp"
                android:background="?attr/colorPrimary"
                android:textColor="@android:color/white" />

            <!-- Register Button -->
            <Button
                android:id="@+id/btn_register"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:text="@string/register"
                android:textSize="16sp"
                android:background="@android:color/transparent"
                android:textColor="?attr/colorPrimary"
                style="?android:attr/borderlessButtonStyle" />

            <!-- Google Sign In Button -->
            <Button
                android:id="@+id/btn_google_sign_in"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:text="@string/sign_in_with_google"
                android:textSize="16sp"
                android:drawableStart="@drawable/ic_google"
                android:drawablePadding="8dp"
                android:background="@android:color/transparent"
                android:textColor="?attr/colorPrimary"
                style="?android:attr/borderlessButtonStyle" />

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="16dp"
                android:background="?android:attr/listDivider" />

            <!-- Skip Login Button -->
            <Button
                android:id="@+id/btn_skip_login"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="@string/skip_login"
                android:textSize="14sp"
                style="@style/Widget.MaterialComponents.Button.TextButton" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
