<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="161" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="16" endLine="74" endOffset="42"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="87" startOffset="16" endLine="92" endOffset="42"/></Target><Target id="@+id/tv_forgot_password" view="TextView"><Expressions/><location startLine="97" startOffset="12" endLine="109" endOffset="39"/></Target><Target id="@+id/btn_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="112" startOffset="12" endLine="119" endOffset="40"/></Target><Target id="@+id/btn_register" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="122" startOffset="12" endLine="130" endOffset="71"/></Target><Target id="@+id/btn_skip_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="140" startOffset="12" endLine="146" endOffset="67"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="149" startOffset="12" endLine="155" endOffset="43"/></Target></Targets></Layout>