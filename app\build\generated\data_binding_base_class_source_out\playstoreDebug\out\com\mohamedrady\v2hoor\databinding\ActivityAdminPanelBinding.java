// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAdminPanelBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialCardView cardAddServer;

  @NonNull
  public final MaterialCardView cardAdminInfo;

  @NonNull
  public final MaterialCardView cardPromoteUser;

  @NonNull
  public final MaterialCardView cardRealTimeLogs;

  @NonNull
  public final MaterialCardView cardServerLogs;

  @NonNull
  public final MaterialCardView cardServerManagement;

  @NonNull
  public final MaterialCardView cardServerUpdate;

  @NonNull
  public final MaterialCardView cardSystemSettings;

  @NonNull
  public final MaterialCardView cardUserManagement;

  @NonNull
  public final MaterialCardView cardViewLogs;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAdminEmail;

  @NonNull
  public final TextView tvAdminLevel;

  @NonNull
  public final TextView tvPermissionsCount;

  @NonNull
  public final TextView tvUpdateStatus;

  private ActivityAdminPanelBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialCardView cardAddServer, @NonNull MaterialCardView cardAdminInfo,
      @NonNull MaterialCardView cardPromoteUser, @NonNull MaterialCardView cardRealTimeLogs,
      @NonNull MaterialCardView cardServerLogs, @NonNull MaterialCardView cardServerManagement,
      @NonNull MaterialCardView cardServerUpdate, @NonNull MaterialCardView cardSystemSettings,
      @NonNull MaterialCardView cardUserManagement, @NonNull MaterialCardView cardViewLogs,
      @NonNull ProgressBar progressBar, @NonNull Toolbar toolbar, @NonNull TextView tvAdminEmail,
      @NonNull TextView tvAdminLevel, @NonNull TextView tvPermissionsCount,
      @NonNull TextView tvUpdateStatus) {
    this.rootView = rootView;
    this.cardAddServer = cardAddServer;
    this.cardAdminInfo = cardAdminInfo;
    this.cardPromoteUser = cardPromoteUser;
    this.cardRealTimeLogs = cardRealTimeLogs;
    this.cardServerLogs = cardServerLogs;
    this.cardServerManagement = cardServerManagement;
    this.cardServerUpdate = cardServerUpdate;
    this.cardSystemSettings = cardSystemSettings;
    this.cardUserManagement = cardUserManagement;
    this.cardViewLogs = cardViewLogs;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
    this.tvAdminEmail = tvAdminEmail;
    this.tvAdminLevel = tvAdminLevel;
    this.tvPermissionsCount = tvPermissionsCount;
    this.tvUpdateStatus = tvUpdateStatus;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAdminPanelBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAdminPanelBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_admin_panel, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAdminPanelBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_add_server;
      MaterialCardView cardAddServer = ViewBindings.findChildViewById(rootView, id);
      if (cardAddServer == null) {
        break missingId;
      }

      id = R.id.card_admin_info;
      MaterialCardView cardAdminInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardAdminInfo == null) {
        break missingId;
      }

      id = R.id.card_promote_user;
      MaterialCardView cardPromoteUser = ViewBindings.findChildViewById(rootView, id);
      if (cardPromoteUser == null) {
        break missingId;
      }

      id = R.id.card_real_time_logs;
      MaterialCardView cardRealTimeLogs = ViewBindings.findChildViewById(rootView, id);
      if (cardRealTimeLogs == null) {
        break missingId;
      }

      id = R.id.card_server_logs;
      MaterialCardView cardServerLogs = ViewBindings.findChildViewById(rootView, id);
      if (cardServerLogs == null) {
        break missingId;
      }

      id = R.id.card_server_management;
      MaterialCardView cardServerManagement = ViewBindings.findChildViewById(rootView, id);
      if (cardServerManagement == null) {
        break missingId;
      }

      id = R.id.card_server_update;
      MaterialCardView cardServerUpdate = ViewBindings.findChildViewById(rootView, id);
      if (cardServerUpdate == null) {
        break missingId;
      }

      id = R.id.card_system_settings;
      MaterialCardView cardSystemSettings = ViewBindings.findChildViewById(rootView, id);
      if (cardSystemSettings == null) {
        break missingId;
      }

      id = R.id.card_user_management;
      MaterialCardView cardUserManagement = ViewBindings.findChildViewById(rootView, id);
      if (cardUserManagement == null) {
        break missingId;
      }

      id = R.id.card_view_logs;
      MaterialCardView cardViewLogs = ViewBindings.findChildViewById(rootView, id);
      if (cardViewLogs == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_admin_email;
      TextView tvAdminEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvAdminEmail == null) {
        break missingId;
      }

      id = R.id.tv_admin_level;
      TextView tvAdminLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvAdminLevel == null) {
        break missingId;
      }

      id = R.id.tv_permissions_count;
      TextView tvPermissionsCount = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionsCount == null) {
        break missingId;
      }

      id = R.id.tv_update_status;
      TextView tvUpdateStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvUpdateStatus == null) {
        break missingId;
      }

      return new ActivityAdminPanelBinding((CoordinatorLayout) rootView, cardAddServer,
          cardAdminInfo, cardPromoteUser, cardRealTimeLogs, cardServerLogs, cardServerManagement,
          cardServerUpdate, cardSystemSettings, cardUserManagement, cardViewLogs, progressBar,
          toolbar, tvAdminEmail, tvAdminLevel, tvPermissionsCount, tvUpdateStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
