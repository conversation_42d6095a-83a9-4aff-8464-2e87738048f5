@echo off
echo ========================================
echo   Firebase Realtime Database Setup
echo ========================================
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Firebase CLI not found!
    echo Please install Firebase CLI first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo Firebase CLI found. Proceeding with setup...
echo.

REM Login to Firebase (if not already logged in)
echo Checking Firebase authentication...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo Please login to Firebase:
    firebase login
    if %errorlevel% neq 0 (
        echo Failed to login to Firebase
        pause
        exit /b 1
    )
)

echo.
echo Setting Firebase project...
firebase use mrelfeky-209615
if %errorlevel% neq 0 (
    echo Failed to set Firebase project
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Deploying Realtime Database Rules
echo ========================================
echo.

REM Create database rules file if it doesn't exist
if not exist "database.rules.json" (
    echo Creating database.rules.json...
    (
        echo {
        echo   "rules": {
        echo     "users": {
        echo       "$uid": {
        echo         ".read": "auth != null && auth.uid == $uid",
        echo         ".write": "auth != null && auth.uid == $uid",
        echo         "servers": {
        echo           "$serverId": {
        echo             ".validate": "newData.hasChildren(['id', 'name', 'server', 'server_port', 'protocol', 'uuid'])"
        echo           }
        echo         }
        echo       }
        echo     },
        echo     "admin": {
        echo       ".read": "auth != null && auth.token.email == '<EMAIL>'",
        echo       ".write": "auth != null && auth.token.email == '<EMAIL>'"
        echo     }
        echo   }
        echo }
    ) > database.rules.json
)

REM Deploy database rules
echo Deploying Realtime Database rules...
firebase deploy --only database
if %errorlevel% neq 0 (
    echo Failed to deploy database rules
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Setting up Sample Data
echo ========================================
echo.

echo Sample data structure is available in firebase-realtime-database-structure.json
echo.
echo To add sample data for a user:
echo 1. Go to Firebase Console: https://console.firebase.google.com/project/mrelfeky-209615/database
echo 2. Select Realtime Database
echo 3. Import the JSON structure
echo 4. Replace USER_UID_PLACEHOLDER with actual user UID
echo.

echo ========================================
echo   Database Structure Information
echo ========================================
echo.
echo Database URL: https://mrelfeky-209615-default-rtdb.firebaseio.com/
echo.
echo Structure:
echo /users/{uid}/servers/
echo   - server1: { ... server configuration ... }
echo   - server2: { ... server configuration ... }
echo   - ...
echo.
echo Each server object contains:
echo - Basic info: id, name, remarks, server, server_port
echo - Protocol: protocol, uuid, alter_id, security
echo - Network: network, header_type, request_host, path
echo - TLS: tls, sni, alpn, fingerprint
echo - Advanced: public_key, short_id, spider_x, flow, encryption
echo - Metadata: country, city, flag, is_active, priority
echo - Timestamps: created_at, updated_at, expires_at, valid_until
echo - User data: subscription_id, user_id, tags
echo - Testing: test_result object
echo.

echo ========================================
echo   Testing Instructions
echo ========================================
echo.
echo 1. Install and run the V2Hoor app
echo 2. Login with a Firebase user account
echo 3. Add server data to /users/{user_uid}/servers/ in Firebase Console
echo 4. Use the "Refresh Firebase Servers" menu option in the app
echo 5. Servers should appear in the main server list
echo.

echo ========================================
echo   Firebase Realtime Database Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Add server data for your users in Firebase Console
echo 2. Test the app with real user accounts
echo 3. Monitor the database usage and performance
echo.

pause
