package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.AdminServerConfig
import com.mohamedrady.v2hoor.service.ServerManagementService
import kotlinx.coroutines.launch

/**
 * ViewModel for managing user servers
 */
class UserServersViewModel : ViewModel() {

    private val serverManagementService = ServerManagementService.getInstance()

    private val _userServers = MutableLiveData<List<AdminServerConfig>>()
    val userServers: LiveData<List<AdminServerConfig>> = _userServers

    private val _availableServers = MutableLiveData<List<AdminServerConfig>>()
    val availableServers: LiveData<List<AdminServerConfig>> = _availableServers

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    /**
     * Load servers assigned to user
     */
    fun loadUserServers(userId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val result = serverManagementService.getUserServers()
                result.fold(
                    onSuccess = { servers ->
                        _userServers.value = servers
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تحميل سيرفرات المستخدم: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ غير متوقع: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load all available servers
     */
    fun loadAvailableServers() {
        viewModelScope.launch {
            try {
                val result = serverManagementService.getAllServers()
                result.fold(
                    onSuccess = { servers ->
                        _availableServers.value = servers.filter { it.isActive }
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تحميل السيرفرات المتاحة: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في تحميل السيرفرات: ${e.message}"
            }
        }
    }

    /**
     * Assign server to user
     */
    fun assignServerToUser(userId: String, serverId: String) {
        viewModelScope.launch {
            _isLoading.value = true

            try {
                val result = serverManagementService.assignServerToUser(userId, serverId)
                result.fold(
                    onSuccess = {
                        // Reload user servers
                        loadUserServers(userId)
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تخصيص السيرفر: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في تخصيص السيرفر: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Remove server from user
     */
    fun removeServerFromUser(userId: String, serverId: String) {
        viewModelScope.launch {
            _isLoading.value = true

            try {
                val result = serverManagementService.removeServerFromUser(userId, serverId)
                result.fold(
                    onSuccess = {
                        // Reload user servers
                        loadUserServers(userId)
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في إزالة السيرفر: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في إزالة السيرفر: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Assign all available servers to user
     */
    fun assignAllServersToUser(userId: String) {
        viewModelScope.launch {
            _isLoading.value = true

            try {
                val availableServersList = _availableServers.value ?: emptyList()
                val userServersList = _userServers.value ?: emptyList()
                
                // Get servers not already assigned
                val serversToAssign = availableServersList.filter { available ->
                    userServersList.none { user -> user.id == available.id }
                }

                var successCount = 0
                var errorCount = 0

                for (server in serversToAssign) {
                    try {
                        val result = serverManagementService.assignServerToUser(userId, server.id)
                        if (result.isSuccess) {
                            successCount++
                        } else {
                            errorCount++
                        }
                    } catch (e: Exception) {
                        errorCount++
                    }
                }

                if (successCount > 0) {
                    loadUserServers(userId)
                }

                if (errorCount > 0) {
                    _error.value = "تم تخصيص $successCount سيرفر بنجاح، فشل في $errorCount سيرفر"
                }

            } catch (e: Exception) {
                _error.value = "خطأ في تخصيص السيرفرات: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Remove all servers from user
     */
    fun removeAllServersFromUser(userId: String) {
        viewModelScope.launch {
            _isLoading.value = true

            try {
                val userServersList = _userServers.value ?: emptyList()
                
                var successCount = 0
                var errorCount = 0

                for (server in userServersList) {
                    try {
                        val result = serverManagementService.removeServerFromUser(userId, server.id)
                        if (result.isSuccess) {
                            successCount++
                        } else {
                            errorCount++
                        }
                    } catch (e: Exception) {
                        errorCount++
                    }
                }

                if (successCount > 0) {
                    loadUserServers(userId)
                }

                if (errorCount > 0) {
                    _error.value = "تم إزالة $successCount سيرفر بنجاح، فشل في $errorCount سيرفر"
                }

            } catch (e: Exception) {
                _error.value = "خطأ في إزالة السيرفرات: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear error
     */
    fun clearError() {
        _error.value = null
    }
}
