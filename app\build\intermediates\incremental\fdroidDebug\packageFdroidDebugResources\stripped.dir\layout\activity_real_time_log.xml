<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Control Panel -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp"
        android:background="@color/control_panel_background">

        <!-- Status and Stats Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/textViewStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🟢 يعمل"
                android:textSize="14sp"
                android:textStyle="bold" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/textViewStats"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📊 Total: 0"
                android:textSize="12sp"
                android:fontFamily="monospace" />

        </LinearLayout>

        <!-- Controls Row 1 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Spinner
                android:id="@+id/spinnerLogLevel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/buttonFilter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🔍 فلتر"
                android:textSize="12sp"
                android:layout_marginEnd="4dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <Button
                android:id="@+id/buttonPauseResume"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⏸️ إيقاف"
                android:textSize="12sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        </LinearLayout>

        <!-- Controls Row 2 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchAutoScroll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تمرير تلقائي"
                android:textSize="12sp"
                android:checked="true" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/buttonClearLogs"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🗑️ مسح"
                android:textSize="12sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_color" />

    <!-- Log List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewLogs"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical"
        android:fadeScrollbars="false" />

</LinearLayout>
