// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityUserServersBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button buttonRefresh;

  @NonNull
  public final FloatingActionButton fabAddServer;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewUserServers;

  @NonNull
  public final TextView textViewUserServersCount;

  @NonNull
  public final Toolbar toolbar;

  private ActivityUserServersBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button buttonRefresh, @NonNull FloatingActionButton fabAddServer,
      @NonNull LinearLayout layoutEmptyState, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewUserServers, @NonNull TextView textViewUserServersCount,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonRefresh = buttonRefresh;
    this.fabAddServer = fabAddServer;
    this.layoutEmptyState = layoutEmptyState;
    this.progressBar = progressBar;
    this.recyclerViewUserServers = recyclerViewUserServers;
    this.textViewUserServersCount = textViewUserServersCount;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityUserServersBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityUserServersBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_user_servers, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityUserServersBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonRefresh;
      Button buttonRefresh = ViewBindings.findChildViewById(rootView, id);
      if (buttonRefresh == null) {
        break missingId;
      }

      id = R.id.fabAddServer;
      FloatingActionButton fabAddServer = ViewBindings.findChildViewById(rootView, id);
      if (fabAddServer == null) {
        break missingId;
      }

      id = R.id.layoutEmptyState;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewUserServers;
      RecyclerView recyclerViewUserServers = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewUserServers == null) {
        break missingId;
      }

      id = R.id.textViewUserServersCount;
      TextView textViewUserServersCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserServersCount == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityUserServersBinding((CoordinatorLayout) rootView, buttonRefresh,
          fabAddServer, layoutEmptyState, progressBar, recyclerViewUserServers,
          textViewUserServersCount, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
