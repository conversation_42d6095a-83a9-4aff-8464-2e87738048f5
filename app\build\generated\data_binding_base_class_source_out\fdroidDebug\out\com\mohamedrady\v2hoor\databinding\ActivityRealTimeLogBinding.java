// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRealTimeLogBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button buttonClearLogs;

  @NonNull
  public final Button buttonFilter;

  @NonNull
  public final Button buttonPauseResume;

  @NonNull
  public final RecyclerView recyclerViewLogs;

  @NonNull
  public final Spinner spinnerLogLevel;

  @NonNull
  public final SwitchCompat switchAutoScroll;

  @NonNull
  public final TextView textViewStats;

  @NonNull
  public final TextView textViewStatus;

  @NonNull
  public final Toolbar toolbar;

  private ActivityRealTimeLogBinding(@NonNull LinearLayout rootView,
      @NonNull Button buttonClearLogs, @NonNull Button buttonFilter,
      @NonNull Button buttonPauseResume, @NonNull RecyclerView recyclerViewLogs,
      @NonNull Spinner spinnerLogLevel, @NonNull SwitchCompat switchAutoScroll,
      @NonNull TextView textViewStats, @NonNull TextView textViewStatus, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonClearLogs = buttonClearLogs;
    this.buttonFilter = buttonFilter;
    this.buttonPauseResume = buttonPauseResume;
    this.recyclerViewLogs = recyclerViewLogs;
    this.spinnerLogLevel = spinnerLogLevel;
    this.switchAutoScroll = switchAutoScroll;
    this.textViewStats = textViewStats;
    this.textViewStatus = textViewStatus;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRealTimeLogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRealTimeLogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_real_time_log, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRealTimeLogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonClearLogs;
      Button buttonClearLogs = ViewBindings.findChildViewById(rootView, id);
      if (buttonClearLogs == null) {
        break missingId;
      }

      id = R.id.buttonFilter;
      Button buttonFilter = ViewBindings.findChildViewById(rootView, id);
      if (buttonFilter == null) {
        break missingId;
      }

      id = R.id.buttonPauseResume;
      Button buttonPauseResume = ViewBindings.findChildViewById(rootView, id);
      if (buttonPauseResume == null) {
        break missingId;
      }

      id = R.id.recyclerViewLogs;
      RecyclerView recyclerViewLogs = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewLogs == null) {
        break missingId;
      }

      id = R.id.spinnerLogLevel;
      Spinner spinnerLogLevel = ViewBindings.findChildViewById(rootView, id);
      if (spinnerLogLevel == null) {
        break missingId;
      }

      id = R.id.switchAutoScroll;
      SwitchCompat switchAutoScroll = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoScroll == null) {
        break missingId;
      }

      id = R.id.textViewStats;
      TextView textViewStats = ViewBindings.findChildViewById(rootView, id);
      if (textViewStats == null) {
        break missingId;
      }

      id = R.id.textViewStatus;
      TextView textViewStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityRealTimeLogBinding((LinearLayout) rootView, buttonClearLogs, buttonFilter,
          buttonPauseResume, recyclerViewLogs, spinnerLogLevel, switchAutoScroll, textViewStats,
          textViewStatus, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
