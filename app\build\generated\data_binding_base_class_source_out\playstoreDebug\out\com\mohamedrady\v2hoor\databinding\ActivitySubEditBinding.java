// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySubEditBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final SwitchCompat allowInsecureUrl;

  @NonNull
  public final SwitchCompat autoUpdateCheck;

  @NonNull
  public final SwitchCompat chkEnable;

  @NonNull
  public final EditText etFilter;

  @NonNull
  public final EditText etNextProfile;

  @NonNull
  public final EditText etPreProfile;

  @NonNull
  public final EditText etRemarks;

  @NonNull
  public final EditText etUrl;

  private ActivitySubEditBinding(@NonNull ScrollView rootView,
      @NonNull SwitchCompat allowInsecureUrl, @NonNull SwitchCompat autoUpdateCheck,
      @NonNull SwitchCompat chkEnable, @NonNull EditText etFilter, @NonNull EditText etNextProfile,
      @NonNull EditText etPreProfile, @NonNull EditText etRemarks, @NonNull EditText etUrl) {
    this.rootView = rootView;
    this.allowInsecureUrl = allowInsecureUrl;
    this.autoUpdateCheck = autoUpdateCheck;
    this.chkEnable = chkEnable;
    this.etFilter = etFilter;
    this.etNextProfile = etNextProfile;
    this.etPreProfile = etPreProfile;
    this.etRemarks = etRemarks;
    this.etUrl = etUrl;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySubEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySubEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_sub_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySubEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.allow_insecure_url;
      SwitchCompat allowInsecureUrl = ViewBindings.findChildViewById(rootView, id);
      if (allowInsecureUrl == null) {
        break missingId;
      }

      id = R.id.auto_update_check;
      SwitchCompat autoUpdateCheck = ViewBindings.findChildViewById(rootView, id);
      if (autoUpdateCheck == null) {
        break missingId;
      }

      id = R.id.chk_enable;
      SwitchCompat chkEnable = ViewBindings.findChildViewById(rootView, id);
      if (chkEnable == null) {
        break missingId;
      }

      id = R.id.et_filter;
      EditText etFilter = ViewBindings.findChildViewById(rootView, id);
      if (etFilter == null) {
        break missingId;
      }

      id = R.id.et_next_profile;
      EditText etNextProfile = ViewBindings.findChildViewById(rootView, id);
      if (etNextProfile == null) {
        break missingId;
      }

      id = R.id.et_pre_profile;
      EditText etPreProfile = ViewBindings.findChildViewById(rootView, id);
      if (etPreProfile == null) {
        break missingId;
      }

      id = R.id.et_remarks;
      EditText etRemarks = ViewBindings.findChildViewById(rootView, id);
      if (etRemarks == null) {
        break missingId;
      }

      id = R.id.et_url;
      EditText etUrl = ViewBindings.findChildViewById(rootView, id);
      if (etUrl == null) {
        break missingId;
      }

      return new ActivitySubEditBinding((ScrollView) rootView, allowInsecureUrl, autoUpdateCheck,
          chkEnable, etFilter, etNextProfile, etPreProfile, etRemarks, etUrl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
