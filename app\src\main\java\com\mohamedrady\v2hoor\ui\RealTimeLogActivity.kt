package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.ArrayAdapter
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityRealTimeLogBinding
import com.mohamedrady.v2hoor.service.LogListenerService
import com.mohamedrady.v2hoor.ui.adapter.LogEntryAdapter
import com.mohamedrady.v2hoor.util.CrashHandler
import kotlinx.coroutines.launch

class RealTimeLogActivity : AppCompatActivity() {
    private lateinit var binding: ActivityRealTimeLogBinding
    private lateinit var logAdapter: LogEntryAdapter
    private var autoScroll = true
    private var currentLogLevel = LogListenerService.LogLevel.DEBUG

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRealTimeLogBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupControls()
        startLogListening()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "مراقب السجلات المباشر"
        }
    }

    private fun setupRecyclerView() {
        logAdapter = LogEntryAdapter { entry ->
            // Handle log entry click - show details
            showLogDetails(entry)
        }
        
        binding.recyclerViewLogs.apply {
            layoutManager = LinearLayoutManager(this@RealTimeLogActivity)
            adapter = logAdapter
            
            // Auto-scroll to bottom when new logs arrive
            logAdapter.registerAdapterDataObserver(object : androidx.recyclerview.widget.RecyclerView.AdapterDataObserver() {
                override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                    if (autoScroll) {
                        scrollToPosition(logAdapter.itemCount - 1)
                    }
                }
            })
        }
    }

    private fun setupControls() {
        // Auto-scroll toggle
        binding.switchAutoScroll.isChecked = autoScroll
        binding.switchAutoScroll.setOnCheckedChangeListener { _, isChecked ->
            autoScroll = isChecked
            CrashHandler.logInfo("RealTimeLog", "Auto-scroll: $autoScroll")
        }

        // Clear logs button
        binding.buttonClearLogs.setOnClickListener {
            clearLogs()
        }

        // Pause/Resume button
        binding.buttonPauseResume.setOnClickListener {
            toggleLogListening()
        }

        // Filter button
        binding.buttonFilter.setOnClickListener {
            showFilterDialog()
        }

        // Log level spinner
        val logLevels = LogListenerService.LogLevel.values().map { it.name }
        val levelAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, logLevels)
        levelAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerLogLevel.adapter = levelAdapter
        binding.spinnerLogLevel.setSelection(currentLogLevel.ordinal)
        
        binding.spinnerLogLevel.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                currentLogLevel = LogListenerService.LogLevel.values()[position]
                LogListenerService.setMinLogLevel(currentLogLevel)
                refreshLogs()
            }
            
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        }
    }

    private fun startLogListening() {
        // Start the log listener service
        LogListenerService.startListening()
        
        // Collect logs from the flow
        lifecycleScope.launch {
            LogListenerService.logFlow.collect { logEntry ->
                runOnUiThread {
                    logAdapter.addLogEntry(logEntry)
                    updateLogStats()
                }
            }
        }
        
        // Load existing logs
        refreshLogs()
        updateUI()
    }

    private fun refreshLogs() {
        val logs = LogListenerService.getAllLogs()
        logAdapter.setLogs(logs)
        updateLogStats()
    }

    private fun updateLogStats() {
        val stats = LogListenerService.getLogStats()
        val statusText = "📊 Total: ${stats["total"]} | " +
                "❌ Errors: ${stats["errors"]} | " +
                "⚠️ Warnings: ${stats["warnings"]} | " +
                "ℹ️ Info: ${stats["info"]}"
        
        binding.textViewStats.text = statusText
    }

    private fun updateUI() {
        val isListening = LogListenerService.isListening()
        binding.buttonPauseResume.text = if (isListening) "⏸️ إيقاف مؤقت" else "▶️ استئناف"
        binding.textViewStatus.text = if (isListening) "🟢 يعمل" else "🔴 متوقف"
    }

    private fun toggleLogListening() {
        if (LogListenerService.isListening()) {
            LogListenerService.stopListening()
            CrashHandler.logInfo("RealTimeLog", "Log listening paused")
        } else {
            LogListenerService.startListening()
            CrashHandler.logInfo("RealTimeLog", "Log listening resumed")
        }
        updateUI()
    }

    private fun clearLogs() {
        AlertDialog.Builder(this)
            .setTitle("مسح السجلات")
            .setMessage("هل تريد مسح جميع السجلات؟")
            .setPositiveButton("نعم") { _, _ ->
                LogListenerService.clearLogs()
                logAdapter.clearLogs()
                updateLogStats()
                CrashHandler.logInfo("RealTimeLog", "Logs cleared by user")
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showFilterDialog() {
        val options = arrayOf(
            "فلترة حسب العلامة (Tag)",
            "فلترة حسب الرسالة",
            "إزالة جميع الفلاتر"
        )
        
        AlertDialog.Builder(this)
            .setTitle("خيارات الفلترة")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> showTagFilterDialog()
                    1 -> showMessageFilterDialog()
                    2 -> {
                        LogListenerService.clearFilters()
                        refreshLogs()
                    }
                }
            }
            .show()
    }

    private fun showTagFilterDialog() {
        val input = android.widget.EditText(this)
        input.hint = "أدخل العلامة للفلترة"
        
        AlertDialog.Builder(this)
            .setTitle("فلترة حسب العلامة")
            .setView(input)
            .setPositiveButton("تطبيق") { _, _ ->
                val tag = input.text.toString().trim()
                if (tag.isNotEmpty()) {
                    LogListenerService.addTagFilter(tag)
                    refreshLogs()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showMessageFilterDialog() {
        val input = android.widget.EditText(this)
        input.hint = "أدخل النص للبحث في الرسائل"
        
        AlertDialog.Builder(this)
            .setTitle("فلترة حسب الرسالة")
            .setView(input)
            .setPositiveButton("تطبيق") { _, _ ->
                val message = input.text.toString().trim()
                if (message.isNotEmpty()) {
                    LogListenerService.addMessageFilter(message)
                    refreshLogs()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showLogDetails(entry: LogListenerService.LogEntry) {
        val details = """
            🕐 الوقت: ${entry.timestamp}
            📊 المستوى: ${entry.level}
            🏷️ العلامة: ${entry.tag}
            💬 الرسالة: ${entry.message}
            ${if (entry.pid.isNotEmpty()) "🔢 PID: ${entry.pid}" else ""}
            ${if (entry.tid.isNotEmpty()) "🧵 TID: ${entry.tid}" else ""}
        """.trimIndent()
        
        AlertDialog.Builder(this)
            .setTitle("تفاصيل السجل")
            .setMessage(details)
            .setPositiveButton("موافق", null)
            .setNeutralButton("نسخ") { _, _ ->
                val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
                val clip = android.content.ClipData.newPlainText("Log Entry", details)
                clipboard.setPrimaryClip(clip)
                android.widget.Toast.makeText(this, "تم النسخ", android.widget.Toast.LENGTH_SHORT).show()
            }
            .show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_real_time_log, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_export_logs -> {
                exportLogs()
                true
            }
            R.id.action_settings -> {
                showSettingsDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun exportLogs() {
        try {
            val logs = LogListenerService.getAllLogs()
            val logText = logs.joinToString("\n") { entry ->
                "${entry.timestamp} [${entry.level}] ${entry.tag}: ${entry.message}"
            }
            
            val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("V2HoorVPN Logs", logText)
            clipboard.setPrimaryClip(clip)
            
            android.widget.Toast.makeText(this, "تم نسخ السجلات إلى الحافظة", android.widget.Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            CrashHandler.logError("RealTimeLog", "Failed to export logs", e)
            android.widget.Toast.makeText(this, "فشل في تصدير السجلات", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    private fun showSettingsDialog() {
        // Implementation for settings dialog
        android.widget.Toast.makeText(this, "الإعدادات قريباً", android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Keep the service running in background
        CrashHandler.logInfo("RealTimeLog", "Activity destroyed, service continues running")
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
