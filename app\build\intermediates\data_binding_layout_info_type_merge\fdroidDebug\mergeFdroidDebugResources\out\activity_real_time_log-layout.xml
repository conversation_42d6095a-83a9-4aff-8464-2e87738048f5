<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_real_time_log" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_real_time_log.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_real_time_log_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="139" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="12" startOffset="8" endLine="17" endOffset="59"/></Target><Target id="@+id/textViewStatus" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="43" endOffset="42"/></Target><Target id="@+id/textViewStats" view="TextView"><Expressions/><location startLine="50" startOffset="12" endLine="56" endOffset="48"/></Target><Target id="@+id/spinnerLogLevel" view="Spinner"><Expressions/><location startLine="67" startOffset="12" endLine="72" endOffset="48"/></Target><Target id="@+id/buttonFilter" view="Button"><Expressions/><location startLine="74" startOffset="12" endLine="81" endOffset="80"/></Target><Target id="@+id/buttonPauseResume" view="Button"><Expressions/><location startLine="83" startOffset="12" endLine="89" endOffset="80"/></Target><Target id="@+id/switchAutoScroll" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="99" startOffset="12" endLine="105" endOffset="40"/></Target><Target id="@+id/buttonClearLogs" view="Button"><Expressions/><location startLine="112" startOffset="12" endLine="118" endOffset="80"/></Target><Target id="@+id/recyclerViewLogs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="131" startOffset="4" endLine="137" endOffset="40"/></Target></Targets></Layout>