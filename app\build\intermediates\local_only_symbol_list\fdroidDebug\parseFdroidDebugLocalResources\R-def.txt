R_DEF: Internal format may change without notice
local
array allowinsecures
array core_loglevel
array flows
array fragment_packets
array header_type_kcp_and_quic
array header_type_tcp
array language_select
array language_select_value
array mode_entries
array mode_type_grpc
array mode_value
array mux_xudp_quic_entries
array mux_xudp_quic_value
array networks
array outbound_tag
array preset_rulesets
array routing_domain_strategy
array securitys
array share_method
array share_method_more
array share_sub_method
array ss_securitys
array streamsecurity_alpn
array streamsecurity_utls
array streamsecuritys
array streamsecurityxs
array ui_mode_night
array ui_mode_night_value
array vpn_bypass_lan
array vpn_bypass_lan_value
array xhttp_mode
bool config_materialPreferenceIconSpaceReserved
color background_color
color blue
color card_background
color colorAccent
color colorConfigType
color colorPing
color colorPingRed
color colorPrimary
color color_fab_active
color color_fab_inactive
color color_highlight_material
color color_secondary
color divider_color
color divider_color_light
color google_text_color
color gray
color green
color ic_banner_background
color ic_launcher_background
color light_gray
color login_background_end
color login_background_start
color orange
color primary_color
color primary_dark_color
color purple
color red
color secondary_text_color
color text_primary
color text_secondary
color white
dimen image_size_dp24
dimen padding_spacing_dp16
dimen padding_spacing_dp4
dimen padding_spacing_dp8
dimen preference_category_padding_start
dimen view_height_dp160
dimen view_height_dp36
dimen view_height_dp48
dimen view_height_dp64
drawable bg_button_danger
drawable bg_button_primary
drawable bg_button_secondary
drawable bg_role_badge
drawable bg_search
drawable bg_status_badge
drawable custom_divider
drawable google_button_background
drawable ic_about_24dp
drawable ic_action_done
drawable ic_add
drawable ic_add_24dp
drawable ic_admin_panel
drawable ic_arrow_forward
drawable ic_backup_24dp
drawable ic_bar_chart
drawable ic_check_update_24dp
drawable ic_circle
drawable ic_clear_all
drawable ic_cloud_download_24dp
drawable ic_copy
drawable ic_delete_24dp
drawable ic_description_24dp
drawable ic_download
drawable ic_edit_24dp
drawable ic_fab_check
drawable ic_feedback_24dp
drawable ic_file_24dp
drawable ic_filter
drawable ic_google
drawable ic_image_24dp
drawable ic_list
drawable ic_lock_24dp
drawable ic_logcat_24dp
drawable ic_menu
drawable ic_more_vert
drawable ic_more_vert_24dp
drawable ic_outline_filter_alt_24
drawable ic_people
drawable ic_per_apps_24dp
drawable ic_play_24dp
drawable ic_privacy_24dp
drawable ic_promotion_24dp
drawable ic_qu_scan_24dp
drawable ic_qu_switch_24dp
drawable ic_refresh
drawable ic_restore_24dp
drawable ic_rounded_corner_active
drawable ic_rounded_corner_inactive
drawable ic_routing_24dp
drawable ic_save_24dp
drawable ic_scan_24dp
drawable ic_search
drawable ic_select_all
drawable ic_select_all_24dp
drawable ic_server
drawable ic_server_off
drawable ic_settings
drawable ic_settings_24dp
drawable ic_share_24dp
drawable ic_source_code_24dp
drawable ic_star
drawable ic_stat_direct
drawable ic_stat_name
drawable ic_stat_name_black
drawable ic_stat_proxy
drawable ic_stop_24dp
drawable ic_subscriptions_24dp
drawable ic_telegram_24dp
drawable ic_upload
drawable ic_user_default
drawable ic_visibility
drawable ic_visibility_off
drawable input_background
drawable license_24px
drawable login_background
drawable login_button_background
drawable nav_header_bg
drawable user_avatar_background
font montserrat_thin
id about
id action_add_server
id action_add_user
id action_assign_all
id action_bulk_operations
id action_export_servers
id action_export_users
id action_filter
id action_import_servers
id action_import_users
id action_more
id action_refresh
id action_remove_all
id action_test_all
id add_config
id add_file
id add_qrcode
id add_rule
id add_url
id admin_panel
id admin_servers
id admin_users
id allow_insecure_url
id asset_name
id asset_properties
id auto_update_check
id btn_google_sign_in
id btn_login
id buttonBack
id buttonCancel
id buttonDeleteServer
id buttonDeleteUser
id buttonEditProfile
id buttonEditServer
id buttonManageServers
id buttonRefresh
id buttonRemoveServer
id buttonSave
id buttonToggleStatus
id buttonViewUsers
id cardView
id card_add_server
id card_admin_info
id card_promote_user
id card_server_logs
id card_server_management
id card_server_update
id card_system_settings
id card_user_management
id card_view_logs
id check_box
id check_for_update
id check_pre_release
id chk_enable
id chk_locked
id clear_all
id container_bypass_apps
id container_per_app_proxy
id copy_all
id del_all_config
id del_config
id del_duplicate_config
id del_invalid_config
id domainIp
id download_file
id drawer_layout
id editTextCity
id editTextCountry
id editTextDisplayName
id editTextEmail
id editTextMaxUsers
id editTextPhoneNumber
id editTextPriority
id editTextSearch
id editTextServerConfig
id editTextServerName
id editor
id et_address
id et_bandwidth_down
id et_bandwidth_up
id et_domain
id et_email
id et_extra
id et_filter
id et_id
id et_ip
id et_keyword
id et_local_address
id et_local_mtu
id et_network
id et_next_profile
id et_obfs_password
id et_password
id et_path
id et_pinsha256
id et_port
id et_port_hop
id et_port_hop_interval
id et_pre_profile
id et_preshared_key
id et_protocol
id et_public_key
id et_remarks
id et_request_host
id et_reserved1
id et_security
id et_short_id
id et_sni
id et_spider_x
id et_url
id export_all
id export_proxy_app
id export_rulesets_to_clipboard
id fab
id fabAddServer
id fabAddUser
id fragment_settings
id group_id2
id group_main
id header_view
id icon
id image_switch
id img_locked
id import_clipboard
id import_local
id import_manually_http
id import_manually_hysteria2
id import_manually_socks
id import_manually_ss
id import_manually_trojan
id import_manually_vless
id import_manually_vmess
id import_manually_wireguard
id import_predefined_rulesets
id import_proxy_app
id import_qrcode
id import_rulesets_from_clipboard
id import_rulesets_from_qrcode
id info_container
id item_bg
id iv_qcode
id iv_toggle_password
id iv_user_avatar
id lay_allow_insecure
id lay_public_key
id lay_short_id
id lay_sni
id lay_spider_x
id lay_stream_alpn
id lay_stream_fingerprint
id lay_stream_security
id layoutEmptyState
id layout_background
id layout_backup
id layout_check_update
id layout_domain_strategy
id layout_edit
id layout_extra
id layout_feedback
id layout_geo_files_sources
id layout_indicator
id layout_more
id layout_oss_licenses
id layout_privacy_policy
id layout_remove
id layout_restore
id layout_share
id layout_soure_ccode
id layout_subscription
id layout_switch
id layout_switch_bypass_apps_tips
id layout_test
id layout_tg_channel
id layout_url
id listview
id log_content
id log_tag
id logcat
id logout
id main_content
id name
id nav_view
id outboundTag
id package_name
id pb_waiting
id per_app_proxy_settings
id ping_all
id placeholder
id progressBar
id progressBarUsage
id progress_bar
id radioButtonFree
id radioButtonPremium
id radioGroupSubscription
id real_ping_all
id recyclerViewServers
id recyclerViewUserServers
id recyclerViewUsers
id recycler_view
id refreshLayout
id remarks
id routing_setting
id save_config
id scan_code
id scrollView
id search_view
id select_all
id select_photo
id select_proxy_app
id service_restart
id settings
id sort_by_test_results
id sp_allow_insecure
id sp_flow
id sp_header_type
id sp_header_type_title
id sp_network
id sp_outbound_tag
id sp_security
id sp_stream_alpn
id sp_stream_fingerprint
id sp_stream_security
id sp_subscriptionId
id sub_setting
id sub_update
id switchServerActive
id switch_bypass_apps
id switch_per_app_proxy
id switch_start_service
id tab_group
id textViewActiveServers
id textViewActiveUsers
id textViewCity
id textViewComingSoon
id textViewCountry
id textViewCreationDate
id textViewFullIndicator
id textViewInactiveServers
id textViewInactiveUsers
id textViewLastLogin
id textViewLogs
id textViewPhoneNumber
id textViewPremiumUsers
id textViewRole
id textViewServerConfig
id textViewServerCount
id textViewServerLocation
id textViewServerName
id textViewServerPriority
id textViewServerStatus
id textViewServerType
id textViewServerUsage
id textViewServersCount
id textViewSubscriptionType
id textViewTotalAssignments
id textViewTotalServers
id textViewTotalUsers
id textViewUserEmail
id textViewUserId
id textViewUserName
id textViewUserServersCount
id textViewUserStatus
id textViewUsersCount
id toolbar
id tv_admin_email
id tv_admin_level
id tv_backup_summary
id tv_domain_strategy_summary
id tv_forgot_password
id tv_geo_files_sources_summary
id tv_name
id tv_path
id tv_permissions_count
id tv_request_host
id tv_statistics
id tv_subscription
id tv_test_result
id tv_test_state
id tv_type
id tv_update_status
id tv_url
id tv_user_email
id tv_user_name
id tv_version
id user_asset_setting
layout activity_about
layout activity_add_server
layout activity_add_user
layout activity_admin_panel
layout activity_admin_servers
layout activity_admin_users
layout activity_bypass_list
layout activity_check_update
layout activity_edit_server
layout activity_log_viewer
layout activity_logcat
layout activity_login
layout activity_main
layout activity_none
layout activity_promote_user
layout activity_routing_edit
layout activity_routing_setting
layout activity_server_custom_config
layout activity_server_details
layout activity_server_hysteria2
layout activity_server_logs
layout activity_server_management
layout activity_server_shadowsocks
layout activity_server_socks
layout activity_server_trojan
layout activity_server_users
layout activity_server_vless
layout activity_server_vmess
layout activity_server_wireguard
layout activity_settings
layout activity_sub_edit
layout activity_sub_setting
layout activity_system_settings
layout activity_tasker
layout activity_user_asset
layout activity_user_asset_url
layout activity_user_details
layout activity_user_management
layout activity_user_servers
layout dialog_config_filter
layout item_admin_server
layout item_admin_user
layout item_qrcode
layout item_recycler_bypass_list
layout item_recycler_footer
layout item_recycler_logcat
layout item_recycler_main
layout item_recycler_routing_setting
layout item_recycler_sub_setting
layout item_recycler_user_asset
layout item_user_server
layout layout_address_port
layout layout_tls
layout layout_tls_hysteria2
layout layout_transport
layout nav_header
layout preference_with_help_link
layout widget_switch
menu action_server
menu action_sub_setting
menu menu_admin_servers
menu menu_admin_users
menu menu_asset
menu menu_bypass_list
menu menu_drawer
menu menu_logcat
menu menu_main
menu menu_routing_setting
menu menu_scanner
menu menu_user_details
menu menu_user_servers
mipmap ic_banner
mipmap ic_banner_foreground
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
raw licenses
string access_denied
string add_server
string admin_info_title
string admin_panel_title
string app_name
string app_tile_first_use
string app_tile_name
string app_widget_name
string asset_geo_files_sources
string connection_connected
string connection_not_connected
string connection_test_available
string connection_test_error
string connection_test_error_status_code
string connection_test_fail
string connection_test_pending
string connection_test_testing
string connection_test_testing_count
string default_web_client_id
string del_config_comfirm
string del_invalid_config_comfirm
string email
string enter_email_for_reset
string error_email_required
string error_invalid_email
string error_password_required
string error_password_too_short
string filter_config_all
string forgot_password
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_sign_in_failed
string google_storage_bucket
string guest_user
string import_subscription_failure
string import_subscription_success
string logcat_clear
string logcat_copy
string login
string login_failed
string login_subtitle
string login_success
string logout
string logout_confirmation
string logout_success
string manage_servers
string manage_users
string menu_item_add_asset
string menu_item_add_config
string menu_item_add_file
string menu_item_add_url
string menu_item_del_config
string menu_item_download_file
string menu_item_export_proxy_app
string menu_item_import_config_clipboard
string menu_item_import_config_local
string menu_item_import_config_manually_http
string menu_item_import_config_manually_hysteria2
string menu_item_import_config_manually_socks
string menu_item_import_config_manually_ss
string menu_item_import_config_manually_trojan
string menu_item_import_config_manually_vless
string menu_item_import_config_manually_vmess
string menu_item_import_config_manually_wireguard
string menu_item_import_config_qrcode
string menu_item_import_proxy_app
string menu_item_save_config
string menu_item_scan_qrcode
string menu_item_search
string menu_item_select_all
string menu_item_select_proxy_app
string migration_fail
string migration_success
string msg_dialog_progress
string msg_downloading_content
string msg_enter_keywords
string msg_file_not_found
string msg_remark_is_duplicate
string navigation_drawer_close
string navigation_drawer_open
string notification_action_more
string notification_action_stop_v2ray
string password
string password_reset_failed
string password_reset_sent
string per_app_proxy_settings
string per_app_proxy_settings_enable
string project_id
string promote_user
string pull_down_to_refresh
string register
string register_failed
string register_success
string routing_settings_add_rule
string routing_settings_delete
string routing_settings_domain
string routing_settings_domain_strategy
string routing_settings_export_rulesets_to_clipboard
string routing_settings_import_predefined_rulesets
string routing_settings_import_rulesets_from_clipboard
string routing_settings_import_rulesets_from_qrcode
string routing_settings_import_rulesets_tip
string routing_settings_ip
string routing_settings_locked
string routing_settings_network
string routing_settings_network_tip
string routing_settings_outbound_tag
string routing_settings_port
string routing_settings_protocol
string routing_settings_protocol_tip
string routing_settings_rule_title
string routing_settings_save
string routing_settings_tips
string routing_settings_title
string server_customize_config
string server_lab_address
string server_lab_address3
string server_lab_allow_insecure
string server_lab_alterid
string server_lab_bandwidth_down
string server_lab_bandwidth_up
string server_lab_content
string server_lab_encryption
string server_lab_flow
string server_lab_head_type
string server_lab_id
string server_lab_id3
string server_lab_id4
string server_lab_local_address
string server_lab_local_mtu
string server_lab_mode_type
string server_lab_more_function
string server_lab_need_inbound
string server_lab_network
string server_lab_path
string server_lab_path_grpc
string server_lab_path_h2
string server_lab_path_httpupgrade
string server_lab_path_kcp
string server_lab_path_quic
string server_lab_path_ws
string server_lab_path_xhttp
string server_lab_port
string server_lab_port3
string server_lab_port_hop
string server_lab_port_hop_interval
string server_lab_preshared_key
string server_lab_public_key
string server_lab_remarks
string server_lab_request_host
string server_lab_request_host6
string server_lab_request_host_grpc
string server_lab_request_host_h2
string server_lab_request_host_http
string server_lab_request_host_httpupgrade
string server_lab_request_host_quic
string server_lab_request_host_ws
string server_lab_request_host_xhttp
string server_lab_reserved
string server_lab_secret_key
string server_lab_security
string server_lab_security3
string server_lab_security4
string server_lab_short_id
string server_lab_sni
string server_lab_spider_x
string server_lab_stream_alpn
string server_lab_stream_fingerprint
string server_lab_stream_pinsha256
string server_lab_stream_security
string server_lab_xhttp_extra
string server_lab_xhttp_mode
string server_logs
string server_management_section
string server_obfs_password
string server_update_failure
string server_update_in_progress
string server_update_no_new
string server_update_success
string sign_in_with_google
string sub_allow_insecure_url
string sub_auto_update
string sub_setting_enable
string sub_setting_filter
string sub_setting_next_profile
string sub_setting_pre_profile
string sub_setting_pre_profile_tip
string sub_setting_remarks
string sub_setting_url
string summary_configuration_backup
string summary_pref_allow_insecure
string summary_pref_append_http_proxy
string summary_pref_auto_server_update
string summary_pref_auto_update_subscription
string summary_pref_confirm_remove
string summary_pref_delay_test_url
string summary_pref_dns_hosts
string summary_pref_domestic_dns
string summary_pref_double_column_display
string summary_pref_fake_dns_enabled
string summary_pref_feedback
string summary_pref_is_booted
string summary_pref_local_dns_enabled
string summary_pref_local_dns_port
string summary_pref_manual_server_update
string summary_pref_mux_enabled
string summary_pref_per_app_proxy
string summary_pref_prefer_ipv6
string summary_pref_proxy_sharing_enabled
string summary_pref_remote_dns
string summary_pref_route_only_enabled
string summary_pref_sniffing_enabled
string summary_pref_socks_port
string summary_pref_speed_enabled
string summary_pref_start_scan_immediate
string summary_pref_tg_group
string switch_bypass_apps_mode
string system_settings
string system_settings_section
string tasker_setting_confirm
string tasker_start_service
string title_about
string title_advanced
string title_configuration_backup
string title_configuration_restore
string title_configuration_share
string title_core_loglevel
string title_del_all_config
string title_del_config_count
string title_del_duplicate_config
string title_del_duplicate_config_count
string title_del_invalid_config
string title_export_all
string title_export_config_count
string title_file_chooser
string title_filter_config
string title_fragment_settings
string title_import_config_count
string title_language
string title_logcat
string title_login
string title_mode
string title_mode_help
string title_mux_settings
string title_oss_license
string title_ping_all_server
string title_pref_allow_insecure
string title_pref_append_http_proxy
string title_pref_auto_server_update
string title_pref_auto_server_update_interval
string title_pref_auto_update_interval
string title_pref_auto_update_subscription
string title_pref_confirm_remove
string title_pref_delay_test_url
string title_pref_dns_hosts
string title_pref_domestic_dns
string title_pref_double_column_display
string title_pref_fake_dns_enabled
string title_pref_feedback
string title_pref_fragment_enabled
string title_pref_fragment_interval
string title_pref_fragment_length
string title_pref_fragment_packets
string title_pref_is_booted
string title_pref_local_dns_enabled
string title_pref_local_dns_port
string title_pref_manual_server_update
string title_pref_mux_concurency
string title_pref_mux_enabled
string title_pref_mux_xudp_concurency
string title_pref_mux_xudp_quic
string title_pref_per_app_proxy
string title_pref_prefer_ipv6
string title_pref_proxy_sharing_enabled
string title_pref_remote_dns
string title_pref_route_only_enabled
string title_pref_sniffing_enabled
string title_pref_socks_port
string title_pref_speed_enabled
string title_pref_start_scan_immediate
string title_pref_ui_mode_night
string title_pref_vpn_bypass_lan
string title_pref_vpn_dns
string title_privacy_policy
string title_real_ping_all_server
string title_server
string title_server_settings
string title_service_restart
string title_settings
string title_sort_by_test_results
string title_source_code
string title_sub_setting
string title_sub_update
string title_tg_channel
string title_ui_settings
string title_update_config_count
string title_url
string title_user_asset_add_url
string title_user_asset_setting
string title_vpn_settings
string toast_action_not_allowed
string toast_asset_copy_failed
string toast_config_file_invalid
string toast_decoding_failed
string toast_failure
string toast_incorrect_protocol
string toast_insecure_url_protocol
string toast_invalid_url
string toast_malformed_josn
string toast_none_data
string toast_none_data_clipboard
string toast_permission_denied
string toast_permission_denied_notification
string toast_require_file_manager
string toast_services_failure
string toast_services_start
string toast_services_stop
string toast_services_success
string toast_success
string toast_tg_app_not_found
string toast_warning_pref_proxysharing_short
string toggle_password_visibility
string update_already_latest_version
string update_check_for_update
string update_check_pre_release
string update_checking_for_update
string update_new_version_found
string update_now
string update_servers_manually
string user_avatar
string user_management_section
string welcome_to_app
style AppTheme
style AppThemeDayNight
style AppThemeDayNight.NoActionBar
style AppTheme.AppBarOverlay
style AppTheme.NoActionBar
style AppTheme.NoActionBar.Translucent
style AppTheme.PopupOverlay
style BrandedSwitch
style TabLayoutTextStyle
xml app_widget_provider
xml cache_paths
xml network_security_config
xml pref_settings
xml shortcuts
