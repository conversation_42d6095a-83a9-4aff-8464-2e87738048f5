package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityServerManagementBinding

class ServerManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityServerManagementBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityServerManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.manage_servers)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
