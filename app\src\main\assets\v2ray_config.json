{"stats": {}, "log": {"loglevel": "warning"}, "policy": {"levels": {"8": {"handshake": 4, "connIdle": 300, "uplinkOnly": 1, "downlinkOnly": 1}}, "system": {"statsOutboundUplink": true, "statsOutboundDownlink": true}}, "inbounds": [{"tag": "socks", "port": 10808, "protocol": "socks", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "userLevel": 8}, "sniffing": {"enabled": true, "destOverride": ["http", "tls"]}}, {"tag": "http", "port": 10809, "protocol": "http", "settings": {"userLevel": 8}}], "outbounds": [{"tag": "proxy", "protocol": "vmess", "settings": {"vnext": [{"address": "v2ray.cool", "port": 10086, "users": [{"id": "a3482e88-686a-4a58-8126-99c9df64b7bf", "alterId": 0, "security": "auto", "level": 8}]}], "servers": [{"address": "v2ray.cool", "method": "chacha20", "ota": false, "password": "123456", "port": 10086, "level": 8}]}, "streamSettings": {"network": "tcp"}, "mux": {"enabled": false}}, {"protocol": "freedom", "settings": {"domainStrategy": "UseIP"}, "tag": "direct"}, {"protocol": "blackhole", "tag": "block", "settings": {"response": {"type": "http"}}}], "routing": {"domainStrategy": "AsIs", "rules": []}, "dns": {"hosts": {}, "servers": []}}