<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_routing_edit" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_routing_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_routing_edit_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="192" endOffset="12"/></Target><Target id="@+id/et_remarks" view="EditText"><Expressions/><location startLine="31" startOffset="16" endLine="35" endOffset="46"/></Target><Target id="@+id/chk_locked" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="44" startOffset="16" endLine="50" endOffset="54"/></Target><Target id="@+id/et_domain" view="EditText"><Expressions/><location startLine="77" startOffset="16" endLine="83" endOffset="43"/></Target><Target id="@+id/et_ip" view="EditText"><Expressions/><location startLine="97" startOffset="16" endLine="103" endOffset="43"/></Target><Target id="@+id/et_port" view="EditText"><Expressions/><location startLine="117" startOffset="16" endLine="121" endOffset="46"/></Target><Target id="@+id/et_protocol" view="EditText"><Expressions/><location startLine="135" startOffset="16" endLine="140" endOffset="46"/></Target><Target id="@+id/et_network" view="EditText"><Expressions/><location startLine="154" startOffset="16" endLine="159" endOffset="46"/></Target><Target id="@+id/sp_outbound_tag" view="Spinner"><Expressions/><location startLine="173" startOffset="16" endLine="179" endOffset="59"/></Target></Targets></Layout>