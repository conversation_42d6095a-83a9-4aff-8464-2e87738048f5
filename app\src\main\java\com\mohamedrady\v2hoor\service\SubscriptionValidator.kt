package com.mohamedrady.v2hoor.service

import android.content.Context
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.SubscriptionInfo
import com.mohamedrady.v2hoor.model.SubscriptionStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Subscription Validator
 * Handles subscription validation and access control logic
 */
class SubscriptionValidator private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: SubscriptionValidator? = null
        
        fun getInstance(context: Context): SubscriptionValidator {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SubscriptionValidator(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val subscriptionService = SubscriptionService.getInstance(context)
    private val userRoleService = UserRoleService.getInstance(context)
    
    /**
     * Validation result
     */
    data class ValidationResult(
        val isValid: <PERSON><PERSON><PERSON>,
        val canAccessApp: Boolean,
        val canConnectToServers: <PERSON><PERSON>an,
        val canViewServers: <PERSON><PERSON><PERSON>,
        val blockReason: String? = null,
        val warningMessage: String? = null,
        val subscriptionStatus: SubscriptionStatus = SubscriptionStatus.UNKNOWN
    )
    
    /**
     * Validate current user's subscription and access
     */
    suspend fun validateCurrentUser(): ValidationResult {
        return withContext(Dispatchers.IO) {
            try {
                // Admin users have unlimited access
                if (userRoleService.isCurrentUserAdmin()) {
                    android.util.Log.i(AppConfig.TAG, "Admin user detected - unlimited access granted")
                    return@withContext ValidationResult(
                        isValid = true,
                        canAccessApp = true,
                        canConnectToServers = true,
                        canViewServers = true,
                        subscriptionStatus = SubscriptionStatus.UNLIMITED
                    )
                }
                
                // Get current subscription
                val subscription = subscriptionService.getCurrentSubscription()
                if (subscription == null) {
                    android.util.Log.w(AppConfig.TAG, "No subscription found for current user")
                    return@withContext ValidationResult(
                        isValid = false,
                        canAccessApp = false,
                        canConnectToServers = false,
                        canViewServers = false,
                        blockReason = "لم يتم العثور على معلومات الاشتراك",
                        subscriptionStatus = SubscriptionStatus.UNKNOWN
                    )
                }
                
                // Validate subscription
                validateSubscription(subscription)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error validating subscription", e)
                ValidationResult(
                    isValid = false,
                    canAccessApp = false,
                    canConnectToServers = false,
                    canViewServers = false,
                    blockReason = "خطأ في التحقق من الاشتراك: ${e.message}",
                    subscriptionStatus = SubscriptionStatus.UNKNOWN
                )
            }
        }
    }
    
    /**
     * Validate specific subscription
     */
    fun validateSubscription(subscription: SubscriptionInfo): ValidationResult {
        val status = subscription.getStatus()
        
        return when (status) {
            SubscriptionStatus.ACTIVE -> {
                // Check data limits
                if (subscription.isDataLimitExceeded()) {
                    ValidationResult(
                        isValid = true,
                        canAccessApp = true,
                        canConnectToServers = false,
                        canViewServers = true,
                        blockReason = "تم تجاوز حد البيانات المسموح (${subscription.dataUsedGb}/${subscription.dataLimitGb} GB)",
                        subscriptionStatus = status
                    )
                } else {
                    ValidationResult(
                        isValid = true,
                        canAccessApp = true,
                        canConnectToServers = true,
                        canViewServers = true,
                        subscriptionStatus = status
                    )
                }
            }
            
            SubscriptionStatus.EXPIRING_SOON -> {
                val days = subscription.getDaysUntilExpiry()
                val warningMessage = "تنبيه: ينتهي اشتراكك خلال $days ${if (days == 1L) "يوم" else "أيام"}"
                
                if (subscription.isDataLimitExceeded()) {
                    ValidationResult(
                        isValid = true,
                        canAccessApp = true,
                        canConnectToServers = false,
                        canViewServers = true,
                        blockReason = "تم تجاوز حد البيانات المسموح",
                        warningMessage = warningMessage,
                        subscriptionStatus = status
                    )
                } else {
                    ValidationResult(
                        isValid = true,
                        canAccessApp = true,
                        canConnectToServers = true,
                        canViewServers = true,
                        warningMessage = warningMessage,
                        subscriptionStatus = status
                    )
                }
            }
            
            SubscriptionStatus.EXPIRED -> {
                ValidationResult(
                    isValid = false,
                    canAccessApp = false,
                    canConnectToServers = false,
                    canViewServers = false,
                    blockReason = "انتهت صلاحية اشتراكك. يرجى التواصل مع الدعم الفني لتجديد الاشتراك.",
                    subscriptionStatus = status
                )
            }
            
            SubscriptionStatus.UNLIMITED -> {
                ValidationResult(
                    isValid = true,
                    canAccessApp = true,
                    canConnectToServers = true,
                    canViewServers = true,
                    subscriptionStatus = status
                )
            }
            
            SubscriptionStatus.UNKNOWN -> {
                ValidationResult(
                    isValid = false,
                    canAccessApp = false,
                    canConnectToServers = false,
                    canViewServers = false,
                    blockReason = "حالة الاشتراك غير معروفة. يرجى التواصل مع الدعم الفني.",
                    subscriptionStatus = status
                )
            }
        }
    }
    
    /**
     * Check if user can access specific feature
     */
    suspend fun canAccessFeature(feature: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Admin users can access all features
                if (userRoleService.isCurrentUserAdmin()) {
                    return@withContext true
                }
                
                val validation = validateCurrentUser()
                
                when (feature) {
                    "app_access" -> validation.canAccessApp
                    "server_connection" -> validation.canConnectToServers
                    "server_list" -> validation.canViewServers
                    "settings" -> validation.canAccessApp
                    "profile" -> validation.canAccessApp
                    else -> validation.canAccessApp
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error checking feature access: $feature", e)
                false
            }
        }
    }
    
    /**
     * Get access block message for specific feature
     */
    suspend fun getAccessBlockMessage(feature: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                val validation = validateCurrentUser()
                
                when (feature) {
                    "server_connection" -> {
                        if (!validation.canConnectToServers) {
                            validation.blockReason ?: "لا يمكن الاتصال بالسيرفرات"
                        } else null
                    }
                    "server_list" -> {
                        if (!validation.canViewServers) {
                            validation.blockReason ?: "لا يمكن عرض قائمة السيرفرات"
                        } else null
                    }
                    "app_access" -> {
                        if (!validation.canAccessApp) {
                            validation.blockReason ?: "لا يمكن الوصول للتطبيق"
                        } else null
                    }
                    else -> {
                        if (!validation.canAccessApp) {
                            validation.blockReason ?: "الوصول مرفوض"
                        } else null
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error getting access block message: $feature", e)
                "خطأ في التحقق من الصلاحيات"
            }
        }
    }
    
    /**
     * Check if subscription needs renewal warning
     */
    suspend fun needsRenewalWarning(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (userRoleService.isCurrentUserAdmin()) {
                    return@withContext false
                }
                
                val subscription = subscriptionService.getCurrentSubscription()
                subscription?.isExpiringSoon() ?: false
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error checking renewal warning", e)
                false
            }
        }
    }
    
    /**
     * Get renewal warning message
     */
    suspend fun getRenewalWarningMessage(): String? {
        return withContext(Dispatchers.IO) {
            try {
                if (userRoleService.isCurrentUserAdmin()) {
                    return@withContext null
                }
                
                val subscription = subscriptionService.getCurrentSubscription()
                if (subscription?.isExpiringSoon() == true) {
                    val days = subscription.getDaysUntilExpiry()
                    "تنبيه: ينتهي اشتراكك خلال $days ${if (days == 1L) "يوم" else "أيام"}. يرجى التواصل مع الدعم الفني للتجديد."
                } else null
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error getting renewal warning message", e)
                null
            }
        }
    }
    
    /**
     * Check if user can connect to specific server
     */
    suspend fun canConnectToServer(serverId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Admin users can connect to any server
                if (userRoleService.isCurrentUserAdmin()) {
                    return@withContext true
                }
                
                val validation = validateCurrentUser()
                validation.canConnectToServers
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error checking server connection access", e)
                false
            }
        }
    }
    
    /**
     * Get server connection block message
     */
    suspend fun getServerConnectionBlockMessage(serverId: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                if (userRoleService.isCurrentUserAdmin()) {
                    return@withContext null
                }
                
                val validation = validateCurrentUser()
                if (!validation.canConnectToServers) {
                    validation.blockReason ?: "لا يمكن الاتصال بالسيرفر"
                } else null
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error getting server connection block message", e)
                "خطأ في التحقق من صلاحية الاتصال"
            }
        }
    }
    
    /**
     * Log access attempt
     */
    fun logAccessAttempt(feature: String, granted: Boolean, reason: String? = null) {
        val userProfile = userRoleService.getCurrentUserProfile()
        val userEmail = userProfile?.email ?: "unknown"
        val subscription = subscriptionService.getCurrentSubscription()
        val status = subscription?.getStatus()?.displayName ?: "unknown"
        
        val logMessage = if (granted) {
            "Access granted: $userEmail ($status) -> $feature"
        } else {
            "Access denied: $userEmail ($status) -> $feature${reason?.let { " - $it" } ?: ""}"
        }
        
        if (granted) {
            android.util.Log.i(AppConfig.TAG, logMessage)
        } else {
            android.util.Log.w(AppConfig.TAG, logMessage)
        }
    }
}
