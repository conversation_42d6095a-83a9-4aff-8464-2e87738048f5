<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_user_server" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_user_server.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_user_server_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="171" endOffset="35"/></Target><Target id="@+id/textViewServerName" view="TextView"><Expressions/><location startLine="30" startOffset="16" endLine="37" endOffset="47"/></Target><Target id="@+id/textViewServerLocation" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="60"/></Target><Target id="@+id/textViewServerStatus" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="66" endOffset="46"/></Target><Target id="@+id/textViewFullIndicator" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="80" endOffset="48"/></Target><Target id="@+id/textViewServerType" view="TextView"><Expressions/><location startLine="99" startOffset="16" endLine="106" endOffset="40"/></Target><Target id="@+id/textViewServerPriority" view="TextView"><Expressions/><location startLine="108" startOffset="16" endLine="115" endOffset="46"/></Target><Target id="@+id/textViewServerUsage" view="TextView"><Expressions/><location startLine="125" startOffset="16" endLine="131" endOffset="58"/></Target><Target id="@+id/progressBarUsage" view="ProgressBar"><Expressions/><location startLine="133" startOffset="16" endLine="141" endOffset="57"/></Target><Target id="@+id/textViewCreationDate" view="TextView"><Expressions/><location startLine="148" startOffset="8" endLine="155" endOffset="55"/></Target><Target id="@+id/buttonRemoveServer" view="Button"><Expressions/><location startLine="158" startOffset="8" endLine="167" endOffset="37"/></Target></Targets></Layout>