# 🔒 Firebase Security Rules Documentation - V2HoorVPN

## 📋 Overview

This document describes the comprehensive Firebase security rules implemented for the V2HoorVPN application. These rules ensure proper access control, data protection, and admin functionality.

---

## 🎯 **Security Principles**

### **1. Authentication Required**
- All operations require user authentication
- No anonymous access allowed
- JWT token validation for all requests

### **2. Role-Based Access Control (RBAC)**
- **Super Admin**: `<EMAIL>` - Full access
- **Admin**: Users with admin role - Management access
- **Regular Users**: Limited to their own data

### **3. Data Isolation**
- Users can only access their own data
- <PERSON><PERSON> can access all user data
- Server configurations are admin-only

---

## 🗂️ **Collection Rules**

### **👥 Users & Roles**

#### `/user_roles/{userId}`
- **Read**: Owner or Admin
- **Write**: Admin only
- **Create**: Admin or first-time user setup
- **Purpose**: Stores user roles and permissions

#### `/users/{userId}`
- **Read**: Owner or Admin
- **Write**: Owner or Admin
- **Create**: Owner or Admin
- **Delete**: Admin only
- **Purpose**: User profile and account information

#### `/admins/{adminId}`
- **Read/Write**: Super Admin only
- **Purpose**: Admin management and permissions

---

### **🖥️ Servers & Configuration**

#### `/public_servers/{serverId}`
- **Read**: Authenticated users with valid roles
- **Write**: Admin only
- **Purpose**: VPN server configurations available to users

#### `/user_servers/{userId}`
- **Read**: Owner or Admin
- **Write**: Admin only
- **Purpose**: User-specific server assignments

#### `/server_assignments/{assignmentId}`
- **Read/Write**: Admin only
- **Purpose**: Mapping users to specific servers

---

### **📊 Monitoring & Logs**

#### `/app_logs/{logId}`
- **Read/Write**: Admin only
- **Purpose**: Application logs and system monitoring

#### `/user_activity/{userId}/logs/{logId}`
- **Read**: Owner or Admin
- **Write**: Owner or Admin
- **Purpose**: User activity tracking

#### `/server_stats/{statId}`
- **Read/Write**: Admin only
- **Purpose**: Server performance and usage statistics

#### `/user_stats/{userId}`
- **Read**: Owner or Admin
- **Write**: Admin only
- **Purpose**: User usage statistics and analytics

---

### **💳 Subscriptions & Billing**

#### `/user_subscriptions/{userId}`
- **Read**: Owner or Admin
- **Write**: Admin only
- **Purpose**: User subscription status and billing

---

### **🎯 Support & Communication**

#### `/user_feedback/{feedbackId}`
- **Read**: Admin only
- **Create**: Authenticated users
- **Update**: Admin or feedback owner
- **Purpose**: User feedback and suggestions

#### `/support_tickets/{ticketId}`
- **Read**: Admin or ticket owner
- **Create**: Authenticated users (own tickets only)
- **Update**: Admin or ticket owner
- **Purpose**: Customer support system

#### `/announcements/{announcementId}`
- **Read**: Authenticated users with valid roles
- **Write**: Admin only
- **Purpose**: App announcements and notifications

---

### **⚙️ System Configuration**

#### `/system_settings/{settingId}`
- **Read**: Authenticated users with valid roles
- **Write**: Admin only
- **Purpose**: App-wide configuration settings

#### `/app_config/{configId}`
- **Read**: Authenticated users with valid roles
- **Write**: Admin only
- **Purpose**: Application configuration parameters

---

## 🗄️ **Storage Rules**

### **👤 User Content**

#### `/user_profiles/{userId}/{fileName}`
- **Read**: Authenticated users
- **Write**: Owner only (images, 10MB limit)
- **Delete**: Owner or Admin
- **Purpose**: User profile pictures and avatars

#### `/user_backups/{userId}/{fileName}`
- **Read/Write**: Owner only (10MB limit)
- **Delete**: Owner or Admin
- **Purpose**: User data backups

---

### **🔧 Admin Content**

#### `/server_configs/{configId}`
- **Read/Write**: Admin only
- **Purpose**: Server configuration files

#### `/system_backups/{fileName}`
- **Read/Write**: Admin only
- **Delete**: Super Admin only
- **Purpose**: System-wide backups

#### `/logs/{logFile}`
- **Read/Write**: Admin only
- **Delete**: Super Admin only
- **Purpose**: System log files

---

### **📁 Shared Content**

#### `/app_assets/{assetPath}`
- **Read**: Authenticated users
- **Write**: Admin only
- **Purpose**: App resources and assets

#### `/public_downloads/{fileName}`
- **Read**: Authenticated users
- **Write**: Admin only
- **Purpose**: Public downloadable content

#### `/support_attachments/{ticketId}/{fileName}`
- **Read**: Admin or ticket owner
- **Write**: Authenticated users (10MB limit)
- **Delete**: Admin only
- **Purpose**: Support ticket attachments

---

## 🚀 **Deployment Instructions**

### **Prerequisites**
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Ensure you have admin access to project `mrelfeky-209615`

### **Deploy Rules**
```bash
# Run the deployment script
./deploy-firebase-rules.bat

# Or deploy manually
firebase use mrelfeky-209615
firebase deploy --only firestore:rules
firebase deploy --only storage
firebase deploy --only firestore:indexes
```

### **Verify Deployment**
1. Check Firebase Console → Firestore → Rules
2. Check Firebase Console → Storage → Rules
3. Test with the app to ensure proper access control

---

## 🔍 **Testing & Validation**

### **Test Cases**
1. **Super Admin Access**: `<EMAIL>` should have full access
2. **Regular User Access**: Users should only see their own data
3. **Unauthenticated Access**: Should be completely blocked
4. **Admin Functions**: Admin users should access management features

### **Security Validation**
- ✅ No anonymous access allowed
- ✅ Users isolated to their own data
- ✅ Admin functions properly protected
- ✅ File upload restrictions enforced
- ✅ Super admin privileges working

---

## ⚠️ **Important Notes**

### **Super Admin Email**
- Hardcoded: `<EMAIL>`
- Has unrestricted access to all collections
- Can manage other admins

### **File Size Limits**
- General uploads: 10MB maximum
- Images only for profile pictures
- Config files for server configurations

### **Performance Optimization**
- Indexes created for common queries
- Efficient rule evaluation order
- Minimal Firestore reads in rules

---

## 🛠️ **Maintenance**

### **Adding New Admins**
1. Add user to `/user_roles/{userId}` with role: "admin"
2. Set `isActive: true`
3. User will gain admin access on next login

### **Updating Rules**
1. Modify `firestore.rules` or `storage.rules`
2. Run deployment script
3. Test thoroughly before production use

### **Monitoring**
- Check Firebase Console for rule violations
- Monitor authentication logs
- Review access patterns regularly

---

## 📞 **Support**

For issues with Firebase rules:
1. Check Firebase Console logs
2. Verify user authentication status
3. Confirm user roles in Firestore
4. Test with Firebase emulator for debugging

**Project ID**: `mrelfeky-209615`
**Super Admin**: `<EMAIL>`

---

## 🧪 **Rule Testing Script**

Create a test file to validate your rules:

```javascript
// test-firebase-rules.js
const firebase = require('@firebase/testing');

const projectId = 'mrelfeky-209615';

// Test super admin access
async function testSuperAdminAccess() {
  const auth = { uid: 'super-admin-uid', email: '<EMAIL>' };
  const db = firebase.initializeTestApp({ projectId, auth }).firestore();

  try {
    await db.collection('users').get();
    console.log('✅ Super admin can read users');
  } catch (error) {
    console.log('❌ Super admin cannot read users:', error.message);
  }
}

// Test regular user access
async function testRegularUserAccess() {
  const auth = { uid: 'regular-user-uid', email: '<EMAIL>' };
  const db = firebase.initializeTestApp({ projectId, auth }).firestore();

  try {
    await db.collection('users').doc('other-user-uid').get();
    console.log('❌ Regular user can read other users (should fail)');
  } catch (error) {
    console.log('✅ Regular user cannot read other users');
  }
}

// Run tests
testSuperAdminAccess();
testRegularUserAccess();
```
