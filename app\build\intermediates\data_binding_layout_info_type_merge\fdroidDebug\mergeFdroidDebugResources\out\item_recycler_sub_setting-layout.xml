<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recycler_sub_setting" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_recycler_sub_setting.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/item_bg"><Targets><Target id="@+id/item_bg" tag="layout/item_recycler_sub_setting_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="157" endOffset="14"/></Target><Target id="@+id/info_container" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="155" endOffset="18"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="38" startOffset="20" endLine="44" endOffset="90"/></Target><Target id="@+id/layout_share" view="LinearLayout"><Expressions/><location startLine="54" startOffset="20" endLine="71" endOffset="34"/></Target><Target id="@+id/layout_edit" view="LinearLayout"><Expressions/><location startLine="73" startOffset="20" endLine="89" endOffset="34"/></Target><Target id="@+id/layout_remove" view="LinearLayout"><Expressions/><location startLine="91" startOffset="20" endLine="107" endOffset="34"/></Target><Target id="@+id/layout_url" view="LinearLayout"><Expressions/><location startLine="114" startOffset="12" endLine="151" endOffset="26"/></Target><Target id="@+id/tv_url" view="TextView"><Expressions/><location startLine="129" startOffset="20" endLine="134" endOffset="88"/></Target><Target id="@+id/chk_enable" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="144" startOffset="20" endLine="148" endOffset="58"/></Target></Targets></Layout>