<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_admin_user" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_admin_user.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_admin_user_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="189" endOffset="35"/></Target><Target id="@+id/textViewUserName" view="TextView"><Expressions/><location startLine="36" startOffset="20" endLine="44" endOffset="48"/></Target><Target id="@+id/textViewRole" view="TextView"><Expressions/><location startLine="46" startOffset="20" endLine="58" endOffset="52"/></Target><Target id="@+id/textViewUserEmail" view="TextView"><Expressions/><location startLine="62" startOffset="16" endLine="69" endOffset="54"/></Target><Target id="@+id/textViewUserStatus" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="84" endOffset="42"/></Target><Target id="@+id/textViewSubscriptionType" view="TextView"><Expressions/><location startLine="101" startOffset="16" endLine="108" endOffset="39"/></Target><Target id="@+id/textViewServerCount" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="117" endOffset="47"/></Target><Target id="@+id/textViewCreationDate" view="TextView"><Expressions/><location startLine="127" startOffset="16" endLine="133" endOffset="51"/></Target><Target id="@+id/textViewLastLogin" view="TextView"><Expressions/><location startLine="135" startOffset="16" endLine="142" endOffset="53"/></Target><Target id="@+id/buttonDeleteUser" view="Button"><Expressions/><location startLine="156" startOffset="12" endLine="164" endOffset="41"/></Target><Target id="@+id/buttonManageServers" view="Button"><Expressions/><location startLine="166" startOffset="12" endLine="174" endOffset="41"/></Target><Target id="@+id/buttonToggleStatus" view="Button"><Expressions/><location startLine="176" startOffset="12" endLine="183" endOffset="41"/></Target></Targets></Layout>