<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical">

    <LinearLayout
        android:id="@+id/info_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:nextFocusRight="@+id/layout_edit"
        android:orientation="horizontal"
        android:padding="@dimen/padding_spacing_dp8">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="@dimen/padding_spacing_dp8">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/remarks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />

                <ImageView
                    android:id="@+id/img_locked"
                    android:layout_width="@dimen/padding_spacing_dp16"
                    android:layout_height="@dimen/padding_spacing_dp16"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/padding_spacing_dp16"
                    app:srcCompat="@drawable/ic_lock_24dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/domainIp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp8"
                android:lines="1"
                android:textAppearance="@style/TextAppearance.AppCompat.Small" />

            <TextView
                android:id="@+id/outboundTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp8"
                android:lines="1"
                android:textAppearance="@style/TextAppearance.AppCompat.Small" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/padding_spacing_dp8">

            <LinearLayout
                android:id="@+id/layout_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusLeft="@+id/info_container"
                android:orientation="vertical"
                android:padding="@dimen/padding_spacing_dp8">

                <ImageView
                    android:layout_width="@dimen/image_size_dp24"
                    android:layout_height="@dimen/image_size_dp24"
                    app:srcCompat="@drawable/ic_edit_24dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp8"
                android:orientation="horizontal">

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/chk_enable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:theme="@style/BrandedSwitch" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>
