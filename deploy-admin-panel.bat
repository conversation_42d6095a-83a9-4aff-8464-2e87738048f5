@echo off
echo ========================================
echo   V2Hoor Admin Panel System
echo   Deployment Script
echo ========================================
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Firebase CLI not found!
    echo Please install Firebase CLI first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo Firebase CLI found. Proceeding with deployment...
echo.

REM Login to Firebase (if not already logged in)
echo Checking Firebase authentication...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo Please login to Firebase:
    firebase login
    if %errorlevel% neq 0 (
        echo Failed to login to Firebase
        pause
        exit /b 1
    )
)

echo.
echo Setting Firebase project...
firebase use mrelfeky-209615
if %errorlevel% neq 0 (
    echo Failed to set Firebase project
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Deploying Admin Panel System
echo ========================================
echo.

echo Step 1: Deploying Firebase Realtime Database rules...
firebase deploy --only database
if %errorlevel% neq 0 (
    echo Failed to deploy database rules
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Admin Panel Features
echo ========================================
echo.

echo ✅ User Management:
echo    - View all users with detailed information
echo    - Edit user roles (user ↔ admin)
echo    - Manage subscription end dates and types
echo    - Delete users with confirmation
echo    - Search and filter users
echo.

echo ✅ Server Management:
echo    - View user-specific servers
echo    - Add new servers to users
echo    - Edit existing server configurations
echo    - Delete servers with confirmation
echo    - Toggle server active/inactive status
echo.

echo ✅ Admin Dashboard:
echo    - Real-time statistics display
echo    - Total users, active users, expired users
echo    - Total servers and system overview
echo    - Quick action buttons for common tasks
echo.

echo ✅ Security Features:
echo    - Role-based access control
echo    - Admin-only menu visibility
echo    - Permission checks at all levels
echo    - Audit trail for admin operations
echo.

echo ========================================
echo   Admin Panel Access
echo ========================================
echo.

echo Navigation:
echo 1. Login with admin account (<EMAIL>)
echo 2. Open navigation drawer
echo 3. Click "Admin Panel" (visible only to admins)
echo 4. Access admin dashboard with statistics
echo 5. Navigate to Users Management or other admin features
echo.

echo Admin Menu Items:
echo - Admin Panel: Main dashboard with statistics
echo - Users Management: Complete user administration
echo - System Logs: Application monitoring and logs
echo - Admin Features: Advanced administrative tools
echo.

echo ========================================
echo   User Management Interface
echo ========================================
echo.

echo User List Features:
echo - Comprehensive user information display
echo - Role indicators (Admin/User badges)
echo - Subscription status with color coding
echo - Server count for each user
echo - Data usage progress bars
echo - Last login timestamps
echo.

echo User Actions:
echo - Edit Role: Change between user and admin
echo - Edit Subscription: Modify end date and type
echo - View Servers: Manage user's assigned servers
echo - Delete User: Remove user with confirmation
echo.

echo Search and Filter:
echo - Real-time search by name, email, or UID
echo - Filter by: All, Active, Expired, Admins
echo - Sort by creation date, last login, etc.
echo.

echo ========================================
echo   Server Management Interface
echo ========================================
echo.

echo Server Operations:
echo - View all servers assigned to a user
echo - Add new server configurations
echo - Edit existing server settings
echo - Delete servers with confirmation
echo - Toggle server active/inactive status
echo.

echo Server Information:
echo - Protocol type (VMess, VLESS, Trojan, etc.)
echo - Server address and port
echo - Location and country information
echo - Creation and update timestamps
echo - Active/inactive status indicators
echo.

echo Server Configuration:
echo - Complete V2Ray/Xray server settings
echo - Security parameters (TLS, SNI, ALPN)
echo - Network settings (TCP, WebSocket, gRPC)
echo - Custom configurations and notes
echo.

echo ========================================
echo   Admin Statistics Dashboard
echo ========================================
echo.

echo Real-time Metrics:
echo - Total Users: All registered users
echo - Active Users: Users with valid subscriptions
echo - Expired Users: Users with expired subscriptions
echo - Admin Users: Users with admin role
echo - Total Servers: All configured servers
echo - Data Usage: Aggregate bandwidth consumption
echo.

echo Quick Actions:
echo - Users Management: Direct access to user admin
echo - Expired Users: View users needing renewal
echo - System Logs: Application monitoring
echo - Refresh Data: Update all statistics
echo.

echo ========================================
echo   Security and Permissions
echo ========================================
echo.

echo Role-Based Access Control:
echo - Admin role required for all admin features
echo - Dynamic UI based on user permissions
echo - Code-level permission validation
echo - Firebase security rules enforcement
echo.

echo Permission Levels:
echo - MANAGE_USERS: User administration
echo - MANAGE_SERVERS: Server configuration
echo - MANAGE_SUBSCRIPTIONS: Subscription control
echo - DELETE_USERS: User removal
echo - VIEW_ADMIN_PANEL: Dashboard access
echo.

echo Security Checks:
echo - UI: Admin menu items hidden from users
echo - Code: Permission checks before operations
echo - Database: Firebase rules enforce access
echo - Session: Real-time role validation
echo.

echo ========================================
echo   Testing Instructions
echo ========================================
echo.

echo Admin Access Testing:
echo 1. Login with admin account (<EMAIL>)
echo 2. Verify admin menu items are visible
echo 3. Access admin panel and check statistics
echo 4. Test user management operations
echo 5. Test server management for users
echo.

echo User Access Testing:
echo 1. Login with regular user account
echo 2. Verify admin menu items are hidden
echo 3. Attempt direct admin URL access (should fail)
echo 4. Confirm no admin features accessible
echo.

echo Functionality Testing:
echo 1. User Management:
echo    - Load user list with correct information
echo    - Edit user roles and verify changes
echo    - Update subscriptions and check dates
echo    - Delete test users and confirm removal
echo.

echo 2. Server Management:
echo    - View user servers correctly
echo    - Add new servers with validation
echo    - Edit server configurations
echo    - Delete servers with confirmation
echo    - Toggle server status
echo.

echo 3. Statistics and Monitoring:
echo    - Verify real-time statistics accuracy
echo    - Check quick action functionality
echo    - Test data refresh operations
echo    - Monitor system performance
echo.

echo ========================================
echo   Configuration and Setup
echo ========================================
echo.

echo Admin User Setup:
echo 1. Ensure <EMAIL> has admin role
echo 2. Set role = "admin" in Firebase Database
echo 3. Verify admin permissions are active
echo 4. Test admin panel access
echo.

echo Firebase Configuration:
echo 1. Database rules deployed with admin access control
echo 2. User roles properly configured
echo 3. Server management permissions set
echo 4. Security rules enforced
echo.

echo App Configuration:
echo 1. Admin panel integrated in navigation
echo 2. Role-based UI adaptation working
echo 3. Permission services initialized
echo 4. Admin management services active
echo.

echo ========================================
echo   Troubleshooting
echo ========================================
echo.

echo Common Issues:
echo.
echo 1. Admin menu not visible:
echo    - Check user role in Firebase Database
echo    - Verify role detection service
echo    - Restart app to refresh permissions
echo.

echo 2. Permission denied errors:
echo    - Confirm admin role assignment
echo    - Check Firebase security rules
echo    - Verify permission service initialization
echo.

echo 3. User list not loading:
echo    - Check Firebase connection
echo    - Verify database read permissions
echo    - Review admin management service
echo.

echo 4. Server operations failing:
echo    - Confirm server management permissions
echo    - Check Firebase write permissions
echo    - Verify server data validation
echo.

echo ========================================
echo   Admin Panel Deployment Complete!
echo ========================================
echo.

echo Next Steps:
echo 1. Test admin panel access with admin account
echo 2. Verify all user management operations
echo 3. Test server management functionality
echo 4. Monitor system statistics and performance
echo 5. Train admin users on panel features
echo.

echo Database URL: https://mrelfeky-209615-default-rtdb.firebaseio.com/
echo Firebase Console: https://console.firebase.google.com/project/mrelfeky-209615
echo.

echo Admin Panel Features:
echo ✅ Complete user management with role control
echo ✅ Comprehensive server administration
echo ✅ Real-time statistics and monitoring
echo ✅ Role-based security and access control
echo ✅ Professional admin interface
echo.

echo The V2Hoor Admin Panel System is now ready for production use!
echo Administrators can now manage users, servers, and monitor the system.
echo.

pause
