package com.mohamedrady.v2hoor.model

import com.google.firebase.database.PropertyName
import java.text.SimpleDateFormat
import java.util.*

/**
 * Admin User Model for user management
 * Extended user model with admin-specific functionality
 */
data class AdminUserModel(
    @PropertyName("uid")
    var uid: String = "",
    
    @PropertyName("email")
    var email: String = "",
    
    @PropertyName("name")
    var name: String = "",
    
    @PropertyName("role")
    var role: String = "user",
    
    @PropertyName("subscription_end")
    var subscriptionEnd: String = "",
    
    @PropertyName("subscription_type")
    var subscriptionType: String = "basic",
    
    @PropertyName("is_active")
    var isActive: Boolean = true,
    
    @PropertyName("created_at")
    var createdAt: Long = 0,
    
    @PropertyName("updated_at")
    var updatedAt: Long = 0,
    
    @PropertyName("last_login")
    var lastLogin: Long = 0,
    
    @PropertyName("data_limit_gb")
    var dataLimitGb: Long = 0,
    
    @PropertyName("data_used_gb")
    var dataUsedGb: Long = 0,
    
    @PropertyName("max_devices")
    var maxDevices: Int = 1,
    
    @PropertyName("notes")
    var notes: String = "",
    
    @PropertyName("created_by")
    var createdBy: String = "",
    
    @PropertyName("avatar_url")
    var avatarUrl: String = "",
    
    // Calculated fields (not stored in Firebase)
    var serverCount: Int = 0,
    var isExpired: Boolean = false,
    var daysUntilExpiry: Long = 0
) {
    constructor() : this(
        uid = "",
        email = "",
        name = "",
        role = "user",
        subscriptionEnd = "",
        subscriptionType = "basic",
        isActive = true,
        createdAt = 0,
        updatedAt = 0,
        lastLogin = 0,
        dataLimitGb = 0,
        dataUsedGb = 0,
        maxDevices = 1,
        notes = "",
        createdBy = "",
        avatarUrl = "",
        serverCount = 0,
        isExpired = false,
        daysUntilExpiry = 0
    )
    
    /**
     * Check if user is admin
     */
    fun isAdmin(): Boolean {
        return role.equals("admin", ignoreCase = true)
    }
    
    /**
     * Get subscription status
     */
    fun getSubscriptionStatus(): String {
        return when {
            !isActive -> "غير نشط"
            subscriptionEnd.isBlank() -> "غير محدود"
            isExpired -> "منتهي الصلاحية"
            daysUntilExpiry in 1..3 -> "ينتهي قريباً"
            else -> "نشط"
        }
    }
    
    /**
     * Get subscription status color
     */
    fun getSubscriptionStatusColor(): Int {
        return when {
            !isActive -> android.R.color.darker_gray
            subscriptionEnd.isBlank() -> android.R.color.holo_blue_dark
            isExpired -> android.R.color.holo_red_dark
            daysUntilExpiry in 1..3 -> android.R.color.holo_orange_dark
            else -> android.R.color.holo_green_dark
        }
    }
    
    /**
     * Get formatted subscription end date
     */
    fun getFormattedSubscriptionEnd(): String {
        if (subscriptionEnd.isBlank()) return "غير محدود"
        
        return try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(subscriptionEnd)
            date?.let { outputFormat.format(it) } ?: subscriptionEnd
        } catch (e: Exception) {
            subscriptionEnd
        }
    }
    
    /**
     * Get formatted creation date
     */
    fun getFormattedCreatedAt(): String {
        return if (createdAt > 0) {
            val date = Date(createdAt)
            SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(date)
        } else {
            "غير محدد"
        }
    }
    
    /**
     * Get formatted last login
     */
    fun getFormattedLastLogin(): String {
        return if (lastLogin > 0) {
            val date = Date(lastLogin)
            SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(date)
        } else {
            "لم يسجل دخول"
        }
    }
    
    /**
     * Get data usage percentage
     */
    fun getDataUsagePercentage(): Float {
        return if (dataLimitGb > 0) {
            (dataUsedGb.toFloat() / dataLimitGb.toFloat()) * 100f
        } else {
            0f
        }
    }
    
    /**
     * Get data usage display
     */
    fun getDataUsageDisplay(): String {
        return if (dataLimitGb > 0) {
            "$dataUsedGb / $dataLimitGb GB"
        } else {
            "غير محدود"
        }
    }
    
    /**
     * Get role display name
     */
    fun getRoleDisplayName(): String {
        return when (role.lowercase()) {
            "admin" -> "مدير"
            "user" -> "مستخدم"
            else -> role
        }
    }
    
    /**
     * Get subscription type display name
     */
    fun getSubscriptionTypeDisplayName(): String {
        return when (subscriptionType.lowercase()) {
            "basic" -> "أساسي"
            "premium" -> "مميز"
            "unlimited" -> "غير محدود"
            "trial" -> "تجريبي"
            else -> subscriptionType
        }
    }
    
    /**
     * Calculate expiration status
     */
    fun calculateExpirationStatus() {
        if (subscriptionEnd.isBlank()) {
            isExpired = false
            daysUntilExpiry = Long.MAX_VALUE
            return
        }
        
        try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val endDate = dateFormat.parse(subscriptionEnd)
            val currentDate = Date()
            
            if (endDate != null) {
                val diffInMillis = endDate.time - currentDate.time
                daysUntilExpiry = diffInMillis / (24 * 60 * 60 * 1000)
                isExpired = diffInMillis < 0
            }
        } catch (e: Exception) {
            isExpired = false
            daysUntilExpiry = 0
        }
    }
    
    /**
     * Validate user data
     */
    fun isValid(): Boolean {
        return uid.isNotBlank() && 
               email.isNotBlank() && 
               name.isNotBlank() && 
               role.isNotBlank()
    }
    
    /**
     * Get summary for display
     */
    fun getSummary(): String {
        val parts = mutableListOf<String>()
        parts.add(getRoleDisplayName())
        parts.add(getSubscriptionStatus())
        if (serverCount > 0) parts.add("$serverCount سيرفر")
        return parts.joinToString(" • ")
    }
    
    /**
     * Create updated user with new role
     */
    fun withRole(newRole: String): AdminUserModel {
        return copy(
            role = newRole,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Create updated user with new subscription
     */
    fun withSubscription(newEndDate: String, newType: String = subscriptionType): AdminUserModel {
        return copy(
            subscriptionEnd = newEndDate,
            subscriptionType = newType,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Create deactivated user
     */
    fun deactivated(): AdminUserModel {
        return copy(
            isActive = false,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Create activated user
     */
    fun activated(): AdminUserModel {
        return copy(
            isActive = true,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Convert to UserProfile for compatibility
     */
    fun toUserProfile(): UserProfile {
        return UserProfile(
            uid = uid,
            email = email,
            name = name,
            role = role,
            subscriptionEnd = subscriptionEnd,
            isActive = isActive,
            createdAt = createdAt,
            updatedAt = updatedAt,
            lastLogin = lastLogin,
            subscriptionType = subscriptionType,
            maxDevices = maxDevices,
            dataLimitGb = dataLimitGb,
            dataUsedGb = dataUsedGb,
            notes = notes,
            createdBy = createdBy,
            avatarUrl = avatarUrl
        )
    }
    
    companion object {
        /**
         * Create from UserProfile
         */
        fun fromUserProfile(userProfile: UserProfile): AdminUserModel {
            return AdminUserModel(
                uid = userProfile.uid,
                email = userProfile.email,
                name = userProfile.name,
                role = userProfile.role,
                subscriptionEnd = userProfile.subscriptionEnd,
                isActive = userProfile.isActive,
                createdAt = userProfile.createdAt,
                updatedAt = userProfile.updatedAt,
                lastLogin = userProfile.lastLogin,
                subscriptionType = userProfile.subscriptionType,
                maxDevices = userProfile.maxDevices,
                dataLimitGb = userProfile.dataLimitGb,
                dataUsedGb = userProfile.dataUsedGb,
                notes = userProfile.notes,
                createdBy = userProfile.createdBy,
                avatarUrl = userProfile.avatarUrl
            ).apply {
                calculateExpirationStatus()
            }
        }
        
        /**
         * Create new user template
         */
        fun createNewUser(email: String, name: String, createdBy: String): AdminUserModel {
            val currentTime = System.currentTimeMillis()
            return AdminUserModel(
                uid = "", // Will be set after Firebase Auth creation
                email = email,
                name = name,
                role = "user",
                subscriptionEnd = "",
                subscriptionType = "trial",
                isActive = true,
                createdAt = currentTime,
                updatedAt = currentTime,
                lastLogin = 0,
                dataLimitGb = 10,
                dataUsedGb = 0,
                maxDevices = 1,
                notes = "تم إنشاؤه بواسطة المدير",
                createdBy = createdBy,
                avatarUrl = ""
            )
        }
    }
}
