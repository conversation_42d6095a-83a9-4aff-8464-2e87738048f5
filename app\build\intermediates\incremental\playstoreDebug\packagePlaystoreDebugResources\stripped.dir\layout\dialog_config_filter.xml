<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_spacing_dp16">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:orientation="vertical">

            <Spinner
                android:id="@+id/sp_subscriptionId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp8"
                android:layout_marginBottom="@dimen/padding_spacing_dp8" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_lab_remarks" />

            <EditText
                android:id="@+id/et_keyword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </LinearLayout>

    </LinearLayout>
</ScrollView>
