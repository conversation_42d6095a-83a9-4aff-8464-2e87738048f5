<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_details" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_user_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_user_details_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="332" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/textViewUserName" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="70" endOffset="48"/></Target><Target id="@+id/textViewUserEmail" view="TextView"><Expressions/><location startLine="72" startOffset="20" endLine="79" endOffset="58"/></Target><Target id="@+id/textViewUserId" view="TextView"><Expressions/><location startLine="81" startOffset="20" endLine="88" endOffset="60"/></Target><Target id="@+id/textViewUserStatus" view="TextView"><Expressions/><location startLine="96" startOffset="24" endLine="106" endOffset="54"/></Target><Target id="@+id/textViewRole" view="TextView"><Expressions/><location startLine="108" startOffset="24" endLine="116" endOffset="56"/></Target><Target id="@+id/textViewSubscriptionType" view="TextView"><Expressions/><location startLine="152" startOffset="20" endLine="160" endOffset="57"/></Target><Target id="@+id/textViewServerCount" view="TextView"><Expressions/><location startLine="162" startOffset="20" endLine="168" endOffset="55"/></Target><Target id="@+id/textViewCreationDate" view="TextView"><Expressions/><location startLine="202" startOffset="20" endLine="209" endOffset="65"/></Target><Target id="@+id/textViewLastLogin" view="TextView"><Expressions/><location startLine="211" startOffset="20" endLine="217" endOffset="63"/></Target><Target id="@+id/textViewPhoneNumber" view="TextView"><Expressions/><location startLine="251" startOffset="20" endLine="258" endOffset="63"/></Target><Target id="@+id/textViewCountry" view="TextView"><Expressions/><location startLine="260" startOffset="20" endLine="267" endOffset="49"/></Target><Target id="@+id/textViewCity" view="TextView"><Expressions/><location startLine="269" startOffset="20" endLine="275" endOffset="55"/></Target><Target id="@+id/buttonToggleStatus" view="Button"><Expressions/><location startLine="287" startOffset="16" endLine="295" endOffset="45"/></Target><Target id="@+id/buttonManageServers" view="Button"><Expressions/><location startLine="297" startOffset="16" endLine="305" endOffset="45"/></Target><Target id="@+id/buttonEditProfile" view="Button"><Expressions/><location startLine="307" startOffset="16" endLine="315" endOffset="45"/></Target><Target id="@+id/buttonDeleteUser" view="Button"><Expressions/><location startLine="317" startOffset="16" endLine="324" endOffset="45"/></Target></Targets></Layout>