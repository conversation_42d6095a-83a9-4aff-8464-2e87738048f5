// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityServerHysteria2Binding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText etBandwidthDown;

  @NonNull
  public final EditText etBandwidthUp;

  @NonNull
  public final EditText etId;

  @NonNull
  public final EditText etObfsPassword;

  @NonNull
  public final EditText etPortHop;

  @NonNull
  public final EditText etPortHopInterval;

  private ActivityServerHysteria2Binding(@NonNull ScrollView rootView,
      @NonNull EditText etBandwidthDown, @NonNull EditText etBandwidthUp, @NonNull EditText etId,
      @NonNull EditText etObfsPassword, @NonNull EditText etPortHop,
      @NonNull EditText etPortHopInterval) {
    this.rootView = rootView;
    this.etBandwidthDown = etBandwidthDown;
    this.etBandwidthUp = etBandwidthUp;
    this.etId = etId;
    this.etObfsPassword = etObfsPassword;
    this.etPortHop = etPortHop;
    this.etPortHopInterval = etPortHopInterval;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityServerHysteria2Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityServerHysteria2Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_server_hysteria2, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityServerHysteria2Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_bandwidth_down;
      EditText etBandwidthDown = ViewBindings.findChildViewById(rootView, id);
      if (etBandwidthDown == null) {
        break missingId;
      }

      id = R.id.et_bandwidth_up;
      EditText etBandwidthUp = ViewBindings.findChildViewById(rootView, id);
      if (etBandwidthUp == null) {
        break missingId;
      }

      id = R.id.et_id;
      EditText etId = ViewBindings.findChildViewById(rootView, id);
      if (etId == null) {
        break missingId;
      }

      id = R.id.et_obfs_password;
      EditText etObfsPassword = ViewBindings.findChildViewById(rootView, id);
      if (etObfsPassword == null) {
        break missingId;
      }

      id = R.id.et_port_hop;
      EditText etPortHop = ViewBindings.findChildViewById(rootView, id);
      if (etPortHop == null) {
        break missingId;
      }

      id = R.id.et_port_hop_interval;
      EditText etPortHopInterval = ViewBindings.findChildViewById(rootView, id);
      if (etPortHopInterval == null) {
        break missingId;
      }

      return new ActivityServerHysteria2Binding((ScrollView) rootView, etBandwidthDown,
          etBandwidthUp, etId, etObfsPassword, etPortHop, etPortHopInterval);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
