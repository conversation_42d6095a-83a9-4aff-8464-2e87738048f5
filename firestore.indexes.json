{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionType", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "public_servers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "ASCENDING"}]}, {"collectionGroup": "public_servers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "country", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "ASCENDING"}]}, {"collectionGroup": "user_roles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "user_activity", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "support_tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "support_tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "announcements", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "server_assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "serverId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "user_subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "expiresAt", "order": "ASCENDING"}]}, {"collectionGroup": "app_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "level", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "user_stats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "users", "fieldPath": "email", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "public_servers", "fieldPath": "name", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}