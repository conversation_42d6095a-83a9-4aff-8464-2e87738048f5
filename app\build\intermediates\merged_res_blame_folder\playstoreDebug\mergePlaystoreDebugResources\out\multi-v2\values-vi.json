{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergePlaystoreDebugResources-73:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "40,41,42,43,44,45,46,146", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3634,3731,3833,3932,4032,4135,4248,13151", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3726,3828,3927,4027,4130,4243,4359,13247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,339,482,651,731", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "172,259,334,477,646,726,803"}, "to": {"startLines": "68,72,137,139,147,148,149", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6891,7206,12409,12562,13252,13421,13501", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "6958,7288,12479,12700,13416,13496,13573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,12831", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,12911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5673", "endColumns": "127", "endOffsets": "5796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,70,71,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3240,3319,3396,3474,3554,4364,4463,4577,7080,7143,7293,7717,7791,7850,7936,7998,8059,8117,8181,8242,8296,8413,8470,8530,8584,8659,8786,8870,8950,9045,9129,9207,9337,9421,9499,9633,9724,9805,9856,9907,9973,10041,10117,10188,10268,10347,10422,10495,10571,10677,10766,10843,10934,11028,11102,11172,11265,11314,11395,11461,11546,11632,11694,11758,11821,11892,11991,12096,12194,12299,12354,12484,12916,12998,13077", "endLines": "5,35,36,37,38,39,47,48,49,70,71,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,143,144,145", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3314,3391,3469,3549,3629,4458,4572,4652,7138,7201,7382,7786,7845,7931,7993,8054,8112,8176,8237,8291,8408,8465,8525,8579,8654,8781,8865,8945,9040,9124,9202,9332,9416,9494,9628,9719,9800,9851,9902,9968,10036,10112,10183,10263,10342,10417,10490,10566,10672,10761,10838,10929,11023,11097,11167,11260,11309,11390,11456,11541,11627,11689,11753,11816,11887,11986,12091,12189,12294,12349,12404,12557,12993,13072,13146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,104", "endOffsets": "167,278,392,497"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6963,7387,7498,7612", "endColumns": "116,110,113,104", "endOffsets": "7075,7493,7607,7712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4657,4759,4921,5046,5155,5320,5450,5569,5801,5974,6081,6238,6368,6527,6676,6744,6808", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "4754,4916,5041,5150,5315,5445,5564,5668,5969,6076,6233,6363,6522,6671,6739,6803,6886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,119", "endColumns": "63,61", "endOffsets": "114,176"}, "to": {"startLines": "140,141", "startColumns": "4,4", "startOffsets": "12705,12769", "endColumns": "63,61", "endOffsets": "12764,12826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3008,3120", "endColumns": "111,119", "endOffsets": "3115,3235"}}]}]}