<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="nav_header" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\nav_header.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/nav_header_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="54" endOffset="14"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="58"/></Target><Target id="@+id/tv_user_name" view="TextView"><Expressions/><location startLine="31" startOffset="4" endLine="40" endOffset="50"/></Target><Target id="@+id/tv_user_email" view="TextView"><Expressions/><location startLine="43" startOffset="4" endLine="52" endOffset="29"/></Target></Targets></Layout>