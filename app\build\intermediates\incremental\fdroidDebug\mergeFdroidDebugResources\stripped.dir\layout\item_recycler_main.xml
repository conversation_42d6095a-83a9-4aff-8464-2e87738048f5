<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical">

    <LinearLayout
        android:id="@+id/info_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:nextFocusRight="@+id/layout_share"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_spacing_dp4"
        android:paddingTop="@dimen/padding_spacing_dp8"
        android:paddingEnd="@dimen/padding_spacing_dp4"
        android:paddingBottom="@dimen/padding_spacing_dp8">

        <LinearLayout
            android:id="@+id/layout_indicator"
            android:layout_width="@dimen/padding_spacing_dp4"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:orientation="vertical" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/padding_spacing_dp8">

                        <TextView
                            android:id="@+id/tv_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="2"
                            android:minLines="1"
                            android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/padding_spacing_dp8">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/layout_subscription"
                                android:layout_width="@dimen/image_size_dp24"
                                android:layout_height="@dimen/image_size_dp24"
                                android:layout_gravity="bottom"
                                android:layout_marginEnd="@dimen/padding_spacing_dp4"
                                android:background="@drawable/ic_circle">

                                <TextView
                                    android:id="@+id/tv_subscription"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                    android:textSize="11sp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />
                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <TextView
                                android:id="@+id/tv_statistics"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:lines="1"
                                android:textAppearance="@style/TextAppearance.AppCompat.Small" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/layout_share"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:nextFocusLeft="@+id/info_container"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_spacing_dp8">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/image_size_dp24"
                            app:srcCompat="@drawable/ic_share_24dp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layout_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_spacing_dp8">

                        <ImageView
                            android:layout_width="@dimen/image_size_dp24"
                            android:layout_height="@dimen/image_size_dp24"
                            app:srcCompat="@drawable/ic_edit_24dp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layout_remove"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_spacing_dp8">

                        <ImageView
                            android:layout_width="@dimen/image_size_dp24"
                            android:layout_height="@dimen/image_size_dp24"
                            app:srcCompat="@drawable/ic_delete_24dp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layout_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_spacing_dp8">

                        <ImageView
                            android:layout_width="@dimen/image_size_dp24"
                            android:layout_height="@dimen/image_size_dp24"
                            app:srcCompat="@drawable/ic_more_vert_24dp" />

                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="@dimen/padding_spacing_dp8"
                android:paddingTop="@dimen/padding_spacing_dp8"
                android:paddingEnd="@dimen/padding_spacing_dp8">

                <TextView
                    android:id="@+id/tv_type"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:lines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/colorConfigType"
                    android:textSize="11sp"
                    tools:text="VLESS" />

                <TextView
                    android:id="@+id/tv_test_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:lines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/colorPing"
                    android:textSize="11sp"
                    tools:text="214ms" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
