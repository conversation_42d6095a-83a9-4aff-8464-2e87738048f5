<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_server_hysteria2" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_server_hysteria2.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_server_hysteria2_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="140" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="139" endOffset="18"/></Target><Target tag="binding_1" include="layout_address_port"><Expressions/><location startLine="14" startOffset="8" endLine="14" endOffset="55"/></Target><Target tag="binding_1" include="layout_tls_hysteria2"><Expressions/><location startLine="130" startOffset="8" endLine="130" endOffset="56"/></Target><Target id="@+id/et_id" view="EditText"><Expressions/><location startLine="27" startOffset="12" endLine="31" endOffset="42"/></Target><Target id="@+id/et_obfs_password" view="EditText"><Expressions/><location startLine="46" startOffset="12" endLine="50" endOffset="42"/></Target><Target id="@+id/et_port_hop" view="EditText"><Expressions/><location startLine="65" startOffset="12" endLine="69" endOffset="42"/></Target><Target id="@+id/et_port_hop_interval" view="EditText"><Expressions/><location startLine="84" startOffset="12" endLine="88" endOffset="44"/></Target><Target id="@+id/et_bandwidth_down" view="EditText"><Expressions/><location startLine="103" startOffset="12" endLine="107" endOffset="42"/></Target><Target id="@+id/et_bandwidth_up" view="EditText"><Expressions/><location startLine="122" startOffset="12" endLine="126" endOffset="42"/></Target></Targets></Layout>