<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="QuickieScannerActivity" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:statusBarColor">@color/quickie_transparent</item>
    <item name="android:navigationBarColor">@color/quickie_transparent</item>
    
    <item name="android:windowLightStatusBar">false</item>
    
    <item name="android:windowLightNavigationBar">false</item>
    <item name="android:navigationBarDividerColor">@color/quickie_transparent</item>
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    
    <item name="android:enforceNavigationBarContrast">false</item>
    <item name="android:enforceStatusBarContrast">false</item>
  </style>
</resources>