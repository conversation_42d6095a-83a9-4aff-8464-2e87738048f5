// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecyclerSubSettingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final SwitchCompat chkEnable;

  @NonNull
  public final LinearLayout infoContainer;

  @NonNull
  public final LinearLayout itemBg;

  @NonNull
  public final LinearLayout layoutEdit;

  @NonNull
  public final LinearLayout layoutRemove;

  @NonNull
  public final LinearLayout layoutShare;

  @NonNull
  public final LinearLayout layoutUrl;

  @NonNull
  public final TextView tvName;

  @NonNull
  public final TextView tvUrl;

  private ItemRecyclerSubSettingBinding(@NonNull LinearLayout rootView,
      @NonNull SwitchCompat chkEnable, @NonNull LinearLayout infoContainer,
      @NonNull LinearLayout itemBg, @NonNull LinearLayout layoutEdit,
      @NonNull LinearLayout layoutRemove, @NonNull LinearLayout layoutShare,
      @NonNull LinearLayout layoutUrl, @NonNull TextView tvName, @NonNull TextView tvUrl) {
    this.rootView = rootView;
    this.chkEnable = chkEnable;
    this.infoContainer = infoContainer;
    this.itemBg = itemBg;
    this.layoutEdit = layoutEdit;
    this.layoutRemove = layoutRemove;
    this.layoutShare = layoutShare;
    this.layoutUrl = layoutUrl;
    this.tvName = tvName;
    this.tvUrl = tvUrl;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecyclerSubSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecyclerSubSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recycler_sub_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecyclerSubSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chk_enable;
      SwitchCompat chkEnable = ViewBindings.findChildViewById(rootView, id);
      if (chkEnable == null) {
        break missingId;
      }

      id = R.id.info_container;
      LinearLayout infoContainer = ViewBindings.findChildViewById(rootView, id);
      if (infoContainer == null) {
        break missingId;
      }

      LinearLayout itemBg = (LinearLayout) rootView;

      id = R.id.layout_edit;
      LinearLayout layoutEdit = ViewBindings.findChildViewById(rootView, id);
      if (layoutEdit == null) {
        break missingId;
      }

      id = R.id.layout_remove;
      LinearLayout layoutRemove = ViewBindings.findChildViewById(rootView, id);
      if (layoutRemove == null) {
        break missingId;
      }

      id = R.id.layout_share;
      LinearLayout layoutShare = ViewBindings.findChildViewById(rootView, id);
      if (layoutShare == null) {
        break missingId;
      }

      id = R.id.layout_url;
      LinearLayout layoutUrl = ViewBindings.findChildViewById(rootView, id);
      if (layoutUrl == null) {
        break missingId;
      }

      id = R.id.tv_name;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      id = R.id.tv_url;
      TextView tvUrl = ViewBindings.findChildViewById(rootView, id);
      if (tvUrl == null) {
        break missingId;
      }

      return new ItemRecyclerSubSettingBinding((LinearLayout) rootView, chkEnable, infoContainer,
          itemBg, layoutEdit, layoutRemove, layoutShare, layoutUrl, tvName, tvUrl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
