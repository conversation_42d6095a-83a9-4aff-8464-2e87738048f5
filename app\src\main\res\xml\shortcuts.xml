<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
    <shortcut
        android:enabled="true"
        android:icon="@drawable/ic_qu_switch_24dp"
        android:shortcutDisabledMessage="@string/app_widget_name"
        android:shortcutId="shortcuts_switch"
        android:shortcutLongLabel="@string/app_widget_name"
        android:shortcutShortLabel="@string/app_widget_name">

        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
            android:targetPackage="com.mohamedrady.v2hoor" />
        <categories android:name="android.shortcut.conversation" />
    </shortcut>
    <shortcut
        android:enabled="true"
        android:icon="@drawable/ic_qu_scan_24dp"
        android:shortcutDisabledMessage="@string/menu_item_import_config_qrcode"
        android:shortcutId="shortcuts_scan"
        android:shortcutLongLabel="@string/menu_item_import_config_qrcode"
        android:shortcutShortLabel="@string/menu_item_import_config_qrcode">

        <intent
            android:action="android.intent.action.VIEW"
            android:targetClass="com.mohamedrady.v2hoor.ui.ScScannerActivity"
            android:targetPackage="com.mohamedrady.v2hoor" />
        <categories android:name="android.shortcut.conversation" />
    </shortcut>
</shortcuts>