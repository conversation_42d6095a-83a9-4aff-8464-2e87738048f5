package com.mohamedrady.v2hoor.dto

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * User profile data class
 */
@Parcelize
data class UserProfile(
    val uid: String,
    val email: String? = null,
    val displayName: String? = null,
    val photoUrl: String? = null,
    val phoneNumber: String? = null,
    val country: String? = null,
    val city: String? = null,
    val isActive: Boolean = true,
    val createdAt: Long? = null,
    val updatedAt: Long? = null,
    val lastLogin: Long? = null,
    val serverCount: Int = 0,
    val subscriptionType: String = "free", // free, premium, vip
    val subscriptionExpiry: Long? = null,
    val role: String? = null // admin, moderator, user
) : Parcelable {
    
    /**
     * Get formatted display name
     */
    fun getFormattedDisplayName(): String {
        return displayName?.takeIf { it.isNotBlank() } 
            ?: email?.substringBefore("@") 
            ?: "مستخدم غير معروف"
    }
    
    /**
     * Check if subscription is active
     */
    fun isSubscriptionActive(): Boolean {
        return when (subscriptionType) {
            "free" -> true
            else -> subscriptionExpiry?.let { it > System.currentTimeMillis() } ?: false
        }
    }
    
    /**
     * Get subscription status text
     */
    fun getSubscriptionStatusText(): String {
        return when {
            subscriptionType == "free" -> "مجاني"
            isSubscriptionActive() -> when (subscriptionType) {
                "premium" -> "مميز"
                "vip" -> "VIP"
                else -> "مدفوع"
            }
            else -> "منتهي الصلاحية"
        }
    }
    
    /**
     * Get role display text
     */
    fun getRoleDisplayText(): String {
        return when (role) {
            "super_admin" -> "المدير العام"
            "admin" -> "مدير"
            "moderator" -> "مشرف"
            else -> "مستخدم"
        }
    }
    
    /**
     * Check if user can access admin features
     */
    fun canAccessAdminFeatures(): Boolean {
        return role in listOf("super_admin", "admin", "moderator")
    }
    
    /**
     * Get formatted creation date
     */
    fun getFormattedCreationDate(): String {
        return createdAt?.let { 
            java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault())
                .format(java.util.Date(it))
        } ?: "غير محدد"
    }
    
    /**
     * Get formatted last login
     */
    fun getFormattedLastLogin(): String {
        return lastLogin?.let { 
            val now = System.currentTimeMillis()
            val diff = now - it
            
            when {
                diff < 60_000 -> "الآن"
                diff < 3600_000 -> "${diff / 60_000} دقيقة"
                diff < 86400_000 -> "${diff / 3600_000} ساعة"
                diff < 2592000_000 -> "${diff / 86400_000} يوم"
                else -> java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault())
                    .format(java.util.Date(it))
            }
        } ?: "لم يسجل دخول"
    }
}

/**
 * User statistics data class
 */
@Parcelize
data class UserStatistics(
    val totalUsers: Int = 0,
    val activeUsers: Int = 0,
    val inactiveUsers: Int = 0,
    val premiumUsers: Int = 0,
    val freeUsers: Int = 0,
    val newUsersToday: Int = 0,
    val newUsersThisWeek: Int = 0,
    val newUsersThisMonth: Int = 0
) : Parcelable
