<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_edit_server" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_edit_server.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_edit_server_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="248" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/editTextServerName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="70" startOffset="24" endLine="74" endOffset="54"/></Target><Target id="@+id/editTextServerConfig" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="86" startOffset="24" endLine="92" endOffset="47"/></Target><Target id="@+id/editTextCountry" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="110" startOffset="28" endLine="114" endOffset="58"/></Target><Target id="@+id/editTextCity" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="127" startOffset="28" endLine="131" endOffset="58"/></Target><Target id="@+id/editTextMaxUsers" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="152" startOffset="28" endLine="157" endOffset="52"/></Target><Target id="@+id/editTextPriority" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="170" startOffset="28" endLine="175" endOffset="50"/></Target><Target id="@+id/switchServerActive" view="Switch"><Expressions/><location startLine="196" startOffset="24" endLine="200" endOffset="52"/></Target><Target id="@+id/buttonCancel" view="Button"><Expressions/><location startLine="215" startOffset="16" endLine="223" endOffset="45"/></Target><Target id="@+id/buttonSave" view="Button"><Expressions/><location startLine="225" startOffset="16" endLine="232" endOffset="45"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="241" startOffset="4" endLine="246" endOffset="35"/></Target></Targets></Layout>