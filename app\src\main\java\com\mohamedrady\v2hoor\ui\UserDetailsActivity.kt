package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityUserDetailsBinding
import com.mohamedrady.v2hoor.dto.UserProfile
import com.mohamedrady.v2hoor.service.AdminPermissionService
import com.mohamedrady.v2hoor.service.UserManagementService
import kotlinx.coroutines.launch

/**
 * Activity for displaying and editing user details
 */
class UserDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUserDetailsBinding
    private lateinit var userProfile: UserProfile
    private val adminService = AdminPermissionService.getInstance()
    private val userManagementService = UserManagementService.getInstance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Check admin permissions
        lifecycleScope.launch {
            if (!adminService.isAdmin()) {
                Toast.makeText(this@UserDetailsActivity, "غير مصرح لك بالوصول لهذه الصفحة", Toast.LENGTH_SHORT).show()
                finish()
                return@launch
            }
        }

        binding = ActivityUserDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Get user profile from intent
        userProfile = intent.getParcelableExtra("user_profile") ?: run {
            Toast.makeText(this, "خطأ في تحميل بيانات المستخدم", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        setupToolbar()
        displayUserDetails()
        setupClickListeners()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "تفاصيل المستخدم"
        }
    }

    private fun displayUserDetails() {
        binding.apply {
            // Basic Info
            textViewUserName.text = userProfile.getFormattedDisplayName()
            textViewUserEmail.text = userProfile.email ?: "بريد غير محدد"
            textViewUserId.text = "معرف المستخدم: ${userProfile.uid}"
            
            // Status
            val statusColor = if (userProfile.isActive) {
                getColor(R.color.green)
            } else {
                getColor(R.color.red)
            }
            textViewUserStatus.text = if (userProfile.isActive) "نشط" else "غير نشط"
            textViewUserStatus.setTextColor(statusColor)
            
            // Subscription
            textViewSubscriptionType.text = "نوع الاشتراك: ${userProfile.getSubscriptionStatusText()}"
            val subscriptionColor = when (userProfile.subscriptionType) {
                "premium" -> getColor(R.color.orange)
                "vip" -> getColor(R.color.purple)
                else -> getColor(R.color.gray)
            }
            textViewSubscriptionType.setTextColor(subscriptionColor)
            
            // Dates
            textViewCreationDate.text = "تاريخ الانضمام: ${userProfile.getFormattedCreationDate()}"
            textViewLastLogin.text = "آخر تسجيل دخول: ${userProfile.getFormattedLastLogin()}"
            
            // Server count
            textViewServerCount.text = "عدد السيرفرات: ${userProfile.serverCount}"
            
            // Role
            if (userProfile.canAccessAdminFeatures()) {
                textViewRole.text = "الدور: ${userProfile.getRoleDisplayText()}"
                textViewRole.setTextColor(getColor(R.color.blue))
            } else {
                textViewRole.text = "الدور: مستخدم عادي"
                textViewRole.setTextColor(getColor(R.color.gray))
            }
            
            // Contact info
            textViewPhoneNumber.text = "رقم الهاتف: ${userProfile.phoneNumber ?: "غير محدد"}"
            textViewCountry.text = "البلد: ${userProfile.country ?: "غير محدد"}"
            textViewCity.text = "المدينة: ${userProfile.city ?: "غير محدد"}"
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            buttonToggleStatus.apply {
                text = if (userProfile.isActive) "إلغاء التفعيل" else "تفعيل"
                setOnClickListener { toggleUserStatus() }
            }
            
            buttonManageServers.setOnClickListener { manageUserServers() }
            
            buttonEditProfile.setOnClickListener { editUserProfile() }
            
            buttonDeleteUser.setOnClickListener { confirmDeleteUser() }
        }
    }

    private fun toggleUserStatus() {
        val newStatus = !userProfile.isActive
        val message = if (newStatus) "تفعيل" else "إلغاء تفعيل"
        
        AlertDialog.Builder(this)
            .setTitle("تأكيد العملية")
            .setMessage("هل تريد $message المستخدم ${userProfile.getFormattedDisplayName()}؟")
            .setPositiveButton("نعم") { _, _ ->
                lifecycleScope.launch {
                    val result = userManagementService.updateUserStatus(userProfile.uid, newStatus)
                    result.fold(
                        onSuccess = {
                            userProfile = userProfile.copy(isActive = newStatus)
                            displayUserDetails()
                            Toast.makeText(this@UserDetailsActivity, "تم تحديث حالة المستخدم", Toast.LENGTH_SHORT).show()
                        },
                        onFailure = { exception ->
                            Toast.makeText(this@UserDetailsActivity, "فشل في تحديث الحالة: ${exception.message}", Toast.LENGTH_LONG).show()
                        }
                    )
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun manageUserServers() {
        val intent = Intent(this, UserServersActivity::class.java).apply {
            putExtra("user_id", userProfile.uid)
            putExtra("user_name", userProfile.getFormattedDisplayName())
        }
        startActivity(intent)
    }

    private fun editUserProfile() {
        // TODO: Implement user profile editing
        Toast.makeText(this, "تعديل الملف الشخصي - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun confirmDeleteUser() {
        lifecycleScope.launch {
            if (!adminService.isSuperAdmin()) {
                Toast.makeText(this@UserDetailsActivity, "فقط المدير العام يمكنه حذف المستخدمين", Toast.LENGTH_SHORT).show()
                return@launch
            }
        }

        AlertDialog.Builder(this)
            .setTitle("تحذير")
            .setMessage("هل تريد حذف المستخدم ${userProfile.getFormattedDisplayName()} نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!")
            .setPositiveButton("حذف") { _, _ ->
                lifecycleScope.launch {
                    val result = userManagementService.deleteUser(userProfile.uid)
                    result.fold(
                        onSuccess = {
                            Toast.makeText(this@UserDetailsActivity, "تم حذف المستخدم", Toast.LENGTH_SHORT).show()
                            finish()
                        },
                        onFailure = { exception ->
                            Toast.makeText(this@UserDetailsActivity, "فشل في حذف المستخدم: ${exception.message}", Toast.LENGTH_LONG).show()
                        }
                    )
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_user_details, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_refresh -> {
                // TODO: Refresh user data
                Toast.makeText(this, "تحديث البيانات - قريباً", Toast.LENGTH_SHORT).show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
