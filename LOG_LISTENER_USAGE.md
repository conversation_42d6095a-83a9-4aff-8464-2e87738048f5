# 🎧 Log Listener Service - Usage Guide

## Overview
The Log Listener Service provides real-time monitoring and display of application logs. It's designed to help developers and administrators track application behavior, debug issues, and monitor system health.

## Features

### 🔍 **Real-time Log Monitoring**
- Monitors application logs in real-time
- Supports multiple log sources (logcat, internal logs, file logs)
- Automatic fallback mechanisms when primary sources are unavailable

### 📊 **Log Filtering & Management**
- Filter by log level (VERBOSE, DEBUG, INFO, WARN, ERROR, FATAL)
- Filter by tag or message content
- Automatic log rotation to prevent memory issues
- Configurable log retention limits

### 🎨 **Rich UI Experience**
- Color-coded log levels with emoji indicators
- Auto-scroll functionality
- Click-to-view detailed log information
- Export logs to clipboard
- Real-time statistics display

## How to Access

### 📱 **From Admin Panel:**
1. Open the V2HoorVPN app
2. Login with admin credentials
3. Navigate to Admin Panel from the side menu
4. In the "Diagnostic Tools" section, tap on:
   - **"عرض سجلات التطبيق"** - Static log viewer
   - **"مراقب السجلات المباشر"** - Real-time log monitor

## Real-time Log Monitor Features

### 🎛️ **Control Panel**
- **Status Indicator**: Shows if monitoring is active (🟢 يعمل / 🔴 متوقف)
- **Statistics**: Real-time count of total logs, errors, warnings, and info messages
- **Log Level Selector**: Choose minimum log level to display
- **Filter Button**: Apply custom filters
- **Pause/Resume**: Control log monitoring
- **Auto-scroll Toggle**: Enable/disable automatic scrolling
- **Clear Logs**: Remove all displayed logs

### 🔍 **Filtering Options**
- **By Tag**: Filter logs from specific components
- **By Message**: Search for specific text in log messages
- **By Level**: Show only logs above a certain severity level

### 📋 **Log Entry Display**
Each log entry shows:
- **Timestamp**: When the log was generated
- **Level**: Log severity with color coding and emoji
- **Tag**: Source component or module
- **Message**: The actual log content
- **PID/TID**: Process and thread IDs (when available)

### 🎨 **Color Coding**
- **❌ ERROR (Red)**: Critical errors requiring attention
- **⚠️ WARNING (Orange)**: Potential issues or warnings
- **ℹ️ INFO (Blue)**: General information messages
- **🐛 DEBUG (Green)**: Debug information for development
- **📝 VERBOSE (Gray)**: Detailed verbose output

## Usage Examples

### 🔧 **For Debugging**
1. Open Real-time Log Monitor
2. Set log level to "DEBUG" or "VERBOSE"
3. Reproduce the issue you're investigating
4. Watch for error messages or unusual patterns
5. Tap on specific log entries for detailed information
6. Export relevant logs for further analysis

### 📊 **For Monitoring**
1. Keep the Real-time Log Monitor open
2. Set appropriate filters (e.g., only ERROR and WARNING)
3. Monitor the statistics panel for error counts
4. Use auto-scroll to see latest logs automatically

### 🔍 **For Troubleshooting Admin Issues**
1. Filter by tag "AdminPanel" or "AdminPermission"
2. Look for authentication and permission-related logs
3. Check for Firebase connection issues
4. Monitor user action logs

## Technical Details

### 📡 **Log Sources**
1. **Logcat Monitoring**: Attempts to read system logcat output
2. **Internal Log Monitoring**: Monitors CrashHandler and ErrorMonitor logs
3. **File Watcher**: Monitors log files for changes
4. **Sample Generation**: Generates sample logs for demonstration

### 🔄 **Automatic Fallbacks**
- If logcat access fails, switches to internal monitoring
- If specific log sources are unavailable, continues with available sources
- Graceful degradation ensures the service remains functional

### 💾 **Memory Management**
- Limits stored log entries to prevent memory issues
- Automatic cleanup of old entries
- Efficient data structures for real-time updates

## Best Practices

### 👨‍💻 **For Developers**
- Use appropriate log levels for different types of messages
- Include meaningful tags to identify log sources
- Keep log messages concise but informative
- Use the real-time monitor during development and testing

### 👑 **For Administrators**
- Regularly check error statistics
- Set up appropriate filters to focus on relevant logs
- Export logs when reporting issues
- Monitor during critical operations

## Troubleshooting

### 🚫 **If No Logs Appear**
1. Check if the service is running (status indicator)
2. Verify log level settings aren't too restrictive
3. Clear any active filters
4. Try pausing and resuming the service

### 🐌 **If Performance Issues Occur**
1. Increase the minimum log level to reduce volume
2. Apply more restrictive filters
3. Clear old logs regularly
4. Restart the monitoring service

### 📱 **If App Crashes**
1. The log service runs independently and should continue
2. Restart the app and check the log viewer for crash information
3. Export logs before clearing for analysis

## Future Enhancements

- Remote log streaming
- Log file export to external storage
- Advanced filtering with regex support
- Log analysis and pattern detection
- Integration with crash reporting services

---

**Note**: The Log Listener Service is designed for debugging and monitoring purposes. It should be used responsibly and may impact battery life if left running continuously.
