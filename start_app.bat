@echo off
echo Starting V2HoorVPN app...

REM Try different possible adb locations
set ADB_PATH=""

if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe" (
    set ADB_PATH="C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe"
) else if exist "C:\Android\Sdk\platform-tools\adb.exe" (
    set ADB_PATH="C:\Android\Sdk\platform-tools\adb.exe"
) else if exist "C:\Program Files\Android\Android Studio\platform-tools\adb.exe" (
    set ADB_PATH="C:\Program Files\Android\Android Studio\platform-tools\adb.exe"
) else (
    echo Searching for adb.exe...
    for /f "delims=" %%i in ('where /r C:\ adb.exe 2^>nul') do (
        set ADB_PATH="%%i"
        goto :found
    )
)

:found
if %ADB_PATH%=="" (
    echo ADB not found. Please install Android SDK or Android Studio.
    echo You can manually start the app from your device.
    pause
    exit /b 1
)

echo Found ADB at: %ADB_PATH%

REM Check if device is connected
%ADB_PATH% devices
echo.

REM Start the app
echo Starting V2HoorVPN...
%ADB_PATH% shell am start -n com.mohamedrady.v2hoor.fdroid/com.mohamedrady.v2hoor.ui.MainActivity

echo.
echo App should be starting on your device now.
echo If you see any errors, please check the logcat output below:
echo.

REM Show recent logs
%ADB_PATH% logcat -t 50 | findstr "V2HoorVPN\|LoginActivity\|MainActivity\|AndroidRuntime"

pause
