rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for admin checks
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isSuperAdmin() {
      return isAuthenticated() && 
             request.auth.token.email == "<EMAIL>";
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             (isSuperAdmin() || 
              exists(/databases/$(database)/documents/user_roles/$(request.auth.uid)) &&
              get(/databases/$(database)/documents/user_roles/$(request.auth.uid)).data.role in ['admin', 'super_admin']);
    }
    
    function hasValidUserRole() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/user_roles/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/user_roles/$(request.auth.uid)).data.isActive == true;
    }
    
    // User roles collection - Admin access only
    match /user_roles/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isAdmin();
      allow create: if isAdmin() || (isOwner(userId) && !exists(/databases/$(database)/documents/user_roles/$(userId)));
    }
    
    // Users collection - Users can read/write their own data, admins can read all
    match /users/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isOwner(userId) || isAdmin();
      allow create: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }
    
    // Admins collection - Super admin only
    match /admins/{adminId} {
      allow read, write: if isSuperAdmin();
    }
    
    // Public servers collection - Read for authenticated users, write for admins
    match /public_servers/{serverId} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }
    
    // User servers collection - Users can access their assigned servers
    match /user_servers/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isAdmin();
    }
    
    // Server assignments - Admin only
    match /server_assignments/{assignmentId} {
      allow read, write: if isAdmin();
    }
    
    // User subscriptions - Users can read their own, admins can read/write all
    match /user_subscriptions/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isAdmin();
    }
    
    // App logs - Admin only
    match /app_logs/{logId} {
      allow read, write: if isAdmin();
    }
    
    // User activity logs - Users can read their own, admins can read all
    match /user_activity/{userId} {
      match /logs/{logId} {
        allow read: if isOwner(userId) || isAdmin();
        allow write: if isOwner(userId) || isAdmin();
      }
    }
    
    // System settings - Admin only
    match /system_settings/{settingId} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isAdmin();
    }
    
    // App configuration - Read for authenticated users, write for admins
    match /app_config/{configId} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isAdmin();
    }
    
    // User feedback - Users can create, admins can read all
    match /user_feedback/{feedbackId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated() && hasValidUserRole();
      allow update: if isAdmin() || (isAuthenticated() && resource.data.userId == request.auth.uid);
    }
    
    // Server statistics - Admin only
    match /server_stats/{statId} {
      allow read, write: if isAdmin();
    }
    
    // User usage statistics - Users can read their own, admins can read all
    match /user_stats/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isAdmin();
    }
    
    // Announcements - Read for all authenticated users, write for admins
    match /announcements/{announcementId} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isAdmin();
    }
    
    // Support tickets - Users can create and read their own, admins can read all
    match /support_tickets/{ticketId} {
      allow read: if isAdmin() || (isAuthenticated() && resource.data.userId == request.auth.uid);
      allow create: if isAuthenticated() && hasValidUserRole() && request.resource.data.userId == request.auth.uid;
      allow update: if isAdmin() || (isAuthenticated() && resource.data.userId == request.auth.uid);
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
