<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_more_function"
            android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_network" />

        <Spinner
            android:id="@+id/sp_network"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/networks" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:id="@+id/sp_header_type_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_head_type" />

        <Spinner
            android:id="@+id/sp_header_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_request_host"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_request_host" />

        <EditText
            android:id="@+id/et_request_host"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_path"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_path" />

        <EditText
            android:id="@+id/et_path"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_extra"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_xhttp_extra" />

        <EditText
            android:id="@+id/et_extra"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:inputType="textMultiLine"
            android:maxLines="20"
            android:minLines="4" />
    </LinearLayout>

</LinearLayout>