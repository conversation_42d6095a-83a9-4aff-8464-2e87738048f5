<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_admin_users" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_admin_users.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_admin_users_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="195" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/textViewTotalUsers" view="TextView"><Expressions/><location startLine="63" startOffset="24" endLine="72" endOffset="54"/></Target><Target id="@+id/textViewActiveUsers" view="TextView"><Expressions/><location startLine="74" startOffset="24" endLine="83" endOffset="54"/></Target><Target id="@+id/textViewInactiveUsers" view="TextView"><Expressions/><location startLine="85" startOffset="24" endLine="94" endOffset="54"/></Target><Target id="@+id/textViewPremiumUsers" view="TextView"><Expressions/><location startLine="96" startOffset="24" endLine="105" endOffset="54"/></Target><Target id="@+id/editTextSearch" view="androidx.appcompat.widget.SearchView"><Expressions/><location startLine="127" startOffset="20" endLine="134" endOffset="64"/></Target><Target id="@+id/textViewUsersCount" view="TextView"><Expressions/><location startLine="141" startOffset="24" endLine="148" endOffset="53"/></Target><Target id="@+id/buttonRefresh" view="Button"><Expressions/><location startLine="150" startOffset="24" endLine="157" endOffset="53"/></Target><Target id="@+id/recyclerViewUsers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="166" startOffset="12" endLine="171" endOffset="58"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="178" startOffset="4" endLine="183" endOffset="35"/></Target><Target id="@+id/fabAddUser" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="186" startOffset="4" endLine="193" endOffset="33"/></Target></Targets></Layout>