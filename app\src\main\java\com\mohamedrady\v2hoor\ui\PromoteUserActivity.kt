package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityPromoteUserBinding

class PromoteUserActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPromoteUserBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPromoteUserBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.promote_user)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
