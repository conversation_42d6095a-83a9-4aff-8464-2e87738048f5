<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.mohamedrady.v2hoor"
    android:versionCode="4000657"
    android:versionName="1.10.7" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="35" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
        android:minSdkVersion="34" />
    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <permission
        android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />

    <application
        android:name="com.mohamedrady.v2hoor.AngApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:banner="@mipmap/ic_banner"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppThemeDayNight"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeDayNight.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
            </intent-filter>

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>
        <activity
            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
            android:exported="false"
            android:windowSoftInputMode="stateUnchanged" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
            android:exported="false"
            android:windowSoftInputMode="stateUnchanged" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:process=":RunSoLibV2RayDaemon"
            android:theme="@style/AppTheme.NoActionBar.Translucent" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="v2rayng" />
                <data android:host="install-config" />
                <data android:host="install-sub" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
            android:exported="false" />
        <activity
            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
            android:exported="false" />

        <service
            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_VPN_SERVICE"
            android:process=":RunSoLibV2RayDaemon" >
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>

            <meta-data
                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
                android:value="true" />

            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="vpn" />
        </service>
        <service
            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:label="@string/app_name"
            android:process=":RunSoLibV2RayDaemon" >
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="proxy" />
        </service>
        <service
            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
            android:exported="false"
            android:process=":RunSoLibV2RayDaemon" />

        <receiver
            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
            android:exported="true"
            android:process=":RunSoLibV2RayDaemon" >
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/app_widget_provider" />

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
                <action android:name="com.mohamedrady.v2hoor.action.activity" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
            android:exported="true"
            android:label="BootReceiver" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.mohamedrady.v2hoor.service.QSTileService"
            android:exported="true"
            android:foregroundServiceType="specialUse"
            android:icon="@drawable/ic_stat_name"
            android:label="@string/app_tile_name"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:process=":RunSoLibV2RayDaemon" >
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>

            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="tile" />
        </service>
        <!-- =====================Tasker===================== -->
        <activity
            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher" >
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
            </intent-filter>
        </activity>

        <receiver
            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
            android:exported="true"
            android:process=":RunSoLibV2RayDaemon" >
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
            </intent-filter>
        </receiver>
        <!-- =====================Tasker===================== -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.mohamedrady.v2hoor.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.mohamedrady.v2hoor.cache"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/cache_paths" />
        </provider>

        <activity
            android:name="io.github.g00fy2.quickie.QRScannerActivity"
            android:screenOrientation="behind"
            android:theme="@style/QuickieScannerActivity" />
        <!--
        Service for holding metadata. Cannot be instantiated.
        Metadata will be merged from other manifests.
        -->
        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>
        <service
            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:clearTaskOnLaunch="true"
            android:screenOrientation="sensorLandscape"
            android:stateNotNeeded="true"
            android:theme="@style/zxing_CaptureTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
    </application>

</manifest>