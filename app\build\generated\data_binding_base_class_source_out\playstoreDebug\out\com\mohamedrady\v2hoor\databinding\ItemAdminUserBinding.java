// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdminUserBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button buttonDeleteUser;

  @NonNull
  public final Button buttonManageServers;

  @NonNull
  public final Button buttonToggleStatus;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final TextView textViewCreationDate;

  @NonNull
  public final TextView textViewLastLogin;

  @NonNull
  public final TextView textViewRole;

  @NonNull
  public final TextView textViewServerCount;

  @NonNull
  public final TextView textViewSubscriptionType;

  @NonNull
  public final TextView textViewUserEmail;

  @NonNull
  public final TextView textViewUserName;

  @NonNull
  public final TextView textViewUserStatus;

  private ItemAdminUserBinding(@NonNull CardView rootView, @NonNull Button buttonDeleteUser,
      @NonNull Button buttonManageServers, @NonNull Button buttonToggleStatus,
      @NonNull CardView cardView, @NonNull TextView textViewCreationDate,
      @NonNull TextView textViewLastLogin, @NonNull TextView textViewRole,
      @NonNull TextView textViewServerCount, @NonNull TextView textViewSubscriptionType,
      @NonNull TextView textViewUserEmail, @NonNull TextView textViewUserName,
      @NonNull TextView textViewUserStatus) {
    this.rootView = rootView;
    this.buttonDeleteUser = buttonDeleteUser;
    this.buttonManageServers = buttonManageServers;
    this.buttonToggleStatus = buttonToggleStatus;
    this.cardView = cardView;
    this.textViewCreationDate = textViewCreationDate;
    this.textViewLastLogin = textViewLastLogin;
    this.textViewRole = textViewRole;
    this.textViewServerCount = textViewServerCount;
    this.textViewSubscriptionType = textViewSubscriptionType;
    this.textViewUserEmail = textViewUserEmail;
    this.textViewUserName = textViewUserName;
    this.textViewUserStatus = textViewUserStatus;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdminUserBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdminUserBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_admin_user, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdminUserBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDeleteUser;
      Button buttonDeleteUser = ViewBindings.findChildViewById(rootView, id);
      if (buttonDeleteUser == null) {
        break missingId;
      }

      id = R.id.buttonManageServers;
      Button buttonManageServers = ViewBindings.findChildViewById(rootView, id);
      if (buttonManageServers == null) {
        break missingId;
      }

      id = R.id.buttonToggleStatus;
      Button buttonToggleStatus = ViewBindings.findChildViewById(rootView, id);
      if (buttonToggleStatus == null) {
        break missingId;
      }

      CardView cardView = (CardView) rootView;

      id = R.id.textViewCreationDate;
      TextView textViewCreationDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewCreationDate == null) {
        break missingId;
      }

      id = R.id.textViewLastLogin;
      TextView textViewLastLogin = ViewBindings.findChildViewById(rootView, id);
      if (textViewLastLogin == null) {
        break missingId;
      }

      id = R.id.textViewRole;
      TextView textViewRole = ViewBindings.findChildViewById(rootView, id);
      if (textViewRole == null) {
        break missingId;
      }

      id = R.id.textViewServerCount;
      TextView textViewServerCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerCount == null) {
        break missingId;
      }

      id = R.id.textViewSubscriptionType;
      TextView textViewSubscriptionType = ViewBindings.findChildViewById(rootView, id);
      if (textViewSubscriptionType == null) {
        break missingId;
      }

      id = R.id.textViewUserEmail;
      TextView textViewUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserEmail == null) {
        break missingId;
      }

      id = R.id.textViewUserName;
      TextView textViewUserName = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserName == null) {
        break missingId;
      }

      id = R.id.textViewUserStatus;
      TextView textViewUserStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserStatus == null) {
        break missingId;
      }

      return new ItemAdminUserBinding((CardView) rootView, buttonDeleteUser, buttonManageServers,
          buttonToggleStatus, cardView, textViewCreationDate, textViewLastLogin, textViewRole,
          textViewServerCount, textViewSubscriptionType, textViewUserEmail, textViewUserName,
          textViewUserStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
