<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="libv2ray.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f374f69b28e65eadf3f70af924282c94\transformed\libv2ray\assets"><file name="geoip.dat" path="C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f374f69b28e65eadf3f70af924282c94\transformed\libv2ray\assets\geoip.dat"/><file name="geosite.dat" path="C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f374f69b28e65eadf3f70af924282c94\transformed\libv2ray\assets\geosite.dat"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets"><file name="custom_routing_black" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\custom_routing_black"/><file name="custom_routing_global" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\custom_routing_global"/><file name="custom_routing_white" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\custom_routing_white"/><file name="custom_routing_white_iran" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\custom_routing_white_iran"/><file name="open_source_licenses.html" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\open_source_licenses.html"/><file name="proxy_packagename.txt" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\proxy_packagename.txt"/><file name="v2ray_config.json" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\assets\v2ray_config.json"/></source></dataSet><dataSet config="playstore" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstore\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\debug\assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstoreDebug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\build\intermediates\shader_assets\playstoreDebug\compilePlaystoreDebugShaders\out"/></dataSet></merger>