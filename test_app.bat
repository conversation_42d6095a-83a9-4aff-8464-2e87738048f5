@echo off
echo ========================================
echo V2HoorVPN App Testing Script
echo ========================================
echo.

set ADB="C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe"

echo 1. Checking if device is connected...
%ADB% devices
echo.

echo 2. Starting the app...
%ADB% shell am start -n com.mohamedrady.v2hoor.fdroid/com.mohamedrady.v2hoor.ui.MainActivity
timeout /t 3 /nobreak >nul
echo.

echo 3. Checking current activity...
%ADB% shell dumpsys window windows | findstr "mCurrentFocus"
echo.

echo 4. Taking screenshot...
%ADB% shell screencap -p /sdcard/screenshot.png
%ADB% pull /sdcard/screenshot.png screenshot.png
echo Screenshot saved as screenshot.png
echo.

echo 5. Checking for any crashes...
%ADB% logcat -d -s "AndroidRuntime:E" | findstr "FATAL"
echo.

echo 6. Checking app logs...
%ADB% logcat -d | findstr "LoginActivity\|MainActivity\|V2HoorVPN" | tail -10
echo.

echo 7. Testing password toggle (simulating tap on password field area)...
echo Tapping on password field...
%ADB% shell input tap 270 400
timeout /t 1 /nobreak >nul

echo Typing test password...
%ADB% shell input text "testpass123"
timeout /t 1 /nobreak >nul

echo Tapping on password toggle button (approximate position)...
%ADB% shell input tap 450 400
timeout /t 1 /nobreak >nul

echo Taking another screenshot to see password toggle...
%ADB% shell screencap -p /sdcard/screenshot2.png
%ADB% pull /sdcard/screenshot2.png screenshot2.png
echo Screenshot after password toggle saved as screenshot2.png
echo.

echo 8. App package info...
%ADB% shell pm list packages | findstr "v2hoor"
echo.

echo ========================================
echo Testing completed!
echo Check the screenshots to verify:
echo - screenshot.png: Initial login screen
echo - screenshot2.png: After password toggle test
echo ========================================
pause
