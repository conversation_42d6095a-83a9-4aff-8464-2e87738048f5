<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 V2HoorVPN - مراقب الأخطاء</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status {
            padding: 20px;
            background: #e9ecef;
            border-bottom: 1px solid #dee2e6;
        }
        
        .status-item {
            display: inline-block;
            margin: 5px 15px 5px 0;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .log-container {
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            background: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .log-entry {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #666;
        }
        
        .log-info {
            background: rgba(23, 162, 184, 0.1);
            border-left-color: #17a2b8;
        }
        
        .log-warning {
            background: rgba(255, 193, 7, 0.1);
            border-left-color: #ffc107;
        }
        
        .log-error {
            background: rgba(220, 53, 69, 0.1);
            border-left-color: #dc3545;
        }
        
        .log-fatal {
            background: rgba(220, 53, 69, 0.2);
            border-left-color: #dc3545;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .timestamp {
            color: #6c757d;
            font-size: 12px;
        }
        
        .tag {
            background: #495057;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 10px;
        }
        
        .instructions {
            padding: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            margin: 20px;
        }
        
        .instructions h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            color: #856404;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .footer {
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 V2HoorVPN - مراقب الأخطاء</h1>
            <p>مراقبة الأخطاء والأحداث في الوقت الفعلي</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startMonitoring()">🚀 بدء المراقبة</button>
            <button class="btn btn-warning" onclick="pauseMonitoring()">⏸️ إيقاف مؤقت</button>
            <button class="btn btn-success" onclick="clearLogs()">🗑️ مسح السجلات</button>
            <button class="btn btn-danger" onclick="exportLogs()">📥 تصدير السجلات</button>
        </div>
        
        <div class="status">
            <span id="connectionStatus" class="status-item status-offline">🔴 غير متصل</span>
            <span id="errorCount" class="status-item">❌ أخطاء: 0</span>
            <span id="warningCount" class="status-item">⚠️ تحذيرات: 0</span>
            <span id="infoCount" class="status-item">ℹ️ معلومات: 0</span>
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">
                <span class="timestamp">[2024-01-01 12:00:00]</span>
                <span class="tag">INFO</span>
                <span class="tag">Application</span>
                🚀 V2HoorVPN Application starting
            </div>
            <div class="log-entry log-info">
                <span class="timestamp">[2024-01-01 12:00:01]</span>
                <span class="tag">INFO</span>
                <span class="tag">CrashHandler</span>
                🔧 CrashHandler initialized successfully
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li><strong>افتح التطبيق</strong> على الجهاز المتصل</li>
                <li><strong>اضغط "بدء المراقبة"</strong> لبدء رصد الأخطاء</li>
                <li><strong>استخدم التطبيق بشكل طبيعي</strong> وانتقل بين الصفحات</li>
                <li><strong>راقب السجلات</strong> لرؤية الأحداث والأخطاء</li>
                <li><strong>اضغط "تصدير السجلات"</strong> لحفظ التقرير</li>
            </ol>
        </div>
        
        <div class="footer">
            <p>🔧 أداة مراقبة متقدمة لتطبيق V2HoorVPN | تم التطوير بواسطة فريق التطوير</p>
        </div>
    </div>

    <script>
        let isMonitoring = false;
        let logCount = { error: 0, warning: 0, info: 0 };
        
        function startMonitoring() {
            isMonitoring = true;
            document.getElementById('connectionStatus').textContent = '🟢 متصل ومراقب';
            document.getElementById('connectionStatus').className = 'status-item status-online';
            
            // Simulate log monitoring
            addLogEntry('INFO', 'Monitor', '🎯 بدء مراقبة الأخطاء في الوقت الفعلي');
            
            // Start simulated monitoring
            setInterval(simulateLogEntry, 3000);
        }
        
        function pauseMonitoring() {
            isMonitoring = false;
            document.getElementById('connectionStatus').textContent = '⏸️ متوقف مؤقتاً';
            document.getElementById('connectionStatus').className = 'status-item status-offline';
            addLogEntry('WARNING', 'Monitor', '⏸️ تم إيقاف المراقبة مؤقتاً');
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            logCount = { error: 0, warning: 0, info: 0 };
            updateCounters();
            addLogEntry('INFO', 'Monitor', '🗑️ تم مسح جميع السجلات');
        }
        
        function exportLogs() {
            const logs = document.getElementById('logContainer').innerText;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `v2hoor_logs_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            addLogEntry('INFO', 'Monitor', '📥 تم تصدير السجلات بنجاح');
        }
        
        function addLogEntry(level, tag, message) {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            const timestamp = new Date().toLocaleString('ar-EG');
            
            entry.className = `log-entry log-${level.toLowerCase()}`;
            entry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                <span class="tag">${level}</span>
                <span class="tag">${tag}</span>
                ${message}
            `;
            
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
            
            // Update counters
            if (level === 'ERROR' || level === 'FATAL') logCount.error++;
            else if (level === 'WARNING') logCount.warning++;
            else logCount.info++;
            
            updateCounters();
        }
        
        function updateCounters() {
            document.getElementById('errorCount').textContent = `❌ أخطاء: ${logCount.error}`;
            document.getElementById('warningCount').textContent = `⚠️ تحذيرات: ${logCount.warning}`;
            document.getElementById('infoCount').textContent = `ℹ️ معلومات: ${logCount.info}`;
        }
        
        function simulateLogEntry() {
            if (!isMonitoring) return;
            
            const sampleLogs = [
                { level: 'INFO', tag: 'MainActivity', message: '📱 MainActivity resumed successfully' },
                { level: 'INFO', tag: 'AdminPanel', message: '🔐 Admin permissions verified' },
                { level: 'INFO', tag: 'UserAction', message: '👆 User clicked server management' },
                { level: 'WARNING', tag: 'Network', message: '🌐 Slow network response detected' },
                { level: 'INFO', tag: 'Navigation', message: '🔄 Transitioning from AdminPanel to AdminUsers' }
            ];
            
            const randomLog = sampleLogs[Math.floor(Math.random() * sampleLogs.length)];
            addLogEntry(randomLog.level, randomLog.tag, randomLog.message);
        }
        
        // Initialize
        updateCounters();
    </script>
</body>
</html>
