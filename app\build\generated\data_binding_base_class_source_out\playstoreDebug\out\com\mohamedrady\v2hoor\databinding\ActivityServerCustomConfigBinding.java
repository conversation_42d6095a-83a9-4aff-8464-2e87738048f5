// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.blacksquircle.ui.editorkit.widget.TextProcessor;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityServerCustomConfigBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextProcessor editor;

  @NonNull
  public final EditText etRemarks;

  private ActivityServerCustomConfigBinding(@NonNull ScrollView rootView,
      @NonNull TextProcessor editor, @NonNull EditText etRemarks) {
    this.rootView = rootView;
    this.editor = editor;
    this.etRemarks = etRemarks;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityServerCustomConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityServerCustomConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_server_custom_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityServerCustomConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.editor;
      TextProcessor editor = ViewBindings.findChildViewById(rootView, id);
      if (editor == null) {
        break missingId;
      }

      id = R.id.et_remarks;
      EditText etRemarks = ViewBindings.findChildViewById(rootView, id);
      if (etRemarks == null) {
        break missingId;
      }

      return new ActivityServerCustomConfigBinding((ScrollView) rootView, editor, etRemarks);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
