package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminServersBinding
import com.mohamedrady.v2hoor.dto.AdminServerConfig
import com.mohamedrady.v2hoor.service.AdminPermissionService
import com.mohamedrady.v2hoor.ui.adapter.AdminServersAdapter
import com.mohamedrady.v2hoor.viewmodel.AdminServersViewModel
import kotlinx.coroutines.launch

/**
 * Activity for managing servers (Admin only)
 */
class AdminServersActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdminServersBinding
    private val viewModel: AdminServersViewModel by viewModels()
    private lateinit var serversAdapter: AdminServersAdapter
    private val adminService = AdminPermissionService.getInstance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminServers", "🚀 Starting AdminServersActivity onCreate")

        try {
            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminServers", "📱 Inflating layout")
            binding = ActivityAdminServersBinding.inflate(layoutInflater)
            setContentView(binding.root)

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminServers", "🎨 Setting up toolbar")
            setupToolbar()

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminServers", "🔐 Checking admin permissions")
            checkAdminPermissions()

            com.mohamedrady.v2hoor.util.CrashHandler.logInfo("AdminServers", "✅ AdminServersActivity onCreate completed successfully")
        } catch (e: Exception) {
            com.mohamedrady.v2hoor.util.CrashHandler.logError("AdminServers", "💥 Error in AdminServersActivity onCreate", e)
            Toast.makeText(this, "خطأ في تحميل صفحة إدارة السيرفرات", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun checkAdminPermissions() {
        lifecycleScope.launch {
            try {
                if (!adminService.isAdmin()) {
                    Toast.makeText(this@AdminServersActivity, "غير مصرح لك بالوصول لهذه الصفحة", Toast.LENGTH_SHORT).show()
                    finish()
                    return@launch
                }

                // Initialize UI after permission check
                setupRecyclerView()
                setupObservers()
                setupClickListeners()

                // Load servers
                viewModel.loadServers()
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Error checking admin permissions", e)
                Toast.makeText(this@AdminServersActivity, "خطأ في التحقق من الصلاحيات", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "إدارة السيرفرات"
        }
    }

    private fun setupRecyclerView() {
        serversAdapter = AdminServersAdapter(
            onServerClick = { server -> showServerDetails(server) },
            onToggleServerStatus = { server -> toggleServerStatus(server) },
            onDeleteServer = { server -> confirmDeleteServer(server) },
            onEditServer = { server -> editServer(server) },
            onViewUsers = { server -> viewServerUsers(server) }
        )

        binding.recyclerViewServers.apply {
            layoutManager = LinearLayoutManager(this@AdminServersActivity)
            adapter = serversAdapter
        }
    }

    private fun setupObservers() {
        viewModel.servers.observe(this) { servers ->
            serversAdapter.submitList(servers)
            binding.textViewServersCount.text = "إجمالي السيرفرات: ${servers.size}"
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) 
                android.view.View.VISIBLE else android.view.View.GONE
        }

        viewModel.error.observe(this) { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }

        viewModel.statistics.observe(this) { stats ->
            stats?.let { updateStatistics(it) }
        }
    }

    private fun setupClickListeners() {
        binding.fabAddServer.setOnClickListener {
            addNewServer()
        }

        binding.buttonRefresh.setOnClickListener {
            viewModel.loadServers()
        }

        binding.editTextSearch.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val query = s?.toString() ?: ""
                if (query.isBlank()) {
                    viewModel.loadServers()
                } else {
                    viewModel.searchServers(query)
                }
            }
        })
    }

    private fun updateStatistics(stats: Map<String, Any>) {
        binding.apply {
            textViewTotalServers.text = "${stats["totalServers"]}"
            textViewActiveServers.text = "${stats["activeServers"]}"
            textViewInactiveServers.text = "${stats["inactiveServers"]}"
            textViewTotalAssignments.text = "${stats["totalAssignments"]}"
        }
    }

    private fun addNewServer() {
        val intent = Intent(this, AddServerActivity::class.java)
        startActivity(intent)
    }

    private fun showFilterDialog() {
        // TODO: Implement filter dialog
        Toast.makeText(this, "تصفية السيرفرات - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun exportServers() {
        // TODO: Export servers data
        Toast.makeText(this, "تصدير السيرفرات - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun importServers() {
        // TODO: Import servers data
        Toast.makeText(this, "استيراد السيرفرات - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun showBulkOperationsDialog() {
        // TODO: Implement bulk operations
        Toast.makeText(this, "العمليات الجماعية - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun testAllServers() {
        // TODO: Test all servers
        Toast.makeText(this, "اختبار جميع السيرفرات - قريباً", Toast.LENGTH_SHORT).show()
    }

    private fun showServerDetails(server: AdminServerConfig) {
        val intent = Intent(this, ServerDetailsActivity::class.java).apply {
            putExtra("server_config", server)
        }
        startActivity(intent)
    }

    private fun toggleServerStatus(server: AdminServerConfig) {
        val newStatus = !server.isActive
        val message = if (newStatus) "تفعيل" else "إلغاء تفعيل"
        
        AlertDialog.Builder(this)
            .setTitle("تأكيد العملية")
            .setMessage("هل تريد $message السيرفر ${server.name}؟")
            .setPositiveButton("نعم") { _, _ ->
                val updatedServer = server.copy(isActive = newStatus)
                viewModel.updateServer(updatedServer)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun confirmDeleteServer(server: AdminServerConfig) {
        AlertDialog.Builder(this)
            .setTitle("تحذير")
            .setMessage("هل تريد حذف السيرفر ${server.name} نهائياً؟\n\nسيتم إزالته من جميع المستخدمين!")
            .setPositiveButton("حذف") { _, _ ->
                viewModel.deleteServer(server.id)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun editServer(server: AdminServerConfig) {
        val intent = Intent(this, EditServerActivity::class.java).apply {
            putExtra("server_config", server)
        }
        startActivity(intent)
    }

    private fun viewServerUsers(server: AdminServerConfig) {
        val intent = Intent(this, ServerUsersActivity::class.java).apply {
            putExtra("server_id", server.id)
            putExtra("server_name", server.name)
        }
        startActivity(intent)
    }



    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_admin_servers, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_add_server -> {
                addNewServer()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            R.id.action_export_servers -> {
                exportServers()
                true
            }
            R.id.action_import_servers -> {
                importServers()
                true
            }
            R.id.action_bulk_operations -> {
                showBulkOperationsDialog()
                true
            }
            R.id.action_test_all -> {
                testAllServers()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showStatisticsDialog() {
        viewModel.statistics.value?.let { stats ->
            val message = """
                إحصائيات السيرفرات:
                
                إجمالي السيرفرات: ${stats["totalServers"]}
                السيرفرات النشطة: ${stats["activeServers"]}
                السيرفرات غير النشطة: ${stats["inactiveServers"]}
                إجمالي التخصيصات: ${stats["totalAssignments"]}
            """.trimIndent()

            AlertDialog.Builder(this)
                .setTitle("إحصائيات السيرفرات")
                .setMessage(message)
                .setPositiveButton("موافق", null)
                .show()
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning to activity
        viewModel.loadServers()
    }
}
