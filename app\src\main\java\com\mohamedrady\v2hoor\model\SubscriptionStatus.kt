package com.mohamedrady.v2hoor.model

import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Subscription status enumeration
 */
enum class SubscriptionStatus(val value: String, val displayName: String, val colorRes: Int) {
    ACTIVE("active", "نشط", android.R.color.holo_green_dark),
    EXPIRING_SOON("expiring_soon", "ينتهي قريباً", android.R.color.holo_orange_dark),
    EXPIRED("expired", "منتهي الصلاحية", android.R.color.holo_red_dark),
    UNLIMITED("unlimited", "غير محدود", android.R.color.holo_blue_dark),
    UNKNOWN("unknown", "غير معروف", android.R.color.darker_gray);
    
    companion object {
        fun fromString(value: String?): SubscriptionStatus {
            return values().find { it.value == value } ?: UNKNOWN
        }
    }
}

/**
 * Subscription information model
 */
data class SubscriptionInfo(
    val subscriptionEnd: String = "",
    val subscriptionType: String = "basic",
    val isActive: Boolean = true,
    val dataLimitGb: Long = 0,
    val dataUsedGb: Long = 0,
    val maxDevices: Int = 1,
    val features: List<String> = emptyList(),
    val notes: String = ""
) {
    companion object {
        private const val DATE_FORMAT = "yyyy-MM-dd"
        private const val EXPIRY_WARNING_DAYS = 3
        
        fun createUnlimited(): SubscriptionInfo {
            return SubscriptionInfo(
                subscriptionEnd = "",
                subscriptionType = "unlimited",
                isActive = true,
                dataLimitGb = 0,
                maxDevices = 999,
                features = listOf("unlimited_data", "unlimited_devices", "premium_servers", "priority_support"),
                notes = "اشتراك غير محدود"
            )
        }
        
        fun createBasic(endDate: String): SubscriptionInfo {
            return SubscriptionInfo(
                subscriptionEnd = endDate,
                subscriptionType = "basic",
                isActive = true,
                dataLimitGb = 10,
                maxDevices = 1,
                features = listOf("basic_servers"),
                notes = "اشتراك أساسي"
            )
        }
        
        fun createPremium(endDate: String): SubscriptionInfo {
            return SubscriptionInfo(
                subscriptionEnd = endDate,
                subscriptionType = "premium",
                isActive = true,
                dataLimitGb = 100,
                maxDevices = 3,
                features = listOf("premium_servers", "high_speed", "multiple_devices"),
                notes = "اشتراك مميز"
            )
        }
    }
    
    /**
     * Get subscription end date as Date object
     */
    fun getSubscriptionEndDate(): Date? {
        if (subscriptionEnd.isBlank()) return null
        
        return try {
            val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
            dateFormat.parse(subscriptionEnd)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Check if subscription is active
     */
    fun isSubscriptionActive(): Boolean {
        if (!isActive) return false
        if (subscriptionEnd.isBlank()) return true // Unlimited subscription
        
        val endDate = getSubscriptionEndDate() ?: return false
        val currentDate = Date()
        return endDate.after(currentDate) || isSameDay(endDate, currentDate)
    }
    
    /**
     * Check if subscription is expiring soon
     */
    fun isExpiringSoon(): Boolean {
        if (subscriptionEnd.isBlank()) return false // Unlimited subscription
        
        val endDate = getSubscriptionEndDate() ?: return false
        val currentDate = Date()
        val diffInMillis = endDate.time - currentDate.time
        val diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis)
        
        return diffInDays in 0..EXPIRY_WARNING_DAYS
    }
    
    /**
     * Get days until expiration
     */
    fun getDaysUntilExpiry(): Long {
        if (subscriptionEnd.isBlank()) return Long.MAX_VALUE // Unlimited
        
        val endDate = getSubscriptionEndDate() ?: return 0
        val currentDate = Date()
        val diffInMillis = endDate.time - currentDate.time
        return TimeUnit.MILLISECONDS.toDays(diffInMillis)
    }
    
    /**
     * Get subscription status
     */
    fun getStatus(): SubscriptionStatus {
        return when {
            !isActive -> SubscriptionStatus.EXPIRED
            subscriptionEnd.isBlank() -> SubscriptionStatus.UNLIMITED
            !isSubscriptionActive() -> SubscriptionStatus.EXPIRED
            isExpiringSoon() -> SubscriptionStatus.EXPIRING_SOON
            else -> SubscriptionStatus.ACTIVE
        }
    }
    
    /**
     * Get formatted subscription end date
     */
    fun getFormattedEndDate(): String {
        if (subscriptionEnd.isBlank()) return "غير محدود"
        
        return try {
            val inputFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(subscriptionEnd)
            date?.let { outputFormat.format(it) } ?: subscriptionEnd
        } catch (e: Exception) {
            subscriptionEnd
        }
    }
    
    /**
     * Get status message
     */
    fun getStatusMessage(): String {
        return when (getStatus()) {
            SubscriptionStatus.ACTIVE -> "نشط حتى ${getFormattedEndDate()}"
            SubscriptionStatus.EXPIRING_SOON -> {
                val days = getDaysUntilExpiry()
                "ينتهي خلال $days ${if (days == 1L) "يوم" else "أيام"}"
            }
            SubscriptionStatus.EXPIRED -> "منتهي الصلاحية منذ ${getFormattedEndDate()}"
            SubscriptionStatus.UNLIMITED -> "اشتراك غير محدود"
            SubscriptionStatus.UNKNOWN -> "حالة الاشتراك غير معروفة"
        }
    }
    
    /**
     * Get data usage percentage
     */
    fun getDataUsagePercentage(): Float {
        return if (dataLimitGb > 0) {
            (dataUsedGb.toFloat() / dataLimitGb.toFloat()) * 100f
        } else {
            0f
        }
    }
    
    /**
     * Check if data limit is exceeded
     */
    fun isDataLimitExceeded(): Boolean {
        return dataLimitGb > 0 && dataUsedGb >= dataLimitGb
    }
    
    /**
     * Get remaining data in GB
     */
    fun getRemainingDataGb(): Long {
        return if (dataLimitGb > 0) {
            maxOf(0, dataLimitGb - dataUsedGb)
        } else {
            Long.MAX_VALUE
        }
    }
    
    /**
     * Check if can connect (subscription active and data available)
     */
    fun canConnect(): Boolean {
        return isSubscriptionActive() && !isDataLimitExceeded()
    }
    
    /**
     * Get connection block reason
     */
    fun getConnectionBlockReason(): String? {
        return when {
            !isSubscriptionActive() -> "انتهت صلاحية الاشتراك"
            isDataLimitExceeded() -> "تم تجاوز حد البيانات المسموح"
            else -> null
        }
    }
    
    /**
     * Validate subscription end date format
     */
    fun isValidDateFormat(): Boolean {
        if (subscriptionEnd.isBlank()) return true // Unlimited is valid
        
        return try {
            val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
            dateFormat.isLenient = false
            dateFormat.parse(subscriptionEnd)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get subscription type display name
     */
    fun getSubscriptionTypeDisplayName(): String {
        return when (subscriptionType.lowercase()) {
            "basic" -> "أساسي"
            "premium" -> "مميز"
            "unlimited" -> "غير محدود"
            "free" -> "مجاني"
            "trial" -> "تجريبي"
            else -> subscriptionType
        }
    }
    
    /**
     * Get features display list
     */
    fun getFeaturesDisplayList(): List<String> {
        return features.map { feature ->
            when (feature) {
                "unlimited_data" -> "بيانات غير محدودة"
                "unlimited_devices" -> "أجهزة غير محدودة"
                "premium_servers" -> "سيرفرات مميزة"
                "priority_support" -> "دعم فني مميز"
                "high_speed" -> "سرعة عالية"
                "multiple_devices" -> "أجهزة متعددة"
                "basic_servers" -> "سيرفرات أساسية"
                else -> feature
            }
        }
    }
    
    /**
     * Check if two dates are the same day
     */
    private fun isSameDay(date1: Date, date2: Date): Boolean {
        val cal1 = Calendar.getInstance().apply { time = date1 }
        val cal2 = Calendar.getInstance().apply { time = date2 }
        
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }
    
    /**
     * Create updated subscription with new end date
     */
    fun withNewEndDate(newEndDate: String): SubscriptionInfo {
        return copy(
            subscriptionEnd = newEndDate,
            isActive = true
        )
    }
    
    /**
     * Create updated subscription with data usage
     */
    fun withDataUsage(usedGb: Long): SubscriptionInfo {
        return copy(dataUsedGb = usedGb)
    }
    
    /**
     * Create deactivated subscription
     */
    fun deactivated(): SubscriptionInfo {
        return copy(isActive = false)
    }
}
