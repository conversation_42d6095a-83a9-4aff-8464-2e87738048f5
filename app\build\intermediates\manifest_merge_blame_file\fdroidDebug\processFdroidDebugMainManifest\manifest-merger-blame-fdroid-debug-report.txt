1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohamedrady.v2hoor.fdroid"
4    android:versionCode="5065700"
5    android:versionName="1.10.7" >
6
7    <uses-sdk
7-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
8        android:minSdkVersion="23"
8-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
9        android:targetSdkVersion="35" />
10
11    <supports-screens
11-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
12        android:anyDensity="true"
12-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
13        android:largeScreens="true"
13-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
14        android:normalScreens="true"
14-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
15        android:smallScreens="true"
15-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
16        android:xlargeScreens="true" />
16-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
17
18    <uses-feature
18-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
19        android:name="android.hardware.camera"
19-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
20        android:required="false" />
20-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
21    <uses-feature
21-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
22        android:name="android.hardware.camera.autofocus"
22-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
23        android:required="false" />
23-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
24    <uses-feature
24-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
25        android:name="android.software.leanback"
25-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
26        android:required="false" />
26-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
27    <uses-feature
27-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
28        android:name="android.hardware.touchscreen"
28-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
29        android:required="false" />
29-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
30
31    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.INTERNET" />
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
37    <uses-permission android:name="android.permission.CAMERA" />
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
38    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
39    <uses-permission
39-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
40        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
40-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
41        android:minSdkVersion="34" />
41-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
42    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:5-68
46-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:22-65
47    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
47-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
47-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
48    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
49    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
49-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
49-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
50    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52
53    <permission
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
54        android:name="com.mohamedrady.v2hoor.fdroid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.mohamedrady.v2hoor.fdroid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
58
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
60        android:name="android.hardware.camera.front"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
63        android:name="android.hardware.camera.flash"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
65    <uses-feature
65-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
66        android:name="android.hardware.screen.landscape"
66-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
67        android:required="false" />
67-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
68    <uses-feature
68-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
69        android:name="android.hardware.wifi"
69-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
70        android:required="false" />
70-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
71
72    <application
72-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:5-343:19
73        android:name="com.mohamedrady.v2hoor.AngApplication"
73-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-39
74        android:allowBackup="true"
74-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-35
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
76        android:banner="@mipmap/ic_banner"
76-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
77        android:debuggable="true"
78        android:extractNativeLibs="true"
79        android:icon="@mipmap/ic_launcher"
79-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-43
80        android:label="@string/app_name"
80-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-41
81        android:networkSecurityConfig="@xml/network_security_config"
81-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-69
82        android:supportsRtl="true"
82-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-35
83        android:theme="@style/AppThemeDayNight"
83-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-48
84        android:usesCleartextTraffic="true" >
84-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:58:9-44
85        <activity
85-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:9-79:20
86            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-44
87            android:exported="true"
87-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-36
88            android:launchMode="singleTask"
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-44
89            android:theme="@style/AppThemeDayNight.NoActionBar" >
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-64
90            <intent-filter>
90-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:13-71:29
91                <action android:name="android.intent.action.MAIN" />
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:17-69
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-77
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-74
94                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
94-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:17-86
94-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:27-83
95            </intent-filter>
96            <intent-filter>
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:13-74:29
97                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:17-99
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:25-96
98            </intent-filter>
99
100            <meta-data
100-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:13-78:53
101                android:name="android.app.shortcuts"
101-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-53
102                android:resource="@xml/shortcuts" />
102-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:78:17-50
103        </activity>
104        <activity
104-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:9-83:60
105            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
105-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-46
106            android:exported="false"
106-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-37
107            android:windowSoftInputMode="stateUnchanged" />
107-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:13-57
108        <activity
108-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:9-87:60
109            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
109-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-58
110            android:exported="false"
110-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-37
111            android:windowSoftInputMode="stateUnchanged" />
111-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:13-57
112        <activity
112-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:9-90:40
113            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
113-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-48
114            android:exported="false" />
114-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:13-37
115        <activity
115-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:9-93:40
116            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
116-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-51
117            android:exported="false" />
117-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:13-37
118        <activity
118-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:9-96:40
119            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
119-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-47
120            android:exported="false" />
120-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:13-37
121        <activity
121-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:9-99:40
122            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
122-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-46
123            android:exported="false" />
123-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:13-37
124        <activity
124-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:9-102:40
125            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
125-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-54
126            android:exported="false" />
126-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:13-37
127        <activity
127-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:9-105:40
128            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
128-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-51
129            android:exported="false" />
129-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:13-37
130        <activity
130-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:9-108:40
131            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
131-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-50
132            android:exported="false" />
132-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:13-37
133        <activity
133-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:9-111:40
134            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
134-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-49
135            android:exported="false" />
135-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:13-37
136        <activity
136-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:9-114:40
137            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
137-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-52
138            android:exported="false" />
138-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:114:13-37
139        <activity
139-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:9-118:40
140            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
140-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-47
141            android:exported="false" />
141-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:13-37
142        <activity
142-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:9-121:40
143            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
143-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-49
144            android:exported="false" />
144-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:13-37
145        <activity
145-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:9-127:71
146            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
146-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-48
147            android:excludeFromRecents="true"
147-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-46
148            android:exported="false"
148-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-37
149            android:process=":RunSoLibV2RayDaemon"
149-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-51
150            android:theme="@style/AppTheme.NoActionBar.Translucent" />
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:127:13-68
151        <activity
151-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:9-147:20
152            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-49
153            android:exported="true" >
153-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-36
154            <intent-filter>
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:13-136:29
155                <action android:name="android.intent.action.SEND" />
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-69
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:25-66
156
157                <category android:name="android.intent.category.DEFAULT" />
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
158
159                <data android:mimeType="text/plain" />
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:23-52
160            </intent-filter>
161            <intent-filter>
161-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:13-146:29
162                <action android:name="android.intent.action.VIEW" />
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
163
164                <category android:name="android.intent.category.BROWSABLE" />
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
165                <category android:name="android.intent.category.DEFAULT" />
165-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
165-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
166
167                <data android:scheme="v2rayng" />
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
168                <data android:host="install-config" />
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
169                <data android:host="install-sub" />
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
170            </intent-filter>
171        </activity>
172        <activity
172-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:9-150:40
173            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
173-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-51
174            android:exported="false" />
174-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:13-37
175        <activity
175-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:9-153:40
176            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
176-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-45
177            android:exported="false" />
177-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:13-37
178        <activity
178-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:9-157:67
179            android:name="com.mohamedrady.v2hoor.ui.LoginActivity"
179-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-45
180            android:exported="false"
180-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-37
181            android:theme="@style/AppThemeDayNight.NoActionBar" />
181-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:157:13-64
182        <activity
182-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-161:67
183            android:name="com.mohamedrady.v2hoor.ui.AdminPanelActivity"
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-50
184            android:exported="false"
184-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-37
185            android:theme="@style/AppThemeDayNight.NoActionBar" />
185-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-64
186        <activity
186-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:9-165:67
187            android:name="com.mohamedrady.v2hoor.ui.LogViewerActivity"
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-49
188            android:exported="false"
188-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-37
189            android:theme="@style/AppThemeDayNight.NoActionBar" />
189-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-64
190        <activity
190-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:9-169:67
191            android:name="com.mohamedrady.v2hoor.ui.RealTimeLogActivity"
191-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:13-51
192            android:exported="false"
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-37
193            android:theme="@style/AppThemeDayNight.NoActionBar" />
193-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-64
194        <activity
194-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:9-173:67
195            android:name="com.mohamedrady.v2hoor.ui.AdminUsersActivity"
195-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:13-50
196            android:exported="false"
196-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-37
197            android:theme="@style/AppThemeDayNight.NoActionBar" />
197-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:13-64
198        <activity
198-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:9-177:67
199            android:name="com.mohamedrady.v2hoor.ui.AddUserActivity"
199-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:175:13-47
200            android:exported="false"
200-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:176:13-37
201            android:theme="@style/AppThemeDayNight.NoActionBar" />
201-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:13-64
202        <activity
202-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:9-181:67
203            android:name="com.mohamedrady.v2hoor.ui.UserDetailsActivity"
203-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-51
204            android:exported="false"
204-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-37
205            android:theme="@style/AppThemeDayNight.NoActionBar" />
205-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-64
206        <activity
206-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:9-185:67
207            android:name="com.mohamedrady.v2hoor.ui.UserServersActivity"
207-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:183:13-51
208            android:exported="false"
208-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:184:13-37
209            android:theme="@style/AppThemeDayNight.NoActionBar" />
209-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:185:13-64
210        <activity
210-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:186:9-189:67
211            android:name="com.mohamedrady.v2hoor.ui.AdminServersActivity"
211-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:187:13-52
212            android:exported="false"
212-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:13-37
213            android:theme="@style/AppThemeDayNight.NoActionBar" />
213-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-64
214        <activity
214-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:9-193:67
215            android:name="com.mohamedrady.v2hoor.ui.ServerDetailsActivity"
215-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-53
216            android:exported="false"
216-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:192:13-37
217            android:theme="@style/AppThemeDayNight.NoActionBar" />
217-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:13-64
218        <activity
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:9-197:67
219            android:name="com.mohamedrady.v2hoor.ui.EditServerActivity"
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-50
220            android:exported="false"
220-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-37
221            android:theme="@style/AppThemeDayNight.NoActionBar" />
221-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-64
222        <activity
222-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:9-201:67
223            android:name="com.mohamedrady.v2hoor.ui.ServerUsersActivity"
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:13-51
224            android:exported="false"
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-37
225            android:theme="@style/AppThemeDayNight.NoActionBar" />
225-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:13-64
226        <activity
226-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:9-205:67
227            android:name="com.mohamedrady.v2hoor.ui.ServerManagementActivity"
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:13-56
228            android:exported="false"
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:204:13-37
229            android:theme="@style/AppThemeDayNight.NoActionBar" />
229-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:205:13-64
230        <activity
230-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-209:67
231            android:name="com.mohamedrady.v2hoor.ui.AddServerActivity"
231-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-49
232            android:exported="false"
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-37
233            android:theme="@style/AppThemeDayNight.NoActionBar" />
233-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-64
234        <activity
234-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:9-213:67
235            android:name="com.mohamedrady.v2hoor.ui.ServerLogsActivity"
235-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:13-50
236            android:exported="false"
236-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:212:13-37
237            android:theme="@style/AppThemeDayNight.NoActionBar" />
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:213:13-64
238        <activity
238-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:214:9-217:67
239            android:name="com.mohamedrady.v2hoor.ui.UserManagementActivity"
239-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:13-54
240            android:exported="false"
240-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-37
241            android:theme="@style/AppThemeDayNight.NoActionBar" />
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-64
242        <activity
242-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:9-221:67
243            android:name="com.mohamedrady.v2hoor.ui.PromoteUserActivity"
243-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-51
244            android:exported="false"
244-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-37
245            android:theme="@style/AppThemeDayNight.NoActionBar" />
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-64
246        <activity
246-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:222:9-225:67
247            android:name="com.mohamedrady.v2hoor.ui.SystemSettingsActivity"
247-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:223:13-54
248            android:exported="false"
248-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-37
249            android:theme="@style/AppThemeDayNight.NoActionBar" />
249-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:13-64
250
251        <service
251-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:227:9-244:19
252            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
252-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:228:13-52
253            android:enabled="true"
253-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:229:13-35
254            android:exported="false"
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:230:13-37
255            android:foregroundServiceType="specialUse"
255-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:231:13-55
256            android:label="@string/app_name"
256-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:13-45
257            android:permission="android.permission.BIND_VPN_SERVICE"
257-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:13-69
258            android:process=":RunSoLibV2RayDaemon" >
258-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-51
259            <intent-filter>
259-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:13-237:29
260                <action android:name="android.net.VpnService" />
260-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:17-65
260-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:25-62
261            </intent-filter>
262
263            <meta-data
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:13-240:40
264                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
264-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:17-73
265                android:value="true" />
265-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:240:17-37
266
267            <property
267-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
268                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
268-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
269                android:value="vpn" />
269-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
270        </service>
271        <service
271-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:9-255:19
272            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
272-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:13-58
273            android:exported="false"
273-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:248:13-37
274            android:foregroundServiceType="specialUse"
274-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:249:13-55
275            android:label="@string/app_name"
275-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:250:13-45
276            android:process=":RunSoLibV2RayDaemon" >
276-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:251:13-51
277            <property
277-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
278                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
278-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
279                android:value="proxy" />
279-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
280        </service>
281        <service
281-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:257:9-260:54
282            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
282-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:258:13-53
283            android:exported="false"
283-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:259:13-37
284            android:process=":RunSoLibV2RayDaemon" />
284-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:260:13-51
285
286        <receiver
286-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:262:9-274:20
287            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
287-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:263:13-52
288            android:exported="true"
288-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:264:13-36
289            android:process=":RunSoLibV2RayDaemon" >
289-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-51
290            <meta-data
290-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:13-268:63
291                android:name="android.appwidget.provider"
291-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:17-58
292                android:resource="@xml/app_widget_provider" />
292-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:17-60
293
294            <intent-filter>
294-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:269:13-273:29
295                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
295-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:17-84
295-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:25-81
296                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
296-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:17-85
296-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:25-82
297                <action android:name="com.mohamedrady.v2hoor.action.activity" />
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:17-81
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:25-78
298            </intent-filter>
299        </receiver>
300        <receiver
300-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:275:9-282:20
301            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
301-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:13-50
302            android:exported="true"
302-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:277:13-36
303            android:label="BootReceiver" >
303-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:278:13-41
304            <intent-filter>
304-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:279:13-281:29
305                <action android:name="android.intent.action.BOOT_COMPLETED" />
305-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:17-79
305-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:25-76
306            </intent-filter>
307        </receiver>
308
309        <service
309-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:284:9-299:19
310            android:name="com.mohamedrady.v2hoor.service.QSTileService"
310-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:285:13-50
311            android:exported="true"
311-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:286:13-36
312            android:foregroundServiceType="specialUse"
312-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:287:13-55
313            android:icon="@drawable/ic_stat_name"
313-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:288:13-50
314            android:label="@string/app_tile_name"
314-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:289:13-50
315            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
315-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:290:13-77
316            android:process=":RunSoLibV2RayDaemon" >
316-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:291:13-51
317            <intent-filter>
317-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:293:13-295:29
318                <action android:name="android.service.quicksettings.action.QS_TILE" />
318-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:294:17-87
318-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:294:25-84
319            </intent-filter>
320
321            <property
321-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:13-243:39
322                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
322-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:17-76
323                android:value="tile" />
323-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-36
324        </service>
325        <!-- =====================Tasker===================== -->
326        <activity
326-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:301:9-308:20
327            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
327-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:302:13-46
328            android:exported="true"
328-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:303:13-36
329            android:icon="@mipmap/ic_launcher" >
329-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:304:13-47
330            <intent-filter>
330-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:305:13-307:29
331                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
331-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:17-95
331-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:25-92
332            </intent-filter>
333        </activity>
334
335        <receiver
335-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:310:9-318:20
336            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
336-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:311:13-52
337            android:exported="true"
337-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:312:13-36
338            android:process=":RunSoLibV2RayDaemon" >
338-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:313:13-51
339            <intent-filter>
339-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:315:13-317:29
340                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
340-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:316:17-95
340-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:316:25-92
341            </intent-filter>
342        </receiver>
343        <!-- =====================Tasker===================== -->
344        <provider
345            android:name="androidx.startup.InitializationProvider"
345-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:321:13-67
346            android:authorities="com.mohamedrady.v2hoor.fdroid.androidx-startup"
346-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:322:13-68
347            android:exported="false" >
347-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:323:13-37
348            <meta-data
348-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
349                android:name="androidx.emoji2.text.EmojiCompatInitializer"
349-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
350                android:value="androidx.startup" />
350-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
351            <meta-data
351-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
352                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
352-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
353                android:value="androidx.startup" />
353-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
354            <meta-data
354-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
355                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
355-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
356                android:value="androidx.startup" />
356-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
357        </provider>
358        <provider
359            android:name="androidx.core.content.FileProvider"
359-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:334:13-62
360            android:authorities="com.mohamedrady.v2hoor.fdroid.cache"
360-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:335:13-57
361            android:exported="false"
361-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:336:13-37
362            android:grantUriPermissions="true" >
362-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:337:13-47
363            <meta-data
363-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:338:13-340:55
364                android:name="android.support.FILE_PROVIDER_PATHS"
364-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:339:17-67
365                android:resource="@xml/cache_paths" />
365-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:340:17-52
366        </provider>
367
368        <activity
368-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
369            android:name="io.github.g00fy2.quickie.QRScannerActivity"
369-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
370            android:screenOrientation="behind"
370-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
371            android:theme="@style/QuickieScannerActivity" />
371-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
372        <!--
373        Service for holding metadata. Cannot be instantiated.
374        Metadata will be merged from other manifests.
375        -->
376        <service
376-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
377            android:name="androidx.camera.core.impl.MetadataHolderService"
377-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
378            android:enabled="false"
378-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
379            android:exported="false" >
379-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
380            <meta-data
380-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
381                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
381-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
382                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
382-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
383        </service>
384        <service
384-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
385            android:name="com.google.firebase.components.ComponentDiscoveryService"
385-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
386            android:directBootAware="true"
386-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
387            android:exported="false" >
387-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
388            <meta-data
388-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
389                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
389-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
390                android:value="com.google.firebase.components.ComponentRegistrar" />
390-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
391            <meta-data
391-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
392                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
392-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
393                android:value="com.google.firebase.components.ComponentRegistrar" />
393-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
394            <meta-data
394-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:11:13-13:85
395                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
395-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:12:17-129
396                android:value="com.google.firebase.components.ComponentRegistrar" />
396-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:13:17-82
397            <meta-data
397-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
398                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
398-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
399                android:value="com.google.firebase.components.ComponentRegistrar" />
399-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
400            <meta-data
400-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
401                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
401-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
402                android:value="com.google.firebase.components.ComponentRegistrar" />
402-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
403            <meta-data
403-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:12:13-14:85
404                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
404-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:13:17-129
405                android:value="com.google.firebase.components.ComponentRegistrar" />
405-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:14:17-82
406            <meta-data
406-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
407                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
407-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
408                android:value="com.google.firebase.components.ComponentRegistrar" />
408-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
409            <meta-data
409-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
410                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
410-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
411                android:value="com.google.firebase.components.ComponentRegistrar" />
411-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
412            <meta-data
412-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
413                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
413-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
414                android:value="com.google.firebase.components.ComponentRegistrar" />
414-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
415            <meta-data
415-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
416                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
416-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
417                android:value="com.google.firebase.components.ComponentRegistrar" />
417-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
418            <meta-data
418-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
419                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
419-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
420                android:value="com.google.firebase.components.ComponentRegistrar" />
420-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
421            <meta-data
421-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
422                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
422-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
423                android:value="com.google.firebase.components.ComponentRegistrar" />
423-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
424            <meta-data
424-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
425                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
425-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
426                android:value="com.google.firebase.components.ComponentRegistrar" />
426-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
427            <meta-data
427-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
428                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
428-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
429                android:value="com.google.firebase.components.ComponentRegistrar" />
429-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
430        </service>
431
432        <activity
432-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
433            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
433-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
434            android:excludeFromRecents="true"
434-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
435            android:exported="true"
435-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
436            android:launchMode="singleTask"
436-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
437            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
437-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
438            <intent-filter>
438-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
439                <action android:name="android.intent.action.VIEW" />
439-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
439-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
440
441                <category android:name="android.intent.category.DEFAULT" />
441-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
441-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
442                <category android:name="android.intent.category.BROWSABLE" />
442-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
442-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
443
444                <data
444-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
445                    android:host="firebase.auth"
445-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
446                    android:path="/"
447                    android:scheme="genericidp" />
447-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
448            </intent-filter>
449        </activity>
450        <activity
450-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
451            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
451-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
452            android:excludeFromRecents="true"
452-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
453            android:exported="true"
453-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
454            android:launchMode="singleTask"
454-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
455            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
455-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
456            <intent-filter>
456-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
457                <action android:name="android.intent.action.VIEW" />
457-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
457-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
458
459                <category android:name="android.intent.category.DEFAULT" />
459-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
459-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
460                <category android:name="android.intent.category.BROWSABLE" />
460-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
460-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
461
462                <data
462-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
463                    android:host="firebase.auth"
463-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
464                    android:path="/"
465                    android:scheme="recaptcha" />
465-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
466            </intent-filter>
467        </activity>
468
469        <service
469-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
470            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
470-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
471            android:enabled="true"
471-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
472            android:exported="false" >
472-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
473            <meta-data
473-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
474                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
474-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
475                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
475-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
476        </service>
477
478        <activity
478-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
479            android:name="androidx.credentials.playservices.HiddenActivity"
479-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
480            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
480-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
481            android:enabled="true"
481-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
482            android:exported="false"
482-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
483            android:fitsSystemWindows="true"
483-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
484            android:theme="@style/Theme.Hidden" >
484-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
485        </activity>
486        <activity
486-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
487            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
487-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
488            android:excludeFromRecents="true"
488-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
489            android:exported="false"
489-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
490            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
490-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
491        <!--
492            Service handling Google Sign-In user revocation. For apps that do not integrate with
493            Google Sign-In, this service will never be started.
494        -->
495        <service
495-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
496            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
496-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
497            android:exported="true"
497-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
498            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
498-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
499            android:visibleToInstantApps="true" />
499-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
500
501        <receiver
501-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
502            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
502-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
503            android:enabled="true"
503-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
504            android:exported="false" >
504-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
505        </receiver>
506
507        <service
507-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
508            android:name="com.google.android.gms.measurement.AppMeasurementService"
508-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
509            android:enabled="true"
509-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
510            android:exported="false" />
510-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
511        <service
511-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
512            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
512-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
513            android:enabled="true"
513-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
514            android:exported="false"
514-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
515            android:permission="android.permission.BIND_JOB_SERVICE" />
515-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
516
517        <provider
517-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
518            android:name="com.google.firebase.provider.FirebaseInitProvider"
518-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
519            android:authorities="com.mohamedrady.v2hoor.fdroid.firebaseinitprovider"
519-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
520            android:directBootAware="true"
520-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
521            android:exported="false"
521-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
522            android:initOrder="100" />
522-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
523
524        <service
524-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
525            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
525-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
526            android:exported="false" />
526-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
527        <service
527-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
528            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
528-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
529            android:directBootAware="false"
529-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
530            android:enabled="@bool/enable_system_alarm_service_default"
530-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
531            android:exported="false" />
531-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
532        <service
532-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
533            android:name="androidx.work.impl.background.systemjob.SystemJobService"
533-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
534            android:directBootAware="false"
534-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
535            android:enabled="@bool/enable_system_job_service_default"
535-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
536            android:exported="true"
536-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
537            android:permission="android.permission.BIND_JOB_SERVICE" />
537-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
538        <service
538-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
539            android:name="androidx.work.impl.foreground.SystemForegroundService"
539-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
540            android:directBootAware="false"
540-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
541            android:enabled="@bool/enable_system_foreground_service_default"
541-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
542            android:exported="false" />
542-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
543
544        <receiver
544-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
545            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
545-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
546            android:directBootAware="false"
546-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
547            android:enabled="true"
547-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
548            android:exported="false" />
548-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
549        <receiver
549-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
550            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
550-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
551            android:directBootAware="false"
551-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
552            android:enabled="false"
552-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
553            android:exported="false" >
553-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
554            <intent-filter>
554-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
555                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
555-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
555-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
556                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
556-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
556-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
557            </intent-filter>
558        </receiver>
559        <receiver
559-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
560            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
560-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
561            android:directBootAware="false"
561-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
562            android:enabled="false"
562-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
563            android:exported="false" >
563-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
564            <intent-filter>
564-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
565                <action android:name="android.intent.action.BATTERY_OKAY" />
565-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
565-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
566                <action android:name="android.intent.action.BATTERY_LOW" />
566-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
566-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
567            </intent-filter>
568        </receiver>
569        <receiver
569-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
570            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
570-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
571            android:directBootAware="false"
571-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
572            android:enabled="false"
572-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
573            android:exported="false" >
573-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
574            <intent-filter>
574-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
575                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
575-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
575-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
576                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
576-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
576-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
577            </intent-filter>
578        </receiver>
579        <receiver
579-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
580            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
580-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
581            android:directBootAware="false"
581-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
582            android:enabled="false"
582-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
583            android:exported="false" >
583-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
584            <intent-filter>
584-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
585                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
585-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
585-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
586            </intent-filter>
587        </receiver>
588        <receiver
588-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
589            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
589-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
590            android:directBootAware="false"
590-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
591            android:enabled="false"
591-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
592            android:exported="false" >
592-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
593            <intent-filter>
593-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
594                <action android:name="android.intent.action.BOOT_COMPLETED" />
594-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:17-79
594-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:25-76
595                <action android:name="android.intent.action.TIME_SET" />
595-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
595-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
596                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
596-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
596-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
597            </intent-filter>
598        </receiver>
599        <receiver
599-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
600            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
600-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
601            android:directBootAware="false"
601-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
602            android:enabled="@bool/enable_system_alarm_service_default"
602-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
603            android:exported="false" >
603-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
604            <intent-filter>
604-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
605                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
605-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
605-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
606            </intent-filter>
607        </receiver>
608        <receiver
608-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
609            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
609-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
610            android:directBootAware="false"
610-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
611            android:enabled="true"
611-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
612            android:exported="true"
612-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
613            android:permission="android.permission.DUMP" >
613-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
614            <intent-filter>
614-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
615                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
615-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
615-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
616            </intent-filter>
617        </receiver>
618
619        <uses-library
619-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
620            android:name="androidx.window.extensions"
620-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
621            android:required="false" />
621-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
622        <uses-library
622-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
623            android:name="androidx.window.sidecar"
623-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
624            android:required="false" />
624-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
625
626        <activity
626-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
627            android:name="com.google.android.gms.common.api.GoogleApiActivity"
627-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
628            android:exported="false"
628-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
629            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
629-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
630
631        <service
631-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
632            android:name="androidx.room.MultiInstanceInvalidationService"
632-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
633            android:directBootAware="true"
633-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
634            android:exported="false" />
634-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
635
636        <uses-library
636-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
637            android:name="android.ext.adservices"
637-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
638            android:required="false" />
638-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
639
640        <receiver
640-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
641            android:name="androidx.profileinstaller.ProfileInstallReceiver"
641-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
642            android:directBootAware="false"
642-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
643            android:enabled="true"
643-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
644            android:exported="true"
644-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
645            android:permission="android.permission.DUMP" >
645-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
646            <intent-filter>
646-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
647                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
647-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
647-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
648            </intent-filter>
649            <intent-filter>
649-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
650                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
650-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
650-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
651            </intent-filter>
652            <intent-filter>
652-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
653                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
653-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
653-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
654            </intent-filter>
655            <intent-filter>
655-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
656                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
656-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
656-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
657            </intent-filter>
658        </receiver>
659
660        <meta-data
660-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
661            android:name="com.google.android.gms.version"
661-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
662            android:value="@integer/google_play_services_version" />
662-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
663
664        <activity
664-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
665            android:name="com.journeyapps.barcodescanner.CaptureActivity"
665-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
666            android:clearTaskOnLaunch="true"
666-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
667            android:screenOrientation="sensorLandscape"
667-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
668            android:stateNotNeeded="true"
668-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
669            android:theme="@style/zxing_CaptureTheme"
669-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
670            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- The activities will be merged into the manifest of the hosting app. -->
670-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
671        <activity
671-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
672            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
672-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
673            android:exported="false"
673-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
674            android:stateNotNeeded="true"
674-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
675            android:theme="@style/Theme.PlayCore.Transparent" />
675-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
676    </application>
677
678</manifest>
