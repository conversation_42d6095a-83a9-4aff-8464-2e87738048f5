1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohamedrady.v2hoor.fdroid"
4    android:versionCode="5065700"
5    android:versionName="1.10.7" >
6
7    <uses-sdk
7-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
8        android:minSdkVersion="23"
8-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
9        android:targetSdkVersion="35" />
10
11    <supports-screens
11-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
12        android:anyDensity="true"
12-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
13        android:largeScreens="true"
13-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
14        android:normalScreens="true"
14-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
15        android:smallScreens="true"
15-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
16        android:xlargeScreens="true" />
16-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
17
18    <uses-feature
18-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
19        android:name="android.hardware.camera"
19-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
20        android:required="false" />
20-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
21    <uses-feature
21-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
22        android:name="android.hardware.camera.autofocus"
22-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
23        android:required="false" />
23-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
24    <uses-feature
24-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
25        android:name="android.software.leanback"
25-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
26        android:required="false" />
26-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
27    <uses-feature
27-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
28        android:name="android.hardware.touchscreen"
28-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
29        android:required="false" />
29-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
30
31    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.INTERNET" />
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
37    <uses-permission android:name="android.permission.CAMERA" />
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
38    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
39    <uses-permission
39-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
40        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
40-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
41        android:minSdkVersion="34" />
41-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
42    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:5-68
46-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:47:22-65
47    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
47-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
47-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
48    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
49    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
49-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
49-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
50    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52
53    <permission
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
54        android:name="com.mohamedrady.v2hoor.fdroid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.mohamedrady.v2hoor.fdroid.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
58
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
60        android:name="android.hardware.camera.front"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
63        android:name="android.hardware.camera.flash"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
65    <uses-feature
65-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
66        android:name="android.hardware.screen.landscape"
66-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
67        android:required="false" />
67-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
68    <uses-feature
68-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
69        android:name="android.hardware.wifi"
69-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
70        android:required="false" />
70-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
71
72    <application
72-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:5-339:19
73        android:name="com.mohamedrady.v2hoor.AngApplication"
73-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-39
74        android:allowBackup="true"
74-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-35
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
76        android:banner="@mipmap/ic_banner"
76-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
77        android:debuggable="true"
78        android:extractNativeLibs="true"
79        android:icon="@mipmap/ic_launcher"
79-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-43
80        android:label="@string/app_name"
80-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-41
81        android:networkSecurityConfig="@xml/network_security_config"
81-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-69
82        android:supportsRtl="true"
82-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-35
83        android:theme="@style/AppThemeDayNight"
83-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-48
84        android:usesCleartextTraffic="true" >
84-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:58:9-44
85        <activity
85-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:9-79:20
86            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-44
87            android:exported="true"
87-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-36
88            android:launchMode="singleTask"
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-44
89            android:theme="@style/AppThemeDayNight.NoActionBar" >
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-64
90            <intent-filter>
90-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:13-71:29
91                <action android:name="android.intent.action.MAIN" />
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:17-69
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:67:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-77
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-74
94                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
94-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:17-86
94-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:70:27-83
95            </intent-filter>
96            <intent-filter>
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:13-74:29
97                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:17-99
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:73:25-96
98            </intent-filter>
99
100            <meta-data
100-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:13-78:53
101                android:name="android.app.shortcuts"
101-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-53
102                android:resource="@xml/shortcuts" />
102-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:78:17-50
103        </activity>
104        <activity
104-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:9-83:60
105            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
105-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-46
106            android:exported="false"
106-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-37
107            android:windowSoftInputMode="stateUnchanged" />
107-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:13-57
108        <activity
108-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:9-87:60
109            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
109-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-58
110            android:exported="false"
110-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-37
111            android:windowSoftInputMode="stateUnchanged" />
111-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:13-57
112        <activity
112-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:9-90:40
113            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
113-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-48
114            android:exported="false" />
114-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:13-37
115        <activity
115-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:9-93:40
116            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
116-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-51
117            android:exported="false" />
117-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:13-37
118        <activity
118-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:9-96:40
119            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
119-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-47
120            android:exported="false" />
120-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:13-37
121        <activity
121-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:9-99:40
122            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
122-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-46
123            android:exported="false" />
123-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:13-37
124        <activity
124-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:9-102:40
125            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
125-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-54
126            android:exported="false" />
126-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:13-37
127        <activity
127-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:9-105:40
128            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
128-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-51
129            android:exported="false" />
129-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:13-37
130        <activity
130-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:9-108:40
131            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
131-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-50
132            android:exported="false" />
132-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:13-37
133        <activity
133-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:9-111:40
134            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
134-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-49
135            android:exported="false" />
135-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:13-37
136        <activity
136-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:9-114:40
137            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
137-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-52
138            android:exported="false" />
138-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:114:13-37
139        <activity
139-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:9-118:40
140            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
140-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-47
141            android:exported="false" />
141-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:13-37
142        <activity
142-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:9-121:40
143            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
143-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-49
144            android:exported="false" />
144-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:13-37
145        <activity
145-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:9-127:71
146            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
146-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-48
147            android:excludeFromRecents="true"
147-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-46
148            android:exported="false"
148-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-37
149            android:process=":RunSoLibV2RayDaemon"
149-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-51
150            android:theme="@style/AppTheme.NoActionBar.Translucent" />
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:127:13-68
151        <activity
151-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:9-147:20
152            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-49
153            android:exported="true" >
153-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-36
154            <intent-filter>
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:13-136:29
155                <action android:name="android.intent.action.SEND" />
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-69
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:25-66
156
157                <category android:name="android.intent.category.DEFAULT" />
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
158
159                <data android:mimeType="text/plain" />
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:23-52
160            </intent-filter>
161            <intent-filter>
161-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:13-146:29
162                <action android:name="android.intent.action.VIEW" />
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
163
164                <category android:name="android.intent.category.BROWSABLE" />
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
165                <category android:name="android.intent.category.DEFAULT" />
165-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
165-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
166
167                <data android:scheme="v2rayng" />
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
168                <data android:host="install-config" />
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
169                <data android:host="install-sub" />
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
170            </intent-filter>
171        </activity>
172        <activity
172-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:9-150:40
173            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
173-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-51
174            android:exported="false" />
174-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:13-37
175        <activity
175-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:9-153:40
176            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
176-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-45
177            android:exported="false" />
177-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:13-37
178        <activity
178-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:9-157:67
179            android:name="com.mohamedrady.v2hoor.ui.LoginActivity"
179-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-45
180            android:exported="false"
180-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-37
181            android:theme="@style/AppThemeDayNight.NoActionBar" />
181-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:157:13-64
182        <activity
182-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-161:67
183            android:name="com.mohamedrady.v2hoor.ui.AdminPanelActivity"
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-50
184            android:exported="false"
184-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-37
185            android:theme="@style/AppThemeDayNight.NoActionBar" />
185-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-64
186        <activity
186-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:9-165:67
187            android:name="com.mohamedrady.v2hoor.ui.LogViewerActivity"
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-49
188            android:exported="false"
188-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-37
189            android:theme="@style/AppThemeDayNight.NoActionBar" />
189-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-64
190        <activity
190-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:9-169:67
191            android:name="com.mohamedrady.v2hoor.ui.AdminUsersActivity"
191-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:13-50
192            android:exported="false"
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-37
193            android:theme="@style/AppThemeDayNight.NoActionBar" />
193-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-64
194        <activity
194-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:9-173:67
195            android:name="com.mohamedrady.v2hoor.ui.AddUserActivity"
195-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:13-47
196            android:exported="false"
196-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-37
197            android:theme="@style/AppThemeDayNight.NoActionBar" />
197-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:13-64
198        <activity
198-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:9-177:67
199            android:name="com.mohamedrady.v2hoor.ui.UserDetailsActivity"
199-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:175:13-51
200            android:exported="false"
200-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:176:13-37
201            android:theme="@style/AppThemeDayNight.NoActionBar" />
201-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:13-64
202        <activity
202-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:9-181:67
203            android:name="com.mohamedrady.v2hoor.ui.UserServersActivity"
203-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-51
204            android:exported="false"
204-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-37
205            android:theme="@style/AppThemeDayNight.NoActionBar" />
205-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-64
206        <activity
206-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:9-185:67
207            android:name="com.mohamedrady.v2hoor.ui.AdminServersActivity"
207-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:183:13-52
208            android:exported="false"
208-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:184:13-37
209            android:theme="@style/AppThemeDayNight.NoActionBar" />
209-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:185:13-64
210        <activity
210-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:186:9-189:67
211            android:name="com.mohamedrady.v2hoor.ui.ServerDetailsActivity"
211-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:187:13-53
212            android:exported="false"
212-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:13-37
213            android:theme="@style/AppThemeDayNight.NoActionBar" />
213-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-64
214        <activity
214-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:9-193:67
215            android:name="com.mohamedrady.v2hoor.ui.EditServerActivity"
215-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-50
216            android:exported="false"
216-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:192:13-37
217            android:theme="@style/AppThemeDayNight.NoActionBar" />
217-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:13-64
218        <activity
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:9-197:67
219            android:name="com.mohamedrady.v2hoor.ui.ServerUsersActivity"
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-51
220            android:exported="false"
220-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-37
221            android:theme="@style/AppThemeDayNight.NoActionBar" />
221-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-64
222        <activity
222-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:9-201:67
223            android:name="com.mohamedrady.v2hoor.ui.ServerManagementActivity"
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:13-56
224            android:exported="false"
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-37
225            android:theme="@style/AppThemeDayNight.NoActionBar" />
225-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:13-64
226        <activity
226-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:9-205:67
227            android:name="com.mohamedrady.v2hoor.ui.AddServerActivity"
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:13-49
228            android:exported="false"
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:204:13-37
229            android:theme="@style/AppThemeDayNight.NoActionBar" />
229-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:205:13-64
230        <activity
230-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-209:67
231            android:name="com.mohamedrady.v2hoor.ui.ServerLogsActivity"
231-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-50
232            android:exported="false"
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-37
233            android:theme="@style/AppThemeDayNight.NoActionBar" />
233-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-64
234        <activity
234-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:9-213:67
235            android:name="com.mohamedrady.v2hoor.ui.UserManagementActivity"
235-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:13-54
236            android:exported="false"
236-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:212:13-37
237            android:theme="@style/AppThemeDayNight.NoActionBar" />
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:213:13-64
238        <activity
238-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:214:9-217:67
239            android:name="com.mohamedrady.v2hoor.ui.PromoteUserActivity"
239-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:13-51
240            android:exported="false"
240-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-37
241            android:theme="@style/AppThemeDayNight.NoActionBar" />
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-64
242        <activity
242-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:9-221:67
243            android:name="com.mohamedrady.v2hoor.ui.SystemSettingsActivity"
243-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-54
244            android:exported="false"
244-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-37
245            android:theme="@style/AppThemeDayNight.NoActionBar" />
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-64
246
247        <service
247-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:223:9-240:19
248            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
248-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-52
249            android:enabled="true"
249-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:13-35
250            android:exported="false"
250-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:226:13-37
251            android:foregroundServiceType="specialUse"
251-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:227:13-55
252            android:label="@string/app_name"
252-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:228:13-45
253            android:permission="android.permission.BIND_VPN_SERVICE"
253-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:229:13-69
254            android:process=":RunSoLibV2RayDaemon" >
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:230:13-51
255            <intent-filter>
255-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:231:13-233:29
256                <action android:name="android.net.VpnService" />
256-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:17-65
256-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:25-62
257            </intent-filter>
258
259            <meta-data
259-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-236:40
260                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
260-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:17-73
261                android:value="true" />
261-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:17-37
262
263            <property
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:13-239:39
264                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
264-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:17-76
265                android:value="vpn" />
265-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:17-36
266        </service>
267        <service
267-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:9-251:19
268            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
268-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:13-58
269            android:exported="false"
269-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:244:13-37
270            android:foregroundServiceType="specialUse"
270-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:245:13-55
271            android:label="@string/app_name"
271-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:13-45
272            android:process=":RunSoLibV2RayDaemon" >
272-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:13-51
273            <property
273-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:13-239:39
274                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
274-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:17-76
275                android:value="proxy" />
275-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:17-36
276        </service>
277        <service
277-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:253:9-256:54
278            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
278-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:254:13-53
279            android:exported="false"
279-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:255:13-37
280            android:process=":RunSoLibV2RayDaemon" />
280-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:256:13-51
281
282        <receiver
282-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:258:9-270:20
283            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
283-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:259:13-52
284            android:exported="true"
284-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:260:13-36
285            android:process=":RunSoLibV2RayDaemon" >
285-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:261:13-51
286            <meta-data
286-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:262:13-264:63
287                android:name="android.appwidget.provider"
287-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:263:17-58
288                android:resource="@xml/app_widget_provider" />
288-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:264:17-60
289
290            <intent-filter>
290-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-269:29
291                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
291-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:17-84
291-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:25-81
292                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
292-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:17-85
292-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:25-82
293                <action android:name="com.mohamedrady.v2hoor.action.activity" />
293-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:17-81
293-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:25-78
294            </intent-filter>
295        </receiver>
296        <receiver
296-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:9-278:20
297            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:272:13-50
298            android:exported="true"
298-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:273:13-36
299            android:label="BootReceiver" >
299-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:274:13-41
300            <intent-filter>
300-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:275:13-277:29
301                <action android:name="android.intent.action.BOOT_COMPLETED" />
301-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:17-79
301-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:25-76
302            </intent-filter>
303        </receiver>
304
305        <service
305-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:280:9-295:19
306            android:name="com.mohamedrady.v2hoor.service.QSTileService"
306-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:281:13-50
307            android:exported="true"
307-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:282:13-36
308            android:foregroundServiceType="specialUse"
308-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:283:13-55
309            android:icon="@drawable/ic_stat_name"
309-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:284:13-50
310            android:label="@string/app_tile_name"
310-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:285:13-50
311            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
311-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:286:13-77
312            android:process=":RunSoLibV2RayDaemon" >
312-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:287:13-51
313            <intent-filter>
313-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:289:13-291:29
314                <action android:name="android.service.quicksettings.action.QS_TILE" />
314-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:290:17-87
314-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:290:25-84
315            </intent-filter>
316
317            <property
317-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:13-239:39
318                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
318-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:17-76
319                android:value="tile" />
319-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:17-36
320        </service>
321        <!-- =====================Tasker===================== -->
322        <activity
322-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:297:9-304:20
323            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
323-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:298:13-46
324            android:exported="true"
324-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:299:13-36
325            android:icon="@mipmap/ic_launcher" >
325-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:300:13-47
326            <intent-filter>
326-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:301:13-303:29
327                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
327-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:302:17-95
327-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:302:25-92
328            </intent-filter>
329        </activity>
330
331        <receiver
331-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:306:9-314:20
332            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
332-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:307:13-52
333            android:exported="true"
333-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:308:13-36
334            android:process=":RunSoLibV2RayDaemon" >
334-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:309:13-51
335            <intent-filter>
335-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:311:13-313:29
336                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
336-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:312:17-95
336-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:312:25-92
337            </intent-filter>
338        </receiver>
339        <!-- =====================Tasker===================== -->
340        <provider
341            android:name="androidx.startup.InitializationProvider"
341-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:317:13-67
342            android:authorities="com.mohamedrady.v2hoor.fdroid.androidx-startup"
342-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:318:13-68
343            android:exported="false" >
343-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:319:13-37
344            <meta-data
344-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
345                android:name="androidx.emoji2.text.EmojiCompatInitializer"
345-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
346                android:value="androidx.startup" />
346-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
347            <meta-data
347-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
348                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
348-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
349                android:value="androidx.startup" />
349-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
350            <meta-data
350-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
351                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
351-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
352                android:value="androidx.startup" />
352-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
353        </provider>
354        <provider
355            android:name="androidx.core.content.FileProvider"
355-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:330:13-62
356            android:authorities="com.mohamedrady.v2hoor.fdroid.cache"
356-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:331:13-57
357            android:exported="false"
357-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:332:13-37
358            android:grantUriPermissions="true" >
358-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:333:13-47
359            <meta-data
359-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:334:13-336:55
360                android:name="android.support.FILE_PROVIDER_PATHS"
360-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:335:17-67
361                android:resource="@xml/cache_paths" />
361-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:336:17-52
362        </provider>
363
364        <activity
364-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
365            android:name="io.github.g00fy2.quickie.QRScannerActivity"
365-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
366            android:screenOrientation="behind"
366-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
367            android:theme="@style/QuickieScannerActivity" />
367-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
368        <!--
369        Service for holding metadata. Cannot be instantiated.
370        Metadata will be merged from other manifests.
371        -->
372        <service
372-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
373            android:name="androidx.camera.core.impl.MetadataHolderService"
373-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
374            android:enabled="false"
374-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
375            android:exported="false" >
375-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
376            <meta-data
376-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
377                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
377-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
378                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
378-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
379        </service>
380        <service
380-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
381            android:name="com.google.firebase.components.ComponentDiscoveryService"
381-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
382            android:directBootAware="true"
382-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
383            android:exported="false" >
383-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
384            <meta-data
384-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
385                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
385-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
386                android:value="com.google.firebase.components.ComponentRegistrar" />
386-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
387            <meta-data
387-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
388                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
388-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
389                android:value="com.google.firebase.components.ComponentRegistrar" />
389-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
390            <meta-data
390-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:11:13-13:85
391                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
391-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:12:17-129
392                android:value="com.google.firebase.components.ComponentRegistrar" />
392-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:13:17-82
393            <meta-data
393-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
394                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
394-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
395                android:value="com.google.firebase.components.ComponentRegistrar" />
395-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
396            <meta-data
396-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
397                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
397-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
398                android:value="com.google.firebase.components.ComponentRegistrar" />
398-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
399            <meta-data
399-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:12:13-14:85
400                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
400-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:13:17-129
401                android:value="com.google.firebase.components.ComponentRegistrar" />
401-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:14:17-82
402            <meta-data
402-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
403                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
403-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
404                android:value="com.google.firebase.components.ComponentRegistrar" />
404-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
405            <meta-data
405-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
406                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
406-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
407                android:value="com.google.firebase.components.ComponentRegistrar" />
407-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
408            <meta-data
408-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
409                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
409-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
410                android:value="com.google.firebase.components.ComponentRegistrar" />
410-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
411            <meta-data
411-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
412                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
412-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
413                android:value="com.google.firebase.components.ComponentRegistrar" />
413-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
414            <meta-data
414-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
415                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
415-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
416                android:value="com.google.firebase.components.ComponentRegistrar" />
416-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
417            <meta-data
417-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
418                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
418-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
419                android:value="com.google.firebase.components.ComponentRegistrar" />
419-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
420            <meta-data
420-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
421                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
421-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
422                android:value="com.google.firebase.components.ComponentRegistrar" />
422-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
423            <meta-data
423-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
424                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
424-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
425                android:value="com.google.firebase.components.ComponentRegistrar" />
425-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
426        </service>
427
428        <activity
428-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
429            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
429-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
430            android:excludeFromRecents="true"
430-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
431            android:exported="true"
431-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
432            android:launchMode="singleTask"
432-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
433            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
433-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
434            <intent-filter>
434-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
435                <action android:name="android.intent.action.VIEW" />
435-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
435-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
436
437                <category android:name="android.intent.category.DEFAULT" />
437-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
437-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
438                <category android:name="android.intent.category.BROWSABLE" />
438-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
438-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
439
440                <data
440-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
441                    android:host="firebase.auth"
441-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
442                    android:path="/"
443                    android:scheme="genericidp" />
443-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
444            </intent-filter>
445        </activity>
446        <activity
446-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
447            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
447-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
448            android:excludeFromRecents="true"
448-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
449            android:exported="true"
449-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
450            android:launchMode="singleTask"
450-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
451            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
451-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
452            <intent-filter>
452-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
453                <action android:name="android.intent.action.VIEW" />
453-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:17-69
453-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:138:25-66
454
455                <category android:name="android.intent.category.DEFAULT" />
455-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-76
455-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:27-73
456                <category android:name="android.intent.category.BROWSABLE" />
456-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:17-78
456-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:140:27-75
457
458                <data
458-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:135:17-55
459                    android:host="firebase.auth"
459-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:144:23-52
460                    android:path="/"
461                    android:scheme="recaptcha" />
461-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-47
462            </intent-filter>
463        </activity>
464
465        <service
465-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
466            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
466-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
467            android:enabled="true"
467-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
468            android:exported="false" >
468-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
469            <meta-data
469-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
470                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
470-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
471                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
471-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
472        </service>
473
474        <activity
474-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
475            android:name="androidx.credentials.playservices.HiddenActivity"
475-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
476            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
476-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
477            android:enabled="true"
477-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
478            android:exported="false"
478-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
479            android:fitsSystemWindows="true"
479-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
480            android:theme="@style/Theme.Hidden" >
480-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
481        </activity>
482        <activity
482-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
483            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
483-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
484            android:excludeFromRecents="true"
484-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
485            android:exported="false"
485-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
486            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
486-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
487        <!--
488            Service handling Google Sign-In user revocation. For apps that do not integrate with
489            Google Sign-In, this service will never be started.
490        -->
491        <service
491-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
492            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
492-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
493            android:exported="true"
493-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
494            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
494-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
495            android:visibleToInstantApps="true" />
495-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
496
497        <receiver
497-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
498            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
498-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
499            android:enabled="true"
499-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
500            android:exported="false" >
500-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
501        </receiver>
502
503        <service
503-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
504            android:name="com.google.android.gms.measurement.AppMeasurementService"
504-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
505            android:enabled="true"
505-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
506            android:exported="false" />
506-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
507        <service
507-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
508            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
508-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
509            android:enabled="true"
509-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
510            android:exported="false"
510-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
511            android:permission="android.permission.BIND_JOB_SERVICE" />
511-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
512
513        <provider
513-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
514            android:name="com.google.firebase.provider.FirebaseInitProvider"
514-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
515            android:authorities="com.mohamedrady.v2hoor.fdroid.firebaseinitprovider"
515-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
516            android:directBootAware="true"
516-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
517            android:exported="false"
517-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
518            android:initOrder="100" />
518-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
519
520        <service
520-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
521            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
521-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
522            android:exported="false" />
522-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
523        <service
523-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
524            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
524-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
525            android:directBootAware="false"
525-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
526            android:enabled="@bool/enable_system_alarm_service_default"
526-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
527            android:exported="false" />
527-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
528        <service
528-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
529            android:name="androidx.work.impl.background.systemjob.SystemJobService"
529-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
530            android:directBootAware="false"
530-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
531            android:enabled="@bool/enable_system_job_service_default"
531-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
532            android:exported="true"
532-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
533            android:permission="android.permission.BIND_JOB_SERVICE" />
533-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
534        <service
534-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
535            android:name="androidx.work.impl.foreground.SystemForegroundService"
535-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
536            android:directBootAware="false"
536-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
537            android:enabled="@bool/enable_system_foreground_service_default"
537-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
538            android:exported="false" />
538-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
539
540        <receiver
540-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
541            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
541-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
542            android:directBootAware="false"
542-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
543            android:enabled="true"
543-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
544            android:exported="false" />
544-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
545        <receiver
545-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
546            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
546-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
547            android:directBootAware="false"
547-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
548            android:enabled="false"
548-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
549            android:exported="false" >
549-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
550            <intent-filter>
550-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
551                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
551-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
551-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
552                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
552-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
552-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
553            </intent-filter>
554        </receiver>
555        <receiver
555-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
556            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
556-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
557            android:directBootAware="false"
557-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
558            android:enabled="false"
558-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
559            android:exported="false" >
559-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
560            <intent-filter>
560-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
561                <action android:name="android.intent.action.BATTERY_OKAY" />
561-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
561-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
562                <action android:name="android.intent.action.BATTERY_LOW" />
562-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
562-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
563            </intent-filter>
564        </receiver>
565        <receiver
565-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
566            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
566-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
567            android:directBootAware="false"
567-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
568            android:enabled="false"
568-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
569            android:exported="false" >
569-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
570            <intent-filter>
570-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
571                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
571-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
571-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
572                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
572-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
572-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
573            </intent-filter>
574        </receiver>
575        <receiver
575-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
576            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
576-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
577            android:directBootAware="false"
577-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
578            android:enabled="false"
578-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
579            android:exported="false" >
579-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
580            <intent-filter>
580-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
581                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
581-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
581-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
582            </intent-filter>
583        </receiver>
584        <receiver
584-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
585            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
585-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
586            android:directBootAware="false"
586-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
587            android:enabled="false"
587-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
588            android:exported="false" >
588-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
589            <intent-filter>
589-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
590                <action android:name="android.intent.action.BOOT_COMPLETED" />
590-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:17-79
590-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:276:25-76
591                <action android:name="android.intent.action.TIME_SET" />
591-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
591-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
592                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
592-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
592-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
593            </intent-filter>
594        </receiver>
595        <receiver
595-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
596            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
596-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
597            android:directBootAware="false"
597-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
598            android:enabled="@bool/enable_system_alarm_service_default"
598-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
599            android:exported="false" >
599-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
600            <intent-filter>
600-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
601                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
601-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
601-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
602            </intent-filter>
603        </receiver>
604        <receiver
604-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
605            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
605-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
606            android:directBootAware="false"
606-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
607            android:enabled="true"
607-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
608            android:exported="true"
608-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
609            android:permission="android.permission.DUMP" >
609-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
610            <intent-filter>
610-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
611                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
611-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
611-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
612            </intent-filter>
613        </receiver>
614
615        <uses-library
615-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
616            android:name="androidx.window.extensions"
616-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
617            android:required="false" />
617-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
618        <uses-library
618-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
619            android:name="androidx.window.sidecar"
619-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
620            android:required="false" />
620-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
621
622        <activity
622-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
623            android:name="com.google.android.gms.common.api.GoogleApiActivity"
623-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
624            android:exported="false"
624-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
625            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
625-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
626
627        <service
627-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
628            android:name="androidx.room.MultiInstanceInvalidationService"
628-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
629            android:directBootAware="true"
629-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
630            android:exported="false" />
630-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
631
632        <uses-library
632-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
633            android:name="android.ext.adservices"
633-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
634            android:required="false" />
634-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
635
636        <receiver
636-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
637            android:name="androidx.profileinstaller.ProfileInstallReceiver"
637-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
638            android:directBootAware="false"
638-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
639            android:enabled="true"
639-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
640            android:exported="true"
640-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
641            android:permission="android.permission.DUMP" >
641-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
642            <intent-filter>
642-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
643                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
643-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
643-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
644            </intent-filter>
645            <intent-filter>
645-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
646                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
646-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
646-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
647            </intent-filter>
648            <intent-filter>
648-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
649                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
649-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
649-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
650            </intent-filter>
651            <intent-filter>
651-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
652                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
652-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
652-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
653            </intent-filter>
654        </receiver>
655
656        <meta-data
656-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
657            android:name="com.google.android.gms.version"
657-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
658            android:value="@integer/google_play_services_version" />
658-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
659
660        <activity
660-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
661            android:name="com.journeyapps.barcodescanner.CaptureActivity"
661-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
662            android:clearTaskOnLaunch="true"
662-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
663            android:screenOrientation="sensorLandscape"
663-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
664            android:stateNotNeeded="true"
664-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
665            android:theme="@style/zxing_CaptureTheme"
665-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
666            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- The activities will be merged into the manifest of the hosting app. -->
666-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
667        <activity
667-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
668            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
668-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
669            android:exported="false"
669-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
670            android:stateNotNeeded="true"
670-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
671            android:theme="@style/Theme.PlayCore.Transparent" />
671-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
672    </application>
673
674</manifest>
