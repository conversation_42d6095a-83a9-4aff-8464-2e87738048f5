package com.mohamedrady.v2hoor.service

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.dto.UserProfile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Service for managing users and their profiles
 */
class UserManagementService private constructor() {

    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()

    companion object {
        @Volatile
        private var INSTANCE: UserManagementService? = null

        // Collections
        private const val USERS_COLLECTION = "users"
        private const val USER_SERVERS_COLLECTION = "user_servers"
        private const val SERVERS_COLLECTION = "servers"

        fun getInstance(): UserManagementService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UserManagementService().also { INSTANCE = it }
            }
        }
    }

    /**
     * Create or update user profile
     */
    suspend fun createOrUpdateUserProfile(userProfile: UserProfile): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val user = auth.currentUser ?: return@withContext Result.failure(Exception("User not authenticated"))
                
                val userData = mapOf(
                    "uid" to user.uid,
                    "email" to (userProfile.email ?: user.email),
                    "displayName" to userProfile.displayName,
                    "photoUrl" to userProfile.photoUrl,
                    "phoneNumber" to userProfile.phoneNumber,
                    "country" to userProfile.country,
                    "city" to userProfile.city,
                    "isActive" to userProfile.isActive,
                    "createdAt" to (userProfile.createdAt ?: System.currentTimeMillis()),
                    "updatedAt" to System.currentTimeMillis(),
                    "lastLogin" to System.currentTimeMillis(),
                    "serverCount" to userProfile.serverCount,
                    "subscriptionType" to userProfile.subscriptionType,
                    "subscriptionExpiry" to userProfile.subscriptionExpiry
                )

                firestore.collection(USERS_COLLECTION)
                    .document(user.uid)
                    .set(userData)
                    .await()

                Log.i(AppConfig.TAG, "User profile created/updated successfully")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to create/update user profile", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get current user profile
     */
    suspend fun getCurrentUserProfile(): Result<UserProfile?> {
        return withContext(Dispatchers.IO) {
            try {
                val user = auth.currentUser ?: return@withContext Result.failure(Exception("User not authenticated"))
                
                val doc = firestore.collection(USERS_COLLECTION)
                    .document(user.uid)
                    .get()
                    .await()

                if (doc.exists()) {
                    val userProfile = UserProfile(
                        uid = doc.getString("uid") ?: user.uid,
                        email = doc.getString("email") ?: user.email,
                        displayName = doc.getString("displayName") ?: user.displayName,
                        photoUrl = doc.getString("photoUrl") ?: user.photoUrl?.toString(),
                        phoneNumber = doc.getString("phoneNumber"),
                        country = doc.getString("country"),
                        city = doc.getString("city"),
                        isActive = doc.getBoolean("isActive") ?: true,
                        createdAt = doc.getLong("createdAt") ?: System.currentTimeMillis(),
                        updatedAt = doc.getLong("updatedAt"),
                        lastLogin = doc.getLong("lastLogin"),
                        serverCount = doc.getLong("serverCount")?.toInt() ?: 0,
                        subscriptionType = doc.getString("subscriptionType") ?: "free",
                        subscriptionExpiry = doc.getLong("subscriptionExpiry")
                    )
                    Result.success(userProfile)
                } else {
                    // Create default profile if doesn't exist
                    val defaultProfile = UserProfile(
                        uid = user.uid,
                        email = user.email,
                        displayName = user.displayName ?: "مستخدم جديد",
                        photoUrl = user.photoUrl?.toString(),
                        isActive = true,
                        createdAt = System.currentTimeMillis(),
                        subscriptionType = "free"
                    )
                    createOrUpdateUserProfile(defaultProfile)
                    Result.success(defaultProfile)
                }
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get user profile", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get all users (admin only)
     */
    suspend fun getAllUsers(): Result<List<UserProfile>> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val querySnapshot = firestore.collection(USERS_COLLECTION)
                    .orderBy("createdAt", Query.Direction.DESCENDING)
                    .get()
                    .await()

                val users = querySnapshot.documents.mapNotNull { doc ->
                    try {
                        UserProfile(
                            uid = doc.getString("uid") ?: doc.id,
                            email = doc.getString("email"),
                            displayName = doc.getString("displayName"),
                            photoUrl = doc.getString("photoUrl"),
                            phoneNumber = doc.getString("phoneNumber"),
                            country = doc.getString("country"),
                            city = doc.getString("city"),
                            isActive = doc.getBoolean("isActive") ?: true,
                            createdAt = doc.getLong("createdAt") ?: 0L,
                            updatedAt = doc.getLong("updatedAt"),
                            lastLogin = doc.getLong("lastLogin"),
                            serverCount = doc.getLong("serverCount")?.toInt() ?: 0,
                            subscriptionType = doc.getString("subscriptionType") ?: "free",
                            subscriptionExpiry = doc.getLong("subscriptionExpiry")
                        )
                    } catch (e: Exception) {
                        Log.e(AppConfig.TAG, "Error parsing user document", e)
                        null
                    }
                }

                Result.success(users)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get all users", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Update user status (admin only)
     */
    suspend fun updateUserStatus(userId: String, isActive: Boolean): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                firestore.collection(USERS_COLLECTION)
                    .document(userId)
                    .update(
                        mapOf(
                            "isActive" to isActive,
                            "updatedAt" to System.currentTimeMillis()
                        )
                    )
                    .await()

                Log.i(AppConfig.TAG, "User status updated: $userId -> $isActive")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to update user status", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Delete user (super admin only)
     */
    suspend fun deleteUser(userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isSuperAdmin()) {
                    return@withContext Result.failure(Exception("Only super admin can delete users"))
                }

                // Delete user document
                firestore.collection(USERS_COLLECTION)
                    .document(userId)
                    .delete()
                    .await()

                // Delete user role
                firestore.collection("user_roles")
                    .document(userId)
                    .delete()
                    .await()

                // Delete user servers
                firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("userId", userId)
                    .get()
                    .await()
                    .documents
                    .forEach { it.reference.delete() }

                Log.i(AppConfig.TAG, "User deleted: $userId")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to delete user", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Search users by email or name
     */
    suspend fun searchUsers(query: String): Result<List<UserProfile>> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val allUsers = getAllUsers().getOrNull() ?: emptyList()
                val filteredUsers = allUsers.filter { user ->
                    user.email?.contains(query, ignoreCase = true) == true ||
                    user.displayName?.contains(query, ignoreCase = true) == true
                }

                Result.success(filteredUsers)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to search users", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get user statistics
     */
    suspend fun getUserStatistics(): Result<Map<String, Any>> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val usersSnapshot = firestore.collection(USERS_COLLECTION).get().await()
                val totalUsers = usersSnapshot.size()
                val activeUsers = usersSnapshot.documents.count { 
                    it.getBoolean("isActive") ?: true 
                }
                val premiumUsers = usersSnapshot.documents.count { 
                    it.getString("subscriptionType") != "free" 
                }

                val stats = mapOf(
                    "totalUsers" to totalUsers,
                    "activeUsers" to activeUsers,
                    "inactiveUsers" to (totalUsers - activeUsers),
                    "premiumUsers" to premiumUsers,
                    "freeUsers" to (totalUsers - premiumUsers)
                )

                Result.success(stats)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get user statistics", e)
                Result.failure(e)
            }
        }
    }
}
