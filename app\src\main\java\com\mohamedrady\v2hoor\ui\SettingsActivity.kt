package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.viewModels
import androidx.preference.CheckBoxPreference
import androidx.preference.EditTextPreference
import androidx.preference.ListPreference
import androidx.preference.Preference
import androidx.preference.PreferenceFragmentCompat
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequest
import androidx.work.multiprocess.RemoteWorkManager
import com.mohamedrady.v2hoor.AngApplication
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.AppConfig.VPN
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.extension.toLongEx
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.service.AutoServerUpdater
import com.mohamedrady.v2hoor.service.SubscriptionUpdater
import com.mohamedrady.v2hoor.util.Utils
import com.mohamedrady.v2hoor.viewmodel.SettingsViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import androidx.lifecycle.lifecycleScope
import java.util.concurrent.TimeUnit

class SettingsActivity : BaseActivity() {
    private val settingsViewModel: SettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        title = getString(R.string.title_settings)

        settingsViewModel.startListenPreferenceChange()
    }

    class SettingsFragment : PreferenceFragmentCompat() {

        private val perAppProxy by lazy { findPreference<CheckBoxPreference>(AppConfig.PREF_PER_APP_PROXY) }
        private val localDns by lazy { findPreference<CheckBoxPreference>(AppConfig.PREF_LOCAL_DNS_ENABLED) }
        private val fakeDns by lazy { findPreference<CheckBoxPreference>(AppConfig.PREF_FAKE_DNS_ENABLED) }
        private val appendHttpProxy by lazy { findPreference<CheckBoxPreference>(AppConfig.PREF_APPEND_HTTP_PROXY) }
        private val localDnsPort by lazy { findPreference<EditTextPreference>(AppConfig.PREF_LOCAL_DNS_PORT) }
        private val vpnDns by lazy { findPreference<EditTextPreference>(AppConfig.PREF_VPN_DNS) }
        private val vpnBypassLan by lazy { findPreference<ListPreference>(AppConfig.PREF_VPN_BYPASS_LAN) }

        private val mux by lazy { findPreference<CheckBoxPreference>(AppConfig.PREF_MUX_ENABLED) }
        private val muxConcurrency by lazy { findPreference<EditTextPreference>(AppConfig.PREF_MUX_CONCURRENCY) }
        private val muxXudpConcurrency by lazy { findPreference<EditTextPreference>(AppConfig.PREF_MUX_XUDP_CONCURRENCY) }
        private val muxXudpQuic by lazy { findPreference<ListPreference>(AppConfig.PREF_MUX_XUDP_QUIC) }

        private val fragment by lazy { findPreference<CheckBoxPreference>(AppConfig.PREF_FRAGMENT_ENABLED) }
        private val fragmentPackets by lazy { findPreference<ListPreference>(AppConfig.PREF_FRAGMENT_PACKETS) }
        private val fragmentLength by lazy { findPreference<EditTextPreference>(AppConfig.PREF_FRAGMENT_LENGTH) }
        private val fragmentInterval by lazy { findPreference<EditTextPreference>(AppConfig.PREF_FRAGMENT_INTERVAL) }

        private val autoUpdateCheck by lazy { findPreference<CheckBoxPreference>(AppConfig.SUBSCRIPTION_AUTO_UPDATE) }
        private val autoUpdateInterval by lazy { findPreference<EditTextPreference>(AppConfig.SUBSCRIPTION_AUTO_UPDATE_INTERVAL) }

        // Server update preferences
        private val autoServerUpdateEnabled by lazy { findPreference<CheckBoxPreference>("pref_auto_server_update_enabled") }
        private val autoServerUpdateInterval by lazy { findPreference<EditTextPreference>("pref_auto_server_update_interval") }
        private val manualServerUpdate by lazy { findPreference<Preference>("pref_manual_server_update") }

        private val socksPort by lazy { findPreference<EditTextPreference>(AppConfig.PREF_SOCKS_PORT) }
        private val remoteDns by lazy { findPreference<EditTextPreference>(AppConfig.PREF_REMOTE_DNS) }
        private val domesticDns by lazy { findPreference<EditTextPreference>(AppConfig.PREF_DOMESTIC_DNS) }
        private val dnsHosts by lazy { findPreference<EditTextPreference>(AppConfig.PREF_DNS_HOSTS) }
        private val delayTestUrl by lazy { findPreference<EditTextPreference>(AppConfig.PREF_DELAY_TEST_URL) }
        private val mode by lazy { findPreference<ListPreference>(AppConfig.PREF_MODE) }

        override fun onCreatePreferences(bundle: Bundle?, s: String?) {
            addPreferencesFromResource(R.xml.pref_settings)

            perAppProxy?.setOnPreferenceClickListener {
                startActivity(Intent(activity, PerAppProxyActivity::class.java))
                perAppProxy?.isChecked = true
                false
            }
            localDns?.setOnPreferenceChangeListener { _, any ->
                updateLocalDns(any as Boolean)
                true
            }
            localDnsPort?.setOnPreferenceChangeListener { _, any ->
                val nval = any as String
                localDnsPort?.summary =
                    if (TextUtils.isEmpty(nval)) AppConfig.PORT_LOCAL_DNS else nval
                true
            }
            vpnDns?.setOnPreferenceChangeListener { _, any ->
                vpnDns?.summary = any as String
                true
            }

            mux?.setOnPreferenceChangeListener { _, newValue ->
                updateMux(newValue as Boolean)
                true
            }
            muxConcurrency?.setOnPreferenceChangeListener { _, newValue ->
                updateMuxConcurrency(newValue as String)
                true
            }
            muxXudpConcurrency?.setOnPreferenceChangeListener { _, newValue ->
                updateMuxXudpConcurrency(newValue as String)
                true
            }

            fragment?.setOnPreferenceChangeListener { _, newValue ->
                updateFragment(newValue as Boolean)
                true
            }
            fragmentPackets?.setOnPreferenceChangeListener { _, newValue ->
                updateFragmentPackets(newValue as String)
                true
            }
            fragmentLength?.setOnPreferenceChangeListener { _, newValue ->
                updateFragmentLength(newValue as String)
                true
            }
            fragmentInterval?.setOnPreferenceChangeListener { _, newValue ->
                updateFragmentInterval(newValue as String)
                true
            }

            autoUpdateCheck?.setOnPreferenceChangeListener { _, newValue ->
                val value = newValue as Boolean
                autoUpdateCheck?.isChecked = value
                autoUpdateInterval?.isEnabled = value
                autoUpdateInterval?.text?.toLongEx()?.let {
                    if (newValue) configureUpdateTask(it) else cancelUpdateTask()
                }
                true
            }
            autoUpdateInterval?.setOnPreferenceChangeListener { _, any ->
                var nval = any as String

                // It must be greater than 15 minutes because WorkManager couldn't run tasks under 15 minutes intervals
                nval =
                    if (TextUtils.isEmpty(nval) || nval.toLongEx() < 15) AppConfig.SUBSCRIPTION_DEFAULT_UPDATE_INTERVAL else nval
                autoUpdateInterval?.summary = nval
                configureUpdateTask(nval.toLongEx())
                true
            }

            // Server update preferences
            autoServerUpdateEnabled?.setOnPreferenceChangeListener { _, newValue ->
                val enabled = newValue as Boolean
                AutoServerUpdater.setAutoUpdateEnabled(enabled)
                autoServerUpdateInterval?.isEnabled = enabled

                if (enabled) {
                    val interval = autoServerUpdateInterval?.text?.toLongEx() ?: 30L
                    AutoServerUpdater.scheduleAutoUpdate(requireContext(), interval)
                } else {
                    AutoServerUpdater.cancelAutoUpdate(requireContext())
                }
                true
            }

            autoServerUpdateInterval?.setOnPreferenceChangeListener { _, any ->
                var nval = any as String

                // Must be greater than 15 minutes
                nval = if (TextUtils.isEmpty(nval) || nval.toLongEx() < 15) "30" else nval
                autoServerUpdateInterval?.summary = nval

                AutoServerUpdater.setAutoUpdateInterval(nval.toInt())
                if (AutoServerUpdater.isAutoUpdateEnabled()) {
                    AutoServerUpdater.scheduleAutoUpdate(requireContext(), nval.toLongEx())
                }
                true
            }

            manualServerUpdate?.setOnPreferenceClickListener {
                manualServerUpdate?.summary = getString(R.string.server_update_in_progress)

                lifecycleScope.launch(Dispatchers.IO) {
                    val result = AutoServerUpdater.manualUpdate(requireContext())

                    launch(Dispatchers.Main) {
                        if (result.isSuccess) {
                            val count = result.getOrNull() ?: 0
                            if (count > 0) {
                                activity?.toast(getString(R.string.server_update_success, count))
                            } else {
                                activity?.toast(getString(R.string.server_update_no_new))
                            }
                        } else {
                            activity?.toastError(getString(R.string.server_update_failure))
                        }

                        manualServerUpdate?.summary = getString(R.string.summary_pref_manual_server_update)
                    }
                }
                true
            }

            socksPort?.setOnPreferenceChangeListener { _, any ->
                val nval = any as String
                socksPort?.summary = if (TextUtils.isEmpty(nval)) AppConfig.PORT_SOCKS else nval
                true
            }

            remoteDns?.setOnPreferenceChangeListener { _, any ->
                val nval = any as String
                remoteDns?.summary = if (nval == "") AppConfig.DNS_PROXY else nval
                true
            }
            domesticDns?.setOnPreferenceChangeListener { _, any ->
                val nval = any as String
                domesticDns?.summary = if (nval == "") AppConfig.DNS_DIRECT else nval
                true
            }
            dnsHosts?.setOnPreferenceChangeListener { _, any ->
                val nval = any as String
                dnsHosts?.summary = nval
                true
            }
            delayTestUrl?.setOnPreferenceChangeListener { _, any ->
                val nval = any as String
                delayTestUrl?.summary = if (nval == "") AppConfig.DELAY_TEST_URL else nval
                true
            }
            mode?.setOnPreferenceChangeListener { _, newValue ->
                updateMode(newValue.toString())
                true
            }
            mode?.dialogLayoutResource = R.layout.preference_with_help_link
            //loglevel.summary = "LogLevel"

        }

        override fun onStart() {
            super.onStart()
            updateMode(MmkvManager.decodeSettingsString(AppConfig.PREF_MODE, VPN))
            localDns?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_LOCAL_DNS_ENABLED, false)
            fakeDns?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_FAKE_DNS_ENABLED, false)
            appendHttpProxy?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_APPEND_HTTP_PROXY, false)
            localDnsPort?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_LOCAL_DNS_PORT, AppConfig.PORT_LOCAL_DNS)
            vpnDns?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_VPN_DNS, AppConfig.DNS_VPN)

            updateMux(MmkvManager.decodeSettingsBool(AppConfig.PREF_MUX_ENABLED, false))
            mux?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_MUX_ENABLED, false)
            muxConcurrency?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_MUX_CONCURRENCY, "8")
            muxXudpConcurrency?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_MUX_XUDP_CONCURRENCY, "8")

            updateFragment(MmkvManager.decodeSettingsBool(AppConfig.PREF_FRAGMENT_ENABLED, false))
            fragment?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_FRAGMENT_ENABLED, false)
            fragmentPackets?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_FRAGMENT_PACKETS, "tlshello")
            fragmentLength?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_FRAGMENT_LENGTH, "50-100")
            fragmentInterval?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_FRAGMENT_INTERVAL, "10-20")

            autoUpdateCheck?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.SUBSCRIPTION_AUTO_UPDATE, false)
            autoUpdateInterval?.summary =
                MmkvManager.decodeSettingsString(AppConfig.SUBSCRIPTION_AUTO_UPDATE_INTERVAL, AppConfig.SUBSCRIPTION_DEFAULT_UPDATE_INTERVAL)
            autoUpdateInterval?.isEnabled = MmkvManager.decodeSettingsBool(AppConfig.SUBSCRIPTION_AUTO_UPDATE, false)

            // Initialize server update preferences
            val serverUpdateEnabled = AutoServerUpdater.isAutoUpdateEnabled()
            val serverUpdateInterval = AutoServerUpdater.getAutoUpdateInterval().toString()

            autoServerUpdateEnabled?.isChecked = serverUpdateEnabled
            autoServerUpdateInterval?.summary = serverUpdateInterval
            autoServerUpdateInterval?.text = serverUpdateInterval
            autoServerUpdateInterval?.isEnabled = serverUpdateEnabled

            socksPort?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_SOCKS_PORT, AppConfig.PORT_SOCKS)
            remoteDns?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_REMOTE_DNS, AppConfig.DNS_PROXY)
            domesticDns?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_DOMESTIC_DNS, AppConfig.DNS_DIRECT)
            dnsHosts?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_DNS_HOSTS)
            delayTestUrl?.summary = MmkvManager.decodeSettingsString(AppConfig.PREF_DELAY_TEST_URL, AppConfig.DELAY_TEST_URL)

            initSharedPreference()
        }

        private fun initSharedPreference() {
            listOf(
                localDnsPort,
                vpnDns,
                muxConcurrency,
                muxXudpConcurrency,
                fragmentLength,
                fragmentInterval,
                autoUpdateInterval,
                socksPort,
                remoteDns,
                domesticDns,
                delayTestUrl
            ).forEach { key ->
                key?.text = key?.summary.toString()
            }

            listOf(
                AppConfig.PREF_SNIFFING_ENABLED,
            ).forEach { key ->
                findPreference<CheckBoxPreference>(key)?.isChecked =
                    MmkvManager.decodeSettingsBool(key, true)
            }

            listOf(
                AppConfig.PREF_ROUTE_ONLY_ENABLED,
                AppConfig.PREF_IS_BOOTED,
                AppConfig.PREF_BYPASS_APPS,
                AppConfig.PREF_SPEED_ENABLED,
                AppConfig.PREF_CONFIRM_REMOVE,
                AppConfig.PREF_START_SCAN_IMMEDIATE,
                AppConfig.PREF_DOUBLE_COLUMN_DISPLAY,
                AppConfig.PREF_PREFER_IPV6,
                AppConfig.PREF_PROXY_SHARING,
                AppConfig.PREF_ALLOW_INSECURE
            ).forEach { key ->
                findPreference<CheckBoxPreference>(key)?.isChecked =
                    MmkvManager.decodeSettingsBool(key, false)
            }

            listOf(
                AppConfig.PREF_VPN_BYPASS_LAN,
                AppConfig.PREF_ROUTING_DOMAIN_STRATEGY,
                AppConfig.PREF_MUX_XUDP_QUIC,
                AppConfig.PREF_FRAGMENT_PACKETS,
                AppConfig.PREF_LANGUAGE,
                AppConfig.PREF_UI_MODE_NIGHT,
                AppConfig.PREF_LOGLEVEL,
                AppConfig.PREF_MODE
            ).forEach { key ->
                if (MmkvManager.decodeSettingsString(key) != null) {
                    findPreference<ListPreference>(key)?.value = MmkvManager.decodeSettingsString(key)
                }
            }
        }

        private fun updateMode(mode: String?) {
            val vpn = mode == VPN
            perAppProxy?.isEnabled = vpn
            perAppProxy?.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_PER_APP_PROXY, false)
            localDns?.isEnabled = vpn
            fakeDns?.isEnabled = vpn
            appendHttpProxy?.isEnabled = vpn
            localDnsPort?.isEnabled = vpn
            vpnDns?.isEnabled = vpn
            vpnBypassLan?.isEnabled = vpn
            vpn
            if (vpn) {
                updateLocalDns(
                    MmkvManager.decodeSettingsBool(
                        AppConfig.PREF_LOCAL_DNS_ENABLED,
                        false
                    )
                )
            }
        }

        private fun updateLocalDns(enabled: Boolean) {
            fakeDns?.isEnabled = enabled
            localDnsPort?.isEnabled = enabled
            vpnDns?.isEnabled = !enabled
        }

        private fun configureUpdateTask(interval: Long) {
            val rw = RemoteWorkManager.getInstance(AngApplication.application)
            rw.cancelUniqueWork(AppConfig.SUBSCRIPTION_UPDATE_TASK_NAME)
            rw.enqueueUniquePeriodicWork(
                AppConfig.SUBSCRIPTION_UPDATE_TASK_NAME,
                ExistingPeriodicWorkPolicy.UPDATE,
                PeriodicWorkRequest.Builder(
                    SubscriptionUpdater.UpdateTask::class.java,
                    interval,
                    TimeUnit.MINUTES
                )
                    .apply {
                        setInitialDelay(interval, TimeUnit.MINUTES)
                    }
                    .build()
            )
        }

        private fun cancelUpdateTask() {
            val rw = RemoteWorkManager.getInstance(AngApplication.application)
            rw.cancelUniqueWork(AppConfig.SUBSCRIPTION_UPDATE_TASK_NAME)
        }

        private fun updateMux(enabled: Boolean) {
            muxConcurrency?.isEnabled = enabled
            muxXudpConcurrency?.isEnabled = enabled
            muxXudpQuic?.isEnabled = enabled
            if (enabled) {
                updateMuxConcurrency(MmkvManager.decodeSettingsString(AppConfig.PREF_MUX_CONCURRENCY, "8"))
                updateMuxXudpConcurrency(MmkvManager.decodeSettingsString(AppConfig.PREF_MUX_XUDP_CONCURRENCY, "8"))
            }
        }

        private fun updateMuxConcurrency(value: String?) {
            val concurrency = value?.toIntOrNull() ?: 8
            muxConcurrency?.summary = concurrency.toString()
        }


        private fun updateMuxXudpConcurrency(value: String?) {
            if (value == null) {
                muxXudpQuic?.isEnabled = true
            } else {
                val concurrency = value.toIntOrNull() ?: 8
                muxXudpConcurrency?.summary = concurrency.toString()
                muxXudpQuic?.isEnabled = concurrency >= 0
            }
        }

        private fun updateFragment(enabled: Boolean) {
            fragmentPackets?.isEnabled = enabled
            fragmentLength?.isEnabled = enabled
            fragmentInterval?.isEnabled = enabled
            if (enabled) {
                updateFragmentPackets(MmkvManager.decodeSettingsString(AppConfig.PREF_FRAGMENT_PACKETS, "tlshello"))
                updateFragmentLength(MmkvManager.decodeSettingsString(AppConfig.PREF_FRAGMENT_LENGTH, "50-100"))
                updateFragmentInterval(MmkvManager.decodeSettingsString(AppConfig.PREF_FRAGMENT_INTERVAL, "10-20"))
            }
        }

        private fun updateFragmentPackets(value: String?) {
            fragmentPackets?.summary = value.toString()
        }

        private fun updateFragmentLength(value: String?) {
            fragmentLength?.summary = value.toString()
        }

        private fun updateFragmentInterval(value: String?) {
            fragmentInterval?.summary = value.toString()
        }
    }

    fun onModeHelpClicked(view: View) {
        Utils.openUri(this, AppConfig.APP_WIKI_MODE)
    }
}
