<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical"
    android:padding="@dimen/padding_spacing_dp8">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/view_height_dp48"
        android:layout_height="@dimen/view_height_dp48"
        android:padding="@dimen/padding_spacing_dp8" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/package_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="3"
            android:paddingTop="@dimen/padding_spacing_dp8"
            android:textAppearance="@style/TextAppearance.AppCompat.Small" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/check_box"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        android:padding="@dimen/padding_spacing_dp8" />

</LinearLayout>