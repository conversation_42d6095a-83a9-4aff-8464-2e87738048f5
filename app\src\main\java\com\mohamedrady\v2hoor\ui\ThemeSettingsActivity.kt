package com.mohamedrady.v2hoor.ui

import android.app.TimePickerDialog
import android.os.Bundle
import android.view.MenuItem
import android.widget.ArrayAdapter
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityThemeSettingsBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.service.ThemeManager
import kotlinx.coroutines.launch

/**
 * Theme Settings Activity
 * Allows users to customize app theme and appearance
 */
class ThemeSettingsActivity : BaseActivity() {
    
    private lateinit var binding: ActivityThemeSettingsBinding
    private lateinit var themeManager: ThemeManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("ThemeSettings", "🎨 Starting ThemeSettingsActivity")
        
        binding = ActivityThemeSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Initialize theme manager
        themeManager = ThemeManager.getInstance(this)
        
        setupToolbar()
        setupThemeOptions()
        setupObservers()
        setupClickListeners()
        loadCurrentSettings()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.theme_settings)
        }
    }
    
    private fun setupThemeOptions() {
        // Setup theme mode spinner
        val themeOptions = themeManager.getAvailableThemes()
        val themeNames = themeOptions.map { themeManager.getThemeDisplayName(it) }
        
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, themeNames)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerThemeMode.adapter = adapter
        
        // Set current theme selection
        val currentTheme = themeManager.getCurrentTheme()
        val currentIndex = themeOptions.indexOf(currentTheme)
        if (currentIndex >= 0) {
            binding.spinnerThemeMode.setSelection(currentIndex)
        }
    }
    
    private fun setupObservers() {
        // Observe theme changes
        themeManager.currentTheme.observe(this) { theme ->
            updateThemeDisplay(theme)
        }
        
        // Observe dark mode status
        themeManager.isDarkMode.observe(this) { isDark ->
            updateDarkModeIndicator(isDark)
        }
    }
    
    private fun setupClickListeners() {
        // Theme mode selection
        binding.spinnerThemeMode.setOnItemSelectedListener(object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                val selectedTheme = themeManager.getAvailableThemes()[position]
                if (selectedTheme != themeManager.getCurrentTheme()) {
                    applyTheme(selectedTheme)
                }
            }
            
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        })
        
        // Quick theme toggle
        binding.btnToggleTheme.setOnClickListener {
            themeManager.toggleTheme()
            toast("تم تغيير المظهر")
        }
        
        // Auto schedule toggle
        binding.switchAutoSchedule.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                enableAutoSchedule()
            } else {
                disableAutoSchedule()
            }
        }
        
        // Schedule start time
        binding.btnScheduleStart.setOnClickListener {
            showTimePickerForStart()
        }
        
        // Schedule end time
        binding.btnScheduleEnd.setOnClickListener {
            showTimePickerForEnd()
        }
        
        // Reset to default
        binding.btnResetTheme.setOnClickListener {
            resetToDefault()
        }
        
        // Preview themes
        binding.btnPreviewLight.setOnClickListener {
            previewTheme(ThemeManager.ThemeMode.LIGHT)
        }
        
        binding.btnPreviewDark.setOnClickListener {
            previewTheme(ThemeManager.ThemeMode.DARK)
        }
    }
    
    private fun loadCurrentSettings() {
        // Load auto schedule settings
        val isAutoScheduleEnabled = themeManager.isAutoThemeScheduleEnabled()
        binding.switchAutoSchedule.isChecked = isAutoScheduleEnabled
        
        // Load schedule times
        val (startHour, endHour) = themeManager.getThemeSchedule()
        updateScheduleTimeDisplay(startHour, endHour)
        
        // Update schedule visibility
        updateScheduleVisibility(isAutoScheduleEnabled)
        
        // Load theme stats
        updateThemeStats()
    }
    
    private fun applyTheme(theme: ThemeManager.ThemeMode) {
        lifecycleScope.launch {
            try {
                themeManager.applyTheme(theme)
                toast("تم تطبيق المظهر: ${themeManager.getThemeDisplayName(theme)}")
                
                com.mohamedrady.v2hoor.util.CrashHandler.logInfo("ThemeSettings", "Theme applied: ${theme.displayName}")
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("ThemeSettings", "Failed to apply theme", e)
                toast("فشل في تطبيق المظهر")
            }
        }
    }
    
    private fun enableAutoSchedule() {
        val (startHour, endHour) = themeManager.getThemeSchedule()
        themeManager.setAutoThemeSchedule(true, startHour, endHour)
        updateScheduleVisibility(true)
        toast("تم تفعيل الجدولة التلقائية")
    }
    
    private fun disableAutoSchedule() {
        themeManager.setAutoThemeSchedule(false)
        updateScheduleVisibility(false)
        toast("تم إيقاف الجدولة التلقائية")
    }
    
    private fun showTimePickerForStart() {
        val (currentStart, _) = themeManager.getThemeSchedule()
        
        TimePickerDialog(this, { _, hourOfDay, _ ->
            val (_, endHour) = themeManager.getThemeSchedule()
            themeManager.setAutoThemeSchedule(true, hourOfDay, endHour)
            updateScheduleTimeDisplay(hourOfDay, endHour)
            toast("تم تحديث وقت البداية")
        }, currentStart, 0, true).show()
    }
    
    private fun showTimePickerForEnd() {
        val (_, currentEnd) = themeManager.getThemeSchedule()
        
        TimePickerDialog(this, { _, hourOfDay, _ ->
            val (startHour, _) = themeManager.getThemeSchedule()
            themeManager.setAutoThemeSchedule(true, startHour, hourOfDay)
            updateScheduleTimeDisplay(startHour, hourOfDay)
            toast("تم تحديث وقت النهاية")
        }, currentEnd, 0, true).show()
    }
    
    private fun resetToDefault() {
        themeManager.resetToDefault()
        loadCurrentSettings()
        setupThemeOptions()
        toast("تم إعادة تعيين إعدادات المظهر")
    }
    
    private fun previewTheme(theme: ThemeManager.ThemeMode) {
        // Temporarily apply theme for preview
        applyTheme(theme)
        
        // Show preview dialog
        android.app.AlertDialog.Builder(this)
            .setTitle("معاينة المظهر")
            .setMessage("هذه معاينة للمظهر ${themeManager.getThemeDisplayName(theme)}. هل تريد الاحتفاظ بهذا المظهر؟")
            .setPositiveButton("احتفظ") { _, _ ->
                // Keep the theme
                toast("تم حفظ المظهر")
            }
            .setNegativeButton("تراجع") { _, _ ->
                // Revert to previous theme
                val previousTheme = themeManager.getCurrentTheme()
                applyTheme(previousTheme)
            }
            .show()
    }
    
    private fun updateThemeDisplay(theme: ThemeManager.ThemeMode) {
        binding.tvCurrentTheme.text = "المظهر الحالي: ${themeManager.getThemeDisplayName(theme)}"
    }
    
    private fun updateDarkModeIndicator(isDark: Boolean) {
        binding.ivThemeIndicator.setImageResource(
            if (isDark) R.drawable.ic_dark_mode_24dp else R.drawable.ic_light_mode_24dp
        )
        
        binding.tvThemeStatus.text = if (isDark) "المظهر الداكن نشط" else "المظهر الفاتح نشط"
    }
    
    private fun updateScheduleVisibility(visible: Boolean) {
        val visibility = if (visible) android.view.View.VISIBLE else android.view.View.GONE
        binding.layoutScheduleSettings.visibility = visibility
    }
    
    private fun updateScheduleTimeDisplay(startHour: Int, endHour: Int) {
        binding.tvScheduleStart.text = String.format("%02d:00", startHour)
        binding.tvScheduleEnd.text = String.format("%02d:00", endHour)
        
        binding.btnScheduleStart.text = "البداية: ${String.format("%02d:00", startHour)}"
        binding.btnScheduleEnd.text = "النهاية: ${String.format("%02d:00", endHour)}"
    }
    
    private fun updateThemeStats() {
        val stats = themeManager.getThemeStats()
        
        binding.tvStatsCurrentTheme.text = "المظهر: ${stats["current_theme"]}"
        binding.tvStatsDarkActive.text = "الوضع الداكن: ${stats["is_dark_active"]}"
        binding.tvStatsAutoSchedule.text = "الجدولة: ${stats["auto_schedule"]}"
        binding.tvStatsScheduleTime.text = "التوقيت: ${stats["schedule_time"]}"
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Check scheduled theme when activity resumes
        themeManager.checkAndApplyScheduledTheme()
        updateThemeStats()
    }
}
