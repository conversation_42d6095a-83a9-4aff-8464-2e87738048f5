<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_bypass_list" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_bypass_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_bypass_list_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="105" endOffset="14"/></Target><Target id="@+id/pb_waiting" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="54"/></Target><Target id="@+id/header_view" view="LinearLayout"><Expressions/><location startLine="18" startOffset="4" endLine="96" endOffset="18"/></Target><Target id="@+id/container_per_app_proxy" view="LinearLayout"><Expressions/><location startLine="35" startOffset="16" endLine="54" endOffset="30"/></Target><Target id="@+id/switch_per_app_proxy" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="44" startOffset="20" endLine="52" endOffset="58"/></Target><Target id="@+id/container_bypass_apps" view="LinearLayout"><Expressions/><location startLine="56" startOffset="16" endLine="91" endOffset="30"/></Target><Target id="@+id/switch_bypass_apps" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="65" startOffset="20" endLine="72" endOffset="58"/></Target><Target id="@+id/layout_switch_bypass_apps_tips" view="LinearLayout"><Expressions/><location startLine="74" startOffset="20" endLine="89" endOffset="34"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="98" startOffset="4" endLine="103" endOffset="76"/></Target></Targets></Layout>