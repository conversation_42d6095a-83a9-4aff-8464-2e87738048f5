{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-62:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\addfe77571c545920b761f990220962d\\transformed\\core-1.16.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3704,3802,3904,4004,4105,4212,4320,10729", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3797,3899,3999,4100,4207,4315,4430,10825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6168aafd8aa1313de7d53dbb6f8061c\\transformed\\appcompat-1.7.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,10409", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,10487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1586d6a1fa7e7bf9ffc9892b585ca703\\transformed\\material-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1149,1214,1308,1381,1442,1567,1633,1701,1762,1834,1894,1948,2068,2128,2190,2244,2321,2451,2538,2615,2705,2788,2870,3011,3091,3176,3303,3394,3470,3524,3577,3643,3717,3798,3869,3949,4022,4099,4176,4250,4360,4453,4528,4618,4709,4781,4859,4950,5004,5087,5155,5239,5326,5388,5452,5515,5587,5697,5810,5913,6022,6080,6137,6214,6299,6377", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1144,1209,1303,1376,1437,1562,1628,1696,1757,1829,1889,1943,2063,2123,2185,2239,2316,2446,2533,2610,2700,2783,2865,3006,3086,3171,3298,3389,3465,3519,3572,3638,3712,3793,3864,3944,4017,4094,4171,4245,4355,4448,4523,4613,4704,4776,4854,4945,4999,5082,5150,5234,5321,5383,5447,5510,5582,5692,5805,5908,6017,6075,6132,6209,6294,6372,6446"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,51,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,4435,4536,4670,4822,4883,5046,5140,5213,5274,5399,5465,5533,5594,5666,5726,5780,5900,5960,6022,6076,6153,6283,6370,6447,6537,6620,6702,6843,6923,7008,7135,7226,7302,7356,7409,7475,7549,7630,7701,7781,7854,7931,8008,8082,8192,8285,8360,8450,8541,8613,8691,8782,8836,8919,8987,9071,9158,9220,9284,9347,9419,9529,9642,9745,9854,9912,10049,10492,10577,10655", "endLines": "6,34,35,36,37,38,46,47,48,50,51,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,120,121,122", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "369,3340,3425,3506,3611,3699,4531,4665,4748,4878,4943,5135,5208,5269,5394,5460,5528,5589,5661,5721,5775,5895,5955,6017,6071,6148,6278,6365,6442,6532,6615,6697,6838,6918,7003,7130,7221,7297,7351,7404,7470,7544,7625,7696,7776,7849,7926,8003,8077,8187,8280,8355,8445,8536,8608,8686,8777,8831,8914,8982,9066,9153,9215,9279,9342,9414,9524,9637,9740,9849,9907,9964,10121,10572,10650,10724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\082507c0f8acc2f43fb9efc345edc657\\transformed\\preference-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,272,352,498,667,752", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "169,267,347,493,662,747,829"}, "to": {"startLines": "49,52,114,116,124,125,126", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4753,4948,9969,10126,10830,10999,11084", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "4817,5041,10044,10267,10994,11079,11161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5e6d251ef110edeab2647bcfbb802f94\\transformed\\quickie-foss-1.14.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,123", "endColumns": "67,68", "endOffsets": "118,187"}, "to": {"startLines": "117,118", "startColumns": "4,4", "startOffsets": "10272,10340", "endColumns": "67,68", "endOffsets": "10335,10404"}}]}]}