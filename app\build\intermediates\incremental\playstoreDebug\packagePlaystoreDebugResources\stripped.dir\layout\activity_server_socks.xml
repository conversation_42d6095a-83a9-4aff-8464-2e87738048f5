<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.ServerActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_spacing_dp16">

        <include layout="@layout/layout_address_port" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_lab_security4" />


            <EditText
                android:id="@+id/et_security"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_lab_id4" />

            <EditText
                android:id="@+id/et_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:layout_marginBottom="@dimen/padding_spacing_dp16"
            android:orientation="vertical" />

    </LinearLayout>
</ScrollView>