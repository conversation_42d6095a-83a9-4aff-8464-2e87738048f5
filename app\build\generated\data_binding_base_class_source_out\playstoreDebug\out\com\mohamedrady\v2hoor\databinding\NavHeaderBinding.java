// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NavHeaderBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivUserAvatar;

  @NonNull
  public final TextView tvUserEmail;

  @NonNull
  public final TextView tvUserName;

  private NavHeaderBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivUserAvatar,
      @NonNull TextView tvUserEmail, @NonNull TextView tvUserName) {
    this.rootView = rootView;
    this.ivUserAvatar = ivUserAvatar;
    this.tvUserEmail = tvUserEmail;
    this.tvUserName = tvUserName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NavHeaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NavHeaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.nav_header, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NavHeaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_user_avatar;
      ImageView ivUserAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivUserAvatar == null) {
        break missingId;
      }

      id = R.id.tv_user_email;
      TextView tvUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvUserEmail == null) {
        break missingId;
      }

      id = R.id.tv_user_name;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      return new NavHeaderBinding((LinearLayout) rootView, ivUserAvatar, tvUserEmail, tvUserName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
