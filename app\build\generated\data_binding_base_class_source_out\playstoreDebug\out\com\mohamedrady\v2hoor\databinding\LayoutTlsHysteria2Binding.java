// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutTlsHysteria2Binding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText etPinsha256;

  @NonNull
  public final EditText etSni;

  @NonNull
  public final LinearLayout layAllowInsecure;

  @NonNull
  public final LinearLayout laySni;

  @NonNull
  public final LinearLayout layStreamSecurity;

  @NonNull
  public final Spinner spAllowInsecure;

  @NonNull
  public final Spinner spStreamSecurity;

  private LayoutTlsHysteria2Binding(@NonNull LinearLayout rootView, @NonNull EditText etPinsha256,
      @NonNull EditText etSni, @NonNull LinearLayout layAllowInsecure, @NonNull LinearLayout laySni,
      @NonNull LinearLayout layStreamSecurity, @NonNull Spinner spAllowInsecure,
      @NonNull Spinner spStreamSecurity) {
    this.rootView = rootView;
    this.etPinsha256 = etPinsha256;
    this.etSni = etSni;
    this.layAllowInsecure = layAllowInsecure;
    this.laySni = laySni;
    this.layStreamSecurity = layStreamSecurity;
    this.spAllowInsecure = spAllowInsecure;
    this.spStreamSecurity = spStreamSecurity;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutTlsHysteria2Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutTlsHysteria2Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_tls_hysteria2, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutTlsHysteria2Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_pinsha256;
      EditText etPinsha256 = ViewBindings.findChildViewById(rootView, id);
      if (etPinsha256 == null) {
        break missingId;
      }

      id = R.id.et_sni;
      EditText etSni = ViewBindings.findChildViewById(rootView, id);
      if (etSni == null) {
        break missingId;
      }

      id = R.id.lay_allow_insecure;
      LinearLayout layAllowInsecure = ViewBindings.findChildViewById(rootView, id);
      if (layAllowInsecure == null) {
        break missingId;
      }

      id = R.id.lay_sni;
      LinearLayout laySni = ViewBindings.findChildViewById(rootView, id);
      if (laySni == null) {
        break missingId;
      }

      id = R.id.lay_stream_security;
      LinearLayout layStreamSecurity = ViewBindings.findChildViewById(rootView, id);
      if (layStreamSecurity == null) {
        break missingId;
      }

      id = R.id.sp_allow_insecure;
      Spinner spAllowInsecure = ViewBindings.findChildViewById(rootView, id);
      if (spAllowInsecure == null) {
        break missingId;
      }

      id = R.id.sp_stream_security;
      Spinner spStreamSecurity = ViewBindings.findChildViewById(rootView, id);
      if (spStreamSecurity == null) {
        break missingId;
      }

      return new LayoutTlsHysteria2Binding((LinearLayout) rootView, etPinsha256, etSni,
          layAllowInsecure, laySni, layStreamSecurity, spAllowInsecure, spStreamSecurity);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
