// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityServerVmessBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText etId;

  @NonNull
  public final Spinner spSecurity;

  private ActivityServerVmessBinding(@NonNull ScrollView rootView, @NonNull EditText etId,
      @NonNull Spinner spSecurity) {
    this.rootView = rootView;
    this.etId = etId;
    this.spSecurity = spSecurity;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityServerVmessBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityServerVmessBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_server_vmess, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityServerVmessBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_id;
      EditText etId = ViewBindings.findChildViewById(rootView, id);
      if (etId == null) {
        break missingId;
      }

      id = R.id.sp_security;
      Spinner spSecurity = ViewBindings.findChildViewById(rootView, id);
      if (spSecurity == null) {
        break missingId;
      }

      return new ActivityServerVmessBinding((ScrollView) rootView, etId, spSecurity);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
