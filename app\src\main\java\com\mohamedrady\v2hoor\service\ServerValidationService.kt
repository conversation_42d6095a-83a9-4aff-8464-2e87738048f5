package com.mohamedrady.v2hoor.service

import android.content.Context
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.FirebaseServerModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.InetSocketAddress
import java.net.Socket
import java.util.concurrent.TimeUnit

/**
 * Server Validation Service
 * Handles server validation, expiration checks, and connectivity testing
 */
class ServerValidationService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ServerValidationService? = null
        
        fun getInstance(context: Context): ServerValidationService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerValidationService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val CONNECTION_TIMEOUT_MS = 5000
        private const val EXPIRY_WARNING_HOURS = 24
        private const val VALIDITY_WARNING_HOURS = 48
    }
    
    /**
     * Validate a single server
     */
    suspend fun validateServer(server: FirebaseServerModel): ServerValidationResult {
        return withContext(Dispatchers.IO) {
            try {
                val result = ServerValidationResult(server.id)
                
                // Basic validation
                result.isBasicValid = validateBasicFields(server)
                result.basicValidationMessage = getBasicValidationMessage(server)
                
                // Expiration validation
                result.isExpired = server.isExpired()
                result.expirationMessage = getExpirationMessage(server)
                
                // Warning validation
                result.hasWarning = hasExpirationWarning(server)
                result.warningMessage = server.getExpirationWarning()
                
                // Connectivity test (optional, can be slow)
                if (result.isBasicValid && !result.isExpired) {
                    result.isConnectable = testServerConnectivity(server)
                    result.connectivityMessage = if (result.isConnectable) {
                        "Server is reachable"
                    } else {
                        "Server is not reachable"
                    }
                }
                
                // Overall validation
                result.isValid = result.isBasicValid && !result.isExpired && result.isConnectable
                
                android.util.Log.d(AppConfig.TAG, "Validated server ${server.name}: ${result.isValid}")
                result
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Error validating server ${server.name}", e)
                ServerValidationResult(server.id).apply {
                    isValid = false
                    basicValidationMessage = "Validation error: ${e.message}"
                }
            }
        }
    }
    
    /**
     * Validate multiple servers
     */
    suspend fun validateServers(servers: List<FirebaseServerModel>): List<ServerValidationResult> {
        return withContext(Dispatchers.IO) {
            servers.map { server ->
                try {
                    validateServer(server)
                } catch (e: Exception) {
                    android.util.Log.e(AppConfig.TAG, "Error validating server ${server.name}", e)
                    ServerValidationResult(server.id).apply {
                        isValid = false
                        basicValidationMessage = "Validation error: ${e.message}"
                    }
                }
            }
        }
    }
    
    /**
     * Validate basic server fields
     */
    private fun validateBasicFields(server: FirebaseServerModel): Boolean {
        return server.server.isNotBlank() &&
               server.serverPort > 0 &&
               server.serverPort <= 65535 &&
               server.protocol.isNotBlank() &&
               server.uuid.isNotBlank() &&
               server.name.isNotBlank()
    }
    
    /**
     * Get basic validation message
     */
    private fun getBasicValidationMessage(server: FirebaseServerModel): String {
        val issues = mutableListOf<String>()
        
        if (server.server.isBlank()) issues.add("Server address is empty")
        if (server.serverPort <= 0 || server.serverPort > 65535) issues.add("Invalid server port")
        if (server.protocol.isBlank()) issues.add("Protocol is not specified")
        if (server.uuid.isBlank()) issues.add("UUID is empty")
        if (server.name.isBlank()) issues.add("Server name is empty")
        
        return if (issues.isEmpty()) {
            "Basic validation passed"
        } else {
            "Issues: ${issues.joinToString(", ")}"
        }
    }
    
    /**
     * Get expiration message
     */
    private fun getExpirationMessage(server: FirebaseServerModel): String? {
        val currentTime = System.currentTimeMillis()
        
        return when {
            server.expiresAt > 0 && currentTime > server.expiresAt -> {
                val expiredDate = java.util.Date(server.expiresAt)
                "Server expired on ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(expiredDate)}"
            }
            server.validUntil > 0 && currentTime > server.validUntil -> {
                val expiredDate = java.util.Date(server.validUntil)
                "Server validity expired on ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(expiredDate)}"
            }
            else -> null
        }
    }
    
    /**
     * Check if server has expiration warning
     */
    private fun hasExpirationWarning(server: FirebaseServerModel): Boolean {
        val currentTime = System.currentTimeMillis()
        val warningThreshold = EXPIRY_WARNING_HOURS * 60 * 60 * 1000L
        
        return (server.expiresAt > 0 && (server.expiresAt - currentTime) in 0..warningThreshold) ||
               (server.validUntil > 0 && (server.validUntil - currentTime) in 0..warningThreshold)
    }
    
    /**
     * Test server connectivity
     */
    private suspend fun testServerConnectivity(server: FirebaseServerModel): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val socket = Socket()
                val address = InetSocketAddress(server.server, server.serverPort)
                socket.connect(address, CONNECTION_TIMEOUT_MS)
                socket.close()
                true
            } catch (e: Exception) {
                android.util.Log.d(AppConfig.TAG, "Connectivity test failed for ${server.name}: ${e.message}")
                false
            }
        }
    }
    
    /**
     * Get expired servers from list
     */
    fun getExpiredServers(servers: List<FirebaseServerModel>): List<FirebaseServerModel> {
        return servers.filter { it.isExpired() }
    }
    
    /**
     * Get servers with warnings from list
     */
    fun getServersWithWarnings(servers: List<FirebaseServerModel>): List<Pair<FirebaseServerModel, String>> {
        return servers.mapNotNull { server ->
            val warning = server.getExpirationWarning()
            if (warning != null) {
                Pair(server, warning)
            } else {
                null
            }
        }
    }
    
    /**
     * Get valid servers from list
     */
    fun getValidServers(servers: List<FirebaseServerModel>): List<FirebaseServerModel> {
        return servers.filter { it.isValid() }
    }
    
    /**
     * Get server statistics
     */
    fun getServerStatistics(servers: List<FirebaseServerModel>): ServerStatistics {
        val total = servers.size
        val valid = servers.count { it.isValid() }
        val expired = servers.count { it.isExpired() }
        val withWarnings = servers.count { it.getExpirationWarning() != null }
        val active = servers.count { it.isActive }
        
        return ServerStatistics(
            total = total,
            valid = valid,
            expired = expired,
            withWarnings = withWarnings,
            active = active,
            inactive = total - active
        )
    }
    
    /**
     * Clean expired servers from list
     */
    fun cleanExpiredServers(servers: List<FirebaseServerModel>): List<FirebaseServerModel> {
        val cleaned = servers.filter { !it.isExpired() }
        val removedCount = servers.size - cleaned.size
        
        if (removedCount > 0) {
            android.util.Log.i(AppConfig.TAG, "Cleaned $removedCount expired servers")
        }
        
        return cleaned
    }
    
    /**
     * Sort servers by priority and validity
     */
    fun sortServersByPriority(servers: List<FirebaseServerModel>): List<FirebaseServerModel> {
        return servers.sortedWith(compareBy<FirebaseServerModel> { it.isExpired() }
            .thenBy { !it.isActive }
            .thenBy { it.priority }
            .thenBy { it.name })
    }
}

/**
 * Server validation result
 */
data class ServerValidationResult(
    val serverId: String,
    var isValid: Boolean = false,
    var isBasicValid: Boolean = false,
    var isExpired: Boolean = false,
    var hasWarning: Boolean = false,
    var isConnectable: Boolean = false,
    var basicValidationMessage: String = "",
    var expirationMessage: String? = null,
    var warningMessage: String? = null,
    var connectivityMessage: String = ""
)

/**
 * Server statistics
 */
data class ServerStatistics(
    val total: Int,
    val valid: Int,
    val expired: Int,
    val withWarnings: Int,
    val active: Int,
    val inactive: Int
) {
    fun getValidPercentage(): Float {
        return if (total > 0) (valid.toFloat() / total.toFloat()) * 100f else 0f
    }
    
    fun getExpiredPercentage(): Float {
        return if (total > 0) (expired.toFloat() / total.toFloat()) * 100f else 0f
    }
    
    fun getActivePercentage(): Float {
        return if (total > 0) (active.toFloat() / total.toFloat()) * 100f else 0f
    }
}
