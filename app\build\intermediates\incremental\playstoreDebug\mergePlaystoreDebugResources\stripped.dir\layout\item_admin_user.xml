<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/textViewUserName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="محمد أحمد" />

                    <TextView
                        android:id="@+id/textViewRole"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/bg_role_badge"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="2dp"
                        android:text="مدير"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textViewUserEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="<EMAIL>" />

            </LinearLayout>

            <TextView
                android:id="@+id/textViewUserStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/bg_status_badge"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:text="نشط"
                android:textColor="@color/green"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- User Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewSubscriptionType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/orange"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="مميز" />

                <TextView
                    android:id="@+id/textViewServerCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="السيرفرات: 3" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewCreationDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="انضم: 15/01/2024" />

                <TextView
                    android:id="@+id/textViewLastLogin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="آخر دخول: منذ ساعة" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/buttonDeleteUser"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/bg_button_danger"
                android:text="حذف"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <Button
                android:id="@+id/buttonManageServers"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/bg_button_secondary"
                android:text="السيرفرات"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <Button
                android:id="@+id/buttonToggleStatus"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:background="@drawable/bg_button_primary"
                android:text="تفعيل"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
