<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".ui.AddUserActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- User Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات المستخدم الأساسية"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="12dp"
                        android:background="@color/divider_color" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="البريد الإلكتروني *"
                        app:boxStrokeColor="@color/primary_color"
                        app:hintTextColor="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextEmail"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="الاسم الكامل *"
                        app:boxStrokeColor="@color/primary_color"
                        app:hintTextColor="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextDisplayName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="رقم الهاتف"
                        app:boxStrokeColor="@color/primary_color"
                        app:hintTextColor="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextPhoneNumber"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:hint="البلد"
                            app:boxStrokeColor="@color/primary_color"
                            app:hintTextColor="@color/primary_color">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/editTextCountry"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:hint="المدينة"
                            app:boxStrokeColor="@color/primary_color"
                            app:hintTextColor="@color/primary_color">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/editTextCity"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Subscription Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="نوع الاشتراك"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="12dp"
                        android:background="@color/divider_color" />

                    <RadioGroup
                        android:id="@+id/radioGroupSubscription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <RadioButton
                            android:id="@+id/radioButtonFree"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="مجاني"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <RadioButton
                            android:id="@+id/radioButtonPremium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="مميز"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                    </RadioGroup>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">

                <Button
                    android:id="@+id/buttonCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/bg_button_secondary"
                    android:text="إلغاء"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/buttonSave"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:background="@drawable/bg_button_primary"
                    android:text="حفظ"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
