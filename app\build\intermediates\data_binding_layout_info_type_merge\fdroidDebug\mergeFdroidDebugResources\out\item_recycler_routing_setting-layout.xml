<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recycler_routing_setting" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_recycler_routing_setting.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/item_bg"><Targets><Target id="@+id/item_bg" tag="layout/item_recycler_routing_setting_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="109" endOffset="14"/></Target><Target id="@+id/info_container" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="106" endOffset="18"/></Target><Target id="@+id/remarks" view="TextView"><Expressions/><location startLine="33" startOffset="16" endLine="37" endOffset="86"/></Target><Target id="@+id/img_locked" view="ImageView"><Expressions/><location startLine="39" startOffset="16" endLine="45" endOffset="60"/></Target><Target id="@+id/domainIp" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="80"/></Target><Target id="@+id/outboundTag" view="TextView"><Expressions/><location startLine="57" startOffset="12" endLine="63" endOffset="80"/></Target><Target id="@+id/layout_edit" view="LinearLayout"><Expressions/><location startLine="74" startOffset="12" endLine="90" endOffset="26"/></Target><Target id="@+id/chk_enable" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="98" startOffset="16" endLine="102" endOffset="54"/></Target></Targets></Layout>