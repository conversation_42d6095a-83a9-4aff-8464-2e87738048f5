// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityUserDetailsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button buttonDeleteUser;

  @NonNull
  public final Button buttonEditProfile;

  @NonNull
  public final Button buttonManageServers;

  @NonNull
  public final Button buttonToggleStatus;

  @NonNull
  public final TextView textViewCity;

  @NonNull
  public final TextView textViewCountry;

  @NonNull
  public final TextView textViewCreationDate;

  @NonNull
  public final TextView textViewLastLogin;

  @NonNull
  public final TextView textViewPhoneNumber;

  @NonNull
  public final TextView textViewRole;

  @NonNull
  public final TextView textViewServerCount;

  @NonNull
  public final TextView textViewSubscriptionType;

  @NonNull
  public final TextView textViewUserEmail;

  @NonNull
  public final TextView textViewUserId;

  @NonNull
  public final TextView textViewUserName;

  @NonNull
  public final TextView textViewUserStatus;

  @NonNull
  public final Toolbar toolbar;

  private ActivityUserDetailsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button buttonDeleteUser, @NonNull Button buttonEditProfile,
      @NonNull Button buttonManageServers, @NonNull Button buttonToggleStatus,
      @NonNull TextView textViewCity, @NonNull TextView textViewCountry,
      @NonNull TextView textViewCreationDate, @NonNull TextView textViewLastLogin,
      @NonNull TextView textViewPhoneNumber, @NonNull TextView textViewRole,
      @NonNull TextView textViewServerCount, @NonNull TextView textViewSubscriptionType,
      @NonNull TextView textViewUserEmail, @NonNull TextView textViewUserId,
      @NonNull TextView textViewUserName, @NonNull TextView textViewUserStatus,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonDeleteUser = buttonDeleteUser;
    this.buttonEditProfile = buttonEditProfile;
    this.buttonManageServers = buttonManageServers;
    this.buttonToggleStatus = buttonToggleStatus;
    this.textViewCity = textViewCity;
    this.textViewCountry = textViewCountry;
    this.textViewCreationDate = textViewCreationDate;
    this.textViewLastLogin = textViewLastLogin;
    this.textViewPhoneNumber = textViewPhoneNumber;
    this.textViewRole = textViewRole;
    this.textViewServerCount = textViewServerCount;
    this.textViewSubscriptionType = textViewSubscriptionType;
    this.textViewUserEmail = textViewUserEmail;
    this.textViewUserId = textViewUserId;
    this.textViewUserName = textViewUserName;
    this.textViewUserStatus = textViewUserStatus;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityUserDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityUserDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_user_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityUserDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDeleteUser;
      Button buttonDeleteUser = ViewBindings.findChildViewById(rootView, id);
      if (buttonDeleteUser == null) {
        break missingId;
      }

      id = R.id.buttonEditProfile;
      Button buttonEditProfile = ViewBindings.findChildViewById(rootView, id);
      if (buttonEditProfile == null) {
        break missingId;
      }

      id = R.id.buttonManageServers;
      Button buttonManageServers = ViewBindings.findChildViewById(rootView, id);
      if (buttonManageServers == null) {
        break missingId;
      }

      id = R.id.buttonToggleStatus;
      Button buttonToggleStatus = ViewBindings.findChildViewById(rootView, id);
      if (buttonToggleStatus == null) {
        break missingId;
      }

      id = R.id.textViewCity;
      TextView textViewCity = ViewBindings.findChildViewById(rootView, id);
      if (textViewCity == null) {
        break missingId;
      }

      id = R.id.textViewCountry;
      TextView textViewCountry = ViewBindings.findChildViewById(rootView, id);
      if (textViewCountry == null) {
        break missingId;
      }

      id = R.id.textViewCreationDate;
      TextView textViewCreationDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewCreationDate == null) {
        break missingId;
      }

      id = R.id.textViewLastLogin;
      TextView textViewLastLogin = ViewBindings.findChildViewById(rootView, id);
      if (textViewLastLogin == null) {
        break missingId;
      }

      id = R.id.textViewPhoneNumber;
      TextView textViewPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (textViewPhoneNumber == null) {
        break missingId;
      }

      id = R.id.textViewRole;
      TextView textViewRole = ViewBindings.findChildViewById(rootView, id);
      if (textViewRole == null) {
        break missingId;
      }

      id = R.id.textViewServerCount;
      TextView textViewServerCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerCount == null) {
        break missingId;
      }

      id = R.id.textViewSubscriptionType;
      TextView textViewSubscriptionType = ViewBindings.findChildViewById(rootView, id);
      if (textViewSubscriptionType == null) {
        break missingId;
      }

      id = R.id.textViewUserEmail;
      TextView textViewUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserEmail == null) {
        break missingId;
      }

      id = R.id.textViewUserId;
      TextView textViewUserId = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserId == null) {
        break missingId;
      }

      id = R.id.textViewUserName;
      TextView textViewUserName = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserName == null) {
        break missingId;
      }

      id = R.id.textViewUserStatus;
      TextView textViewUserStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewUserStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityUserDetailsBinding((CoordinatorLayout) rootView, buttonDeleteUser,
          buttonEditProfile, buttonManageServers, buttonToggleStatus, textViewCity, textViewCountry,
          textViewCreationDate, textViewLastLogin, textViewPhoneNumber, textViewRole,
          textViewServerCount, textViewSubscriptionType, textViewUserEmail, textViewUserId,
          textViewUserName, textViewUserStatus, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
