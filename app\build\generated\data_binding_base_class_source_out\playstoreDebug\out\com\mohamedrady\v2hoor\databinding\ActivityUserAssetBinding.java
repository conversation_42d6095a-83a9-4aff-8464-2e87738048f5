// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityUserAssetBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final LinearLayout layoutGeoFilesSources;

  @NonNull
  public final NestedScrollView mainContent;

  @NonNull
  public final LinearProgressIndicator pbWaiting;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final TextView tvGeoFilesSourcesSummary;

  private ActivityUserAssetBinding(@NonNull RelativeLayout rootView,
      @NonNull LinearLayout layoutGeoFilesSources, @NonNull NestedScrollView mainContent,
      @NonNull LinearProgressIndicator pbWaiting, @NonNull RecyclerView recyclerView,
      @NonNull TextView tvGeoFilesSourcesSummary) {
    this.rootView = rootView;
    this.layoutGeoFilesSources = layoutGeoFilesSources;
    this.mainContent = mainContent;
    this.pbWaiting = pbWaiting;
    this.recyclerView = recyclerView;
    this.tvGeoFilesSourcesSummary = tvGeoFilesSourcesSummary;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityUserAssetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityUserAssetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_user_asset, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityUserAssetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layout_geo_files_sources;
      LinearLayout layoutGeoFilesSources = ViewBindings.findChildViewById(rootView, id);
      if (layoutGeoFilesSources == null) {
        break missingId;
      }

      id = R.id.main_content;
      NestedScrollView mainContent = ViewBindings.findChildViewById(rootView, id);
      if (mainContent == null) {
        break missingId;
      }

      id = R.id.pb_waiting;
      LinearProgressIndicator pbWaiting = ViewBindings.findChildViewById(rootView, id);
      if (pbWaiting == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.tv_geo_files_sources_summary;
      TextView tvGeoFilesSourcesSummary = ViewBindings.findChildViewById(rootView, id);
      if (tvGeoFilesSourcesSummary == null) {
        break missingId;
      }

      return new ActivityUserAssetBinding((RelativeLayout) rootView, layoutGeoFilesSources,
          mainContent, pbWaiting, recyclerView, tvGeoFilesSourcesSummary);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
