<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.RoutingSettingActivity">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layout_domain_strategy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center|start"
                android:orientation="vertical"
                android:padding="@dimen/padding_spacing_dp16">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_domain_strategy"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />

                <TextView
                    android:id="@+id/tv_domain_strategy_summary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp8"
                    android:maxLines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_rule_title"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_recycler_routing_setting" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</RelativeLayout>