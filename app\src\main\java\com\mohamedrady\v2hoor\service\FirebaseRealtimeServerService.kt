package com.mohamedrady.v2hoor.service

import android.content.Context
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.FirebaseServerModel
import com.mohamedrady.v2hoor.model.ServerCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Firebase Realtime Database Service for User Server Management
 * Handles loading, syncing, and managing user-specific server configurations
 */
class FirebaseRealtimeServerService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: FirebaseRealtimeServerService? = null
        
        fun getInstance(context: Context): FirebaseRealtimeServerService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseRealtimeServerService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val USERS_PATH = "users"
        private const val SERVERS_PATH = "servers"
    }
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val serverCache: ServerCache = ServerCache.getInstance(context)
    
    /**
     * Load user servers from Firebase Realtime Database
     */
    suspend fun loadUserServers(forceRefresh: Boolean = false): Result<List<FirebaseServerModel>> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    android.util.Log.w(AppConfig.TAG, "No authenticated user found")
                    return@withContext Result.failure(Exception("User not authenticated"))
                }
                
                val userId = currentUser.uid
                android.util.Log.i(AppConfig.TAG, "Loading servers for user: $userId")
                
                // Check cache first if not forcing refresh
                if (!forceRefresh) {
                    val cachedServers = serverCache.getCachedServers(userId)
                    if (cachedServers != null) {
                        android.util.Log.i(AppConfig.TAG, "Returning ${cachedServers.size} cached servers")
                        return@withContext Result.success(cachedServers)
                    }
                }
                
                // Load from Firebase
                val servers = loadServersFromFirebase(userId)
                
                // Filter valid servers
                val validServers = servers.filter { it.isValid() }
                android.util.Log.i(AppConfig.TAG, "Loaded ${validServers.size} valid servers from Firebase")
                
                // Cache the results
                serverCache.cacheServers(userId, validServers)
                
                Result.success(validServers)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to load user servers", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Load servers from Firebase Realtime Database
     */
    private suspend fun loadServersFromFirebase(userId: String): List<FirebaseServerModel> {
        return suspendCancellableCoroutine { continuation ->
            val serversRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH)
            
            val listener = object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    try {
                        val servers = mutableListOf<FirebaseServerModel>()
                        
                        for (serverSnapshot in snapshot.children) {
                            try {
                                val server = serverSnapshot.getValue(FirebaseServerModel::class.java)
                                if (server != null) {
                                    server.id = serverSnapshot.key ?: ""
                                    server.userId = userId
                                    server.lastSync = System.currentTimeMillis()
                                    servers.add(server)
                                }
                            } catch (e: Exception) {
                                android.util.Log.w(AppConfig.TAG, "Failed to parse server: ${serverSnapshot.key}", e)
                            }
                        }
                        
                        android.util.Log.i(AppConfig.TAG, "Loaded ${servers.size} servers from Firebase")
                        continuation.resume(servers)
                    } catch (e: Exception) {
                        android.util.Log.e(AppConfig.TAG, "Error processing server data", e)
                        continuation.resumeWithException(e)
                    }
                }
                
                override fun onCancelled(error: DatabaseError) {
                    android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                    continuation.resumeWithException(error.toException())
                }
            }
            
            serversRef.addListenerForSingleValueEvent(listener)
            
            continuation.invokeOnCancellation {
                serversRef.removeEventListener(listener)
            }
        }
    }
    
    /**
     * Listen for real-time server updates
     */
    fun listenForServerUpdates(onServersUpdated: (List<FirebaseServerModel>) -> Unit): DatabaseReference? {
        val currentUser = auth.currentUser ?: return null
        val userId = currentUser.uid
        
        val serversRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH)
        
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                try {
                    val servers = mutableListOf<FirebaseServerModel>()
                    
                    for (serverSnapshot in snapshot.children) {
                        try {
                            val server = serverSnapshot.getValue(FirebaseServerModel::class.java)
                            if (server != null && server.isValid()) {
                                server.id = serverSnapshot.key ?: ""
                                server.userId = userId
                                server.lastSync = System.currentTimeMillis()
                                servers.add(server)
                            }
                        } catch (e: Exception) {
                            android.util.Log.w(AppConfig.TAG, "Failed to parse server: ${serverSnapshot.key}", e)
                        }
                    }
                    
                    // Update cache
                    serverCache.cacheServers(userId, servers)
                    
                    android.util.Log.i(AppConfig.TAG, "Real-time update: ${servers.size} servers")
                    onServersUpdated(servers)
                } catch (e: Exception) {
                    android.util.Log.e(AppConfig.TAG, "Error processing real-time server updates", e)
                }
            }
            
            override fun onCancelled(error: DatabaseError) {
                android.util.Log.e(AppConfig.TAG, "Real-time listener cancelled: ${error.message}")
            }
        }
        
        serversRef.addValueEventListener(listener)
        return serversRef
    }
    
    /**
     * Stop listening for server updates
     */
    fun stopListeningForUpdates(serversRef: DatabaseReference?) {
        serversRef?.removeEventListener(object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {}
            override fun onCancelled(error: DatabaseError) {}
        })
    }
    
    /**
     * Get server by ID
     */
    suspend fun getServerById(serverId: String): Result<FirebaseServerModel?> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    return@withContext Result.failure(Exception("User not authenticated"))
                }
                
                val userId = currentUser.uid
                val server = getServerFromFirebase(userId, serverId)
                Result.success(server)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to get server by ID: $serverId", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get single server from Firebase
     */
    private suspend fun getServerFromFirebase(userId: String, serverId: String): FirebaseServerModel? {
        return suspendCancellableCoroutine { continuation ->
            val serverRef = database.child(USERS_PATH).child(userId).child(SERVERS_PATH).child(serverId)
            
            val listener = object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    try {
                        val server = snapshot.getValue(FirebaseServerModel::class.java)
                        if (server != null) {
                            server.id = serverId
                            server.userId = userId
                            server.lastSync = System.currentTimeMillis()
                        }
                        continuation.resume(server)
                    } catch (e: Exception) {
                        android.util.Log.e(AppConfig.TAG, "Error parsing server data", e)
                        continuation.resumeWithException(e)
                    }
                }
                
                override fun onCancelled(error: DatabaseError) {
                    android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                    continuation.resumeWithException(error.toException())
                }
            }
            
            serverRef.addListenerForSingleValueEvent(listener)
            
            continuation.invokeOnCancellation {
                serverRef.removeEventListener(listener)
            }
        }
    }
    
    /**
     * Refresh servers from Firebase
     */
    suspend fun refreshServers(): Result<List<FirebaseServerModel>> {
        return loadUserServers(forceRefresh = true)
    }
    
    /**
     * Clear cached servers for current user
     */
    fun clearCache() {
        val currentUser = auth.currentUser
        if (currentUser != null) {
            serverCache.clearCacheForUser(currentUser.uid)
        }
    }
    
    /**
     * Get cache statistics
     */
    fun getCacheStats(): com.mohamedrady.v2hoor.model.CacheStats? {
        val currentUser = auth.currentUser ?: return null
        return serverCache.getCacheStats()
    }
    
    /**
     * Check if user has any servers
     */
    suspend fun hasUserServers(): Boolean {
        return try {
            val result = loadUserServers()
            result.isSuccess && result.getOrNull()?.isNotEmpty() == true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get expired servers
     */
    suspend fun getExpiredServers(): List<FirebaseServerModel> {
        return try {
            val result = loadUserServers()
            if (result.isSuccess) {
                result.getOrNull()?.filter { it.isExpired() } ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * Get servers with expiration warnings
     */
    suspend fun getServersWithWarnings(): List<Pair<FirebaseServerModel, String>> {
        return try {
            val result = loadUserServers()
            if (result.isSuccess) {
                result.getOrNull()?.mapNotNull { server ->
                    val warning = server.getExpirationWarning()
                    if (warning != null) {
                        Pair(server, warning)
                    } else {
                        null
                    }
                } ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Convert Firebase servers to V2rayNG compatible format and import them
     */
    suspend fun importServersToV2rayNG(): Result<Int> {
        return withContext(Dispatchers.IO) {
            try {
                val result = loadUserServers()
                if (!result.isSuccess) {
                    return@withContext Result.failure(result.exceptionOrNull() ?: Exception("Failed to load servers"))
                }

                val servers = result.getOrNull() ?: emptyList()
                var importedCount = 0

                for (server in servers) {
                    try {
                        val configString = server.toV2RayConfig()
                        if (configString.isNotBlank()) {
                            // Import using AngConfigManager
                            val (count, _) = com.mohamedrady.v2hoor.handler.AngConfigManager.importBatchConfig(
                                configString,
                                server.subscriptionId,
                                false
                            )
                            if (count > 0) {
                                importedCount += count
                                android.util.Log.i(AppConfig.TAG, "Imported Firebase server: ${server.name}")
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.w(AppConfig.TAG, "Failed to import server: ${server.name}", e)
                    }
                }

                android.util.Log.i(AppConfig.TAG, "Imported $importedCount servers from Firebase")
                Result.success(importedCount)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to import servers to V2rayNG", e)
                Result.failure(e)
            }
        }
    }
}
