// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRoutingEditBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final SwitchCompat chkLocked;

  @NonNull
  public final EditText etDomain;

  @NonNull
  public final EditText etIp;

  @NonNull
  public final EditText etNetwork;

  @NonNull
  public final EditText etPort;

  @NonNull
  public final EditText etProtocol;

  @NonNull
  public final EditText etRemarks;

  @NonNull
  public final Spinner spOutboundTag;

  private ActivityRoutingEditBinding(@NonNull ScrollView rootView, @NonNull SwitchCompat chkLocked,
      @NonNull EditText etDomain, @NonNull EditText etIp, @NonNull EditText etNetwork,
      @NonNull EditText etPort, @NonNull EditText etProtocol, @NonNull EditText etRemarks,
      @NonNull Spinner spOutboundTag) {
    this.rootView = rootView;
    this.chkLocked = chkLocked;
    this.etDomain = etDomain;
    this.etIp = etIp;
    this.etNetwork = etNetwork;
    this.etPort = etPort;
    this.etProtocol = etProtocol;
    this.etRemarks = etRemarks;
    this.spOutboundTag = spOutboundTag;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRoutingEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRoutingEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_routing_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRoutingEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chk_locked;
      SwitchCompat chkLocked = ViewBindings.findChildViewById(rootView, id);
      if (chkLocked == null) {
        break missingId;
      }

      id = R.id.et_domain;
      EditText etDomain = ViewBindings.findChildViewById(rootView, id);
      if (etDomain == null) {
        break missingId;
      }

      id = R.id.et_ip;
      EditText etIp = ViewBindings.findChildViewById(rootView, id);
      if (etIp == null) {
        break missingId;
      }

      id = R.id.et_network;
      EditText etNetwork = ViewBindings.findChildViewById(rootView, id);
      if (etNetwork == null) {
        break missingId;
      }

      id = R.id.et_port;
      EditText etPort = ViewBindings.findChildViewById(rootView, id);
      if (etPort == null) {
        break missingId;
      }

      id = R.id.et_protocol;
      EditText etProtocol = ViewBindings.findChildViewById(rootView, id);
      if (etProtocol == null) {
        break missingId;
      }

      id = R.id.et_remarks;
      EditText etRemarks = ViewBindings.findChildViewById(rootView, id);
      if (etRemarks == null) {
        break missingId;
      }

      id = R.id.sp_outbound_tag;
      Spinner spOutboundTag = ViewBindings.findChildViewById(rootView, id);
      if (spOutboundTag == null) {
        break missingId;
      }

      return new ActivityRoutingEditBinding((ScrollView) rootView, chkLocked, etDomain, etIp,
          etNetwork, etPort, etProtocol, etRemarks, spOutboundTag);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
