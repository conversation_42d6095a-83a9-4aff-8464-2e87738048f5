// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class WidgetSwitchBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView imageSwitch;

  @NonNull
  public final LinearLayout layoutBackground;

  @NonNull
  public final LinearLayout layoutSwitch;

  private WidgetSwitchBinding(@NonNull LinearLayout rootView, @NonNull ImageView imageSwitch,
      @NonNull LinearLayout layoutBackground, @NonNull LinearLayout layoutSwitch) {
    this.rootView = rootView;
    this.imageSwitch = imageSwitch;
    this.layoutBackground = layoutBackground;
    this.layoutSwitch = layoutSwitch;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static WidgetSwitchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static WidgetSwitchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.widget_switch, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static WidgetSwitchBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.image_switch;
      ImageView imageSwitch = ViewBindings.findChildViewById(rootView, id);
      if (imageSwitch == null) {
        break missingId;
      }

      id = R.id.layout_background;
      LinearLayout layoutBackground = ViewBindings.findChildViewById(rootView, id);
      if (layoutBackground == null) {
        break missingId;
      }

      LinearLayout layoutSwitch = (LinearLayout) rootView;

      return new WidgetSwitchBinding((LinearLayout) rootView, imageSwitch, layoutBackground,
          layoutSwitch);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
