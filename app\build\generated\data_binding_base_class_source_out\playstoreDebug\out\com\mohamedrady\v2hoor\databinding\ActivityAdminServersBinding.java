// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAdminServersBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonRefresh;

  @NonNull
  public final TextInputEditText editTextSearch;

  @NonNull
  public final FloatingActionButton fabAddServer;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewServers;

  @NonNull
  public final TextView textViewActiveServers;

  @NonNull
  public final TextView textViewInactiveServers;

  @NonNull
  public final TextView textViewServersCount;

  @NonNull
  public final TextView textViewTotalAssignments;

  @NonNull
  public final TextView textViewTotalServers;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAdminServersBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonRefresh, @NonNull TextInputEditText editTextSearch,
      @NonNull FloatingActionButton fabAddServer, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewServers, @NonNull TextView textViewActiveServers,
      @NonNull TextView textViewInactiveServers, @NonNull TextView textViewServersCount,
      @NonNull TextView textViewTotalAssignments, @NonNull TextView textViewTotalServers,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonRefresh = buttonRefresh;
    this.editTextSearch = editTextSearch;
    this.fabAddServer = fabAddServer;
    this.progressBar = progressBar;
    this.recyclerViewServers = recyclerViewServers;
    this.textViewActiveServers = textViewActiveServers;
    this.textViewInactiveServers = textViewInactiveServers;
    this.textViewServersCount = textViewServersCount;
    this.textViewTotalAssignments = textViewTotalAssignments;
    this.textViewTotalServers = textViewTotalServers;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAdminServersBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAdminServersBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_admin_servers, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAdminServersBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonRefresh;
      MaterialButton buttonRefresh = ViewBindings.findChildViewById(rootView, id);
      if (buttonRefresh == null) {
        break missingId;
      }

      id = R.id.editTextSearch;
      TextInputEditText editTextSearch = ViewBindings.findChildViewById(rootView, id);
      if (editTextSearch == null) {
        break missingId;
      }

      id = R.id.fabAddServer;
      FloatingActionButton fabAddServer = ViewBindings.findChildViewById(rootView, id);
      if (fabAddServer == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewServers;
      RecyclerView recyclerViewServers = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewServers == null) {
        break missingId;
      }

      id = R.id.textViewActiveServers;
      TextView textViewActiveServers = ViewBindings.findChildViewById(rootView, id);
      if (textViewActiveServers == null) {
        break missingId;
      }

      id = R.id.textViewInactiveServers;
      TextView textViewInactiveServers = ViewBindings.findChildViewById(rootView, id);
      if (textViewInactiveServers == null) {
        break missingId;
      }

      id = R.id.textViewServersCount;
      TextView textViewServersCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewServersCount == null) {
        break missingId;
      }

      id = R.id.textViewTotalAssignments;
      TextView textViewTotalAssignments = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotalAssignments == null) {
        break missingId;
      }

      id = R.id.textViewTotalServers;
      TextView textViewTotalServers = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotalServers == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAdminServersBinding((CoordinatorLayout) rootView, buttonRefresh,
          editTextSearch, fabAddServer, progressBar, recyclerViewServers, textViewActiveServers,
          textViewInactiveServers, textViewServersCount, textViewTotalAssignments,
          textViewTotalServers, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
