package com.mohamedrady.v2hoor.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemAdminUserBinding
import com.mohamedrady.v2hoor.dto.UserProfile

/**
 * Adapter for displaying users in admin panel
 */
class AdminUsersAdapter(
    private val onUserClick: (UserProfile) -> Unit,
    private val onToggleUserStatus: (UserProfile) -> Unit,
    private val onDeleteUser: (UserProfile) -> Unit,
    private val onManageServers: (UserProfile) -> Unit
) : ListAdapter<UserProfile, AdminUsersAdapter.UserViewHolder>(UserDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val binding = ItemAdminUserBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return UserViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class UserViewHolder(
        private val binding: ItemAdminUserBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(user: UserProfile) {
            binding.apply {
                // User basic info
                textViewUserName.text = user.getFormattedDisplayName()
                textViewUserEmail.text = user.email ?: "بريد غير محدد"
                
                // User status
                val statusColor = if (user.isActive) {
                    ContextCompat.getColor(root.context, R.color.green)
                } else {
                    ContextCompat.getColor(root.context, R.color.red)
                }
                textViewUserStatus.text = if (user.isActive) "نشط" else "غير نشط"
                textViewUserStatus.setTextColor(statusColor)
                
                // Subscription info
                textViewSubscriptionType.text = user.getSubscriptionStatusText()
                val subscriptionColor = when (user.subscriptionType) {
                    "premium" -> ContextCompat.getColor(root.context, R.color.orange)
                    "vip" -> ContextCompat.getColor(root.context, R.color.purple)
                    else -> ContextCompat.getColor(root.context, R.color.gray)
                }
                textViewSubscriptionType.setTextColor(subscriptionColor)
                
                // Server count
                textViewServerCount.text = "السيرفرات: ${user.serverCount}"
                
                // Creation date
                textViewCreationDate.text = "انضم: ${user.getFormattedCreationDate()}"
                
                // Last login
                textViewLastLogin.text = "آخر دخول: ${user.getFormattedLastLogin()}"
                
                // Role badge
                if (user.canAccessAdminFeatures()) {
                    textViewRole.visibility = android.view.View.VISIBLE
                    textViewRole.text = user.getRoleDisplayText()
                    textViewRole.setBackgroundColor(
                        ContextCompat.getColor(root.context, R.color.blue)
                    )
                } else {
                    textViewRole.visibility = android.view.View.GONE
                }
                
                // Click listeners
                root.setOnClickListener { onUserClick(user) }
                
                buttonToggleStatus.apply {
                    text = if (user.isActive) "إلغاء التفعيل" else "تفعيل"
                    setBackgroundColor(
                        if (user.isActive) {
                            ContextCompat.getColor(context, R.color.red)
                        } else {
                            ContextCompat.getColor(context, R.color.green)
                        }
                    )
                    setOnClickListener { onToggleUserStatus(user) }
                }
                
                buttonManageServers.setOnClickListener { onManageServers(user) }
                
                buttonDeleteUser.setOnClickListener { onDeleteUser(user) }
                
                // Card background based on status
                cardView.setCardBackgroundColor(
                    if (user.isActive) {
                        Color.WHITE
                    } else {
                        ContextCompat.getColor(root.context, R.color.light_gray)
                    }
                )
            }
        }
    }

    private class UserDiffCallback : DiffUtil.ItemCallback<UserProfile>() {
        override fun areItemsTheSame(oldItem: UserProfile, newItem: UserProfile): Boolean {
            return oldItem.uid == newItem.uid
        }

        override fun areContentsTheSame(oldItem: UserProfile, newItem: UserProfile): Boolean {
            return oldItem == newItem
        }
    }
}
