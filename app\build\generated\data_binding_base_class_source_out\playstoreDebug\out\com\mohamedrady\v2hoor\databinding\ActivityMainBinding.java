// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.tabs.TabLayout;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final FloatingActionButton fab;

  @NonNull
  public final LinearLayout layoutTest;

  @NonNull
  public final CoordinatorLayout mainContent;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final LinearProgressIndicator pbWaiting;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final TabLayout tabGroup;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvTestState;

  private ActivityMainBinding(@NonNull DrawerLayout rootView, @NonNull DrawerLayout drawerLayout,
      @NonNull FloatingActionButton fab, @NonNull LinearLayout layoutTest,
      @NonNull CoordinatorLayout mainContent, @NonNull NavigationView navView,
      @NonNull LinearProgressIndicator pbWaiting, @NonNull RecyclerView recyclerView,
      @NonNull TabLayout tabGroup, @NonNull Toolbar toolbar, @NonNull TextView tvTestState) {
    this.rootView = rootView;
    this.drawerLayout = drawerLayout;
    this.fab = fab;
    this.layoutTest = layoutTest;
    this.mainContent = mainContent;
    this.navView = navView;
    this.pbWaiting = pbWaiting;
    this.recyclerView = recyclerView;
    this.tabGroup = tabGroup;
    this.toolbar = toolbar;
    this.tvTestState = tvTestState;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.fab;
      FloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.layout_test;
      LinearLayout layoutTest = ViewBindings.findChildViewById(rootView, id);
      if (layoutTest == null) {
        break missingId;
      }

      id = R.id.main_content;
      CoordinatorLayout mainContent = ViewBindings.findChildViewById(rootView, id);
      if (mainContent == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.pb_waiting;
      LinearProgressIndicator pbWaiting = ViewBindings.findChildViewById(rootView, id);
      if (pbWaiting == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.tab_group;
      TabLayout tabGroup = ViewBindings.findChildViewById(rootView, id);
      if (tabGroup == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_test_state;
      TextView tvTestState = ViewBindings.findChildViewById(rootView, id);
      if (tvTestState == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, drawerLayout, fab, layoutTest,
          mainContent, navView, pbWaiting, recyclerView, tabGroup, toolbar, tvTestState);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
