// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRoutingSettingBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final LinearLayout layoutDomainStrategy;

  @NonNull
  public final NestedScrollView mainContent;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final TextView tvDomainStrategySummary;

  private ActivityRoutingSettingBinding(@NonNull RelativeLayout rootView,
      @NonNull LinearLayout layoutDomainStrategy, @NonNull NestedScrollView mainContent,
      @NonNull RecyclerView recyclerView, @NonNull TextView tvDomainStrategySummary) {
    this.rootView = rootView;
    this.layoutDomainStrategy = layoutDomainStrategy;
    this.mainContent = mainContent;
    this.recyclerView = recyclerView;
    this.tvDomainStrategySummary = tvDomainStrategySummary;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRoutingSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRoutingSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_routing_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRoutingSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layout_domain_strategy;
      LinearLayout layoutDomainStrategy = ViewBindings.findChildViewById(rootView, id);
      if (layoutDomainStrategy == null) {
        break missingId;
      }

      id = R.id.main_content;
      NestedScrollView mainContent = ViewBindings.findChildViewById(rootView, id);
      if (mainContent == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.tv_domain_strategy_summary;
      TextView tvDomainStrategySummary = ViewBindings.findChildViewById(rootView, id);
      if (tvDomainStrategySummary == null) {
        break missingId;
      }

      return new ActivityRoutingSettingBinding((RelativeLayout) rootView, layoutDomainStrategy,
          mainContent, recyclerView, tvDomainStrategySummary);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
