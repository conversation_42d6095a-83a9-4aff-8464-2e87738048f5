// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecyclerRoutingSettingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final SwitchCompat chkEnable;

  @NonNull
  public final TextView domainIp;

  @NonNull
  public final ImageView imgLocked;

  @NonNull
  public final LinearLayout infoContainer;

  @NonNull
  public final LinearLayout itemBg;

  @NonNull
  public final LinearLayout layoutEdit;

  @NonNull
  public final TextView outboundTag;

  @NonNull
  public final TextView remarks;

  private ItemRecyclerRoutingSettingBinding(@NonNull LinearLayout rootView,
      @NonNull SwitchCompat chkEnable, @NonNull TextView domainIp, @NonNull ImageView imgLocked,
      @NonNull LinearLayout infoContainer, @NonNull LinearLayout itemBg,
      @NonNull LinearLayout layoutEdit, @NonNull TextView outboundTag, @NonNull TextView remarks) {
    this.rootView = rootView;
    this.chkEnable = chkEnable;
    this.domainIp = domainIp;
    this.imgLocked = imgLocked;
    this.infoContainer = infoContainer;
    this.itemBg = itemBg;
    this.layoutEdit = layoutEdit;
    this.outboundTag = outboundTag;
    this.remarks = remarks;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecyclerRoutingSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecyclerRoutingSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recycler_routing_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecyclerRoutingSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chk_enable;
      SwitchCompat chkEnable = ViewBindings.findChildViewById(rootView, id);
      if (chkEnable == null) {
        break missingId;
      }

      id = R.id.domainIp;
      TextView domainIp = ViewBindings.findChildViewById(rootView, id);
      if (domainIp == null) {
        break missingId;
      }

      id = R.id.img_locked;
      ImageView imgLocked = ViewBindings.findChildViewById(rootView, id);
      if (imgLocked == null) {
        break missingId;
      }

      id = R.id.info_container;
      LinearLayout infoContainer = ViewBindings.findChildViewById(rootView, id);
      if (infoContainer == null) {
        break missingId;
      }

      LinearLayout itemBg = (LinearLayout) rootView;

      id = R.id.layout_edit;
      LinearLayout layoutEdit = ViewBindings.findChildViewById(rootView, id);
      if (layoutEdit == null) {
        break missingId;
      }

      id = R.id.outboundTag;
      TextView outboundTag = ViewBindings.findChildViewById(rootView, id);
      if (outboundTag == null) {
        break missingId;
      }

      id = R.id.remarks;
      TextView remarks = ViewBindings.findChildViewById(rootView, id);
      if (remarks == null) {
        break missingId;
      }

      return new ItemRecyclerRoutingSettingBinding((LinearLayout) rootView, chkEnable, domainIp,
          imgLocked, infoContainer, itemBg, layoutEdit, outboundTag, remarks);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
