// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityServerWireguardBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText etId;

  @NonNull
  public final EditText etLocalAddress;

  @NonNull
  public final EditText etLocalMtu;

  @NonNull
  public final EditText etPresharedKey;

  @NonNull
  public final EditText etPublicKey;

  @NonNull
  public final EditText etReserved1;

  private ActivityServerWireguardBinding(@NonNull ScrollView rootView, @NonNull EditText etId,
      @NonNull EditText etLocalAddress, @NonNull EditText etLocalMtu,
      @NonNull EditText etPresharedKey, @NonNull EditText etPublicKey,
      @NonNull EditText etReserved1) {
    this.rootView = rootView;
    this.etId = etId;
    this.etLocalAddress = etLocalAddress;
    this.etLocalMtu = etLocalMtu;
    this.etPresharedKey = etPresharedKey;
    this.etPublicKey = etPublicKey;
    this.etReserved1 = etReserved1;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityServerWireguardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityServerWireguardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_server_wireguard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityServerWireguardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_id;
      EditText etId = ViewBindings.findChildViewById(rootView, id);
      if (etId == null) {
        break missingId;
      }

      id = R.id.et_local_address;
      EditText etLocalAddress = ViewBindings.findChildViewById(rootView, id);
      if (etLocalAddress == null) {
        break missingId;
      }

      id = R.id.et_local_mtu;
      EditText etLocalMtu = ViewBindings.findChildViewById(rootView, id);
      if (etLocalMtu == null) {
        break missingId;
      }

      id = R.id.et_preshared_key;
      EditText etPresharedKey = ViewBindings.findChildViewById(rootView, id);
      if (etPresharedKey == null) {
        break missingId;
      }

      id = R.id.et_public_key;
      EditText etPublicKey = ViewBindings.findChildViewById(rootView, id);
      if (etPublicKey == null) {
        break missingId;
      }

      id = R.id.et_reserved1;
      EditText etReserved1 = ViewBindings.findChildViewById(rootView, id);
      if (etReserved1 == null) {
        break missingId;
      }

      return new ActivityServerWireguardBinding((ScrollView) rootView, etId, etLocalAddress,
          etLocalMtu, etPresharedKey, etPublicKey, etReserved1);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
