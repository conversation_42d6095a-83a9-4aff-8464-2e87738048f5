@echo off
echo ========================================
echo   V2HoorVPN Firebase Rules Deployment
echo ========================================
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Firebase CLI is not installed!
    echo Please install it with: npm install -g firebase-tools
    echo.
    pause
    exit /b 1
)

echo Firebase CLI found!
echo.

REM Login to Firebase (if not already logged in)
echo Checking Firebase authentication...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo Please login to Firebase...
    firebase login
    if %errorlevel% neq 0 (
        echo ERROR: Firebase login failed!
        pause
        exit /b 1
    )
)

echo Firebase authentication successful!
echo.

REM Set the Firebase project
echo Setting Firebase project to: mrelfeky-209615
firebase use mrelfeky-209615
if %errorlevel% neq 0 (
    echo ERROR: Failed to set Firebase project!
    echo Make sure the project ID 'mrelfeky-209615' is correct.
    pause
    exit /b 1
)

echo Project set successfully!
echo.

REM Deploy Firestore rules
echo Deploying Firestore security rules...
firebase deploy --only firestore:rules
if %errorlevel% neq 0 (
    echo ERROR: Failed to deploy Firestore rules!
    pause
    exit /b 1
)

echo Firestore rules deployed successfully!
echo.

REM Deploy Storage rules
echo Deploying Storage security rules...
firebase deploy --only storage
if %errorlevel% neq 0 (
    echo ERROR: Failed to deploy Storage rules!
    pause
    exit /b 1
)

echo Storage rules deployed successfully!
echo.

REM Deploy Firestore indexes
echo Deploying Firestore indexes...
firebase deploy --only firestore:indexes
if %errorlevel% neq 0 (
    echo WARNING: Failed to deploy Firestore indexes!
    echo This is not critical, but may affect query performance.
    echo.
)

echo.
echo ========================================
echo   Firebase Rules Deployment Complete!
echo ========================================
echo.
echo The following have been deployed:
echo - Firestore Security Rules
echo - Storage Security Rules  
echo - Firestore Indexes (if successful)
echo.
echo Your V2HoorVPN app is now secured with:
echo - Admin-only access controls
echo - User data protection
echo - Server configuration security
echo - Proper authentication checks
echo.
pause
