package com.mohamedrady.v2hoor.service

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.UserProfile
import com.mohamedrady.v2hoor.model.UserRole
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * User Role Management Service
 * Handles user roles, permissions, and profile management
 */
class UserRoleService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: UserRoleService? = null
        
        fun getInstance(context: Context): UserRoleService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UserRoleService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val PREF_NAME = "user_role_prefs"
        private const val KEY_CURRENT_USER_ROLE = "current_user_role"
        private const val KEY_CURRENT_USER_PROFILE = "current_user_profile"
        private const val USERS_PATH = "users"
    }
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    // LiveData for current user role and profile
    private val _currentUserRole = MutableLiveData<UserRole>()
    val currentUserRole: LiveData<UserRole> = _currentUserRole
    
    private val _currentUserProfile = MutableLiveData<UserProfile?>()
    val currentUserProfile: LiveData<UserProfile?> = _currentUserProfile
    
    private val _allUsers = MutableLiveData<List<UserProfile>>()
    val allUsers: LiveData<List<UserProfile>> = _allUsers
    
    /**
     * Initialize user role service
     */
    suspend fun initialize(): Result<UserProfile?> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    android.util.Log.w(AppConfig.TAG, "No authenticated user found")
                    return@withContext Result.failure(Exception("User not authenticated"))
                }
                
                // Load user profile from Firebase
                val profile = loadUserProfile(currentUser.uid)
                if (profile != null) {
                    // Update last login
                    profile.updateLastLogin()
                    updateUserProfile(profile)
                    
                    // Cache the profile and role
                    cacheUserProfile(profile)
                    _currentUserProfile.postValue(profile)
                    _currentUserRole.postValue(profile.getUserRole())
                    
                    android.util.Log.i(AppConfig.TAG, "User role initialized: ${profile.role} for ${profile.email}")
                    Result.success(profile)
                } else {
                    // Create default user profile if not exists
                    val newProfile = createDefaultUserProfile(currentUser.uid, currentUser.email ?: "")
                    val createResult = createUserProfile(newProfile)
                    
                    if (createResult.isSuccess) {
                        cacheUserProfile(newProfile)
                        _currentUserProfile.postValue(newProfile)
                        _currentUserRole.postValue(newProfile.getUserRole())
                        
                        android.util.Log.i(AppConfig.TAG, "Created new user profile: ${newProfile.email}")
                        Result.success(newProfile)
                    } else {
                        Result.failure(createResult.exceptionOrNull() ?: Exception("Failed to create user profile"))
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to initialize user role service", e)
                
                // Try to load from cache as fallback
                val cachedProfile = getCachedUserProfile()
                if (cachedProfile != null) {
                    _currentUserProfile.postValue(cachedProfile)
                    _currentUserRole.postValue(cachedProfile.getUserRole())
                    Result.success(cachedProfile)
                } else {
                    Result.failure(e)
                }
            }
        }
    }
    
    /**
     * Load user profile from Firebase
     */
    private suspend fun loadUserProfile(uid: String): UserProfile? {
        return suspendCancellableCoroutine { continuation ->
            val userRef = database.child(USERS_PATH).child(uid)
            
            val listener = object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    try {
                        val profile = snapshot.getValue(UserProfile::class.java)
                        if (profile != null) {
                            profile.uid = uid
                        }
                        continuation.resume(profile)
                    } catch (e: Exception) {
                        android.util.Log.e(AppConfig.TAG, "Error parsing user profile", e)
                        continuation.resumeWithException(e)
                    }
                }
                
                override fun onCancelled(error: DatabaseError) {
                    android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                    continuation.resumeWithException(error.toException())
                }
            }
            
            userRef.addListenerForSingleValueEvent(listener)
            
            continuation.invokeOnCancellation {
                userRef.removeEventListener(listener)
            }
        }
    }
    
    /**
     * Create default user profile
     */
    private fun createDefaultUserProfile(uid: String, email: String): UserProfile {
        val currentTime = System.currentTimeMillis()
        return UserProfile(
            uid = uid,
            email = email,
            name = email.substringBefore("@"),
            role = UserRole.USER.value,
            subscriptionEnd = "",
            isActive = true,
            createdAt = currentTime,
            updatedAt = currentTime,
            lastLogin = currentTime,
            subscriptionType = "basic",
            maxDevices = 1,
            dataLimitGb = 0,
            dataUsedGb = 0,
            notes = "تم إنشاء الحساب تلقائياً",
            createdBy = "system",
            avatarUrl = ""
        )
    }
    
    /**
     * Create user profile in Firebase
     */
    suspend fun createUserProfile(profile: UserProfile): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(profile.uid)
                    
                    userRef.setValue(profile)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "User profile created: ${profile.email}")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to create user profile", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update user profile in Firebase
     */
    suspend fun updateUserProfile(profile: UserProfile): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                profile.updatedAt = System.currentTimeMillis()
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(profile.uid)
                    
                    userRef.setValue(profile)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "User profile updated: ${profile.email}")
                            
                            // Update cache and LiveData
                            cacheUserProfile(profile)
                            _currentUserProfile.postValue(profile)
                            _currentUserRole.postValue(profile.getUserRole())
                            
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to update user profile", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get current user role
     */
    fun getCurrentUserRole(): UserRole {
        return _currentUserRole.value ?: UserRole.USER
    }
    
    /**
     * Get current user profile
     */
    fun getCurrentUserProfile(): UserProfile? {
        return _currentUserProfile.value
    }
    
    /**
     * Check if current user is admin
     */
    fun isCurrentUserAdmin(): Boolean {
        return getCurrentUserRole().isAdmin()
    }
    
    /**
     * Check if current user is regular user
     */
    fun isCurrentUserRegularUser(): Boolean {
        return getCurrentUserRole().isUser()
    }
    
    /**
     * Load all users (admin only)
     */
    suspend fun loadAllUsers(): Result<List<UserProfile>> {
        return withContext(Dispatchers.IO) {
            try {
                if (!isCurrentUserAdmin()) {
                    return@withContext Result.failure(Exception("Access denied: Admin role required"))
                }
                
                val users = suspendCancellableCoroutine<List<UserProfile>> { continuation ->
                    val usersRef = database.child(USERS_PATH)
                    
                    val listener = object : ValueEventListener {
                        override fun onDataChange(snapshot: DataSnapshot) {
                            try {
                                val userList = mutableListOf<UserProfile>()
                                
                                for (userSnapshot in snapshot.children) {
                                    val profile = userSnapshot.getValue(UserProfile::class.java)
                                    if (profile != null) {
                                        profile.uid = userSnapshot.key ?: ""
                                        userList.add(profile)
                                    }
                                }
                                
                                // Sort by creation date (newest first)
                                userList.sortByDescending { it.createdAt }
                                
                                android.util.Log.i(AppConfig.TAG, "Loaded ${userList.size} users")
                                continuation.resume(userList)
                            } catch (e: Exception) {
                                android.util.Log.e(AppConfig.TAG, "Error parsing users list", e)
                                continuation.resumeWithException(e)
                            }
                        }
                        
                        override fun onCancelled(error: DatabaseError) {
                            android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                            continuation.resumeWithException(error.toException())
                        }
                    }
                    
                    usersRef.addListenerForSingleValueEvent(listener)
                    
                    continuation.invokeOnCancellation {
                        usersRef.removeEventListener(listener)
                    }
                }
                
                _allUsers.postValue(users)
                Result.success(users)
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to load all users", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Delete user (admin only)
     */
    suspend fun deleteUser(uid: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                if (!isCurrentUserAdmin()) {
                    return@withContext Result.failure(Exception("Access denied: Admin role required"))
                }
                
                if (uid == auth.currentUser?.uid) {
                    return@withContext Result.failure(Exception("Cannot delete your own account"))
                }
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(uid)
                    
                    userRef.removeValue()
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "User deleted: $uid")
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to delete user", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Cache user profile locally
     */
    private fun cacheUserProfile(profile: UserProfile) {
        try {
            val gson = com.google.gson.Gson()
            val profileJson = gson.toJson(profile)
            
            prefs.edit()
                .putString(KEY_CURRENT_USER_ROLE, profile.role)
                .putString(KEY_CURRENT_USER_PROFILE, profileJson)
                .apply()
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to cache user profile", e)
        }
    }
    
    /**
     * Get cached user profile
     */
    private fun getCachedUserProfile(): UserProfile? {
        return try {
            val profileJson = prefs.getString(KEY_CURRENT_USER_PROFILE, null)
            if (profileJson != null) {
                val gson = com.google.gson.Gson()
                gson.fromJson(profileJson, UserProfile::class.java)
            } else {
                null
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to get cached user profile", e)
            null
        }
    }
    
    /**
     * Clear cached data
     */
    fun clearCache() {
        prefs.edit().clear().apply()
        _currentUserRole.postValue(UserRole.USER)
        _currentUserProfile.postValue(null)
        _allUsers.postValue(emptyList())
        android.util.Log.i(AppConfig.TAG, "User role cache cleared")
    }
    
    /**
     * Logout and clear data
     */
    fun logout() {
        clearCache()
        android.util.Log.i(AppConfig.TAG, "User logged out, role data cleared")
    }
}
