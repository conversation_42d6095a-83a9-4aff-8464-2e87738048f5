package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAdminUsersBinding
import com.mohamedrady.v2hoor.dto.UserProfile
import com.mohamedrady.v2hoor.service.AdminPermissionService
import com.mohamedrady.v2hoor.ui.adapter.AdminUsersAdapter
import com.mohamedrady.v2hoor.viewmodel.AdminUsersViewModel
import kotlinx.coroutines.launch

/**
 * Activity for managing users (Admin only)
 */
class AdminUsersActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdminUsersBinding
    private val viewModel: AdminUsersViewModel by viewModels()
    private lateinit var usersAdapter: AdminUsersAdapter
    private val adminService = AdminPermissionService.getInstance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Check admin permissions
        lifecycleScope.launch {
            if (!adminService.isAdmin()) {
                Toast.makeText(this@AdminUsersActivity, "غير مصرح لك بالوصول لهذه الصفحة", Toast.LENGTH_SHORT).show()
                finish()
                return@launch
            }
        }

        binding = ActivityAdminUsersBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupObservers()
        setupClickListeners()
        
        // Load users
        viewModel.loadUsers()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "إدارة المستخدمين"
        }
    }

    private fun setupRecyclerView() {
        usersAdapter = AdminUsersAdapter(
            onUserClick = { user -> showUserDetails(user) },
            onToggleUserStatus = { user -> toggleUserStatus(user) },
            onDeleteUser = { user -> confirmDeleteUser(user) },
            onManageServers = { user -> manageUserServers(user) }
        )

        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@AdminUsersActivity)
            adapter = usersAdapter
        }
    }

    private fun setupObservers() {
        viewModel.users.observe(this) { users ->
            usersAdapter.submitList(users)
            binding.textViewUsersCount.text = "إجمالي المستخدمين: ${users.size}"
        }

        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) 
                android.view.View.VISIBLE else android.view.View.GONE
        }

        viewModel.error.observe(this) { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
            }
        }

        viewModel.statistics.observe(this) { stats ->
            stats?.let { updateStatistics(it) }
        }
    }

    private fun setupClickListeners() {
        binding.fabAddUser.setOnClickListener {
            // TODO: Implement add user functionality
            Toast.makeText(this, "إضافة مستخدم جديد - قريباً", Toast.LENGTH_SHORT).show()
        }

        binding.buttonRefresh.setOnClickListener {
            viewModel.loadUsers()
        }

        binding.editTextSearch.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { viewModel.searchUsers(it) }
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText.isNullOrBlank()) {
                    viewModel.loadUsers()
                } else {
                    viewModel.searchUsers(newText)
                }
                return true
            }
        })
    }

    private fun updateStatistics(stats: Map<String, Any>) {
        binding.apply {
            textViewTotalUsers.text = "المجموع: ${stats["totalUsers"]}"
            textViewActiveUsers.text = "نشط: ${stats["activeUsers"]}"
            textViewInactiveUsers.text = "غير نشط: ${stats["inactiveUsers"]}"
            textViewPremiumUsers.text = "مميز: ${stats["premiumUsers"]}"
        }
    }

    private fun showUserDetails(user: UserProfile) {
        val intent = Intent(this, UserDetailsActivity::class.java).apply {
            putExtra("user_profile", user)
        }
        startActivity(intent)
    }

    private fun toggleUserStatus(user: UserProfile) {
        val newStatus = !user.isActive
        val message = if (newStatus) "تفعيل" else "إلغاء تفعيل"
        
        AlertDialog.Builder(this)
            .setTitle("تأكيد العملية")
            .setMessage("هل تريد $message المستخدم ${user.getFormattedDisplayName()}؟")
            .setPositiveButton("نعم") { _, _ ->
                viewModel.updateUserStatus(user.uid, newStatus)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun confirmDeleteUser(user: UserProfile) {
        lifecycleScope.launch {
            if (!adminService.isSuperAdmin()) {
                Toast.makeText(this@AdminUsersActivity, "فقط المدير العام يمكنه حذف المستخدمين", Toast.LENGTH_SHORT).show()
                return@launch
            }
        }

        AlertDialog.Builder(this)
            .setTitle("تحذير")
            .setMessage("هل تريد حذف المستخدم ${user.getFormattedDisplayName()} نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!")
            .setPositiveButton("حذف") { _, _ ->
                viewModel.deleteUser(user.uid)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun manageUserServers(user: UserProfile) {
        val intent = Intent(this, UserServersActivity::class.java).apply {
            putExtra("user_id", user.uid)
            putExtra("user_name", user.getFormattedDisplayName())
        }
        startActivity(intent)
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_admin_users, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_export -> {
                // TODO: Export users data
                Toast.makeText(this, "تصدير البيانات - قريباً", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_statistics -> {
                showStatisticsDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showStatisticsDialog() {
        viewModel.statistics.value?.let { stats ->
            val message = """
                إحصائيات المستخدمين:
                
                إجمالي المستخدمين: ${stats["totalUsers"]}
                المستخدمين النشطين: ${stats["activeUsers"]}
                المستخدمين غير النشطين: ${stats["inactiveUsers"]}
                المستخدمين المميزين: ${stats["premiumUsers"]}
                المستخدمين المجانيين: ${stats["freeUsers"]}
            """.trimIndent()

            AlertDialog.Builder(this)
                .setTitle("إحصائيات المستخدمين")
                .setMessage(message)
                .setPositiveButton("موافق", null)
                .show()
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning to activity
        viewModel.loadUsers()
    }
}
