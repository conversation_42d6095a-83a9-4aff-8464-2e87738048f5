{"users": {"ADMIN_UID_PLACEHOLDER": {"uid": "ADMIN_UID_PLACEHOLDER", "email": "<EMAIL>", "name": "محم<PERSON> رضا", "role": "admin", "subscription_end": "", "is_active": true, "created_at": 1704067200000, "updated_at": 1704067200000, "last_login": 1704067200000, "subscription_type": "unlimited", "max_devices": 999, "data_limit_gb": 0, "data_used_gb": 0, "notes": "المدير الرئيسي للنظام", "created_by": "system", "avatar_url": "", "servers": {"admin_server1": {"id": "admin_server1", "name": "Admin Test Server", "remarks": "Admin Only Server", "server": "admin.example.com", "server_port": 443, "protocol": "vmess", "uuid": "admin123-1234-1234-1234-123456789abc", "alter_id": 0, "security": "auto", "network": "ws", "header_type": "none", "request_host": "admin.example.com", "path": "/admin", "tls": "tls", "sni": "admin.example.com", "alpn": "", "fingerprint": "", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "US", "city": "Admin", "flag": "🔧", "is_active": true, "priority": 1, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 0, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "ADMIN_UID_PLACEHOLDER", "tags": ["admin", "test"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}}}, "USER_UID_PLACEHOLDER_1": {"uid": "USER_UID_PLACEHOLDER_1", "email": "<EMAIL>", "name": "مستخدم تجريبي 1", "role": "user", "subscription_end": "2025-12-31", "is_active": true, "created_at": 1704067200000, "updated_at": 1704067200000, "last_login": 1704067200000, "subscription_type": "premium", "max_devices": 3, "data_limit_gb": 100, "data_used_gb": 25, "notes": "مستخدم مميز", "created_by": "ADMIN_UID_PLACEHOLDER", "avatar_url": "", "servers": {"server1": {"id": "server1", "name": "US Server 1", "remarks": "United States - New York", "server": "us1.example.com", "server_port": 443, "protocol": "vmess", "uuid": "12345678-1234-1234-1234-123456789abc", "alter_id": 0, "security": "auto", "network": "ws", "header_type": "none", "request_host": "us1.example.com", "path": "/v2ray", "tls": "tls", "sni": "us1.example.com", "alpn": "", "fingerprint": "", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "US", "city": "New York", "flag": "🇺🇸", "is_active": true, "priority": 1, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 1735689600000, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER_1", "tags": ["premium", "fast"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}, "server2": {"id": "server2", "name": "UK Server 1", "remarks": "United Kingdom - London", "server": "uk1.example.com", "server_port": 443, "protocol": "vless", "uuid": "*************-4321-4321-cba987654321", "alter_id": 0, "security": "tls", "network": "tcp", "header_type": "none", "request_host": "", "path": "", "tls": "tls", "sni": "uk1.example.com", "alpn": "h2,http/1.1", "fingerprint": "chrome", "public_key": "", "short_id": "", "spider_x": "", "flow": "xtls-rprx-vision", "encryption": "none", "country": "UK", "city": "London", "flag": "🇬🇧", "is_active": true, "priority": 2, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 1735689600000, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER_1", "tags": ["premium", "streaming"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}}}, "USER_UID_PLACEHOLDER_2": {"uid": "USER_UID_PLACEHOLDER_2", "email": "<EMAIL>", "name": "مستخدم تجريبي 2", "role": "user", "subscription_end": "2025-06-30", "is_active": true, "created_at": 1704067200000, "updated_at": 1704067200000, "last_login": 1704067200000, "subscription_type": "basic", "max_devices": 1, "data_limit_gb": 50, "data_used_gb": 45, "notes": "مستخدم أساسي", "created_by": "ADMIN_UID_PLACEHOLDER", "avatar_url": "", "servers": {"server3": {"id": "server3", "name": "Germany Server 1", "remarks": "Germany - Frankfurt", "server": "de1.example.com", "server_port": 443, "protocol": "trojan", "uuid": "abcdef12-3456-7890-abcd-ef1234567890", "alter_id": 0, "security": "tls", "network": "tcp", "header_type": "none", "request_host": "", "path": "", "tls": "tls", "sni": "de1.example.com", "alpn": "h2,http/1.1", "fingerprint": "chrome", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "DE", "city": "Frankfurt", "flag": "🇩🇪", "is_active": true, "priority": 3, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 0, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER_2", "tags": ["basic", "europe"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}}}, "USER_UID_PLACEHOLDER_3": {"uid": "USER_UID_PLACEHOLDER_3", "email": "<EMAIL>", "name": "مستخدم منتهي الصلاحية", "role": "user", "subscription_end": "2024-01-01", "is_active": false, "created_at": 1704067200000, "updated_at": 1704067200000, "last_login": 1704067200000, "subscription_type": "basic", "max_devices": 1, "data_limit_gb": 10, "data_used_gb": 12, "notes": "حساب منتهي الصلاحية للاختبار", "created_by": "ADMIN_UID_PLACEHOLDER", "avatar_url": "", "servers": {}}}}