1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohamedrady.v2hoor"
4    android:versionCode="4000657"
5    android:versionName="1.10.7" >
6
7    <uses-sdk
7-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
8        android:minSdkVersion="23"
8-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
9        android:targetSdkVersion="35" />
10
11    <supports-screens
11-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
12        android:anyDensity="true"
12-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
13        android:largeScreens="true"
13-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
14        android:normalScreens="true"
14-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
15        android:smallScreens="true"
15-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
16        android:xlargeScreens="true" />
16-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
17
18    <uses-feature
18-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
19        android:name="android.hardware.camera"
19-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
20        android:required="false" />
20-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
21    <uses-feature
21-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
22        android:name="android.hardware.camera.autofocus"
22-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
23        android:required="false" />
23-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
24    <uses-feature
24-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
25        android:name="android.software.leanback"
25-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
26        android:required="false" />
26-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
27    <uses-feature
27-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
28        android:name="android.hardware.touchscreen"
28-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
29        android:required="false" />
29-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
30
31    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.INTERNET" />
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
37    <uses-permission android:name="android.permission.CAMERA" />
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
38    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
39    <uses-permission
39-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
40        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
40-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
41        android:minSdkVersion="34" />
41-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
42    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
46-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:25:22-65
47    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
47-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
47-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
48    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
49    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
49-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
49-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
50    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
50-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
51    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
51-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\526acd70ff7892f7cdfe42b01c80492d\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
52
53    <permission
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
54        android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
58
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
60        android:name="android.hardware.camera.front"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
63        android:name="android.hardware.camera.flash"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
65    <uses-feature
65-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
66        android:name="android.hardware.screen.landscape"
66-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
67        android:required="false" />
67-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
68    <uses-feature
68-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
69        android:name="android.hardware.wifi"
69-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
70        android:required="false" />
70-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
71
72    <application
72-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:48:5-274:19
73        android:name="com.mohamedrady.v2hoor.AngApplication"
73-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:9-39
74        android:allowBackup="true"
74-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-35
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d304b09fc6c5965909d0ef57a0fa0ff6\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
76        android:banner="@mipmap/ic_banner"
76-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-43
77        android:debuggable="true"
78        android:extractNativeLibs="true"
79        android:icon="@mipmap/ic_launcher"
79-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
80        android:label="@string/app_name"
80-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-41
81        android:networkSecurityConfig="@xml/network_security_config"
81-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-69
82        android:supportsRtl="true"
82-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-35
83        android:theme="@style/AppThemeDayNight"
83-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-48
84        android:usesCleartextTraffic="true" >
84-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-44
85        <activity
85-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:60:9-78:20
86            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:13-44
87            android:exported="true"
87-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-36
88            android:launchMode="singleTask"
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-44
89            android:theme="@style/AppThemeDayNight.NoActionBar" >
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-64
90            <intent-filter>
90-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-70:29
91                <action android:name="android.intent.action.MAIN" />
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:17-69
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:17-77
93-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:27-74
94                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
94-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-86
94-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-83
95            </intent-filter>
96            <intent-filter>
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:71:13-73:29
97                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:17-99
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:25-96
98            </intent-filter>
99
100            <meta-data
100-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:75:13-77:53
101                android:name="android.app.shortcuts"
101-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:17-53
102                android:resource="@xml/shortcuts" />
102-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-50
103        </activity>
104        <activity
104-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:79:9-82:60
105            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
105-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:13-46
106            android:exported="false"
106-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-37
107            android:windowSoftInputMode="stateUnchanged" />
107-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-57
108        <activity
108-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:9-86:60
109            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
109-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:13-58
110            android:exported="false"
110-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-37
111            android:windowSoftInputMode="stateUnchanged" />
111-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-57
112        <activity
112-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:9-89:40
113            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
113-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:13-48
114            android:exported="false" />
114-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-37
115        <activity
115-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:9-92:40
116            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
116-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:13-51
117            android:exported="false" />
117-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-37
118        <activity
118-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:9-95:40
119            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
119-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:13-47
120            android:exported="false" />
120-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-37
121        <activity
121-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:9-98:40
122            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
122-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:13-46
123            android:exported="false" />
123-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-37
124        <activity
124-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:9-101:40
125            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
125-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:13-54
126            android:exported="false" />
126-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-37
127        <activity
127-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:9-104:40
128            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
128-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:13-51
129            android:exported="false" />
129-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-37
130        <activity
130-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:9-107:40
131            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
131-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:13-50
132            android:exported="false" />
132-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-37
133        <activity
133-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:9-110:40
134            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
134-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:13-49
135            android:exported="false" />
135-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-37
136        <activity
136-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:9-113:40
137            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
137-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:13-52
138            android:exported="false" />
138-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-37
139        <activity
139-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:115:9-117:40
140            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
140-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:13-47
141            android:exported="false" />
141-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-37
142        <activity
142-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:9-120:40
143            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
143-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:13-49
144            android:exported="false" />
144-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-37
145        <activity
145-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:9-126:71
146            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
146-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:13-48
147            android:excludeFromRecents="true"
147-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-46
148            android:exported="false"
148-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-37
149            android:process=":RunSoLibV2RayDaemon"
149-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-51
150            android:theme="@style/AppTheme.NoActionBar.Translucent" />
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-68
151        <activity
151-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:128:9-146:20
152            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:13-49
153            android:exported="true" >
153-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-36
154            <intent-filter>
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-135:29
155                <action android:name="android.intent.action.SEND" />
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:17-69
155-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:25-66
156
157                <category android:name="android.intent.category.DEFAULT" />
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
158
159                <data android:mimeType="text/plain" />
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:23-52
160            </intent-filter>
161            <intent-filter>
161-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:136:13-145:29
162                <action android:name="android.intent.action.VIEW" />
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:17-69
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:25-66
163
164                <category android:name="android.intent.category.BROWSABLE" />
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:17-78
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:27-75
165                <category android:name="android.intent.category.DEFAULT" />
165-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
165-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
166
167                <data android:scheme="v2rayng" />
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:142:23-47
168                <data android:host="install-config" />
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
169                <data android:host="install-sub" />
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
170            </intent-filter>
171        </activity>
172        <activity
172-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:147:9-149:40
173            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
173-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:13-51
174            android:exported="false" />
174-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-37
175        <activity
175-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:9-152:40
176            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
176-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:13-45
177            android:exported="false" />
177-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-37
178        <activity
178-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:9-156:67
179            android:name="com.mohamedrady.v2hoor.ui.LoginActivity"
179-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:13-45
180            android:exported="false"
180-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-37
181            android:theme="@style/AppThemeDayNight.NoActionBar" />
181-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-64
182
183        <service
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-175:19
184            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
184-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-52
185            android:enabled="true"
185-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-35
186            android:exported="false"
186-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-37
187            android:foregroundServiceType="specialUse"
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:13-55
188            android:label="@string/app_name"
188-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-45
189            android:permission="android.permission.BIND_VPN_SERVICE"
189-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-69
190            android:process=":RunSoLibV2RayDaemon" >
190-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-51
191            <intent-filter>
191-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:13-168:29
192                <action android:name="android.net.VpnService" />
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:17-65
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:25-62
193            </intent-filter>
194
195            <meta-data
195-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-171:40
196                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
196-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:17-73
197                android:value="true" />
197-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:17-37
198
199            <property
199-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
200                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
200-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
201                android:value="vpn" />
201-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
202        </service>
203        <service
203-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:9-186:19
204            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
204-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:13-58
205            android:exported="false"
205-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-37
206            android:foregroundServiceType="specialUse"
206-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-55
207            android:label="@string/app_name"
207-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-45
208            android:process=":RunSoLibV2RayDaemon" >
208-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:13-51
209            <property
209-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
210                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
210-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
211                android:value="proxy" />
211-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
212        </service>
213        <service
213-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:9-191:54
214            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
214-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-53
215            android:exported="false"
215-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:13-37
216            android:process=":RunSoLibV2RayDaemon" />
216-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-51
217
218        <receiver
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:9-205:20
219            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:13-52
220            android:exported="true"
220-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-36
221            android:process=":RunSoLibV2RayDaemon" >
221-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-51
222            <meta-data
222-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-199:63
223                android:name="android.appwidget.provider"
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:17-58
224                android:resource="@xml/app_widget_provider" />
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:17-60
225
226            <intent-filter>
226-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-204:29
227                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:17-84
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:25-81
228                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:17-85
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:25-82
229                <action android:name="com.mohamedrady.v2hoor.action.activity" />
229-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:17-81
229-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:25-78
230            </intent-filter>
231        </receiver>
232        <receiver
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-213:20
233            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
233-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-50
234            android:exported="true"
234-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-36
235            android:label="BootReceiver" >
235-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-41
236            <intent-filter>
236-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:13-212:29
237                <action android:name="android.intent.action.BOOT_COMPLETED" />
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:17-79
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:25-76
238            </intent-filter>
239        </receiver>
240
241        <service
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:9-230:19
242            android:name="com.mohamedrady.v2hoor.service.QSTileService"
242-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-50
243            android:exported="true"
243-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-36
244            android:foregroundServiceType="specialUse"
244-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:13-55
245            android:icon="@drawable/ic_stat_name"
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-50
246            android:label="@string/app_tile_name"
246-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-50
247            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
247-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-77
248            android:process=":RunSoLibV2RayDaemon" >
248-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:222:13-51
249            <intent-filter>
249-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-226:29
250                <action android:name="android.service.quicksettings.action.QS_TILE" />
250-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:17-87
250-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:25-84
251            </intent-filter>
252
253            <property
253-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
254                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
255                android:value="tile" />
255-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
256        </service>
257        <!-- =====================Tasker===================== -->
258        <activity
258-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:9-239:20
259            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
259-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:13-46
260            android:exported="true"
260-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-36
261            android:icon="@mipmap/ic_launcher" >
261-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:13-47
262            <intent-filter>
262-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:13-238:29
263                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:17-95
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:25-92
264            </intent-filter>
265        </activity>
266
267        <receiver
267-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:9-249:20
268            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
268-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:13-52
269            android:exported="true"
269-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:13-36
270            android:process=":RunSoLibV2RayDaemon" >
270-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:244:13-51
271            <intent-filter>
271-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:13-248:29
272                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
272-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:17-95
272-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:25-92
273            </intent-filter>
274        </receiver>
275        <!-- =====================Tasker===================== -->
276        <provider
277            android:name="androidx.startup.InitializationProvider"
277-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:252:13-67
278            android:authorities="com.mohamedrady.v2hoor.androidx-startup"
278-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:253:13-68
279            android:exported="false" >
279-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:254:13-37
280            <meta-data
280-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
281                android:name="androidx.emoji2.text.EmojiCompatInitializer"
281-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
282                android:value="androidx.startup" />
282-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\31731f64f20ca48acdb112bd960c0eb0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
283            <meta-data
283-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
284                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
284-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
285                android:value="androidx.startup" />
285-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c46b5d28e761f398a018e8e06bfa4d19\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
286            <meta-data
286-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
287                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
287-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
288                android:value="androidx.startup" />
288-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
289        </provider>
290        <provider
291            android:name="androidx.core.content.FileProvider"
291-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-62
292            android:authorities="com.mohamedrady.v2hoor.cache"
292-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:13-57
293            android:exported="false"
293-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:13-37
294            android:grantUriPermissions="true" >
294-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:13-47
295            <meta-data
295-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:269:13-271:55
296                android:name="android.support.FILE_PROVIDER_PATHS"
296-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:17-67
297                android:resource="@xml/cache_paths" />
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:17-52
298        </provider>
299
300        <activity
300-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
301            android:name="io.github.g00fy2.quickie.QRScannerActivity"
301-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
302            android:screenOrientation="behind"
302-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
303            android:theme="@style/QuickieScannerActivity" />
303-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\76702c707d43210cfa7dee4ec2c1641f\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
304        <!--
305        Service for holding metadata. Cannot be instantiated.
306        Metadata will be merged from other manifests.
307        -->
308        <service
308-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
309            android:name="androidx.camera.core.impl.MetadataHolderService"
309-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
310            android:enabled="false"
310-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
311            android:exported="false" >
311-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7d515466246bfeaad41d5ca5412343cf\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
312            <meta-data
312-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
313                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
313-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
314                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
314-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36c76e706bd2961bbdc07a7f7aa852e5\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
315        </service>
316        <service
316-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:8:9-14:19
317            android:name="com.google.firebase.components.ComponentDiscoveryService"
317-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:9:13-84
318            android:directBootAware="true"
318-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
319            android:exported="false" >
319-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
320            <meta-data
320-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
321                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
321-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
322                android:value="com.google.firebase.components.ComponentRegistrar" />
322-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9e50baba9a9eb34a2a484e978a8f15c1\transformed\firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
323            <meta-data
323-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
324                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
324-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
325                android:value="com.google.firebase.components.ComponentRegistrar" />
325-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
326            <meta-data
326-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:11:13-13:85
327                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
327-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:12:17-129
328                android:value="com.google.firebase.components.ComponentRegistrar" />
328-->[com.google.firebase:firebase-analytics-ktx:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ccd9f2ea821224d162b497e60f6aefa\transformed\firebase-analytics-ktx-22.5.0\AndroidManifest.xml:13:17-82
329            <meta-data
329-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
330                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
330-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
331                android:value="com.google.firebase.components.ComponentRegistrar" />
331-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d707204c856363c23646dd0a871a3e2\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
332            <meta-data
332-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
333                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
333-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
334                android:value="com.google.firebase.components.ComponentRegistrar" />
334-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b82b6623623a1cfe2ce9d87dfec648e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
335            <meta-data
335-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:12:13-14:85
336                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
336-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:13:17-129
337                android:value="com.google.firebase.components.ComponentRegistrar" />
337-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c2af6b4318b8aa3f071cef49d3eccf8\transformed\firebase-firestore-ktx-25.1.4\AndroidManifest.xml:14:17-82
338            <meta-data
338-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
339                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
339-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
340                android:value="com.google.firebase.components.ComponentRegistrar" />
340-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
341            <meta-data
341-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
342                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
342-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
343                android:value="com.google.firebase.components.ComponentRegistrar" />
343-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4519aef3e62d009b210d8b93d2deca84\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
344            <meta-data
344-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
345                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
345-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
346                android:value="com.google.firebase.components.ComponentRegistrar" />
346-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
347            <meta-data
347-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
348                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
348-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
349                android:value="com.google.firebase.components.ComponentRegistrar" />
349-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\02b9ca03b6c81aa854239e80ed186df0\transformed\firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
350            <meta-data
350-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
351                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
351-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
352                android:value="com.google.firebase.components.ComponentRegistrar" />
352-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
353            <meta-data
353-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
354                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
354-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
355                android:value="com.google.firebase.components.ComponentRegistrar" />
355-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8bbb01b881618fb451db81496ff59ad9\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
356            <meta-data
356-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
357                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
357-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
358                android:value="com.google.firebase.components.ComponentRegistrar" />
358-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8deaa3b9fa91f3fa99b5c205f2701561\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
359            <meta-data
359-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
360                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
360-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
361                android:value="com.google.firebase.components.ComponentRegistrar" />
361-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
362        </service>
363
364        <activity
364-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
365            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
365-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
366            android:excludeFromRecents="true"
366-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
367            android:exported="true"
367-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
368            android:launchMode="singleTask"
368-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
369            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
369-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
370            <intent-filter>
370-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
371                <action android:name="android.intent.action.VIEW" />
371-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:17-69
371-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:25-66
372
373                <category android:name="android.intent.category.DEFAULT" />
373-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
373-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
374                <category android:name="android.intent.category.BROWSABLE" />
374-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:17-78
374-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:27-75
375
376                <data
376-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
377                    android:host="firebase.auth"
377-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
378                    android:path="/"
379                    android:scheme="genericidp" />
379-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:142:23-47
380            </intent-filter>
381        </activity>
382        <activity
382-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
383            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
383-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
384            android:excludeFromRecents="true"
384-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
385            android:exported="true"
385-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
386            android:launchMode="singleTask"
386-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
387            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
387-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
388            <intent-filter>
388-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4603d676a530a6da7d642c62f6dba820\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
389                <action android:name="android.intent.action.VIEW" />
389-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:17-69
389-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:25-66
390
391                <category android:name="android.intent.category.DEFAULT" />
391-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
391-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
392                <category android:name="android.intent.category.BROWSABLE" />
392-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:17-78
392-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:27-75
393
394                <data
394-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
395                    android:host="firebase.auth"
395-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
396                    android:path="/"
397                    android:scheme="recaptcha" />
397-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:142:23-47
398            </intent-filter>
399        </activity>
400
401        <service
401-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
402            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
402-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
403            android:enabled="true"
403-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
404            android:exported="false" >
404-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
405            <meta-data
405-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
406                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
406-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
407                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
407-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
408        </service>
409
410        <activity
410-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
411            android:name="androidx.credentials.playservices.HiddenActivity"
411-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
412            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
412-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
413            android:enabled="true"
413-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
414            android:exported="false"
414-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
415            android:fitsSystemWindows="true"
415-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
416            android:theme="@style/Theme.Hidden" >
416-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f7eb1da5e42044f662b8e59e06978dfd\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
417        </activity>
418        <activity
418-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
419            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
419-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
420            android:excludeFromRecents="true"
420-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
421            android:exported="false"
421-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
422            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
422-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
423        <!--
424            Service handling Google Sign-In user revocation. For apps that do not integrate with
425            Google Sign-In, this service will never be started.
426        -->
427        <service
427-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
428            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
428-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
429            android:exported="true"
429-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
430            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
430-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
431            android:visibleToInstantApps="true" />
431-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a14be4331047d7200d6deae3060c2ed3\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
432
433        <receiver
433-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
434            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
434-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
435            android:enabled="true"
435-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
436            android:exported="false" >
436-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
437        </receiver>
438
439        <service
439-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
440            android:name="com.google.android.gms.measurement.AppMeasurementService"
440-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
441            android:enabled="true"
441-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
442            android:exported="false" />
442-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
443        <service
443-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
444            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
444-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
445            android:enabled="true"
445-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
446            android:exported="false"
446-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
447            android:permission="android.permission.BIND_JOB_SERVICE" />
447-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bd389d1c83839cab651e7e139c1259e0\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
448
449        <provider
449-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
450            android:name="com.google.firebase.provider.FirebaseInitProvider"
450-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
451            android:authorities="com.mohamedrady.v2hoor.firebaseinitprovider"
451-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
452            android:directBootAware="true"
452-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
453            android:exported="false"
453-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
454            android:initOrder="100" />
454-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\307c4e69a22222fd8b2b5d0daec3d8db\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
455
456        <service
456-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
457            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
457-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
458            android:exported="false" />
458-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eda4c25cfb3849f432c1669702edf89c\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
459        <service
459-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
460            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
460-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
462            android:enabled="@bool/enable_system_alarm_service_default"
462-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
463            android:exported="false" />
463-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
464        <service
464-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
465            android:name="androidx.work.impl.background.systemjob.SystemJobService"
465-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
466            android:directBootAware="false"
466-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
467            android:enabled="@bool/enable_system_job_service_default"
467-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
468            android:exported="true"
468-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
469            android:permission="android.permission.BIND_JOB_SERVICE" />
469-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
470        <service
470-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
471            android:name="androidx.work.impl.foreground.SystemForegroundService"
471-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
472            android:directBootAware="false"
472-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
473            android:enabled="@bool/enable_system_foreground_service_default"
473-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
474            android:exported="false" />
474-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
475
476        <receiver
476-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
477            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
477-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
478            android:directBootAware="false"
478-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
479            android:enabled="true"
479-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
480            android:exported="false" />
480-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
481        <receiver
481-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
482            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
482-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
483            android:directBootAware="false"
483-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
484            android:enabled="false"
484-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
485            android:exported="false" >
485-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
486            <intent-filter>
486-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
487                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
487-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
487-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
488                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
488-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
488-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
489            </intent-filter>
490        </receiver>
491        <receiver
491-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
492            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
492-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
493            android:directBootAware="false"
493-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
494            android:enabled="false"
494-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
495            android:exported="false" >
495-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
496            <intent-filter>
496-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
497                <action android:name="android.intent.action.BATTERY_OKAY" />
497-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
497-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
498                <action android:name="android.intent.action.BATTERY_LOW" />
498-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
498-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
499            </intent-filter>
500        </receiver>
501        <receiver
501-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
502            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
502-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
503            android:directBootAware="false"
503-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
504            android:enabled="false"
504-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
505            android:exported="false" >
505-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
506            <intent-filter>
506-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
507                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
507-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
507-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
508                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
508-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
508-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
509            </intent-filter>
510        </receiver>
511        <receiver
511-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
512            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
512-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
513            android:directBootAware="false"
513-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
514            android:enabled="false"
514-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
515            android:exported="false" >
515-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
516            <intent-filter>
516-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
517                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
517-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
517-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
518            </intent-filter>
519        </receiver>
520        <receiver
520-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
521            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
521-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
522            android:directBootAware="false"
522-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
523            android:enabled="false"
523-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
524            android:exported="false" >
524-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
525            <intent-filter>
525-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
526                <action android:name="android.intent.action.BOOT_COMPLETED" />
526-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:17-79
526-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:25-76
527                <action android:name="android.intent.action.TIME_SET" />
527-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
527-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
528                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
528-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
528-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
529            </intent-filter>
530        </receiver>
531        <receiver
531-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
532            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
532-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
533            android:directBootAware="false"
533-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
534            android:enabled="@bool/enable_system_alarm_service_default"
534-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
535            android:exported="false" >
535-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
536            <intent-filter>
536-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
537                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
537-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
537-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
538            </intent-filter>
539        </receiver>
540        <receiver
540-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
541            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
541-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
542            android:directBootAware="false"
542-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
543            android:enabled="true"
543-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
544            android:exported="true"
544-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
545            android:permission="android.permission.DUMP" >
545-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
546            <intent-filter>
546-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
547                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
547-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
547-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8db806c88c82d97fba3e067706ad47ec\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
548            </intent-filter>
549        </receiver>
550
551        <uses-library
551-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
552            android:name="androidx.window.extensions"
552-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
553            android:required="false" />
553-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
554        <uses-library
554-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
555            android:name="androidx.window.sidecar"
555-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
556            android:required="false" />
556-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\700cabc0e517e16b3b1a2efd32cecc35\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
557
558        <activity
558-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
559            android:name="com.google.android.gms.common.api.GoogleApiActivity"
559-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
560            android:exported="false"
560-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
561            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
561-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d5b14458b73464e26d2134afde20b1\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
562
563        <service
563-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
564            android:name="androidx.room.MultiInstanceInvalidationService"
564-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
565            android:directBootAware="true"
565-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
566            android:exported="false" />
566-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e9fb008114b0136c1375ee2e657a444e\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
567
568        <uses-library
568-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
569            android:name="android.ext.adservices"
569-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
570            android:required="false" />
570-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b77d8fa079b259506bae30589a35736f\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
571
572        <receiver
572-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
573            android:name="androidx.profileinstaller.ProfileInstallReceiver"
573-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
574            android:directBootAware="false"
574-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
575            android:enabled="true"
575-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
576            android:exported="true"
576-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
577            android:permission="android.permission.DUMP" >
577-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
578            <intent-filter>
578-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
579                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
579-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
579-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
580            </intent-filter>
581            <intent-filter>
581-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
582                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
582-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
582-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
583            </intent-filter>
584            <intent-filter>
584-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
585                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
585-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
585-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
586            </intent-filter>
587            <intent-filter>
587-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
588                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
588-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
588-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3bd633d74419b92e583c7336cb183ab1\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
589            </intent-filter>
590        </receiver>
591
592        <meta-data
592-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
593            android:name="com.google.android.gms.version"
593-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
594            android:value="@integer/google_play_services_version" />
594-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4578899047b14a577ffdc3b874f24398\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
595
596        <activity
596-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
597            android:name="com.journeyapps.barcodescanner.CaptureActivity"
597-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
598            android:clearTaskOnLaunch="true"
598-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
599            android:screenOrientation="sensorLandscape"
599-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
600            android:stateNotNeeded="true"
600-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
601            android:theme="@style/zxing_CaptureTheme"
601-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
602            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- The activities will be merged into the manifest of the hosting app. -->
602-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ebcc5988bd502c901a17c4d3fc1eb859\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
603        <activity
603-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
604            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
604-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
605            android:exported="false"
605-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
606            android:stateNotNeeded="true"
606-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
607            android:theme="@style/Theme.PlayCore.Transparent" />
607-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\59e516bb99fd3e6425d2c9627b07a4fa\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
608    </application>
609
610</manifest>
