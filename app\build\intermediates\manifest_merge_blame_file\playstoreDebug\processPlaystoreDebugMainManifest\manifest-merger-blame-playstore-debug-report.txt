1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohamedrady.v2hoor"
4    android:versionCode="4000657"
5    android:versionName="1.10.7" >
6
7    <uses-sdk
7-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
8        android:minSdkVersion="21"
8-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
9        android:targetSdkVersion="35" />
10
11    <supports-screens
11-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
12        android:anyDensity="true"
12-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
13        android:largeScreens="true"
13-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
14        android:normalScreens="true"
14-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
15        android:smallScreens="true"
15-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
16        android:xlargeScreens="true" />
16-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
17
18    <uses-feature
18-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
19        android:name="android.hardware.camera"
19-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
20        android:required="false" />
20-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
21    <uses-feature
21-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
22        android:name="android.hardware.camera.autofocus"
22-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
23        android:required="false" />
23-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
24    <uses-feature
24-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
25        android:name="android.software.leanback"
25-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
26        android:required="false" />
26-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
27    <uses-feature
27-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
28        android:name="android.hardware.touchscreen"
28-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
29        android:required="false" />
29-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
30
31    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.INTERNET" />
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
37    <uses-permission android:name="android.permission.CAMERA" />
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
38    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
39    <uses-permission
39-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
40        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
40-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
41        android:minSdkVersion="34" />
41-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
42    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
46-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
47
48    <permission
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
49        android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
53
54    <uses-feature
54-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
55        android:name="android.hardware.camera.front"
55-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
56        android:required="false" />
56-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
57    <uses-feature
57-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
58        android:name="android.hardware.camera.flash"
58-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
59        android:required="false" />
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
60    <uses-feature
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
61        android:name="android.hardware.screen.landscape"
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
62        android:required="false" />
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
63    <uses-feature
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
64        android:name="android.hardware.wifi"
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
65        android:required="false" />
65-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
66
67    <application
67-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:48:5-274:19
68        android:name="com.mohamedrady.v2hoor.AngApplication"
68-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:9-39
69        android:allowBackup="true"
69-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-35
70        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
71        android:banner="@mipmap/ic_banner"
71-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-43
72        android:debuggable="true"
73        android:extractNativeLibs="true"
74        android:icon="@mipmap/ic_launcher"
74-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
75        android:label="@string/app_name"
75-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-41
76        android:networkSecurityConfig="@xml/network_security_config"
76-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-69
77        android:supportsRtl="true"
77-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-35
78        android:theme="@style/AppThemeDayNight"
78-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-48
79        android:usesCleartextTraffic="true" >
79-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-44
80        <activity
80-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:60:9-78:20
81            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
81-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:13-44
82            android:exported="true"
82-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-36
83            android:launchMode="singleTask"
83-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-44
84            android:theme="@style/AppThemeDayNight.NoActionBar" >
84-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-64
85            <intent-filter>
85-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-70:29
86                <action android:name="android.intent.action.MAIN" />
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:17-69
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:25-66
87
88                <category android:name="android.intent.category.LAUNCHER" />
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:17-77
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:27-74
89                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-86
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-83
90            </intent-filter>
91            <intent-filter>
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:71:13-73:29
92                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
92-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:17-99
92-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:25-96
93            </intent-filter>
94
95            <meta-data
95-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:75:13-77:53
96                android:name="android.app.shortcuts"
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:17-53
97                android:resource="@xml/shortcuts" />
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-50
98        </activity>
99        <activity
99-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:79:9-82:60
100            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
100-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:13-46
101            android:exported="false"
101-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-37
102            android:windowSoftInputMode="stateUnchanged" />
102-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-57
103        <activity
103-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:9-86:60
104            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
104-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:13-58
105            android:exported="false"
105-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-37
106            android:windowSoftInputMode="stateUnchanged" />
106-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-57
107        <activity
107-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:9-89:40
108            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
108-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:13-48
109            android:exported="false" />
109-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-37
110        <activity
110-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:9-92:40
111            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
111-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:13-51
112            android:exported="false" />
112-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-37
113        <activity
113-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:9-95:40
114            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
114-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:13-47
115            android:exported="false" />
115-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-37
116        <activity
116-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:9-98:40
117            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
117-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:13-46
118            android:exported="false" />
118-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-37
119        <activity
119-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:9-101:40
120            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
120-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:13-54
121            android:exported="false" />
121-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-37
122        <activity
122-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:9-104:40
123            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
123-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:13-51
124            android:exported="false" />
124-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-37
125        <activity
125-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:9-107:40
126            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
126-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:13-50
127            android:exported="false" />
127-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-37
128        <activity
128-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:9-110:40
129            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
129-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:13-49
130            android:exported="false" />
130-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-37
131        <activity
131-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:9-113:40
132            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
132-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:13-52
133            android:exported="false" />
133-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-37
134        <activity
134-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:115:9-117:40
135            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
135-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:13-47
136            android:exported="false" />
136-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-37
137        <activity
137-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:9-120:40
138            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
138-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:13-49
139            android:exported="false" />
139-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-37
140        <activity
140-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:9-126:71
141            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
141-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:13-48
142            android:excludeFromRecents="true"
142-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-46
143            android:exported="false"
143-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-37
144            android:process=":RunSoLibV2RayDaemon"
144-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-51
145            android:theme="@style/AppTheme.NoActionBar.Translucent" />
145-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-68
146        <activity
146-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:128:9-146:20
147            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
147-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:13-49
148            android:exported="true" >
148-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-36
149            <intent-filter>
149-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-135:29
150                <action android:name="android.intent.action.SEND" />
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:17-69
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:25-66
151
152                <category android:name="android.intent.category.DEFAULT" />
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
153
154                <data android:mimeType="text/plain" />
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:23-52
155            </intent-filter>
156            <intent-filter>
156-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:136:13-145:29
157                <action android:name="android.intent.action.VIEW" />
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:17-69
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:25-66
158
159                <category android:name="android.intent.category.BROWSABLE" />
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:17-78
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:27-75
160                <category android:name="android.intent.category.DEFAULT" />
160-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
160-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
161
162                <data android:scheme="v2rayng" />
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:142:23-47
163                <data android:host="install-config" />
163-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
163-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
164                <data android:host="install-sub" />
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
165            </intent-filter>
166        </activity>
167        <activity
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:147:9-149:40
168            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:13-51
169            android:exported="false" />
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-37
170        <activity
170-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:9-152:40
171            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
171-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:13-45
172            android:exported="false" />
172-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-37
173        <activity
173-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:9-156:55
174            android:name="com.mohamedrady.v2hoor.ui.LoginActivity"
174-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:13-45
175            android:exported="false"
175-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-37
176            android:theme="@style/AppThemeDayNight" />
176-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-52
177
178        <service
178-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-175:19
179            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
179-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-52
180            android:enabled="true"
180-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-35
181            android:exported="false"
181-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-37
182            android:foregroundServiceType="specialUse"
182-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:13-55
183            android:label="@string/app_name"
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-45
184            android:permission="android.permission.BIND_VPN_SERVICE"
184-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-69
185            android:process=":RunSoLibV2RayDaemon" >
185-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-51
186            <intent-filter>
186-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:13-168:29
187                <action android:name="android.net.VpnService" />
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:17-65
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:25-62
188            </intent-filter>
189
190            <meta-data
190-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-171:40
191                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
191-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:17-73
192                android:value="true" />
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:17-37
193
194            <property
194-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
195                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
195-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
196                android:value="vpn" />
196-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
197        </service>
198        <service
198-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:9-186:19
199            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
199-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:13-58
200            android:exported="false"
200-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-37
201            android:foregroundServiceType="specialUse"
201-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-55
202            android:label="@string/app_name"
202-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-45
203            android:process=":RunSoLibV2RayDaemon" >
203-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:13-51
204            <property
204-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
205                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
205-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
206                android:value="proxy" />
206-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
207        </service>
208        <service
208-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:9-191:54
209            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
209-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-53
210            android:exported="false"
210-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:13-37
211            android:process=":RunSoLibV2RayDaemon" />
211-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-51
212
213        <receiver
213-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:9-205:20
214            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
214-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:13-52
215            android:exported="true"
215-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-36
216            android:process=":RunSoLibV2RayDaemon" >
216-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-51
217            <meta-data
217-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-199:63
218                android:name="android.appwidget.provider"
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:17-58
219                android:resource="@xml/app_widget_provider" />
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:17-60
220
221            <intent-filter>
221-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-204:29
222                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
222-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:17-84
222-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:25-81
223                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:17-85
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:25-82
224                <action android:name="com.mohamedrady.v2hoor.action.activity" />
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:17-81
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:25-78
225            </intent-filter>
226        </receiver>
227        <receiver
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-213:20
228            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-50
229            android:exported="true"
229-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-36
230            android:label="BootReceiver" >
230-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-41
231            <intent-filter>
231-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:13-212:29
232                <action android:name="android.intent.action.BOOT_COMPLETED" />
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:17-79
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:25-76
233            </intent-filter>
234        </receiver>
235
236        <service
236-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:9-230:19
237            android:name="com.mohamedrady.v2hoor.service.QSTileService"
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-50
238            android:exported="true"
238-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-36
239            android:foregroundServiceType="specialUse"
239-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:13-55
240            android:icon="@drawable/ic_stat_name"
240-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-50
241            android:label="@string/app_tile_name"
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-50
242            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
242-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-77
243            android:process=":RunSoLibV2RayDaemon" >
243-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:222:13-51
244            <intent-filter>
244-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-226:29
245                <action android:name="android.service.quicksettings.action.QS_TILE" />
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:17-87
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:25-84
246            </intent-filter>
247
248            <property
248-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
249                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
249-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
250                android:value="tile" />
250-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
251        </service>
252        <!-- =====================Tasker===================== -->
253        <activity
253-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:9-239:20
254            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:13-46
255            android:exported="true"
255-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-36
256            android:icon="@mipmap/ic_launcher" >
256-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:13-47
257            <intent-filter>
257-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:13-238:29
258                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
258-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:17-95
258-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:25-92
259            </intent-filter>
260        </activity>
261
262        <receiver
262-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:9-249:20
263            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:13-52
264            android:exported="true"
264-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:13-36
265            android:process=":RunSoLibV2RayDaemon" >
265-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:244:13-51
266            <intent-filter>
266-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:13-248:29
267                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
267-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:17-95
267-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:25-92
268            </intent-filter>
269        </receiver>
270        <!-- =====================Tasker===================== -->
271        <provider
272            android:name="androidx.startup.InitializationProvider"
272-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:252:13-67
273            android:authorities="com.mohamedrady.v2hoor.androidx-startup"
273-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:253:13-68
274            android:exported="false" >
274-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:254:13-37
275            <meta-data
275-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
276                android:name="androidx.emoji2.text.EmojiCompatInitializer"
276-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
277                android:value="androidx.startup" />
277-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
278            <meta-data
278-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
279                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
279-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
280                android:value="androidx.startup" />
280-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
281            <meta-data
281-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
282                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
282-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
283                android:value="androidx.startup" />
283-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
284            <meta-data
284-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
285                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
285-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
286                android:value="androidx.startup" />
286-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
287            <meta-data
287-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
288                android:name="com.russhwolf.settings.SettingsInitializer"
288-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
289                android:value="androidx.startup" />
289-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
290        </provider>
291        <provider
292            android:name="androidx.core.content.FileProvider"
292-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-62
293            android:authorities="com.mohamedrady.v2hoor.cache"
293-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:13-57
294            android:exported="false"
294-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:13-37
295            android:grantUriPermissions="true" >
295-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:13-47
296            <meta-data
296-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:269:13-271:55
297                android:name="android.support.FILE_PROVIDER_PATHS"
297-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:17-67
298                android:resource="@xml/cache_paths" />
298-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:17-52
299        </provider>
300
301        <activity
301-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
302            android:name="io.github.g00fy2.quickie.QRScannerActivity"
302-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
303            android:screenOrientation="behind"
303-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
304            android:theme="@style/QuickieScannerActivity" />
304-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
305        <!--
306        Service for holding metadata. Cannot be instantiated.
307        Metadata will be merged from other manifests.
308        -->
309        <service
309-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
310            android:name="androidx.camera.core.impl.MetadataHolderService"
310-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
311            android:enabled="false"
311-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
312            android:exported="false" >
312-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
313            <meta-data
313-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
314                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
314-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
315                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
315-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
316        </service>
317        <service
317-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
318            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
318-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
319            android:exported="false" />
319-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
320        <service
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
321            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
323            android:enabled="@bool/enable_system_alarm_service_default"
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
324            android:exported="false" />
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
325        <service
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
326            android:name="androidx.work.impl.background.systemjob.SystemJobService"
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
328            android:enabled="@bool/enable_system_job_service_default"
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
329            android:exported="true"
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
330            android:permission="android.permission.BIND_JOB_SERVICE" />
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
331        <service
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
332            android:name="androidx.work.impl.foreground.SystemForegroundService"
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
334            android:enabled="@bool/enable_system_foreground_service_default"
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
335            android:exported="false" />
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
336
337        <receiver
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
338            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
340            android:enabled="true"
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
341            android:exported="false" />
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
342        <receiver
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
345            android:enabled="false"
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
348                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
349                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
350            </intent-filter>
351        </receiver>
352        <receiver
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
353            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
354            android:directBootAware="false"
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
355            android:enabled="false"
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
356            android:exported="false" >
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
357            <intent-filter>
357-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
358                <action android:name="android.intent.action.BATTERY_OKAY" />
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
359                <action android:name="android.intent.action.BATTERY_LOW" />
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
360            </intent-filter>
361        </receiver>
362        <receiver
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
363            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
365            android:enabled="false"
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
366            android:exported="false" >
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
367            <intent-filter>
367-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
368                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
369                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
369-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
369-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
370            </intent-filter>
371        </receiver>
372        <receiver
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
373            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
375            android:enabled="false"
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
376            android:exported="false" >
376-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
377            <intent-filter>
377-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
378                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
379            </intent-filter>
380        </receiver>
381        <receiver
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
382            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
383            android:directBootAware="false"
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
384            android:enabled="false"
384-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
385            android:exported="false" >
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
386            <intent-filter>
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
387                <action android:name="android.intent.action.BOOT_COMPLETED" />
387-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:17-79
387-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:25-76
388                <action android:name="android.intent.action.TIME_SET" />
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
389                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
389-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
389-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
390            </intent-filter>
391        </receiver>
392        <receiver
392-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
393            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
395            android:enabled="@bool/enable_system_alarm_service_default"
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
396            android:exported="false" >
396-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
397            <intent-filter>
397-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
398                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
398-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
398-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
399            </intent-filter>
400        </receiver>
401        <receiver
401-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
402            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
402-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
404            android:enabled="true"
404-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
405            android:exported="true"
405-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
406            android:permission="android.permission.DUMP" >
406-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
407            <intent-filter>
407-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
408                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
408-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
408-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
409            </intent-filter>
410        </receiver>
411
412        <uses-library
412-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
413            android:name="androidx.window.extensions"
413-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
414            android:required="false" />
414-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
415        <uses-library
415-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
416            android:name="androidx.window.sidecar"
416-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
417            android:required="false" />
417-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
418
419        <receiver
419-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
420            android:name="androidx.profileinstaller.ProfileInstallReceiver"
420-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
421            android:directBootAware="false"
421-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
422            android:enabled="true"
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
423            android:exported="true"
423-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
424            android:permission="android.permission.DUMP" >
424-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
425            <intent-filter>
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
426                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
426-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
426-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
427            </intent-filter>
428            <intent-filter>
428-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
429                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
429-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
429-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
430            </intent-filter>
431            <intent-filter>
431-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
432                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
432-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
432-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
433            </intent-filter>
434            <intent-filter>
434-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
435                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
435-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
435-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
436            </intent-filter>
437        </receiver>
438
439        <service
439-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
440            android:name="androidx.room.MultiInstanceInvalidationService"
440-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
441            android:directBootAware="true"
441-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
442            android:exported="false" />
442-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
443
444        <activity
444-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
445            android:name="com.journeyapps.barcodescanner.CaptureActivity"
445-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
446            android:clearTaskOnLaunch="true"
446-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
447            android:screenOrientation="sensorLandscape"
447-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
448            android:stateNotNeeded="true"
448-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
449            android:theme="@style/zxing_CaptureTheme"
449-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
450            android:windowSoftInputMode="stateAlwaysHidden" />
450-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
451    </application>
452
453</manifest>
