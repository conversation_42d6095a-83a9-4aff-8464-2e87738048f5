1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohamedrady.v2hoor"
4    android:versionCode="4000657"
5    android:versionName="1.10.7" >
6
7    <uses-sdk
7-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
8        android:minSdkVersion="21"
8-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
9        android:targetSdkVersion="35" />
10
11    <supports-screens
11-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
12        android:anyDensity="true"
12-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
13        android:largeScreens="true"
13-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
14        android:normalScreens="true"
14-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
15        android:smallScreens="true"
15-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
16        android:xlargeScreens="true" />
16-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
17
18    <uses-feature
18-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
19        android:name="android.hardware.camera"
19-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
20        android:required="false" />
20-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
21    <uses-feature
21-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
22        android:name="android.hardware.camera.autofocus"
22-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
23        android:required="false" />
23-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
24    <uses-feature
24-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
25        android:name="android.software.leanback"
25-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
26        android:required="false" />
26-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
27    <uses-feature
27-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
28        android:name="android.hardware.touchscreen"
28-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
29        android:required="false" />
29-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
30
31    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
32    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
32-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
33-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
34-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.INTERNET" />
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
35-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
36    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
36-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
37    <uses-permission android:name="android.permission.CAMERA" />
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
37-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
38    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
38-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
39    <uses-permission
39-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
40        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
40-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
41        android:minSdkVersion="34" />
41-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
42    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
43-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
44-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
45-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
46-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
47
48    <permission
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
49        android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
53
54    <uses-feature
54-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
55        android:name="android.hardware.camera.front"
55-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
56        android:required="false" />
56-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
57    <uses-feature
57-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
58        android:name="android.hardware.camera.flash"
58-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
59        android:required="false" />
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
60    <uses-feature
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
61        android:name="android.hardware.screen.landscape"
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
62        android:required="false" />
62-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
63    <uses-feature
63-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
64        android:name="android.hardware.wifi"
64-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
65        android:required="false" />
65-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
66
67    <application
67-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:48:5-270:19
68        android:name="com.mohamedrady.v2hoor.AngApplication"
68-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:9-39
69        android:allowBackup="true"
69-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-35
70        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
71        android:banner="@mipmap/ic_banner"
71-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-43
72        android:debuggable="true"
73        android:extractNativeLibs="true"
74        android:icon="@mipmap/ic_launcher"
74-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
75        android:label="@string/app_name"
75-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-41
76        android:networkSecurityConfig="@xml/network_security_config"
76-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-69
77        android:supportsRtl="true"
77-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-35
78        android:theme="@style/AppThemeDayNight"
78-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-48
79        android:usesCleartextTraffic="true" >
79-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-44
80        <activity
80-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:60:9-78:20
81            android:name="com.mohamedrady.v2hoor.ui.MainActivity"
81-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:13-44
82            android:exported="true"
82-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-36
83            android:launchMode="singleTask"
83-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-44
84            android:theme="@style/AppThemeDayNight.NoActionBar" >
84-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-64
85            <intent-filter>
85-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-70:29
86                <action android:name="android.intent.action.MAIN" />
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:17-69
86-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:25-66
87
88                <category android:name="android.intent.category.LAUNCHER" />
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:17-77
88-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:27-74
89                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-86
89-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-83
90            </intent-filter>
91            <intent-filter>
91-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:71:13-73:29
92                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
92-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:17-99
92-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:25-96
93            </intent-filter>
94
95            <meta-data
95-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:75:13-77:53
96                android:name="android.app.shortcuts"
96-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:17-53
97                android:resource="@xml/shortcuts" />
97-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-50
98        </activity>
99        <activity
99-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:79:9-82:60
100            android:name="com.mohamedrady.v2hoor.ui.ServerActivity"
100-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:13-46
101            android:exported="false"
101-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-37
102            android:windowSoftInputMode="stateUnchanged" />
102-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-57
103        <activity
103-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:9-86:60
104            android:name="com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity"
104-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:13-58
105            android:exported="false"
105-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-37
106            android:windowSoftInputMode="stateUnchanged" />
106-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-57
107        <activity
107-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:9-89:40
108            android:name="com.mohamedrady.v2hoor.ui.SettingsActivity"
108-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:13-48
109            android:exported="false" />
109-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-37
110        <activity
110-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:9-92:40
111            android:name="com.mohamedrady.v2hoor.ui.PerAppProxyActivity"
111-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:13-51
112            android:exported="false" />
112-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-37
113        <activity
113-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:9-95:40
114            android:name="com.mohamedrady.v2hoor.ui.ScannerActivity"
114-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:13-47
115            android:exported="false" />
115-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-37
116        <activity
116-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:9-98:40
117            android:name="com.mohamedrady.v2hoor.ui.LogcatActivity"
117-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:13-46
118            android:exported="false" />
118-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-37
119        <activity
119-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:9-101:40
120            android:name="com.mohamedrady.v2hoor.ui.RoutingSettingActivity"
120-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:13-54
121            android:exported="false" />
121-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-37
122        <activity
122-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:9-104:40
123            android:name="com.mohamedrady.v2hoor.ui.RoutingEditActivity"
123-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:13-51
124            android:exported="false" />
124-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-37
125        <activity
125-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:9-107:40
126            android:name="com.mohamedrady.v2hoor.ui.SubSettingActivity"
126-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:13-50
127            android:exported="false" />
127-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-37
128        <activity
128-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:9-110:40
129            android:name="com.mohamedrady.v2hoor.ui.UserAssetActivity"
129-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:13-49
130            android:exported="false" />
130-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-37
131        <activity
131-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:9-113:40
132            android:name="com.mohamedrady.v2hoor.ui.UserAssetUrlActivity"
132-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:13-52
133            android:exported="false" />
133-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-37
134        <activity
134-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:115:9-117:40
135            android:name="com.mohamedrady.v2hoor.ui.SubEditActivity"
135-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:13-47
136            android:exported="false" />
136-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-37
137        <activity
137-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:9-120:40
138            android:name="com.mohamedrady.v2hoor.ui.ScScannerActivity"
138-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:13-49
139            android:exported="false" />
139-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-37
140        <activity
140-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:9-126:71
141            android:name="com.mohamedrady.v2hoor.ui.ScSwitchActivity"
141-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:13-48
142            android:excludeFromRecents="true"
142-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-46
143            android:exported="false"
143-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-37
144            android:process=":RunSoLibV2RayDaemon"
144-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-51
145            android:theme="@style/AppTheme.NoActionBar.Translucent" />
145-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-68
146        <activity
146-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:128:9-146:20
147            android:name="com.mohamedrady.v2hoor.ui.UrlSchemeActivity"
147-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:13-49
148            android:exported="true" >
148-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-36
149            <intent-filter>
149-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-135:29
150                <action android:name="android.intent.action.SEND" />
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:17-69
150-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:25-66
151
152                <category android:name="android.intent.category.DEFAULT" />
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
152-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
153
154                <data android:mimeType="text/plain" />
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
154-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:23-52
155            </intent-filter>
156            <intent-filter>
156-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:136:13-145:29
157                <action android:name="android.intent.action.VIEW" />
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:17-69
157-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:25-66
158
159                <category android:name="android.intent.category.BROWSABLE" />
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:17-78
159-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:27-75
160                <category android:name="android.intent.category.DEFAULT" />
160-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
160-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
161
162                <data android:scheme="v2rayng" />
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
162-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:142:23-47
163                <data android:host="install-config" />
163-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
163-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
164                <data android:host="install-sub" />
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
164-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
165            </intent-filter>
166        </activity>
167        <activity
167-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:147:9-149:40
168            android:name="com.mohamedrady.v2hoor.ui.CheckUpdateActivity"
168-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:13-51
169            android:exported="false" />
169-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-37
170        <activity
170-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:9-152:40
171            android:name="com.mohamedrady.v2hoor.ui.AboutActivity"
171-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:13-45
172            android:exported="false" />
172-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-37
173
174        <service
174-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:9-171:19
175            android:name="com.mohamedrady.v2hoor.service.V2RayVpnService"
175-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-52
176            android:enabled="true"
176-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-35
177            android:exported="false"
177-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:157:13-37
178            android:foregroundServiceType="specialUse"
178-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:13-55
179            android:label="@string/app_name"
179-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-45
180            android:permission="android.permission.BIND_VPN_SERVICE"
180-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-69
181            android:process=":RunSoLibV2RayDaemon" >
181-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-51
182            <intent-filter>
182-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:13-164:29
183                <action android:name="android.net.VpnService" />
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:17-65
183-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:25-62
184            </intent-filter>
185
186            <meta-data
186-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-167:40
187                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
187-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:17-73
188                android:value="true" />
188-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:17-37
189
190            <property
190-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-170:39
191                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
191-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:17-76
192                android:value="vpn" />
192-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:17-36
193        </service>
194        <service
194-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:9-182:19
195            android:name="com.mohamedrady.v2hoor.service.V2RayProxyOnlyService"
195-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:13-58
196            android:exported="false"
196-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:175:13-37
197            android:foregroundServiceType="specialUse"
197-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:176:13-55
198            android:label="@string/app_name"
198-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:13-45
199            android:process=":RunSoLibV2RayDaemon" >
199-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:13-51
200            <property
200-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-170:39
201                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
201-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:17-76
202                android:value="proxy" />
202-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:17-36
203        </service>
204        <service
204-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:184:9-187:54
205            android:name="com.mohamedrady.v2hoor.service.V2RayTestService"
205-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:185:13-53
206            android:exported="false"
206-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:186:13-37
207            android:process=":RunSoLibV2RayDaemon" />
207-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:187:13-51
208
209        <receiver
209-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:9-201:20
210            android:name="com.mohamedrady.v2hoor.receiver.WidgetProvider"
210-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:13-52
211            android:exported="true"
211-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-36
212            android:process=":RunSoLibV2RayDaemon" >
212-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:192:13-51
213            <meta-data
213-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:13-195:63
214                android:name="android.appwidget.provider"
214-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:17-58
215                android:resource="@xml/app_widget_provider" />
215-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:17-60
216
217            <intent-filter>
217-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-200:29
218                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:17-84
218-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:25-81
219                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:17-85
219-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:25-82
220                <action android:name="com.mohamedrady.v2hoor.action.activity" />
220-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:17-81
220-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:25-78
221            </intent-filter>
222        </receiver>
223        <receiver
223-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:9-209:20
224            android:name="com.mohamedrady.v2hoor.receiver.BootReceiver"
224-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:13-50
225            android:exported="true"
225-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:204:13-36
226            android:label="BootReceiver" >
226-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:205:13-41
227            <intent-filter>
227-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:13-208:29
228                <action android:name="android.intent.action.BOOT_COMPLETED" />
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:17-79
228-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:25-76
229            </intent-filter>
230        </receiver>
231
232        <service
232-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:9-226:19
233            android:name="com.mohamedrady.v2hoor.service.QSTileService"
233-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:212:13-50
234            android:exported="true"
234-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:213:13-36
235            android:foregroundServiceType="specialUse"
235-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:214:13-55
236            android:icon="@drawable/ic_stat_name"
236-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:13-50
237            android:label="@string/app_tile_name"
237-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-50
238            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
238-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-77
239            android:process=":RunSoLibV2RayDaemon" >
239-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:13-51
240            <intent-filter>
240-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-222:29
241                <action android:name="android.service.quicksettings.action.QS_TILE" />
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:17-87
241-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:25-84
242            </intent-filter>
243
244            <property
244-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:168:13-170:39
245                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
245-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:17-76
246                android:value="tile" />
246-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:17-36
247        </service>
248        <!-- =====================Tasker===================== -->
249        <activity
249-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:228:9-235:20
250            android:name="com.mohamedrady.v2hoor.ui.TaskerActivity"
250-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:229:13-46
251            android:exported="true"
251-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:230:13-36
252            android:icon="@mipmap/ic_launcher" >
252-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:231:13-47
253            <intent-filter>
253-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:13-234:29
254                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:17-95
254-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:25-92
255            </intent-filter>
256        </activity>
257
258        <receiver
258-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:9-245:20
259            android:name="com.mohamedrady.v2hoor.receiver.TaskerReceiver"
259-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:238:13-52
260            android:exported="true"
260-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:239:13-36
261            android:process=":RunSoLibV2RayDaemon" >
261-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:240:13-51
262            <intent-filter>
262-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:13-244:29
263                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:17-95
263-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:25-92
264            </intent-filter>
265        </receiver>
266        <!-- =====================Tasker===================== -->
267        <provider
268            android:name="androidx.startup.InitializationProvider"
268-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:248:13-67
269            android:authorities="com.mohamedrady.v2hoor.androidx-startup"
269-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:249:13-68
270            android:exported="false" >
270-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:250:13-37
271            <meta-data
271-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
272                android:name="androidx.emoji2.text.EmojiCompatInitializer"
272-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
273                android:value="androidx.startup" />
273-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
274            <meta-data
274-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
275                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
275-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
276                android:value="androidx.startup" />
276-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
277            <meta-data
277-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
278                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
278-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
279                android:value="androidx.startup" />
279-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
280        </provider>
281        <provider
282            android:name="androidx.core.content.FileProvider"
282-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:261:13-62
283            android:authorities="com.mohamedrady.v2hoor.cache"
283-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:262:13-57
284            android:exported="false"
284-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:263:13-37
285            android:grantUriPermissions="true" >
285-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:264:13-47
286            <meta-data
286-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-267:55
287                android:name="android.support.FILE_PROVIDER_PATHS"
287-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:17-67
288                android:resource="@xml/cache_paths" />
288-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:17-52
289        </provider>
290
291        <activity
291-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
292            android:name="io.github.g00fy2.quickie.QRScannerActivity"
292-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
293            android:screenOrientation="behind"
293-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
294            android:theme="@style/QuickieScannerActivity" />
294-->[com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
295        <!--
296        Service for holding metadata. Cannot be instantiated.
297        Metadata will be merged from other manifests.
298        -->
299        <service
299-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
300            android:name="androidx.camera.core.impl.MetadataHolderService"
300-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
301            android:enabled="false"
301-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
302            android:exported="false" >
302-->[androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
303            <meta-data
303-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
304                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
304-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
305                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
305-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
306        </service>
307        <service
307-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
308            android:name="androidx.work.multiprocess.RemoteWorkManagerService"
308-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
309            android:exported="false" />
309-->[androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
310        <service
310-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
311            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
313            android:enabled="@bool/enable_system_alarm_service_default"
313-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
314            android:exported="false" />
314-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
315        <service
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
316            android:name="androidx.work.impl.background.systemjob.SystemJobService"
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
317            android:directBootAware="false"
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
318            android:enabled="@bool/enable_system_job_service_default"
318-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
319            android:exported="true"
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
320            android:permission="android.permission.BIND_JOB_SERVICE" />
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
321        <service
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
322            android:name="androidx.work.impl.foreground.SystemForegroundService"
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
324            android:enabled="@bool/enable_system_foreground_service_default"
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
325            android:exported="false" />
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
326
327        <receiver
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
328            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
330            android:enabled="true"
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
331            android:exported="false" />
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
332        <receiver
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
333            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
335            android:enabled="false"
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
336            android:exported="false" >
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
337            <intent-filter>
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
338                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
339                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
340            </intent-filter>
341        </receiver>
342        <receiver
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
345            android:enabled="false"
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
348                <action android:name="android.intent.action.BATTERY_OKAY" />
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
349                <action android:name="android.intent.action.BATTERY_LOW" />
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
350            </intent-filter>
351        </receiver>
352        <receiver
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
353            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
354            android:directBootAware="false"
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
355            android:enabled="false"
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
356            android:exported="false" >
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
357            <intent-filter>
357-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
358                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
359                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
360            </intent-filter>
361        </receiver>
362        <receiver
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
363            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
365            android:enabled="false"
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
366            android:exported="false" >
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
367            <intent-filter>
367-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
368                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
369            </intent-filter>
370        </receiver>
371        <receiver
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
372            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
373            android:directBootAware="false"
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
374            android:enabled="false"
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
375            android:exported="false" >
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
376            <intent-filter>
376-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
377                <action android:name="android.intent.action.BOOT_COMPLETED" />
377-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:17-79
377-->D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:25-76
378                <action android:name="android.intent.action.TIME_SET" />
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
379                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
379-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
379-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
383            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
385            android:enabled="@bool/enable_system_alarm_service_default"
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
388                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
389            </intent-filter>
390        </receiver>
391        <receiver
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
392            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
392-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
393            android:directBootAware="false"
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
394            android:enabled="true"
394-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
395            android:exported="true"
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
396            android:permission="android.permission.DUMP" >
396-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
397            <intent-filter>
397-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
398                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
398-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
398-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
399            </intent-filter>
400        </receiver>
401
402        <uses-library
402-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
403            android:name="androidx.window.extensions"
403-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
404            android:required="false" />
404-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
405        <uses-library
405-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
406            android:name="androidx.window.sidecar"
406-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
407            android:required="false" />
407-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
408
409        <receiver
409-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
410            android:name="androidx.profileinstaller.ProfileInstallReceiver"
410-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
411            android:directBootAware="false"
411-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
412            android:enabled="true"
412-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
413            android:exported="true"
413-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
414            android:permission="android.permission.DUMP" >
414-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
415            <intent-filter>
415-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
416                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
416-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
416-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
417            </intent-filter>
418            <intent-filter>
418-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
419                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
419-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
419-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
420            </intent-filter>
421            <intent-filter>
421-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
422                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
422-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
423            </intent-filter>
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
425                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
425-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
426            </intent-filter>
427        </receiver>
428
429        <service
429-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
430            android:name="androidx.room.MultiInstanceInvalidationService"
430-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
431            android:directBootAware="true"
431-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
432            android:exported="false" />
432-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
433
434        <activity
434-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
435            android:name="com.journeyapps.barcodescanner.CaptureActivity"
435-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
436            android:clearTaskOnLaunch="true"
436-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
437            android:screenOrientation="sensorLandscape"
437-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
438            android:stateNotNeeded="true"
438-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
439            android:theme="@style/zxing_CaptureTheme"
439-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
440            android:windowSoftInputMode="stateAlwaysHidden" />
440-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
441    </application>
442
443</manifest>
