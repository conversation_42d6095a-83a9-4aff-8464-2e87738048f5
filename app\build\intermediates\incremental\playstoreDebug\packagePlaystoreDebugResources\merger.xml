<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res"><file name="color_highlight_material" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\color\color_highlight_material.xml" qualifiers="" type="color"/><file name="bg_button_danger" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\bg_button_danger.xml" qualifiers="" type="drawable"/><file name="bg_button_primary" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\bg_button_primary.xml" qualifiers="" type="drawable"/><file name="bg_button_secondary" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\bg_button_secondary.xml" qualifiers="" type="drawable"/><file name="bg_role_badge" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\bg_role_badge.xml" qualifiers="" type="drawable"/><file name="bg_search" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\bg_search.xml" qualifiers="" type="drawable"/><file name="bg_status_badge" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\bg_status_badge.xml" qualifiers="" type="drawable"/><file name="custom_divider" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\custom_divider.xml" qualifiers="" type="drawable"/><file name="google_button_background" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\google_button_background.xml" qualifiers="" type="drawable"/><file name="ic_about_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_about_24dp.xml" qualifiers="" type="drawable"/><file name="ic_action_done" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_action_done.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_add_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_add_24dp.xml" qualifiers="" type="drawable"/><file name="ic_admin_panel" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_admin_panel.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_backup_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_backup_24dp.xml" qualifiers="" type="drawable"/><file name="ic_bar_chart" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_bar_chart.xml" qualifiers="" type="drawable"/><file name="ic_check_update_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_check_update_24dp.xml" qualifiers="" type="drawable"/><file name="ic_circle" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_circle.xml" qualifiers="" type="drawable"/><file name="ic_clear_all" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_clear_all.xml" qualifiers="" type="drawable"/><file name="ic_cloud_download_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_cloud_download_24dp.xml" qualifiers="" type="drawable"/><file name="ic_copy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_copy.xml" qualifiers="" type="drawable"/><file name="ic_delete_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_delete_24dp.xml" qualifiers="" type="drawable"/><file name="ic_description_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_description_24dp.xml" qualifiers="" type="drawable"/><file name="ic_download" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_download.xml" qualifiers="" type="drawable"/><file name="ic_edit_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_edit_24dp.xml" qualifiers="" type="drawable"/><file name="ic_fab_check" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_fab_check.xml" qualifiers="" type="drawable"/><file name="ic_feedback_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_feedback_24dp.xml" qualifiers="" type="drawable"/><file name="ic_file_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_file_24dp.xml" qualifiers="" type="drawable"/><file name="ic_google" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_google.xml" qualifiers="" type="drawable"/><file name="ic_image_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_image_24dp.xml" qualifiers="" type="drawable"/><file name="ic_list" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_list.xml" qualifiers="" type="drawable"/><file name="ic_lock_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_lock_24dp.xml" qualifiers="" type="drawable"/><file name="ic_logcat_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_logcat_24dp.xml" qualifiers="" type="drawable"/><file name="ic_more_vert_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_more_vert_24dp.xml" qualifiers="" type="drawable"/><file name="ic_outline_filter_alt_24" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_outline_filter_alt_24.xml" qualifiers="" type="drawable"/><file name="ic_people" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_people.xml" qualifiers="" type="drawable"/><file name="ic_per_apps_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_per_apps_24dp.xml" qualifiers="" type="drawable"/><file name="ic_play_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_play_24dp.xml" qualifiers="" type="drawable"/><file name="ic_privacy_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_privacy_24dp.xml" qualifiers="" type="drawable"/><file name="ic_promotion_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_promotion_24dp.xml" qualifiers="" type="drawable"/><file name="ic_qu_scan_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_qu_scan_24dp.xml" qualifiers="" type="drawable"/><file name="ic_qu_switch_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_qu_switch_24dp.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_restore_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_restore_24dp.xml" qualifiers="" type="drawable"/><file name="ic_rounded_corner_active" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_rounded_corner_active.xml" qualifiers="" type="drawable"/><file name="ic_rounded_corner_inactive" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_rounded_corner_inactive.xml" qualifiers="" type="drawable"/><file name="ic_routing_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_routing_24dp.xml" qualifiers="" type="drawable"/><file name="ic_save_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_save_24dp.xml" qualifiers="" type="drawable"/><file name="ic_scan_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_scan_24dp.xml" qualifiers="" type="drawable"/><file name="ic_select_all" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_select_all.xml" qualifiers="" type="drawable"/><file name="ic_select_all_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_select_all_24dp.xml" qualifiers="" type="drawable"/><file name="ic_server" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_server.xml" qualifiers="" type="drawable"/><file name="ic_server_off" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_server_off.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_settings_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_settings_24dp.xml" qualifiers="" type="drawable"/><file name="ic_share_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_share_24dp.xml" qualifiers="" type="drawable"/><file name="ic_source_code_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_source_code_24dp.xml" qualifiers="" type="drawable"/><file name="ic_star" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_star.xml" qualifiers="" type="drawable"/><file name="ic_stop_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_stop_24dp.xml" qualifiers="" type="drawable"/><file name="ic_subscriptions_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_subscriptions_24dp.xml" qualifiers="" type="drawable"/><file name="ic_telegram_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_telegram_24dp.xml" qualifiers="" type="drawable"/><file name="ic_upload" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_upload.xml" qualifiers="" type="drawable"/><file name="ic_user_default" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_user_default.xml" qualifiers="" type="drawable"/><file name="ic_visibility" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_visibility.xml" qualifiers="" type="drawable"/><file name="ic_visibility_off" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_visibility_off.xml" qualifiers="" type="drawable"/><file name="input_background" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\input_background.xml" qualifiers="" type="drawable"/><file name="license_24px" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\license_24px.xml" qualifiers="" type="drawable"/><file name="login_background" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\login_background.xml" qualifiers="" type="drawable"/><file name="login_button_background" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\login_button_background.xml" qualifiers="" type="drawable"/><file name="nav_header_bg" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\nav_header_bg.png" qualifiers="" type="drawable"/><file name="user_avatar_background" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\user_avatar_background.xml" qualifiers="" type="drawable"/><file name="ic_stat_direct" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-hdpi\ic_stat_direct.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_name" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-hdpi\ic_stat_name.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_proxy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-hdpi\ic_stat_proxy.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_direct" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-mdpi\ic_stat_direct.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_name" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-mdpi\ic_stat_name.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_name_black" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-mdpi\ic_stat_name_black.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_proxy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-mdpi\ic_stat_proxy.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_about_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_about_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_action_done" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_action_done.xml" qualifiers="night-v8" type="drawable"/><file name="ic_add_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_add_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_backup_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_backup_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_check_update_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_check_update_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_cloud_download_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_cloud_download_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_copy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_copy.xml" qualifiers="night-v8" type="drawable"/><file name="ic_delete_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_delete_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_description_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_description_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_edit_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_edit_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_fab_check" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_fab_check.xml" qualifiers="night-v8" type="drawable"/><file name="ic_feedback_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_feedback_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_file_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_file_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_image_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_image_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_lock_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_lock_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_logcat_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_logcat_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_more_vert_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_more_vert_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_outline_filter_alt_24" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_outline_filter_alt_24.xml" qualifiers="night-v8" type="drawable"/><file name="ic_per_apps_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_per_apps_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_play_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_play_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_privacy_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_privacy_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_promotion_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_promotion_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_restore_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_restore_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_routing_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_routing_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_save_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_save_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_scan_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_scan_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_select_all_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_select_all_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_settings_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_settings_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_share_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_share_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_source_code_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_source_code_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_stop_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_stop_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_subscriptions_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_subscriptions_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="ic_telegram_24dp" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\ic_telegram_24dp.xml" qualifiers="night-v8" type="drawable"/><file name="nav_header_bg" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-night\nav_header_bg.png" qualifiers="night-v8" type="drawable"/><file name="ic_stat_direct" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xhdpi\ic_stat_direct.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_name" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xhdpi\ic_stat_name.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_name_black" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xhdpi\ic_stat_name_black.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_proxy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xhdpi\ic_stat_proxy.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_direct" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxhdpi\ic_stat_direct.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_name" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxhdpi\ic_stat_name.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_name_black" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxhdpi\ic_stat_name_black.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_proxy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxhdpi\ic_stat_proxy.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_direct" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxxhdpi\ic_stat_direct.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_stat_name" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxxhdpi\ic_stat_name.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_stat_name_black" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxxhdpi\ic_stat_name_black.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_stat_proxy" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable-xxxhdpi\ic_stat_proxy.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="montserrat_thin" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\font\montserrat_thin.ttf" qualifiers="" type="font"/><file name="activity_about" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_about.xml" qualifiers="" type="layout"/><file name="activity_add_server" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_add_server.xml" qualifiers="" type="layout"/><file name="activity_add_user" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_add_user.xml" qualifiers="" type="layout"/><file name="activity_admin_panel" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_admin_panel.xml" qualifiers="" type="layout"/><file name="activity_admin_servers" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_admin_servers.xml" qualifiers="" type="layout"/><file name="activity_admin_users" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_admin_users.xml" qualifiers="" type="layout"/><file name="activity_bypass_list" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_bypass_list.xml" qualifiers="" type="layout"/><file name="activity_check_update" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_check_update.xml" qualifiers="" type="layout"/><file name="activity_edit_server" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_edit_server.xml" qualifiers="" type="layout"/><file name="activity_logcat" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_logcat.xml" qualifiers="" type="layout"/><file name="activity_login" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_none" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_none.xml" qualifiers="" type="layout"/><file name="activity_promote_user" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_promote_user.xml" qualifiers="" type="layout"/><file name="activity_routing_edit" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_routing_edit.xml" qualifiers="" type="layout"/><file name="activity_routing_setting" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_routing_setting.xml" qualifiers="" type="layout"/><file name="activity_server_custom_config" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_custom_config.xml" qualifiers="" type="layout"/><file name="activity_server_details" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_details.xml" qualifiers="" type="layout"/><file name="activity_server_hysteria2" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_hysteria2.xml" qualifiers="" type="layout"/><file name="activity_server_logs" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_logs.xml" qualifiers="" type="layout"/><file name="activity_server_management" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_management.xml" qualifiers="" type="layout"/><file name="activity_server_shadowsocks" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_shadowsocks.xml" qualifiers="" type="layout"/><file name="activity_server_socks" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_socks.xml" qualifiers="" type="layout"/><file name="activity_server_trojan" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_trojan.xml" qualifiers="" type="layout"/><file name="activity_server_users" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_users.xml" qualifiers="" type="layout"/><file name="activity_server_vless" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_vless.xml" qualifiers="" type="layout"/><file name="activity_server_vmess" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_vmess.xml" qualifiers="" type="layout"/><file name="activity_server_wireguard" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_server_wireguard.xml" qualifiers="" type="layout"/><file name="activity_settings" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_sub_edit" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_sub_edit.xml" qualifiers="" type="layout"/><file name="activity_sub_setting" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_sub_setting.xml" qualifiers="" type="layout"/><file name="activity_system_settings" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_system_settings.xml" qualifiers="" type="layout"/><file name="activity_tasker" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_tasker.xml" qualifiers="" type="layout"/><file name="activity_user_asset" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_user_asset.xml" qualifiers="" type="layout"/><file name="activity_user_asset_url" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_user_asset_url.xml" qualifiers="" type="layout"/><file name="activity_user_details" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_user_details.xml" qualifiers="" type="layout"/><file name="activity_user_management" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_user_management.xml" qualifiers="" type="layout"/><file name="activity_user_servers" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\activity_user_servers.xml" qualifiers="" type="layout"/><file name="dialog_config_filter" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\dialog_config_filter.xml" qualifiers="" type="layout"/><file name="item_admin_server" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_admin_server.xml" qualifiers="" type="layout"/><file name="item_admin_user" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_admin_user.xml" qualifiers="" type="layout"/><file name="item_qrcode" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_qrcode.xml" qualifiers="" type="layout"/><file name="item_recycler_bypass_list" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_bypass_list.xml" qualifiers="" type="layout"/><file name="item_recycler_footer" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_footer.xml" qualifiers="" type="layout"/><file name="item_recycler_logcat" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_logcat.xml" qualifiers="" type="layout"/><file name="item_recycler_main" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_main.xml" qualifiers="" type="layout"/><file name="item_recycler_routing_setting" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_routing_setting.xml" qualifiers="" type="layout"/><file name="item_recycler_sub_setting" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_sub_setting.xml" qualifiers="" type="layout"/><file name="item_recycler_user_asset" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_recycler_user_asset.xml" qualifiers="" type="layout"/><file name="item_user_server" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\item_user_server.xml" qualifiers="" type="layout"/><file name="layout_address_port" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\layout_address_port.xml" qualifiers="" type="layout"/><file name="layout_tls" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\layout_tls.xml" qualifiers="" type="layout"/><file name="layout_tls_hysteria2" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\layout_tls_hysteria2.xml" qualifiers="" type="layout"/><file name="layout_transport" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\layout_transport.xml" qualifiers="" type="layout"/><file name="nav_header" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\nav_header.xml" qualifiers="" type="layout"/><file name="preference_with_help_link" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\preference_with_help_link.xml" qualifiers="" type="layout"/><file name="widget_switch" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\layout\widget_switch.xml" qualifiers="" type="layout"/><file name="action_server" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\action_server.xml" qualifiers="" type="menu"/><file name="action_sub_setting" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\action_sub_setting.xml" qualifiers="" type="menu"/><file name="menu_admin_servers" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_admin_servers.xml" qualifiers="" type="menu"/><file name="menu_admin_users" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_admin_users.xml" qualifiers="" type="menu"/><file name="menu_asset" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_asset.xml" qualifiers="" type="menu"/><file name="menu_bypass_list" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_bypass_list.xml" qualifiers="" type="menu"/><file name="menu_drawer" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_drawer.xml" qualifiers="" type="menu"/><file name="menu_logcat" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_logcat.xml" qualifiers="" type="menu"/><file name="menu_main" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="menu_routing_setting" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_routing_setting.xml" qualifiers="" type="menu"/><file name="menu_scanner" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_scanner.xml" qualifiers="" type="menu"/><file name="menu_user_details" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_user_details.xml" qualifiers="" type="menu"/><file name="menu_user_servers" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\menu\menu_user_servers.xml" qualifiers="" type="menu"/><file name="ic_banner" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-anydpi-v26\ic_banner.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_banner" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xhdpi\ic_banner.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_banner_foreground" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xhdpi\ic_banner_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="licenses" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\raw\licenses.xml" qualifiers="" type="raw"/><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="securitys" translatable="false">
        <item>chacha20-poly1305</item>
        <item>aes-128-gcm</item>
        <item>auto</item>
        <item>none</item>
        <item>zero</item>
    </string-array><string-array name="ss_securitys" translatable="false">
        <item>aes-256-gcm</item>
        <item>aes-128-gcm</item>
        <item>chacha20-poly1305</item>
        <item>chacha20-ietf-poly1305</item>
        <item>xchacha20-poly1305</item>
        <item>xchacha20-ietf-poly1305</item>
        <item>none</item>
        <item>plain</item>
        <item>2022-blake3-aes-128-gcm</item>
        <item>2022-blake3-aes-256-gcm</item>
        <item>2022-blake3-chacha20-poly1305</item>
    </string-array><string-array name="networks" translatable="false">
        <item>tcp</item>
        <item>kcp</item>
        <item>ws</item>
        <item>httpupgrade</item>
        <item>xhttp</item>
        <item>h2</item>
        <item>grpc</item>
    </string-array><string-array name="header_type_tcp" translatable="false">
        <item>none</item>
        <item>http</item>
    </string-array><string-array name="header_type_kcp_and_quic" translatable="false">
        <item>none</item>
        <item>srtp</item>
        <item>utp</item>
        <item>wechat-video</item>
        <item>dtls</item>
        <item>wireguard</item>
        <item>dns</item>
    </string-array><string-array name="mode_type_grpc" translatable="false">
        <item>gun</item>
        <item>multi</item>
        
    </string-array><string-array name="streamsecuritys" translatable="false">
        <item/>
        <item>tls</item>
    </string-array><string-array name="streamsecurityxs" translatable="false">
        <item/>
        <item>tls</item>
        <item>reality</item>
    </string-array><string-array name="fragment_packets" translatable="false">
        <item>tlshello</item>
        <item>1-2</item>
        <item>1-3</item>
        <item>1-5</item>
    </string-array><string-array name="streamsecurity_utls" translatable="false">
        <item/>
        <item>chrome</item>
        <item>firefox</item>
        <item>safari</item>
        <item>ios</item>
        <item>android</item>
        <item>edge</item>
        <item>360</item>
        <item>qq</item>
        <item>random</item>
        <item>randomized</item>
    </string-array><string-array name="streamsecurity_alpn" translatable="false">
        <item/>
        <item>h3</item>
        <item>h2</item>
        <item>http/1.1</item>
        <item>h3,h2,http/1.1</item>
        <item>h3,h2</item>
        <item>h2,http/1.1</item>
    </string-array><string-array name="allowinsecures" translatable="false">
        <item/>
        <item>true</item>
        <item>false</item>
    </string-array><string-array name="routing_domain_strategy" translatable="false">
        <item>AsIs</item>
        <item>IPIfNonMatch</item>
        <item>IPOnDemand</item>
    </string-array><string-array name="core_loglevel" translatable="false">
        <item>debug</item>
        <item>info</item>
        <item>warning</item>
        <item>error</item>
        <item>none</item>
    </string-array><string-array name="mode_value" translatable="false">
        <item>VPN</item>
        <item>Proxy only</item>
    </string-array><string-array name="flows" translatable="false">
        <item/>
        <item>xtls-rprx-vision</item>
        <item>xtls-rprx-vision-udp443</item>
    </string-array><string-array name="language_select" translatable="false">
        <item>العربية (مصر)</item>
        <item>English</item>
    </string-array><string-array name="language_select_value" translatable="false">
        <item>ar-rEG</item>
        <item>en</item>
    </string-array><string-array name="mux_xudp_quic_value" translatable="false">
        <item>reject</item>
        <item>allow</item>
        <item>skip</item>
    </string-array><string-array name="ui_mode_night_value" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array><string-array name="outbound_tag" translatable="false">
        <item>proxy</item>
        <item>direct</item>
        <item>block</item>
    </string-array><string-array name="xhttp_mode" translatable="false">
        <item>auto</item>
        <item>packet-up</item>
        <item>stream-up</item>
        <item>stream-one</item>
    </string-array><string-array name="vpn_bypass_lan_value" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\attrs.xml" qualifiers=""><style name="TabLayoutTextStyle" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
    </style></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPing">#009966</color><color name="colorPingRed">#FF0099</color><color name="colorConfigType">#f97910</color><color name="color_fab_active">#f97910</color><color name="color_fab_inactive">#9C9C9C</color><color name="color_secondary">#727272</color><color name="divider_color_light">#E0E0E0</color><color name="colorPrimary">#F5F5F5</color><color name="colorAccent">#000000</color><color name="primary_color">#2196F3</color><color name="primary_dark_color">#1976D2</color><color name="google_text_color">#757575</color><color name="secondary_text_color">#9E9E9E</color><color name="divider_color">#E0E0E0</color><color name="card_background">#FFFFFF</color><color name="login_background_start">#E3F2FD</color><color name="login_background_end">#BBDEFB</color><color name="green">#4CAF50</color><color name="red">#F44336</color><color name="blue">#2196F3</color><color name="orange">#FF9800</color><color name="purple">#9C27B0</color><color name="gray">#757575</color><color name="light_gray">#F5F5F5</color><color name="background_color">#FAFAFA</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="white">#FFFFFF</color></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="padding_spacing_dp4">4dp</dimen><dimen name="padding_spacing_dp8">8dp</dimen><dimen name="padding_spacing_dp16">16dp</dimen><dimen name="image_size_dp24">24dp</dimen><dimen name="view_height_dp36">36dp</dimen><dimen name="view_height_dp48">48dp</dimen><dimen name="view_height_dp64">64dp</dimen><dimen name="view_height_dp160">160dp</dimen></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\ic_banner_background.xml" qualifiers=""><color name="ic_banner_background">#FFFFFF</color></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name" translatable="false">v2hoor</string><string name="app_widget_name">Switch</string><string name="app_tile_name">Switch</string><string name="app_tile_first_use">First use of this feature, please use the app to add server</string><string name="navigation_drawer_open">Open navigation drawer</string><string name="navigation_drawer_close">Close navigation drawer</string><string name="migration_success">Data migration success!</string><string name="migration_fail">Data migration failed!</string><string name="pull_down_to_refresh">Please pull down to refresh!</string><string name="notification_action_stop_v2ray">Stop</string><string name="toast_permission_denied">Unable to obtain the permission</string><string name="toast_permission_denied_notification">Unable to obtain the notification permission</string><string name="notification_action_more">Click for more</string><string name="toast_services_start">Start Services</string><string name="toast_services_stop">Stop Services</string><string name="toast_services_success">Start Services Success</string><string name="toast_services_failure">Start Services Failure</string><string name="title_server">Configuration file</string><string name="menu_item_add_config">Add config</string><string name="menu_item_save_config">Save config</string><string name="menu_item_del_config">Delete config</string><string name="menu_item_import_config_qrcode">Import config from QRcode</string><string name="menu_item_import_config_clipboard">Import config from Clipboard</string><string name="menu_item_import_config_local">Import config from locally</string><string name="menu_item_import_config_manually_vmess">Type manually[VMess]</string><string name="menu_item_import_config_manually_vless">Type manually[VLESS]</string><string name="menu_item_import_config_manually_ss">Type manually[Shadowsocks]</string><string name="menu_item_import_config_manually_socks">Type manually[SOCKS]</string><string name="menu_item_import_config_manually_http">Type manually[HTTP]</string><string name="menu_item_import_config_manually_trojan">Type manually[Trojan]</string><string name="menu_item_import_config_manually_wireguard">Type manually[Wireguard]</string><string name="menu_item_import_config_manually_hysteria2">Type manually[Hysteria2]</string><string name="del_config_comfirm">Confirm delete ?</string><string name="del_invalid_config_comfirm">Please test before deleting! Confirm delete ?</string><string name="server_lab_remarks">remarks</string><string name="server_lab_address">address</string><string name="server_lab_port">port</string><string name="server_lab_id">id</string><string name="server_lab_alterid">alterId</string><string name="server_lab_security">security</string><string name="server_lab_network">Network</string><string name="server_lab_more_function">Transport</string><string name="server_lab_head_type">head type</string><string name="server_lab_mode_type">gRPC mode</string><string name="server_lab_request_host">host</string><string name="server_lab_request_host_http">http host</string><string name="server_lab_request_host_ws">ws host</string><string name="server_lab_request_host_httpupgrade">httpupgrade host</string><string name="server_lab_request_host_xhttp">xhttp host</string><string name="server_lab_request_host_h2">h2 host</string><string name="server_lab_request_host_quic">QUIC security</string><string name="server_lab_request_host_grpc">gRPC Authority</string><string name="server_lab_path">path</string><string name="server_lab_path_ws">ws path</string><string name="server_lab_path_httpupgrade">httpupgrade path</string><string name="server_lab_path_xhttp">xhttp path</string><string name="server_lab_path_h2">h2 path</string><string name="server_lab_path_quic">QUIC key</string><string name="server_lab_path_kcp">kcp seed</string><string name="server_lab_path_grpc">gRPC serviceName</string><string name="server_lab_stream_security">TLS</string><string name="server_lab_stream_fingerprint" translatable="false">Fingerprint</string><string name="server_lab_stream_alpn">Alpn</string><string name="server_lab_allow_insecure">allowInsecure</string><string name="server_lab_sni">SNI</string><string name="server_lab_address3">address</string><string name="server_lab_port3">port</string><string name="server_lab_id3">password</string><string name="server_lab_security3">security</string><string name="server_lab_id4">Password(Optional)</string><string name="server_lab_security4">User(Optional)</string><string name="server_lab_encryption">encryption</string><string name="server_lab_flow">flow</string><string name="server_lab_public_key">PublicKey</string><string name="server_lab_preshared_key">PreSharedKey(optional)</string><string name="server_lab_short_id">ShortId</string><string name="server_lab_spider_x">SpiderX</string><string name="server_lab_secret_key">SecretKey</string><string name="server_lab_reserved">Reserved(Optional, separated by commas)</string><string name="server_lab_local_address">Local address (optional IPv4/IPv6, separated by commas)</string><string name="server_lab_local_mtu">Mtu(optional, default 1420)</string><string name="toast_success">Success</string><string name="toast_failure">Failure</string><string name="toast_none_data">There is nothing</string><string name="toast_incorrect_protocol">Incorrect protocol</string><string name="toast_decoding_failed">Decoding failed</string><string name="title_file_chooser">Select a Config File</string><string name="toast_require_file_manager">Please install a File Manager.</string><string name="server_customize_config">Customize Config</string><string name="toast_config_file_invalid">Invalid Config</string><string name="server_lab_content">Content</string><string name="toast_none_data_clipboard">There is no data in the clipboard</string><string name="toast_invalid_url">Invalid URL</string><string name="toast_insecure_url_protocol">Please do not use the insecure HTTP protocol subscription address</string><string name="server_lab_need_inbound">Ensure inbounds port is consistent with the settings</string><string name="toast_malformed_josn">Config malformed</string><string name="server_lab_request_host6">Host(SNI)(Optional)</string><string name="toast_action_not_allowed">Action not allowed</string><string name="server_obfs_password">Obfs password</string><string name="server_lab_port_hop">Port Hopping(will override the port)</string><string name="server_lab_port_hop_interval">Port Hopping Interval</string><string name="server_lab_stream_pinsha256">pinSHA256</string><string name="server_lab_bandwidth_down">Bandwidth down (units)</string><string name="server_lab_bandwidth_up">Bandwidth up (units)</string><string name="server_lab_xhttp_mode">XHTTP Mode</string><string name="server_lab_xhttp_extra">XHTTP Extra raw JSON, format: { XHTTPObject }</string><string name="toast_asset_copy_failed">File copy failed, please use File Manager</string><string name="menu_item_add_asset">Add asset</string><string name="menu_item_add_file">Add files</string><string name="menu_item_add_url">Add URL</string><string name="menu_item_scan_qrcode">Scan QRcode</string><string name="title_url">URL</string><string name="menu_item_download_file">Download files</string><string name="title_user_asset_add_url">Add asset URL</string><string name="msg_file_not_found">File not found</string><string name="msg_remark_is_duplicate">The remarks already exists</string><string name="asset_geo_files_sources">Geo files source (optional)</string><string name="msg_dialog_progress">Loading</string><string name="menu_item_search">Search</string><string name="menu_item_select_all">Select all</string><string name="msg_enter_keywords">Enter keywords</string><string name="switch_bypass_apps_mode">Bypass Mode</string><string name="menu_item_select_proxy_app">Auto select proxy app</string><string name="msg_downloading_content">Downloading content</string><string name="menu_item_export_proxy_app">Export to Clipboard</string><string name="menu_item_import_proxy_app">Import from Clipboard</string><string name="per_app_proxy_settings">Per-app settings</string><string name="per_app_proxy_settings_enable">Enable per-app</string><string name="title_settings">Settings</string><string name="title_advanced">Advanced Settings</string><string name="title_vpn_settings">VPN Settings</string><string name="title_pref_per_app_proxy">Per-app proxy</string><string name="summary_pref_per_app_proxy">General: Checked apps use proxy, unchecked apps connect directly; \nBypass mode: checked apps connect directly, unchecked apps use proxy. \nThe option to automatically select proxy applications is in the menu</string><string name="title_pref_is_booted">Auto connect at startup</string><string name="summary_pref_is_booted">Automatically connects to the selected server at startup, which may be unsuccessful</string><string name="title_mux_settings">Mux Settings</string><string name="title_pref_mux_enabled">Enable Mux</string><string name="summary_pref_mux_enabled">Faster, but it may cause unstable connectivity\nCustomize how to handle TCP, UDP and QUIC below</string><string name="title_pref_mux_concurency">TCP connections（range -1 to 1024）</string><string name="title_pref_mux_xudp_concurency">XUDP connections（range -1 to 1024）</string><string name="title_pref_mux_xudp_quic">Handling of QUIC in mux tunnel</string><string-array name="mux_xudp_quic_entries">
        <item>reject</item>
        <item>allow</item>
        <item>skip</item>
    </string-array><string name="title_pref_speed_enabled">Enable speed display</string><string name="summary_pref_speed_enabled">Display current speed in the notification.\nNotification icon would change based on
        usage.</string><string name="title_pref_sniffing_enabled">Enable Sniffing</string><string name="summary_pref_sniffing_enabled">Try sniff domain from the packet (default on)</string><string name="title_pref_route_only_enabled">Enable routeOnly</string><string name="summary_pref_route_only_enabled">Use the sniffed domain name for routing only, and keep the target address as the IP address.</string><string name="title_pref_local_dns_enabled">Enable local DNS</string><string name="summary_pref_local_dns_enabled">DNS processed by core‘s DNS module (Recommended if you need routing bypassing LAN and mainland addresses)</string><string name="title_pref_fake_dns_enabled">Enable fake DNS</string><string name="summary_pref_fake_dns_enabled">Local DNS returns fake IP addresses (faster, but it may not work for some apps)</string><string name="title_pref_prefer_ipv6">Prefer IPv6</string><string name="summary_pref_prefer_ipv6">Enable IPv6 routes and Prefer IPv6 addresses</string><string name="title_pref_remote_dns">Remote DNS (udp/tcp/https/quic)(Optional)</string><string name="summary_pref_remote_dns">DNS</string><string name="title_pref_vpn_dns">VPN DNS (only IPv4/v6)</string><string name="title_pref_vpn_bypass_lan">Does VPN bypass LAN</string><string name="title_pref_domestic_dns">Domestic DNS (Optional)</string><string name="summary_pref_domestic_dns">DNS</string><string name="title_pref_dns_hosts">DNS hosts (Format: domain:address,…)</string><string name="summary_pref_dns_hosts">domain:address,…</string><string name="title_pref_delay_test_url">True delay test url (http/https)</string><string name="summary_pref_delay_test_url">Url</string><string name="title_pref_proxy_sharing_enabled">Allow connections from the LAN</string><string name="summary_pref_proxy_sharing_enabled">Other devices can connect to proxy by your IP address through local proxy. Only enable in trusted networks to avoid unauthorized connections</string><string name="toast_warning_pref_proxysharing_short">Allow connections from the LAN. Make sure you are in a trusted network</string><string name="title_pref_allow_insecure">allowInsecure</string><string name="summary_pref_allow_insecure">When TLS is selected, allow insecure connections by default</string><string name="title_pref_socks_port">Local proxy port</string><string name="summary_pref_socks_port">Local proxy port</string><string name="title_pref_local_dns_port">Local DNS port</string><string name="summary_pref_local_dns_port">Local DNS port</string><string name="title_pref_confirm_remove">Delete configuration file confirmation</string><string name="summary_pref_confirm_remove">Whether to delete the configuration file requires a second confirmation by the user</string><string name="title_pref_start_scan_immediate">Start scanning immediately</string><string name="summary_pref_start_scan_immediate">Open the camera to scan immediately at startup, otherwise you can choose to scan the code or select a photo in the toolbar</string><string name="title_pref_append_http_proxy">Append HTTP Proxy to VPN</string><string name="summary_pref_append_http_proxy">HTTP proxy will be used directly from (browser/ some supported apps), without going through the virtual NIC device (Android 10+)</string><string name="title_pref_double_column_display">Enable double column display</string><string name="summary_pref_double_column_display">The profile list is displayed in double columns, allowing more content to be displayed on the screen. You need to restart the application to take effect.</string><string name="title_pref_feedback">Feedback</string><string name="summary_pref_feedback">Feedback enhancements or bugs to GitHub</string><string name="summary_pref_tg_group">Join Telegram Group</string><string name="toast_tg_app_not_found">Telegram app not found</string><string name="title_privacy_policy">Privacy policy</string><string name="title_about">About</string><string name="title_source_code">Source code</string><string name="title_oss_license">Open Source licenses</string><string name="title_tg_channel">Telegram channel</string><string name="title_configuration_backup">Backup configuration</string><string name="summary_configuration_backup">Storage location: [%s], The backup will be cleared after uninstalling the app or clearing the storage</string><string name="title_configuration_restore">Restore configuration</string><string name="title_configuration_share">Share configuration</string><string name="title_pref_auto_update_subscription">Automatic update subscriptions</string><string name="summary_pref_auto_update_subscription">Update your subscriptions automatically at set intervals in the background. Depending on the device, this feature may not always work</string><string name="title_pref_auto_update_interval">Auto Update Interval (Minutes, Min value 15)</string><string name="title_server_settings">Server Settings</string><string name="title_pref_auto_server_update">Auto update servers</string><string name="summary_pref_auto_server_update">Automatically update servers from database at specified intervals</string><string name="title_pref_auto_server_update_interval">Server update interval (minutes, minimum 15)</string><string name="title_pref_manual_server_update">Manual server update</string><string name="summary_pref_manual_server_update">Tap to update servers from database now</string><string name="server_update_success">Successfully updated %d servers</string><string name="server_update_failure">Failed to update servers</string><string name="server_update_no_new">No new servers to update</string><string name="server_update_in_progress">Updating servers...</string><string name="title_core_loglevel">Log Level</string><string name="title_mode">Mode</string><string name="title_mode_help">Click me for more help</string><string name="title_language">Language</string><string name="title_ui_settings">UI settings</string><string name="title_pref_ui_mode_night">UI mode settings</string><string name="title_logcat">Logcat</string><string name="logcat_copy">Copy</string><string name="logcat_clear">Clear</string><string name="title_service_restart">Service restart</string><string name="title_del_all_config">Delete current group configuration</string><string name="title_del_duplicate_config">Delete current group duplicate configuration</string><string name="title_del_invalid_config">Delete current group invalid configuration</string><string name="title_export_all">Export current group non-custom configs to clipboard</string><string name="title_sub_setting">Subscription group setting</string><string name="sub_setting_remarks">remarks</string><string name="sub_setting_url">Optional URL</string><string name="sub_setting_filter">Remarks regular filter</string><string name="sub_setting_enable">Enable update</string><string name="sub_auto_update">Enable automatic update</string><string name="sub_allow_insecure_url">Allow insecure HTTP address</string><string name="sub_setting_pre_profile">Previous proxy configuration remarks</string><string name="sub_setting_next_profile">Next proxy configuration remarks</string><string name="sub_setting_pre_profile_tip">The configuration remarks exists and is unique</string><string name="title_sub_update">Update current group subscription</string><string name="title_ping_all_server">Tcping current group configuration</string><string name="title_real_ping_all_server">Real delay current group configuration</string><string name="title_user_asset_setting">Asset files</string><string name="title_sort_by_test_results">Sorting by test results</string><string name="title_filter_config">Filter configuration file</string><string name="filter_config_all">All groups</string><string name="title_del_duplicate_config_count">Delete %d duplicate configurations</string><string name="title_del_config_count">Delete %d configurations</string><string name="title_import_config_count">Import %d configurations</string><string name="title_export_config_count">Export %d configurations</string><string name="title_update_config_count">Update %d configurations</string><string name="tasker_start_service">Start Service</string><string name="tasker_setting_confirm">Confirm</string><string name="routing_settings_domain_strategy">Domain strategy</string><string name="routing_settings_title">Routing Settings</string><string name="routing_settings_tips">Separated by commas(,), choose domain or ip</string><string name="routing_settings_save">Save</string><string name="routing_settings_delete">Clear</string><string name="routing_settings_rule_title">Routing Rule Settings</string><string name="routing_settings_add_rule">Add rule</string><string name="routing_settings_import_predefined_rulesets">Import predefined rulesets</string><string name="routing_settings_import_rulesets_tip">Existing rulesets will be deleted, are you sure to continue?</string><string name="routing_settings_import_rulesets_from_clipboard">Import ruleset from clipboard</string><string name="routing_settings_import_rulesets_from_qrcode">Import ruleset from QRcode</string><string name="routing_settings_export_rulesets_to_clipboard">Export ruleset to clipboard</string><string name="routing_settings_locked">Locked, keep this rule when import presets</string><string name="routing_settings_domain" translatable="false">domain</string><string name="routing_settings_ip" translatable="false">ip</string><string name="routing_settings_port" translatable="false">port</string><string name="routing_settings_protocol" translatable="false">protocol</string><string name="routing_settings_protocol_tip" translatable="false">[http,tls,bittorrent]</string><string name="routing_settings_network" translatable="false">network</string><string name="routing_settings_network_tip" translatable="false">[udp|tcp]</string><string name="routing_settings_outbound_tag" translatable="false">outboundTag</string><string name="connection_test_pending">Check Connectivity</string><string name="connection_test_testing">Testing…</string><string name="connection_test_testing_count">Testing %d configurations…</string><string name="connection_test_available">Success: Connection took %dms</string><string name="connection_test_error">Fail to detect internet connection: %s</string><string name="connection_test_fail">Internet Unavailable</string><string name="connection_test_error_status_code">Error code: #%d</string><string name="connection_connected">Connected, tap to check connection</string><string name="connection_not_connected">Not connected</string><string name="import_subscription_success">Subscription imported Successfully</string><string name="import_subscription_failure">Import subscription failed</string><string name="title_fragment_settings">Fragment Settings</string><string name="title_pref_fragment_packets">Fragment Packets</string><string name="title_pref_fragment_length">Fragment Length (min-max)</string><string name="title_pref_fragment_interval">Fragment Interval (min-max)</string><string name="title_pref_fragment_enabled">Enable Fragment</string><string name="update_check_for_update">Check for update</string><string name="update_already_latest_version">Already on the latest version</string><string name="update_new_version_found">New version found: %s</string><string name="update_now">Update now</string><string name="update_check_pre_release">Check Pre-release</string><string name="update_checking_for_update">Checking for update…</string><string-array name="share_method">
        <item>QRcode</item>
        <item>Export to clipboard</item>
        <item>Export full configuration to clipboard</item>
    </string-array><string-array name="share_method_more">
        <item>QRcode</item>
        <item>Export to clipboard</item>
        <item>Export full configuration to clipboard</item>
        <item>Edit</item>
        <item>Delete</item>
    </string-array><string-array name="share_sub_method">
        <item>QRcode</item>
        <item>Export to clipboard</item>
    </string-array><string-array name="mode_entries">
        <item>VPN</item>
        <item>Proxy only</item>
    </string-array><string-array name="ui_mode_night">
        <item>Follow system</item>
        <item>Light</item>
        <item>Dark</item>
    </string-array><string-array name="preset_rulesets">
        <item>China Whitelist</item>
        <item>China Blacklist</item>
        <item>Global</item>
        <item>Iran Whitelist</item>
    </string-array><string-array name="vpn_bypass_lan">
        <item>Follow config</item>
        <item>Bypass</item>
        <item>Not Bypass</item>
    </string-array><string name="title_login">Login</string><string name="welcome_to_app">Welcome to V2HoorVPN</string><string name="login_subtitle">قم بتسجيل الدخول للوصول إلى جميع الميزات</string><string name="email">Email</string><string name="password">Password</string><string name="login">Login</string><string name="register">Register</string><string name="forgot_password">Forgot Password?</string><string name="toggle_password_visibility">Toggle password visibility</string><string name="sign_in_with_google">Sign in with Google</string><string name="login_success">Login successful!</string><string name="login_failed">Login failed</string><string name="register_success">Registration successful!</string><string name="register_failed">Registration failed</string><string name="google_sign_in_failed">Google sign in failed</string><string name="error_email_required">Email is required</string><string name="error_invalid_email">Please enter a valid email address</string><string name="error_password_required">Password is required</string><string name="error_password_too_short">Password must be at least 6 characters</string><string name="enter_email_for_reset">Please enter your email address</string><string name="password_reset_sent">Password reset email sent</string><string name="password_reset_failed">Failed to send password reset email</string><string name="logout">Logout</string><string name="logout_confirmation">Are you sure you want to logout?</string><string name="logout_success">Logged out successfully</string><string name="default_web_client_id">426934856046-a8sh16r2vvp6ufeipctdiu5sfi24o9m1.apps.googleusercontent.com</string><string name="guest_user">Guest User</string><string name="user_avatar">User Avatar</string><string name="admin_panel_title">لوحة الإدارة</string><string name="admin_info_title">معلومات المدير</string><string name="server_management_section">إدارة السيرفرات</string><string name="user_management_section">إدارة المستخدمين</string><string name="system_settings_section">إعدادات النظام</string><string name="manage_servers">إدارة السيرفرات</string><string name="add_server">إضافة سيرفر</string><string name="update_servers_manually">تحديث السيرفرات يدوياً</string><string name="server_logs">سجلات السيرفرات</string><string name="manage_users">إدارة المستخدمين</string><string name="promote_user">ترقية مستخدم</string><string name="system_settings">إعدادات النظام</string><string name="access_denied">ليس لديك صلاحية للوصول</string></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values\themes.xml" qualifiers=""><style name="AppThemeDayNight" parent="Theme.AppCompat.DayNight">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
    </style><style name="AppThemeDayNight.NoActionBar" parent="AppThemeDayNight">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar"/><style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style><style name="AppTheme.NoActionBar.Translucent">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style><style name="BrandedSwitch" parent="AppTheme">
        <item name="colorAccent">@color/color_fab_active</item>
    </style><style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values-ar-rEG\strings.xml" qualifiers="ar-rEG"><string name="app_widget_name">مفتاح التبديل</string><string name="app_tile_name">مفتاح التبديل</string><string name="app_tile_first_use">هذا أول استخدام لهذه الميزة، يرجى استخدام التطبيق لإضافة خادم</string><string name="navigation_drawer_open">فتح قائمة التنقل</string><string name="navigation_drawer_close">إغلاق قائمة التنقل</string><string name="migration_success">تم ترحيل البيانات بنجاح!</string><string name="migration_fail">فشل في ترحيل البيانات!</string><string name="pull_down_to_refresh">اسحب لأسفل للتحديث!</string><string name="notification_action_stop_v2ray">إيقاف</string><string name="toast_permission_denied">تعذر الحصول على الإذن المطلوب</string><string name="toast_permission_denied_notification">تعذر الحصول على إذن الإشعارات</string><string name="notification_action_more">اضغط للمزيد</string><string name="toast_services_start">بدء تشغيل الخدمات</string><string name="toast_services_stop">إيقاف الخدمات</string><string name="toast_services_success">تم بدء تشغيل الخدمات بنجاح</string><string name="toast_services_failure">فشل في بدء تشغيل الخدمات</string><string name="title_server">ملف الإعدادات</string><string name="menu_item_add_config">إضافة إعدادات</string><string name="menu_item_save_config">حفظ الإعدادات</string><string name="menu_item_del_config">حذف الإعدادات</string><string name="menu_item_import_config_qrcode">استيراد الإعدادات من رمز الاستجابة السريعة</string><string name="menu_item_import_config_clipboard">استيراد الإعدادات من الحافظة</string><string name="menu_item_import_config_local">استيراد الإعدادات من ملف محلي</string><string name="menu_item_import_config_manually_vmess">إدخال يدوي [VMess]</string><string name="menu_item_import_config_manually_vless">إدخال يدوي [VLESS]</string><string name="menu_item_import_config_manually_ss">إدخال يدوي [Shadowsocks]</string><string name="menu_item_import_config_manually_socks">إدخال يدوي [SOCKS]</string><string name="menu_item_import_config_manually_http">إدخال يدوي [HTTP]</string><string name="menu_item_import_config_manually_trojan">إدخال يدوي [Trojan]</string><string name="menu_item_import_config_manually_wireguard">إدخال يدوي [Wireguard]</string><string name="menu_item_import_config_manually_hysteria2">إدخال يدوي [Hysteria2]</string><string name="del_config_comfirm">هل تريد تأكيد الحذف؟</string><string name="del_invalid_config_comfirm">يرجى اختبار الإعدادات قبل الحذف! هل تريد تأكيد الحذف؟</string><string name="server_lab_remarks">الملاحظات</string><string name="server_lab_address">العنوان</string><string name="server_lab_port">المنفذ</string><string name="server_lab_id">المعرف</string><string name="server_lab_alterid">معرف بديل</string><string name="server_lab_security">الأمان</string><string name="server_lab_network">الشبكة</string><string name="server_lab_more_function">النقل</string><string name="server_lab_head_type">نوع الرأس</string><string name="server_lab_mode_type">وضع gRPC</string><string name="server_lab_request_host">المضيف</string><string name="server_lab_request_host_http">مضيف HTTP</string><string name="server_lab_request_host_ws">مضيف WebSocket</string><string name="server_lab_request_host_httpupgrade">مضيف HTTP Upgrade</string><string name="server_lab_request_host_xhttp">مضيف XHTTP</string><string name="server_lab_request_host_h2">مضيف HTTP/2</string><string name="server_lab_request_host_quic">أمان QUIC</string><string name="server_lab_request_host_grpc">سلطة gRPC</string><string name="server_lab_path">المسار</string><string name="server_lab_path_ws">مسار WebSocket</string><string name="server_lab_path_httpupgrade">مسار HTTP Upgrade</string><string name="server_lab_path_xhttp">مسار XHTTP</string><string name="server_lab_path_h2">مسار HTTP/2</string><string name="server_lab_path_quic">مفتاح QUIC</string><string name="server_lab_path_kcp">بذرة KCP</string><string name="server_lab_path_grpc">اسم خدمة gRPC</string><string name="server_lab_stream_security">أمان TLS</string><string name="server_lab_stream_fingerprint" translatable="false">البصمة</string><string name="server_lab_stream_alpn">بروتوكول ALPN</string><string name="server_lab_allow_insecure">السماح بالاتصالات غير الآمنة</string><string name="server_lab_sni">مؤشر اسم الخادم (SNI)</string><string name="server_lab_address3">العنوان</string><string name="server_lab_port3">المنفذ</string><string name="server_lab_id3">كلمة المرور</string><string name="server_lab_security3">الأمان</string><string name="server_lab_id4">كلمة المرور (اختياري)</string><string name="server_lab_security4">اسم المستخدم (اختياري)</string><string name="server_lab_encryption">التشفير</string><string name="server_lab_flow">التدفق</string><string name="server_lab_public_key">المفتاح العام</string><string name="server_lab_preshared_key">المفتاح المشترك مسبقاً (اختياري)</string><string name="server_lab_short_id">المعرف المختصر</string><string name="server_lab_spider_x">SpiderX</string><string name="server_lab_secret_key">المفتاح السري</string><string name="server_lab_reserved">محجوز (اختياري، مفصول بفواصل)</string><string name="server_lab_local_address">العنوان المحلي (اختياري IPv4/IPv6، مفصول بفواصل)</string><string name="server_lab_local_mtu">وحدة النقل القصوى (اختياري، الافتراضي 1420)</string><string name="toast_success">تم بنجاح</string><string name="toast_failure">فشل</string><string name="toast_none_data">لا توجد بيانات</string><string name="toast_incorrect_protocol">بروتوكول غير صحيح</string><string name="toast_decoding_failed">فشل في فك التشفير</string><string name="title_file_chooser">اختر ملف الإعدادات</string><string name="toast_require_file_manager">يرجى تثبيت مدير الملفات</string><string name="server_customize_config">تخصيص الإعدادات</string><string name="toast_config_file_invalid">ملف إعدادات غير صالح</string><string name="server_lab_content">المحتوى</string><string name="toast_none_data_clipboard">لا توجد بيانات في الحافظة</string><string name="toast_invalid_url">رابط غير صالح</string><string name="toast_insecure_url_protocol">يرجى عدم استخدام عنوان اشتراك بروتوكول HTTP غير الآمن</string><string name="server_lab_need_inbound">تأكد من أن منفذ الاتصالات الواردة متوافق مع الإعدادات</string><string name="toast_malformed_josn">ملف إعدادات تالف</string><string name="server_lab_request_host6">المضيف (SNI) (اختياري)</string><string name="toast_action_not_allowed">الإجراء غير مسموح</string><string name="server_obfs_password">كلمة مرور التشويش</string><string name="server_lab_port_hop">تنقل المنافذ (سيتجاوز المنفذ الحالي)</string><string name="server_lab_port_hop_interval">فترة تنقل المنافذ</string><string name="server_lab_stream_pinsha256">تثبيت SHA256</string><string name="server_lab_bandwidth_down">عرض النطاق للتنزيل (وحدات)</string><string name="server_lab_bandwidth_up">عرض النطاق للرفع (وحدات)</string><string name="server_lab_xhttp_mode">وضع XHTTP</string><string name="server_lab_xhttp_extra">إضافات XHTTP الخام JSON، التنسيق: { XHTTPObject }</string><string name="toast_asset_copy_failed">فشل في نسخ الملف، يرجى استخدام مدير الملفات</string><string name="menu_item_add_asset">إضافة مورد</string><string name="menu_item_add_file">إضافة ملفات</string><string name="menu_item_add_url">إضافة رابط</string><string name="menu_item_scan_qrcode">مسح رمز الاستجابة السريعة</string><string name="title_url">الرابط</string><string name="menu_item_download_file">تنزيل الملفات</string><string name="title_user_asset_add_url">إضافة رابط المورد</string><string name="msg_file_not_found">الملف غير موجود</string><string name="msg_remark_is_duplicate">الملاحظة موجودة بالفعل</string><string name="asset_geo_files_sources">مصدر ملفات الموقع الجغرافي (اختياري)</string><string name="msg_dialog_progress">جاري التحميل</string><string name="menu_item_search">بحث</string><string name="menu_item_select_all">تحديد الكل</string><string name="msg_enter_keywords">أدخل الكلمات المفتاحية</string><string name="switch_bypass_apps_mode">وضع التجاوز</string><string name="menu_item_select_proxy_app">تحديد تطبيقات البروكسي تلقائياً</string><string name="msg_downloading_content">جاري تنزيل المحتوى</string><string name="menu_item_export_proxy_app">تصدير إلى الحافظة</string><string name="menu_item_import_proxy_app">استيراد من الحافظة</string><string name="per_app_proxy_settings">إعدادات البروكسي لكل تطبيق</string><string name="per_app_proxy_settings_enable">تفعيل البروكسي لكل تطبيق</string><string name="title_settings">الإعدادات</string><string name="title_advanced">الإعدادات المتقدمة</string><string name="title_vpn_settings">إعدادات الشبكة الافتراضية الخاصة</string><string name="title_pref_per_app_proxy">البروكسي لكل تطبيق</string><string name="summary_pref_per_app_proxy">عام: التطبيقات المحددة تستخدم البروكسي، غير المحددة تتصل مباشرة؛ \nوضع التجاوز: التطبيقات المحددة تتصل مباشرة، غير المحددة تستخدم البروكسي. \nخيار التحديد التلقائي لتطبيقات البروكسي موجود في القائمة</string><string name="title_pref_is_booted">الاتصال التلقائي عند بدء التشغيل</string><string name="summary_pref_is_booted">يتصل تلقائياً بالخادم المحدد عند بدء تشغيل الجهاز، قد لا ينجح دائماً</string><string name="title_mux_settings">إعدادات التعدد</string><string name="title_pref_mux_enabled">تفعيل التعدد</string><string name="summary_pref_mux_enabled">أسرع، لكن قد يسبب عدم استقرار في الاتصال\nخصص كيفية التعامل مع TCP و UDP و QUIC أدناه</string><string name="title_pref_mux_concurency">اتصالات TCP (النطاق من -1 إلى 1024)</string><string name="title_pref_mux_xudp_concurency">اتصالات XUDP (النطاق من -1 إلى 1024)</string><string name="title_pref_mux_xudp_quic">التعامل مع QUIC في نفق التعدد</string><string-array name="mux_xudp_quic_entries">
        <item>رفض</item>
        <item>سماح</item>
        <item>تخطي</item>
    </string-array><string name="title_pref_speed_enabled">تفعيل عرض السرعة</string><string name="summary_pref_speed_enabled">عرض السرعة الحالية في الإشعار.\nستتغير أيقونة الإشعار حسب الاستخدام.</string><string name="title_pref_sniffing_enabled">تفعيل استشعار الحزم</string><string name="summary_pref_sniffing_enabled">محاولة استشعار النطاق من الحزمة (مفعل افتراضياً)</string><string name="title_pref_route_only_enabled">تفعيل التوجيه فقط</string><string name="summary_pref_route_only_enabled">استخدام اسم النطاق المستشعر للتوجيه فقط، والاحتفاظ بعنوان الوجهة كعنوان IP.</string><string name="title_pref_local_dns_enabled">تفعيل DNS المحلي</string><string name="summary_pref_local_dns_enabled">معالجة DNS بواسطة وحدة DNS الأساسية (موصى به إذا كنت تحتاج لتجاوز توجيه الشبكة المحلية وعناوين البر الرئيسي)</string><string name="title_pref_fake_dns_enabled">تفعيل DNS الوهمي</string><string name="summary_pref_fake_dns_enabled">يعيد DNS المحلي عناوين IP وهمية (أسرع، لكن قد لا يعمل مع بعض التطبيقات)</string><string name="title_pref_prefer_ipv6">تفضيل IPv6</string><string name="summary_pref_prefer_ipv6">تفعيل مسارات IPv6 وتفضيل عناوين IPv6</string><string name="title_pref_remote_dns">DNS البعيد (udp/tcp/https/quic) (اختياري)</string><string name="summary_pref_remote_dns">خادم DNS</string><string name="title_pref_vpn_dns">DNS الشبكة الافتراضية (IPv4/v6 فقط)</string><string name="title_pref_vpn_bypass_lan">هل تتجاوز الشبكة الافتراضية الشبكة المحلية</string><string name="title_pref_domestic_dns">DNS المحلي (اختياري)</string><string name="summary_pref_domestic_dns">خادم DNS</string><string name="title_pref_dns_hosts">مضيفو DNS (التنسيق: domain:address،...)</string><string name="summary_pref_dns_hosts">domain:address,...</string><string name="title_pref_delay_test_url">رابط اختبار التأخير الحقيقي (http/https)</string><string name="summary_pref_delay_test_url">الرابط</string><string name="title_pref_proxy_sharing_enabled">السماح بالاتصالات من الشبكة المحلية</string><string name="summary_pref_proxy_sharing_enabled">يمكن للأجهزة الأخرى الاتصال بالبروكسي عبر عنوان IP الخاص بك من خلال البروكسي المحلي. فعل فقط في الشبكات الموثوقة لتجنب الاتصالات غير المصرح بها</string><string name="toast_warning_pref_proxysharing_short">السماح بالاتصالات من الشبكة المحلية. تأكد من أنك في شبكة موثوقة</string><string name="title_pref_allow_insecure">السماح بالاتصالات غير الآمنة</string><string name="summary_pref_allow_insecure">عند اختيار TLS، السماح بالاتصالات غير الآمنة افتراضياً</string><string name="title_pref_socks_port">منفذ البروكسي المحلي</string><string name="summary_pref_socks_port">منفذ البروكسي المحلي</string><string name="title_pref_local_dns_port">منفذ DNS المحلي</string><string name="summary_pref_local_dns_port">منفذ DNS المحلي</string><string name="title_pref_confirm_remove">تأكيد حذف ملف الإعدادات</string><string name="summary_pref_confirm_remove">ما إذا كان حذف ملف الإعدادات يتطلب تأكيداً ثانياً من المستخدم</string><string name="title_pref_start_scan_immediate">بدء المسح فوراً</string><string name="summary_pref_start_scan_immediate">فتح الكاميرا لمسح الرمز فوراً عند بدء التشغيل، وإلا يمكنك اختيار مسح الرمز أو تحديد صورة من شريط الأدوات</string><string name="title_pref_append_http_proxy">إلحاق بروكسي HTTP بالشبكة الافتراضية</string><string name="summary_pref_append_http_proxy">سيتم استخدام بروكسي HTTP مباشرة من (المتصفح / بعض التطبيقات المدعومة)، دون المرور عبر جهاز الشبكة الافتراضي (Android 10+)</string><string name="title_pref_double_column_display">تفعيل العرض بعمودين</string><string name="summary_pref_double_column_display">عرض قائمة الملفات الشخصية في عمودين، مما يسمح بعرض المزيد من المحتوى على الشاشة. تحتاج إلى إعادة تشغيل التطبيق ليصبح ساري المفعول.</string><string name="title_pref_feedback">التعليقات</string><string name="summary_pref_feedback">إرسال تعليقات التحسينات أو الأخطاء إلى GitHub</string><string name="summary_pref_tg_group">الانضمام إلى مجموعة Telegram</string><string name="toast_tg_app_not_found">تطبيق Telegram غير موجود</string><string name="title_privacy_policy">سياسة الخصوصية</string><string name="title_about">حول التطبيق</string><string name="title_source_code">الكود المصدري</string><string name="title_oss_license">تراخيص المصادر المفتوحة</string><string name="title_tg_channel">قناة Telegram</string><string name="title_configuration_backup">نسخ احتياطي للإعدادات</string><string name="summary_configuration_backup">موقع التخزين: [%s]، سيتم مسح النسخة الاحتياطية بعد إلغاء تثبيت التطبيق أو مسح التخزين</string><string name="title_configuration_restore">استعادة الإعدادات</string><string name="title_configuration_share">مشاركة الإعدادات</string><string name="title_pref_auto_update_subscription">التحديث التلقائي للاشتراكات</string><string name="summary_pref_auto_update_subscription">تحديث اشتراكاتك تلقائياً بفترات زمنية محددة في الخلفية. حسب الجهاز، قد لا تعمل هذه الميزة دائماً</string><string name="title_pref_auto_update_interval">فترة التحديث التلقائي (بالدقائق، الحد الأدنى 15)</string><string name="title_server_settings">إعدادات السيرفرات</string><string name="title_pref_auto_server_update">التحديث التلقائي للسيرفرات</string><string name="summary_pref_auto_server_update">تحديث السيرفرات تلقائياً من قاعدة البيانات بفترات زمنية محددة</string><string name="title_pref_auto_server_update_interval">فترة تحديث السيرفرات (بالدقائق، الحد الأدنى 15)</string><string name="title_pref_manual_server_update">تحديث السيرفرات يدوياً</string><string name="summary_pref_manual_server_update">اضغط لتحديث السيرفرات من قاعدة البيانات الآن</string><string name="server_update_success">تم تحديث %d سيرفر بنجاح</string><string name="server_update_failure">فشل في تحديث السيرفرات</string><string name="server_update_no_new">لا توجد سيرفرات جديدة للتحديث</string><string name="server_update_in_progress">جاري تحديث السيرفرات...</string><string name="title_core_loglevel">مستوى السجل</string><string name="title_mode">الوضع</string><string name="title_mode_help">اضغط هنا للمزيد من المساعدة</string><string name="title_language">اللغة</string><string name="title_ui_settings">إعدادات واجهة المستخدم</string><string name="title_pref_ui_mode_night">إعدادات الوضع الليلي لواجهة المستخدم</string><string name="title_logcat">سجل النظام</string><string name="logcat_copy">نسخ</string><string name="logcat_clear">مسح</string><string name="title_service_restart">إعادة تشغيل الخدمة</string><string name="title_del_all_config">حذف إعدادات المجموعة الحالية</string><string name="title_del_duplicate_config">حذف الإعدادات المكررة للمجموعة الحالية</string><string name="title_del_invalid_config">حذف الإعدادات غير الصالحة للمجموعة الحالية</string><string name="title_export_all">تصدير الإعدادات غير المخصصة للمجموعة الحالية إلى الحافظة</string><string name="title_sub_setting">إعدادات مجموعة الاشتراك</string><string name="sub_setting_remarks">الملاحظات</string><string name="sub_setting_url">الرابط (اختياري)</string><string name="sub_setting_filter">مرشح الملاحظات بالتعبير النمطي</string><string name="sub_setting_enable">تفعيل التحديث</string><string name="sub_auto_update">تفعيل التحديث التلقائي</string><string name="sub_allow_insecure_url">السماح بعنوان HTTP غير الآمن</string><string name="sub_setting_pre_profile">ملاحظات إعدادات البروكسي السابقة</string><string name="sub_setting_next_profile">ملاحظات إعدادات البروكسي التالية</string><string name="sub_setting_pre_profile_tip">ملاحظات الإعدادات موجودة وفريدة</string><string name="title_sub_update">تحديث اشتراك المجموعة الحالية</string><string name="title_ping_all_server">اختبار ping TCP لإعدادات المجموعة الحالية</string><string name="title_real_ping_all_server">اختبار التأخير الحقيقي لإعدادات المجموعة الحالية</string><string name="title_user_asset_setting">ملفات الموارد</string><string name="title_sort_by_test_results">الترتيب حسب نتائج الاختبار</string><string name="title_filter_config">تصفية ملف الإعدادات</string><string name="filter_config_all">جميع المجموعات</string><string name="title_del_duplicate_config_count">حذف %d إعدادات مكررة</string><string name="title_del_config_count">حذف %d إعدادات</string><string name="title_import_config_count">استيراد %d إعدادات</string><string name="title_export_config_count">تصدير %d إعدادات</string><string name="title_update_config_count">تحديث %d إعدادات</string><string name="tasker_start_service">بدء الخدمة</string><string name="tasker_setting_confirm">تأكيد</string><string name="routing_settings_domain_strategy">استراتيجية النطاق</string><string name="routing_settings_title">إعدادات التوجيه</string><string name="routing_settings_tips">مفصولة بفواصل (،)، اختر النطاق أو IP</string><string name="routing_settings_save">حفظ</string><string name="routing_settings_delete">مسح</string><string name="routing_settings_rule_title">إعدادات قواعد التوجيه</string><string name="routing_settings_add_rule">إضافة قاعدة</string><string name="routing_settings_import_predefined_rulesets">استيراد مجموعات قواعد محددة مسبقاً</string><string name="routing_settings_import_rulesets_tip">سيتم حذف مجموعات القواعد الحالية، هل أنت متأكد من المتابعة؟</string><string name="routing_settings_import_rulesets_from_clipboard">استيراد مجموعة قواعد من الحافظة</string><string name="routing_settings_import_rulesets_from_qrcode">استيراد مجموعة قواعد من رمز الاستجابة السريعة</string><string name="routing_settings_export_rulesets_to_clipboard">تصدير مجموعة قواعد إلى الحافظة</string><string name="routing_settings_locked">مؤمن، الاحتفاظ بهذه القاعدة عند استيراد الإعدادات المسبقة</string><string name="routing_settings_domain" translatable="false">النطاق</string><string name="routing_settings_ip" translatable="false">عنوان IP</string><string name="routing_settings_port" translatable="false">المنفذ</string><string name="routing_settings_protocol" translatable="false">البروتوكول</string><string name="routing_settings_protocol_tip" translatable="false">[http,tls,bittorrent]</string><string name="routing_settings_network" translatable="false">الشبكة</string><string name="routing_settings_network_tip" translatable="false">[udp|tcp]</string><string name="routing_settings_outbound_tag" translatable="false">علامة الصادر</string><string name="connection_test_pending">فحص الاتصال</string><string name="connection_test_testing">جاري الاختبار…</string><string name="connection_test_testing_count">اختبار %d إعدادات…</string><string name="connection_test_available">نجح: استغرق الاتصال %d ميلي ثانية</string><string name="connection_test_error">فشل في اكتشاف اتصال الإنترنت: %s</string><string name="connection_test_fail">الإنترنت غير متاح</string><string name="connection_test_error_status_code">رمز الخطأ: #%d</string><string name="connection_connected">متصل، اضغط للتحقق من الاتصال</string><string name="connection_not_connected">غير متصل</string><string name="import_subscription_success">تم استيراد الاشتراك بنجاح</string><string name="import_subscription_failure">فشل في استيراد الاشتراك</string><string name="title_fragment_settings">إعدادات التجزئة</string><string name="title_pref_fragment_packets">حزم التجزئة</string><string name="title_pref_fragment_length">طول التجزئة (الحد الأدنى - الحد الأقصى)</string><string name="title_pref_fragment_interval">فترة التجزئة (الحد الأدنى - الحد الأقصى)</string><string name="title_pref_fragment_enabled">تفعيل التجزئة</string><string name="update_check_for_update">البحث عن تحديث</string><string name="update_already_latest_version">أنت تستخدم أحدث إصدار بالفعل</string><string name="update_new_version_found">تم العثور على إصدار جديد: %s</string><string name="update_now">التحديث الآن</string><string name="update_check_pre_release">البحث عن الإصدار التجريبي</string><string name="update_checking_for_update">جاري البحث عن تحديث…</string><string-array name="share_method">
        <item>رمز الاستجابة السريعة</item>
        <item>تصدير إلى الحافظة</item>
        <item>تصدير الإعدادات الكاملة إلى الحافظة</item>
    </string-array><string-array name="share_method_more">
        <item>رمز الاستجابة السريعة</item>
        <item>تصدير إلى الحافظة</item>
        <item>تصدير الإعدادات الكاملة إلى الحافظة</item>
        <item>تعديل</item>
        <item>حذف</item>
    </string-array><string-array name="share_sub_method">
        <item>رمز الاستجابة السريعة</item>
        <item>تصدير إلى الحافظة</item>
    </string-array><string-array name="mode_entries">
        <item>الشبكة الافتراضية الخاصة</item>
        <item>البروكسي فقط</item>
    </string-array><string-array name="ui_mode_night">
        <item>اتباع النظام</item>
        <item>فاتح</item>
        <item>داكن</item>
    </string-array><string-array name="preset_rulesets">
        <item>القائمة البيضاء للصين</item>
        <item>القائمة السوداء للصين</item>
        <item>عالمي</item>
        <item>القائمة البيضاء لإيران</item>
    </string-array><string-array name="vpn_bypass_lan">
        <item>اتباع الإعدادات</item>
        <item>تجاوز</item>
        <item>عدم التجاوز</item>
    </string-array><string name="title_login">تسجيل الدخول</string><string name="welcome_to_app">مرحباً بك في V2HoorVPN</string><string name="login_subtitle">قم بتسجيل الدخول للوصول إلى جميع الميزات</string><string name="email">البريد الإلكتروني</string><string name="password">كلمة المرور</string><string name="login">تسجيل الدخول</string><string name="register">إنشاء حساب</string><string name="forgot_password">نسيت كلمة المرور؟</string><string name="toggle_password_visibility">إظهار/إخفاء كلمة المرور</string><string name="sign_in_with_google">تسجيل الدخول بـ Google</string><string name="login_success">تم تسجيل الدخول بنجاح!</string><string name="login_failed">فشل في تسجيل الدخول</string><string name="register_success">تم إنشاء الحساب بنجاح!</string><string name="register_failed">فشل في إنشاء الحساب</string><string name="google_sign_in_failed">فشل في تسجيل الدخول بـ Google</string><string name="error_email_required">البريد الإلكتروني مطلوب</string><string name="error_invalid_email">يرجى إدخال عنوان بريد إلكتروني صحيح</string><string name="error_password_required">كلمة المرور مطلوبة</string><string name="error_password_too_short">كلمة المرور يجب أن تكون 6 أحرف على الأقل</string><string name="enter_email_for_reset">يرجى إدخال عنوان بريدك الإلكتروني</string><string name="password_reset_sent">تم إرسال رابط إعادة تعيين كلمة المرور</string><string name="password_reset_failed">فشل في إرسال رابط إعادة تعيين كلمة المرور</string><string name="logout">تسجيل الخروج</string><string name="logout_confirmation">هل أنت متأكد من تسجيل الخروج؟</string><string name="logout_success">تم تسجيل الخروج بنجاح</string><string name="guest_user">مستخدم ضيف</string><string name="user_avatar">الصورة الشخصية</string></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="color_fab_active">#f97910</color><color name="color_fab_inactive">#646464</color><color name="color_secondary">#BDBDBD</color><color name="divider_color_light">#424242</color><color name="colorPrimary">#212121</color><color name="colorAccent">#FFFFFF</color></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="AppThemeDayNight" parent="Theme.AppCompat.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
    </style></file><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\values-sw360dp-v13\values-preference.xml" qualifiers="sw360dp-v13"><bool name="config_materialPreferenceIconSpaceReserved" ns1:ignore="MissingDefaultResource,PrivateResource">false</bool><dimen name="preference_category_padding_start" ns1:ignore="MissingDefaultResource,PrivateResource">0dp</dimen></file><file name="app_widget_provider" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\xml\app_widget_provider.xml" qualifiers="" type="xml"/><file name="cache_paths" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\xml\cache_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="pref_settings" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\xml\pref_settings.xml" qualifiers="" type="xml"/><file name="shortcuts" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\xml\shortcuts.xml" qualifiers="" type="xml"/><file name="ic_filter" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_search" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="playstore$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstore\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="playstore" generated-set="playstore$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstore\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\build\generated\res\resValues\playstore\debug"/><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\build\generated\res\processPlaystoreDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\build\generated\res\resValues\playstore\debug"/><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\build\generated\res\processPlaystoreDebugGoogleServices"><file path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\build\generated\res\processPlaystoreDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">426934856046-a8sh16r2vvp6ufeipctdiu5sfi24o9m1.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">426934856046</string><string name="google_api_key" translatable="false">AIzaSyAl6p2bf3DCthK7B86C-eNsyrWoHkibmD4</string><string name="google_app_id" translatable="false">1:426934856046:android:6234baf1e1847f718eb97f</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAl6p2bf3DCthK7B86C-eNsyrWoHkibmD4</string><string name="google_storage_bucket" translatable="false">mrelfeky-209615.firebasestorage.app</string><string name="project_id" translatable="false">mrelfeky-209615</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstoreDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstoreDebug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processPlaystoreDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processPlaystoreDebugGoogleServices" generated-set="res-processPlaystoreDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>