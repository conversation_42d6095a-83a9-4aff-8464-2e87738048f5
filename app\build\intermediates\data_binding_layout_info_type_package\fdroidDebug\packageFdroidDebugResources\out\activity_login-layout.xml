<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="224" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="20" endOffset="66"/></Target><Target id="@+id/et_email" view="EditText"><Expressions/><location startLine="97" startOffset="24" endLine="107" endOffset="81"/></Target><Target id="@+id/et_password" view="EditText"><Expressions/><location startLine="128" startOffset="24" endLine="138" endOffset="81"/></Target><Target id="@+id/tv_forgot_password" view="TextView"><Expressions/><location startLine="143" startOffset="20" endLine="155" endOffset="47"/></Target><Target id="@+id/btn_login" view="Button"><Expressions/><location startLine="163" startOffset="24" endLine="172" endOffset="53"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="174" startOffset="24" endLine="180" endOffset="55"/></Target><Target id="@+id/btn_google_sign_in" view="Button"><Expressions/><location startLine="185" startOffset="20" endLine="196" endOffset="49"/></Target><Target id="@+id/btn_skip_login" view="Button"><Expressions/><location startLine="206" startOffset="20" endLine="214" endOffset="69"/></Target></Targets></Layout>