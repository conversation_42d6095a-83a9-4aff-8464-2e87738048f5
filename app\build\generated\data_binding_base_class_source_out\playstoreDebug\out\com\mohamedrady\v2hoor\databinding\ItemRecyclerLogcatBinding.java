// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecyclerLogcatBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatTextView logContent;

  @NonNull
  public final AppCompatTextView logTag;

  private ItemRecyclerLogcatBinding(@NonNull LinearLayout rootView,
      @NonNull AppCompatTextView logContent, @NonNull AppCompatTextView logTag) {
    this.rootView = rootView;
    this.logContent = logContent;
    this.logTag = logTag;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecyclerLogcatBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecyclerLogcatBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recycler_logcat, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecyclerLogcatBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.log_content;
      AppCompatTextView logContent = ViewBindings.findChildViewById(rootView, id);
      if (logContent == null) {
        break missingId;
      }

      id = R.id.log_tag;
      AppCompatTextView logTag = ViewBindings.findChildViewById(rootView, id);
      if (logTag == null) {
        break missingId;
      }

      return new ItemRecyclerLogcatBinding((LinearLayout) rootView, logContent, logTag);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
