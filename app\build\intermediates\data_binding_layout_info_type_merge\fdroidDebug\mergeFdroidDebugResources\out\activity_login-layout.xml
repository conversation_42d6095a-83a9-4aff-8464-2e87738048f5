<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="221" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="20" endOffset="66"/></Target><Target id="@+id/et_email" view="EditText"><Expressions/><location startLine="97" startOffset="24" endLine="107" endOffset="81"/></Target><Target id="@+id/et_password" view="EditText"><Expressions/><location startLine="128" startOffset="24" endLine="138" endOffset="81"/></Target><Target id="@+id/iv_toggle_password" view="ImageView"><Expressions/><location startLine="140" startOffset="24" endLine="151" endOffset="93"/></Target><Target id="@+id/tv_forgot_password" view="TextView"><Expressions/><location startLine="156" startOffset="20" endLine="168" endOffset="47"/></Target><Target id="@+id/btn_login" view="Button"><Expressions/><location startLine="176" startOffset="24" endLine="185" endOffset="53"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="187" startOffset="24" endLine="193" endOffset="55"/></Target><Target id="@+id/btn_google_sign_in" view="Button"><Expressions/><location startLine="198" startOffset="20" endLine="209" endOffset="49"/></Target></Targets></Layout>