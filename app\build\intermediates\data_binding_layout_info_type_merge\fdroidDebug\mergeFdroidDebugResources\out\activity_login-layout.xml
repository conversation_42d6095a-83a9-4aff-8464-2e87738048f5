<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="205" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="20" endOffset="66"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="91" startOffset="24" endLine="97" endOffset="53"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="113" startOffset="24" endLine="119" endOffset="53"/></Target><Target id="@+id/tv_forgot_password" view="TextView"><Expressions/><location startLine="124" startOffset="20" endLine="136" endOffset="47"/></Target><Target id="@+id/btn_login" view="Button"><Expressions/><location startLine="144" startOffset="24" endLine="153" endOffset="53"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="155" startOffset="24" endLine="161" endOffset="55"/></Target><Target id="@+id/btn_google_sign_in" view="Button"><Expressions/><location startLine="166" startOffset="20" endLine="177" endOffset="49"/></Target><Target id="@+id/btn_skip_login" view="Button"><Expressions/><location startLine="187" startOffset="20" endLine="195" endOffset="69"/></Target></Targets></Layout>