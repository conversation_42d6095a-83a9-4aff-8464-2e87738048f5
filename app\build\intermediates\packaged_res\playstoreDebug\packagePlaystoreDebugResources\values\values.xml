<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="allowinsecures" translatable="false">
        <item/>
        <item>true</item>
        <item>false</item>
    </string-array>
    <string-array name="core_loglevel" translatable="false">
        <item>debug</item>
        <item>info</item>
        <item>warning</item>
        <item>error</item>
        <item>none</item>
    </string-array>
    <string-array name="flows" translatable="false">
        <item/>
        <item>xtls-rprx-vision</item>
        <item>xtls-rprx-vision-udp443</item>
    </string-array>
    <string-array name="fragment_packets" translatable="false">
        <item>tlshello</item>
        <item>1-2</item>
        <item>1-3</item>
        <item>1-5</item>
    </string-array>
    <string-array name="header_type_kcp_and_quic" translatable="false">
        <item>none</item>
        <item>srtp</item>
        <item>utp</item>
        <item>wechat-video</item>
        <item>dtls</item>
        <item>wireguard</item>
        <item>dns</item>
    </string-array>
    <string-array name="header_type_tcp" translatable="false">
        <item>none</item>
        <item>http</item>
    </string-array>
    <string-array name="language_select" translatable="false">
        <item>العربية (مصر)</item>
        <item>English</item>
    </string-array>
    <string-array name="language_select_value" translatable="false">
        <item>ar-rEG</item>
        <item>en</item>
    </string-array>
    <string-array name="mode_entries">
        <item>VPN</item>
        <item>Proxy only</item>
    </string-array>
    <string-array name="mode_type_grpc" translatable="false">
        <item>gun</item>
        <item>multi</item>
        
    </string-array>
    <string-array name="mode_value" translatable="false">
        <item>VPN</item>
        <item>Proxy only</item>
    </string-array>
    <string-array name="mux_xudp_quic_entries">
        <item>reject</item>
        <item>allow</item>
        <item>skip</item>
    </string-array>
    <string-array name="mux_xudp_quic_value" translatable="false">
        <item>reject</item>
        <item>allow</item>
        <item>skip</item>
    </string-array>
    <string-array name="networks" translatable="false">
        <item>tcp</item>
        <item>kcp</item>
        <item>ws</item>
        <item>httpupgrade</item>
        <item>xhttp</item>
        <item>h2</item>
        <item>grpc</item>
    </string-array>
    <string-array name="outbound_tag" translatable="false">
        <item>proxy</item>
        <item>direct</item>
        <item>block</item>
    </string-array>
    <string-array name="preset_rulesets">
        <item>China Whitelist</item>
        <item>China Blacklist</item>
        <item>Global</item>
        <item>Iran Whitelist</item>
    </string-array>
    <string-array name="routing_domain_strategy" translatable="false">
        <item>AsIs</item>
        <item>IPIfNonMatch</item>
        <item>IPOnDemand</item>
    </string-array>
    <string-array name="securitys" translatable="false">
        <item>chacha20-poly1305</item>
        <item>aes-128-gcm</item>
        <item>auto</item>
        <item>none</item>
        <item>zero</item>
    </string-array>
    <string-array name="share_method">
        <item>QRcode</item>
        <item>Export to clipboard</item>
        <item>Export full configuration to clipboard</item>
    </string-array>
    <string-array name="share_method_more">
        <item>QRcode</item>
        <item>Export to clipboard</item>
        <item>Export full configuration to clipboard</item>
        <item>Edit</item>
        <item>Delete</item>
    </string-array>
    <string-array name="share_sub_method">
        <item>QRcode</item>
        <item>Export to clipboard</item>
    </string-array>
    <string-array name="ss_securitys" translatable="false">
        <item>aes-256-gcm</item>
        <item>aes-128-gcm</item>
        <item>chacha20-poly1305</item>
        <item>chacha20-ietf-poly1305</item>
        <item>xchacha20-poly1305</item>
        <item>xchacha20-ietf-poly1305</item>
        <item>none</item>
        <item>plain</item>
        <item>2022-blake3-aes-128-gcm</item>
        <item>2022-blake3-aes-256-gcm</item>
        <item>2022-blake3-chacha20-poly1305</item>
    </string-array>
    <string-array name="streamsecurity_alpn" translatable="false">
        <item/>
        <item>h3</item>
        <item>h2</item>
        <item>http/1.1</item>
        <item>h3,h2,http/1.1</item>
        <item>h3,h2</item>
        <item>h2,http/1.1</item>
    </string-array>
    <string-array name="streamsecurity_utls" translatable="false">
        <item/>
        <item>chrome</item>
        <item>firefox</item>
        <item>safari</item>
        <item>ios</item>
        <item>android</item>
        <item>edge</item>
        <item>360</item>
        <item>qq</item>
        <item>random</item>
        <item>randomized</item>
    </string-array>
    <string-array name="streamsecuritys" translatable="false">
        <item/>
        <item>tls</item>
    </string-array>
    <string-array name="streamsecurityxs" translatable="false">
        <item/>
        <item>tls</item>
        <item>reality</item>
    </string-array>
    <string-array name="ui_mode_night">
        <item>Follow system</item>
        <item>Light</item>
        <item>Dark</item>
    </string-array>
    <string-array name="ui_mode_night_value" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>
    <string-array name="vpn_bypass_lan">
        <item>Follow config</item>
        <item>Bypass</item>
        <item>Not Bypass</item>
    </string-array>
    <string-array name="vpn_bypass_lan_value" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>
    <string-array name="xhttp_mode" translatable="false">
        <item>auto</item>
        <item>packet-up</item>
        <item>stream-up</item>
        <item>stream-one</item>
    </string-array>
    <color name="background_color">#FAFAFA</color>
    <color name="blue">#2196F3</color>
    <color name="card_background">#FFFFFF</color>
    <color name="colorAccent">#000000</color>
    <color name="colorConfigType">#f97910</color>
    <color name="colorPing">#009966</color>
    <color name="colorPingRed">#FF0099</color>
    <color name="colorPrimary">#F5F5F5</color>
    <color name="color_fab_active">#f97910</color>
    <color name="color_fab_inactive">#9C9C9C</color>
    <color name="color_secondary">#727272</color>
    <color name="divider_color">#E0E0E0</color>
    <color name="divider_color_light">#E0E0E0</color>
    <color name="google_text_color">#757575</color>
    <color name="gray">#757575</color>
    <color name="green">#4CAF50</color>
    <color name="ic_banner_background">#FFFFFF</color>
    <color name="ic_launcher_background">#FFFFFF</color>
    <color name="light_gray">#F5F5F5</color>
    <color name="login_background_end">#BBDEFB</color>
    <color name="login_background_start">#E3F2FD</color>
    <color name="orange">#FF9800</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="purple">#9C27B0</color>
    <color name="red">#F44336</color>
    <color name="secondary_text_color">#9E9E9E</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="white">#FFFFFF</color>
    <dimen name="image_size_dp24">24dp</dimen>
    <dimen name="padding_spacing_dp16">16dp</dimen>
    <dimen name="padding_spacing_dp4">4dp</dimen>
    <dimen name="padding_spacing_dp8">8dp</dimen>
    <dimen name="view_height_dp160">160dp</dimen>
    <dimen name="view_height_dp36">36dp</dimen>
    <dimen name="view_height_dp48">48dp</dimen>
    <dimen name="view_height_dp64">64dp</dimen>
    <string name="access_denied">ليس لديك صلاحية للوصول</string>
    <string name="add_server">إضافة سيرفر</string>
    <string name="admin_info_title">معلومات المدير</string>
    <string name="admin_panel_title">لوحة الإدارة</string>
    <string name="app_name" translatable="false">v2hoor</string>
    <string name="app_tile_first_use">First use of this feature, please use the app to add server</string>
    <string name="app_tile_name">Switch</string>
    <string name="app_widget_name">Switch</string>
    <string name="asset_geo_files_sources">Geo files source (optional)</string>
    <string name="connection_connected">Connected, tap to check connection</string>
    <string name="connection_not_connected">Not connected</string>
    <string name="connection_test_available">Success: Connection took %dms</string>
    <string name="connection_test_error">Fail to detect internet connection: %s</string>
    <string name="connection_test_error_status_code">Error code: #%d</string>
    <string name="connection_test_fail">Internet Unavailable</string>
    <string name="connection_test_pending">Check Connectivity</string>
    <string name="connection_test_testing">Testing…</string>
    <string name="connection_test_testing_count">Testing %d configurations…</string>
    <string name="default_web_client_id" translatable="false">426934856046-a8sh16r2vvp6ufeipctdiu5sfi24o9m1.apps.googleusercontent.com</string>
    <string name="del_config_comfirm">Confirm delete ?</string>
    <string name="del_invalid_config_comfirm">Please test before deleting! Confirm delete ?</string>
    <string name="email">Email</string>
    <string name="enter_email_for_reset">Please enter your email address</string>
    <string name="error_email_required">Email is required</string>
    <string name="error_invalid_email">Please enter a valid email address</string>
    <string name="error_password_required">Password is required</string>
    <string name="error_password_too_short">Password must be at least 6 characters</string>
    <string name="filter_config_all">All groups</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="gcm_defaultSenderId" translatable="false">426934856046</string>
    <string name="google_api_key" translatable="false">AIzaSyAl6p2bf3DCthK7B86C-eNsyrWoHkibmD4</string>
    <string name="google_app_id" translatable="false">1:426934856046:android:6234baf1e1847f718eb97f</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAl6p2bf3DCthK7B86C-eNsyrWoHkibmD4</string>
    <string name="google_sign_in_failed">Google sign in failed</string>
    <string name="google_storage_bucket" translatable="false">mrelfeky-209615.firebasestorage.app</string>
    <string name="guest_user">Guest User</string>
    <string name="import_subscription_failure">Import subscription failed</string>
    <string name="import_subscription_success">Subscription imported Successfully</string>
    <string name="logcat_clear">Clear</string>
    <string name="logcat_copy">Copy</string>
    <string name="login">Login</string>
    <string name="login_failed">Login failed</string>
    <string name="login_subtitle">قم بتسجيل الدخول للوصول إلى جميع الميزات</string>
    <string name="login_success">Login successful!</string>
    <string name="logout">Logout</string>
    <string name="logout_confirmation">Are you sure you want to logout?</string>
    <string name="logout_success">Logged out successfully</string>
    <string name="manage_servers">إدارة السيرفرات</string>
    <string name="manage_users">إدارة المستخدمين</string>
    <string name="menu_item_add_asset">Add asset</string>
    <string name="menu_item_add_config">Add config</string>
    <string name="menu_item_add_file">Add files</string>
    <string name="menu_item_add_url">Add URL</string>
    <string name="menu_item_del_config">Delete config</string>
    <string name="menu_item_download_file">Download files</string>
    <string name="menu_item_export_proxy_app">Export to Clipboard</string>
    <string name="menu_item_import_config_clipboard">Import config from Clipboard</string>
    <string name="menu_item_import_config_local">Import config from locally</string>
    <string name="menu_item_import_config_manually_http">Type manually[HTTP]</string>
    <string name="menu_item_import_config_manually_hysteria2">Type manually[Hysteria2]</string>
    <string name="menu_item_import_config_manually_socks">Type manually[SOCKS]</string>
    <string name="menu_item_import_config_manually_ss">Type manually[Shadowsocks]</string>
    <string name="menu_item_import_config_manually_trojan">Type manually[Trojan]</string>
    <string name="menu_item_import_config_manually_vless">Type manually[VLESS]</string>
    <string name="menu_item_import_config_manually_vmess">Type manually[VMess]</string>
    <string name="menu_item_import_config_manually_wireguard">Type manually[Wireguard]</string>
    <string name="menu_item_import_config_qrcode">Import config from QRcode</string>
    <string name="menu_item_import_proxy_app">Import from Clipboard</string>
    <string name="menu_item_save_config">Save config</string>
    <string name="menu_item_scan_qrcode">Scan QRcode</string>
    <string name="menu_item_search">Search</string>
    <string name="menu_item_select_all">Select all</string>
    <string name="menu_item_select_proxy_app">Auto select proxy app</string>
    <string name="migration_fail">Data migration failed!</string>
    <string name="migration_success">Data migration success!</string>
    <string name="msg_dialog_progress">Loading</string>
    <string name="msg_downloading_content">Downloading content</string>
    <string name="msg_enter_keywords">Enter keywords</string>
    <string name="msg_file_not_found">File not found</string>
    <string name="msg_remark_is_duplicate">The remarks already exists</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="notification_action_more">Click for more</string>
    <string name="notification_action_stop_v2ray">Stop</string>
    <string name="password">Password</string>
    <string name="password_reset_failed">Failed to send password reset email</string>
    <string name="password_reset_sent">Password reset email sent</string>
    <string name="per_app_proxy_settings">Per-app settings</string>
    <string name="per_app_proxy_settings_enable">Enable per-app</string>
    <string name="project_id" translatable="false">mrelfeky-209615</string>
    <string name="promote_user">ترقية مستخدم</string>
    <string name="pull_down_to_refresh">Please pull down to refresh!</string>
    <string name="register">Register</string>
    <string name="register_failed">Registration failed</string>
    <string name="register_success">Registration successful!</string>
    <string name="routing_settings_add_rule">Add rule</string>
    <string name="routing_settings_delete">Clear</string>
    <string name="routing_settings_domain" translatable="false">domain</string>
    <string name="routing_settings_domain_strategy">Domain strategy</string>
    <string name="routing_settings_export_rulesets_to_clipboard">Export ruleset to clipboard</string>
    <string name="routing_settings_import_predefined_rulesets">Import predefined rulesets</string>
    <string name="routing_settings_import_rulesets_from_clipboard">Import ruleset from clipboard</string>
    <string name="routing_settings_import_rulesets_from_qrcode">Import ruleset from QRcode</string>
    <string name="routing_settings_import_rulesets_tip">Existing rulesets will be deleted, are you sure to continue?</string>
    <string name="routing_settings_ip" translatable="false">ip</string>
    <string name="routing_settings_locked">Locked, keep this rule when import presets</string>
    <string name="routing_settings_network" translatable="false">network</string>
    <string name="routing_settings_network_tip" translatable="false">[udp|tcp]</string>
    <string name="routing_settings_outbound_tag" translatable="false">outboundTag</string>
    <string name="routing_settings_port" translatable="false">port</string>
    <string name="routing_settings_protocol" translatable="false">protocol</string>
    <string name="routing_settings_protocol_tip" translatable="false">[http,tls,bittorrent]</string>
    <string name="routing_settings_rule_title">Routing Rule Settings</string>
    <string name="routing_settings_save">Save</string>
    <string name="routing_settings_tips">Separated by commas(,), choose domain or ip</string>
    <string name="routing_settings_title">Routing Settings</string>
    <string name="server_customize_config">Customize Config</string>
    <string name="server_lab_address">address</string>
    <string name="server_lab_address3">address</string>
    <string name="server_lab_allow_insecure">allowInsecure</string>
    <string name="server_lab_alterid">alterId</string>
    <string name="server_lab_bandwidth_down">Bandwidth down (units)</string>
    <string name="server_lab_bandwidth_up">Bandwidth up (units)</string>
    <string name="server_lab_content">Content</string>
    <string name="server_lab_encryption">encryption</string>
    <string name="server_lab_flow">flow</string>
    <string name="server_lab_head_type">head type</string>
    <string name="server_lab_id">id</string>
    <string name="server_lab_id3">password</string>
    <string name="server_lab_id4">Password(Optional)</string>
    <string name="server_lab_local_address">Local address (optional IPv4/IPv6, separated by commas)</string>
    <string name="server_lab_local_mtu">Mtu(optional, default 1420)</string>
    <string name="server_lab_mode_type">gRPC mode</string>
    <string name="server_lab_more_function">Transport</string>
    <string name="server_lab_need_inbound">Ensure inbounds port is consistent with the settings</string>
    <string name="server_lab_network">Network</string>
    <string name="server_lab_path">path</string>
    <string name="server_lab_path_grpc">gRPC serviceName</string>
    <string name="server_lab_path_h2">h2 path</string>
    <string name="server_lab_path_httpupgrade">httpupgrade path</string>
    <string name="server_lab_path_kcp">kcp seed</string>
    <string name="server_lab_path_quic">QUIC key</string>
    <string name="server_lab_path_ws">ws path</string>
    <string name="server_lab_path_xhttp">xhttp path</string>
    <string name="server_lab_port">port</string>
    <string name="server_lab_port3">port</string>
    <string name="server_lab_port_hop">Port Hopping(will override the port)</string>
    <string name="server_lab_port_hop_interval">Port Hopping Interval</string>
    <string name="server_lab_preshared_key">PreSharedKey(optional)</string>
    <string name="server_lab_public_key">PublicKey</string>
    <string name="server_lab_remarks">remarks</string>
    <string name="server_lab_request_host">host</string>
    <string name="server_lab_request_host6">Host(SNI)(Optional)</string>
    <string name="server_lab_request_host_grpc">gRPC Authority</string>
    <string name="server_lab_request_host_h2">h2 host</string>
    <string name="server_lab_request_host_http">http host</string>
    <string name="server_lab_request_host_httpupgrade">httpupgrade host</string>
    <string name="server_lab_request_host_quic">QUIC security</string>
    <string name="server_lab_request_host_ws">ws host</string>
    <string name="server_lab_request_host_xhttp">xhttp host</string>
    <string name="server_lab_reserved">Reserved(Optional, separated by commas)</string>
    <string name="server_lab_secret_key">SecretKey</string>
    <string name="server_lab_security">security</string>
    <string name="server_lab_security3">security</string>
    <string name="server_lab_security4">User(Optional)</string>
    <string name="server_lab_short_id">ShortId</string>
    <string name="server_lab_sni">SNI</string>
    <string name="server_lab_spider_x">SpiderX</string>
    <string name="server_lab_stream_alpn">Alpn</string>
    <string name="server_lab_stream_fingerprint" translatable="false">Fingerprint</string>
    <string name="server_lab_stream_pinsha256">pinSHA256</string>
    <string name="server_lab_stream_security">TLS</string>
    <string name="server_lab_xhttp_extra">XHTTP Extra raw JSON, format: { XHTTPObject }</string>
    <string name="server_lab_xhttp_mode">XHTTP Mode</string>
    <string name="server_logs">سجلات السيرفرات</string>
    <string name="server_management_section">إدارة السيرفرات</string>
    <string name="server_obfs_password">Obfs password</string>
    <string name="server_update_failure">Failed to update servers</string>
    <string name="server_update_in_progress">Updating servers...</string>
    <string name="server_update_no_new">No new servers to update</string>
    <string name="server_update_success">Successfully updated %d servers</string>
    <string name="sign_in_with_google">Sign in with Google</string>
    <string name="sub_allow_insecure_url">Allow insecure HTTP address</string>
    <string name="sub_auto_update">Enable automatic update</string>
    <string name="sub_setting_enable">Enable update</string>
    <string name="sub_setting_filter">Remarks regular filter</string>
    <string name="sub_setting_next_profile">Next proxy configuration remarks</string>
    <string name="sub_setting_pre_profile">Previous proxy configuration remarks</string>
    <string name="sub_setting_pre_profile_tip">The configuration remarks exists and is unique</string>
    <string name="sub_setting_remarks">remarks</string>
    <string name="sub_setting_url">Optional URL</string>
    <string name="summary_configuration_backup">Storage location: [%s], The backup will be cleared after uninstalling the app or clearing the storage</string>
    <string name="summary_pref_allow_insecure">When TLS is selected, allow insecure connections by default</string>
    <string name="summary_pref_append_http_proxy">HTTP proxy will be used directly from (browser/ some supported apps), without going through the virtual NIC device (Android 10+)</string>
    <string name="summary_pref_auto_server_update">Automatically update servers from database at specified intervals</string>
    <string name="summary_pref_auto_update_subscription">Update your subscriptions automatically at set intervals in the background. Depending on the device, this feature may not always work</string>
    <string name="summary_pref_confirm_remove">Whether to delete the configuration file requires a second confirmation by the user</string>
    <string name="summary_pref_delay_test_url">Url</string>
    <string name="summary_pref_dns_hosts">domain:address,…</string>
    <string name="summary_pref_domestic_dns">DNS</string>
    <string name="summary_pref_double_column_display">The profile list is displayed in double columns, allowing more content to be displayed on the screen. You need to restart the application to take effect.</string>
    <string name="summary_pref_fake_dns_enabled">Local DNS returns fake IP addresses (faster, but it may not work for some apps)</string>
    <string name="summary_pref_feedback">Feedback enhancements or bugs to GitHub</string>
    <string name="summary_pref_is_booted">Automatically connects to the selected server at startup, which may be unsuccessful</string>
    <string name="summary_pref_local_dns_enabled">DNS processed by core‘s DNS module (Recommended if you need routing bypassing LAN and mainland addresses)</string>
    <string name="summary_pref_local_dns_port">Local DNS port</string>
    <string name="summary_pref_manual_server_update">Tap to update servers from database now</string>
    <string name="summary_pref_mux_enabled">Faster, but it may cause unstable connectivity\nCustomize how to handle TCP, UDP and QUIC below</string>
    <string name="summary_pref_per_app_proxy">General: Checked apps use proxy, unchecked apps connect directly; \nBypass mode: checked apps connect directly, unchecked apps use proxy. \nThe option to automatically select proxy applications is in the menu</string>
    <string name="summary_pref_prefer_ipv6">Enable IPv6 routes and Prefer IPv6 addresses</string>
    <string name="summary_pref_proxy_sharing_enabled">Other devices can connect to proxy by your IP address through local proxy. Only enable in trusted networks to avoid unauthorized connections</string>
    <string name="summary_pref_remote_dns">DNS</string>
    <string name="summary_pref_route_only_enabled">Use the sniffed domain name for routing only, and keep the target address as the IP address.</string>
    <string name="summary_pref_sniffing_enabled">Try sniff domain from the packet (default on)</string>
    <string name="summary_pref_socks_port">Local proxy port</string>
    <string name="summary_pref_speed_enabled">Display current speed in the notification.\nNotification icon would change based on
        usage.</string>
    <string name="summary_pref_start_scan_immediate">Open the camera to scan immediately at startup, otherwise you can choose to scan the code or select a photo in the toolbar</string>
    <string name="summary_pref_tg_group">Join Telegram Group</string>
    <string name="switch_bypass_apps_mode">Bypass Mode</string>
    <string name="system_settings">إعدادات النظام</string>
    <string name="system_settings_section">إعدادات النظام</string>
    <string name="tasker_setting_confirm">Confirm</string>
    <string name="tasker_start_service">Start Service</string>
    <string name="title_about">About</string>
    <string name="title_advanced">Advanced Settings</string>
    <string name="title_configuration_backup">Backup configuration</string>
    <string name="title_configuration_restore">Restore configuration</string>
    <string name="title_configuration_share">Share configuration</string>
    <string name="title_core_loglevel">Log Level</string>
    <string name="title_del_all_config">Delete current group configuration</string>
    <string name="title_del_config_count">Delete %d configurations</string>
    <string name="title_del_duplicate_config">Delete current group duplicate configuration</string>
    <string name="title_del_duplicate_config_count">Delete %d duplicate configurations</string>
    <string name="title_del_invalid_config">Delete current group invalid configuration</string>
    <string name="title_export_all">Export current group non-custom configs to clipboard</string>
    <string name="title_export_config_count">Export %d configurations</string>
    <string name="title_file_chooser">Select a Config File</string>
    <string name="title_filter_config">Filter configuration file</string>
    <string name="title_fragment_settings">Fragment Settings</string>
    <string name="title_import_config_count">Import %d configurations</string>
    <string name="title_language">Language</string>
    <string name="title_logcat">Logcat</string>
    <string name="title_login">Login</string>
    <string name="title_mode">Mode</string>
    <string name="title_mode_help">Click me for more help</string>
    <string name="title_mux_settings">Mux Settings</string>
    <string name="title_oss_license">Open Source licenses</string>
    <string name="title_ping_all_server">Tcping current group configuration</string>
    <string name="title_pref_allow_insecure">allowInsecure</string>
    <string name="title_pref_append_http_proxy">Append HTTP Proxy to VPN</string>
    <string name="title_pref_auto_server_update">Auto update servers</string>
    <string name="title_pref_auto_server_update_interval">Server update interval (minutes, minimum 15)</string>
    <string name="title_pref_auto_update_interval">Auto Update Interval (Minutes, Min value 15)</string>
    <string name="title_pref_auto_update_subscription">Automatic update subscriptions</string>
    <string name="title_pref_confirm_remove">Delete configuration file confirmation</string>
    <string name="title_pref_delay_test_url">True delay test url (http/https)</string>
    <string name="title_pref_dns_hosts">DNS hosts (Format: domain:address,…)</string>
    <string name="title_pref_domestic_dns">Domestic DNS (Optional)</string>
    <string name="title_pref_double_column_display">Enable double column display</string>
    <string name="title_pref_fake_dns_enabled">Enable fake DNS</string>
    <string name="title_pref_feedback">Feedback</string>
    <string name="title_pref_fragment_enabled">Enable Fragment</string>
    <string name="title_pref_fragment_interval">Fragment Interval (min-max)</string>
    <string name="title_pref_fragment_length">Fragment Length (min-max)</string>
    <string name="title_pref_fragment_packets">Fragment Packets</string>
    <string name="title_pref_is_booted">Auto connect at startup</string>
    <string name="title_pref_local_dns_enabled">Enable local DNS</string>
    <string name="title_pref_local_dns_port">Local DNS port</string>
    <string name="title_pref_manual_server_update">Manual server update</string>
    <string name="title_pref_mux_concurency">TCP connections（range -1 to 1024）</string>
    <string name="title_pref_mux_enabled">Enable Mux</string>
    <string name="title_pref_mux_xudp_concurency">XUDP connections（range -1 to 1024）</string>
    <string name="title_pref_mux_xudp_quic">Handling of QUIC in mux tunnel</string>
    <string name="title_pref_per_app_proxy">Per-app proxy</string>
    <string name="title_pref_prefer_ipv6">Prefer IPv6</string>
    <string name="title_pref_proxy_sharing_enabled">Allow connections from the LAN</string>
    <string name="title_pref_remote_dns">Remote DNS (udp/tcp/https/quic)(Optional)</string>
    <string name="title_pref_route_only_enabled">Enable routeOnly</string>
    <string name="title_pref_sniffing_enabled">Enable Sniffing</string>
    <string name="title_pref_socks_port">Local proxy port</string>
    <string name="title_pref_speed_enabled">Enable speed display</string>
    <string name="title_pref_start_scan_immediate">Start scanning immediately</string>
    <string name="title_pref_ui_mode_night">UI mode settings</string>
    <string name="title_pref_vpn_bypass_lan">Does VPN bypass LAN</string>
    <string name="title_pref_vpn_dns">VPN DNS (only IPv4/v6)</string>
    <string name="title_privacy_policy">Privacy policy</string>
    <string name="title_real_ping_all_server">Real delay current group configuration</string>
    <string name="title_server">Configuration file</string>
    <string name="title_server_settings">Server Settings</string>
    <string name="title_service_restart">Service restart</string>
    <string name="title_settings">Settings</string>
    <string name="title_sort_by_test_results">Sorting by test results</string>
    <string name="title_source_code">Source code</string>
    <string name="title_sub_setting">Subscription group setting</string>
    <string name="title_sub_update">Update current group subscription</string>
    <string name="title_tg_channel">Telegram channel</string>
    <string name="title_ui_settings">UI settings</string>
    <string name="title_update_config_count">Update %d configurations</string>
    <string name="title_url">URL</string>
    <string name="title_user_asset_add_url">Add asset URL</string>
    <string name="title_user_asset_setting">Asset files</string>
    <string name="title_vpn_settings">VPN Settings</string>
    <string name="toast_action_not_allowed">Action not allowed</string>
    <string name="toast_asset_copy_failed">File copy failed, please use File Manager</string>
    <string name="toast_config_file_invalid">Invalid Config</string>
    <string name="toast_decoding_failed">Decoding failed</string>
    <string name="toast_failure">Failure</string>
    <string name="toast_incorrect_protocol">Incorrect protocol</string>
    <string name="toast_insecure_url_protocol">Please do not use the insecure HTTP protocol subscription address</string>
    <string name="toast_invalid_url">Invalid URL</string>
    <string name="toast_malformed_josn">Config malformed</string>
    <string name="toast_none_data">There is nothing</string>
    <string name="toast_none_data_clipboard">There is no data in the clipboard</string>
    <string name="toast_permission_denied">Unable to obtain the permission</string>
    <string name="toast_permission_denied_notification">Unable to obtain the notification permission</string>
    <string name="toast_require_file_manager">Please install a File Manager.</string>
    <string name="toast_services_failure">Start Services Failure</string>
    <string name="toast_services_start">Start Services</string>
    <string name="toast_services_stop">Stop Services</string>
    <string name="toast_services_success">Start Services Success</string>
    <string name="toast_success">Success</string>
    <string name="toast_tg_app_not_found">Telegram app not found</string>
    <string name="toast_warning_pref_proxysharing_short">Allow connections from the LAN. Make sure you are in a trusted network</string>
    <string name="toggle_password_visibility">Toggle password visibility</string>
    <string name="update_already_latest_version">Already on the latest version</string>
    <string name="update_check_for_update">Check for update</string>
    <string name="update_check_pre_release">Check Pre-release</string>
    <string name="update_checking_for_update">Checking for update…</string>
    <string name="update_new_version_found">New version found: %s</string>
    <string name="update_now">Update now</string>
    <string name="update_servers_manually">تحديث السيرفرات يدوياً</string>
    <string name="user_avatar">User Avatar</string>
    <string name="user_management_section">إدارة المستخدمين</string>
    <string name="welcome_to_app">Welcome to V2HoorVPN</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar"/>
    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="AppTheme.NoActionBar.Translucent">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="AppThemeDayNight" parent="Theme.AppCompat.DayNight">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
    </style>
    <style name="AppThemeDayNight.NoActionBar" parent="AppThemeDayNight">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="BrandedSwitch" parent="AppTheme">
        <item name="colorAccent">@color/color_fab_active</item>
    </style>
    <style name="TabLayoutTextStyle" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
    </style>
</resources>