package com.mohamedrady.v2hoor.service

import android.content.Context
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.Auth
import io.github.jan.supabase.gotrue.auth
import io.github.jan.supabase.gotrue.providers.builtin.Email
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.from
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class SupabaseService private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: SupabaseService? = null

        fun getInstance(): SupabaseService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SupabaseService().also { INSTANCE = it }
            }
        }
    }

    private val supabase = createSupabaseClient(
        supabaseUrl = "https://myhzxxemhhpljbnhokll.supabase.co",
        supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Zt8Zt8Zt8Zt8Zt8Zt8Zt8Zt8Zt8Zt8Zt8Zt8Zt8"
    ) {
        install(Auth)
        install(Postgrest)
    }

    // Authentication methods
    suspend fun signUp(email: String, password: String): Result<Unit> {
        return try {
            supabase.auth.signUpWith(Email) {
                this.email = email
                this.password = password
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun signIn(email: String, password: String): Result<Unit> {
        return try {
            supabase.auth.signInWith(Email) {
                this.email = email
                this.password = password
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun signOut(): Result<Unit> {
        return try {
            supabase.auth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    fun getCurrentUser() = supabase.auth.currentUserOrNull()

    fun isUserLoggedIn() = supabase.auth.currentUserOrNull() != null

    // User session flow
    fun getUserSessionFlow(): Flow<Boolean> = flow {
        supabase.auth.sessionStatus.collect { status ->
            emit(status is io.github.jan.supabase.gotrue.SessionStatus.Authenticated)
        }
    }
}