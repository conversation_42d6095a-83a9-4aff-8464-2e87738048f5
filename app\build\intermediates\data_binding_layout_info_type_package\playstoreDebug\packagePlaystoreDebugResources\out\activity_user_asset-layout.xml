<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_asset" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_user_asset.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_user_asset_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="16"/></Target><Target id="@+id/pb_waiting" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="54"/></Target><Target id="@+id/main_content" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="17" startOffset="4" endLine="74" endOffset="43"/></Target><Target id="@+id/layout_geo_files_sources" view="LinearLayout"><Expressions/><location startLine="27" startOffset="12" endLine="51" endOffset="26"/></Target><Target id="@+id/tv_geo_files_sources_summary" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="50" endOffset="84"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="67" startOffset="12" endLine="72" endOffset="67"/></Target></Targets></Layout>