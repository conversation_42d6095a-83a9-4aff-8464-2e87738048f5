// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAboutBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final LinearLayout layoutBackup;

  @NonNull
  public final LinearLayout layoutFeedback;

  @NonNull
  public final LinearLayout layoutOssLicenses;

  @NonNull
  public final LinearLayout layoutPrivacyPolicy;

  @NonNull
  public final LinearLayout layoutRestore;

  @NonNull
  public final LinearLayout layoutShare;

  @NonNull
  public final LinearLayout layoutSoureCcode;

  @NonNull
  public final LinearLayout layoutTgChannel;

  @NonNull
  public final TextView tvBackupSummary;

  @NonNull
  public final TextView tvVersion;

  private ActivityAboutBinding(@NonNull ScrollView rootView, @NonNull LinearLayout layoutBackup,
      @NonNull LinearLayout layoutFeedback, @NonNull LinearLayout layoutOssLicenses,
      @NonNull LinearLayout layoutPrivacyPolicy, @NonNull LinearLayout layoutRestore,
      @NonNull LinearLayout layoutShare, @NonNull LinearLayout layoutSoureCcode,
      @NonNull LinearLayout layoutTgChannel, @NonNull TextView tvBackupSummary,
      @NonNull TextView tvVersion) {
    this.rootView = rootView;
    this.layoutBackup = layoutBackup;
    this.layoutFeedback = layoutFeedback;
    this.layoutOssLicenses = layoutOssLicenses;
    this.layoutPrivacyPolicy = layoutPrivacyPolicy;
    this.layoutRestore = layoutRestore;
    this.layoutShare = layoutShare;
    this.layoutSoureCcode = layoutSoureCcode;
    this.layoutTgChannel = layoutTgChannel;
    this.tvBackupSummary = tvBackupSummary;
    this.tvVersion = tvVersion;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAboutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_about, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAboutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layout_backup;
      LinearLayout layoutBackup = ViewBindings.findChildViewById(rootView, id);
      if (layoutBackup == null) {
        break missingId;
      }

      id = R.id.layout_feedback;
      LinearLayout layoutFeedback = ViewBindings.findChildViewById(rootView, id);
      if (layoutFeedback == null) {
        break missingId;
      }

      id = R.id.layout_oss_licenses;
      LinearLayout layoutOssLicenses = ViewBindings.findChildViewById(rootView, id);
      if (layoutOssLicenses == null) {
        break missingId;
      }

      id = R.id.layout_privacy_policy;
      LinearLayout layoutPrivacyPolicy = ViewBindings.findChildViewById(rootView, id);
      if (layoutPrivacyPolicy == null) {
        break missingId;
      }

      id = R.id.layout_restore;
      LinearLayout layoutRestore = ViewBindings.findChildViewById(rootView, id);
      if (layoutRestore == null) {
        break missingId;
      }

      id = R.id.layout_share;
      LinearLayout layoutShare = ViewBindings.findChildViewById(rootView, id);
      if (layoutShare == null) {
        break missingId;
      }

      id = R.id.layout_soure_ccode;
      LinearLayout layoutSoureCcode = ViewBindings.findChildViewById(rootView, id);
      if (layoutSoureCcode == null) {
        break missingId;
      }

      id = R.id.layout_tg_channel;
      LinearLayout layoutTgChannel = ViewBindings.findChildViewById(rootView, id);
      if (layoutTgChannel == null) {
        break missingId;
      }

      id = R.id.tv_backup_summary;
      TextView tvBackupSummary = ViewBindings.findChildViewById(rootView, id);
      if (tvBackupSummary == null) {
        break missingId;
      }

      id = R.id.tv_version;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      return new ActivityAboutBinding((ScrollView) rootView, layoutBackup, layoutFeedback,
          layoutOssLicenses, layoutPrivacyPolicy, layoutRestore, layoutShare, layoutSoureCcode,
          layoutTgChannel, tvBackupSummary, tvVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
