package com.mohamedrady.v2hoor.util

import android.content.Context
import android.util.Log
import com.mohamedrady.v2hoor.AppConfig
import kotlinx.coroutines.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Real-time error monitoring utility
 */
object ErrorMonitor {
    
    private var isMonitoring = false
    private var monitoringJob: Job? = null
    private var context: Context? = null
    
    fun startMonitoring(context: Context) {
        this.context = context.applicationContext
        
        if (isMonitoring) {
            CrashHandler.logWarning("ErrorMonitor", "🔄 Monitoring already started")
            return
        }
        
        isMonitoring = true
        CrashHandler.logInfo("ErrorMonitor", "🎯 Starting real-time error monitoring")
        
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            while (isMonitoring) {
                try {
                    checkForNewErrors()
                    delay(2000) // Check every 2 seconds
                } catch (e: Exception) {
                    CrashHandler.logError("ErrorMonitor", "Error in monitoring loop", e)
                    delay(5000) // Wait longer if there's an error
                }
            }
        }
    }
    
    fun stopMonitoring() {
        CrashHandler.logInfo("ErrorMonitor", "🛑 Stopping error monitoring")
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
    }
    
    private suspend fun checkForNewErrors() {
        context?.let { ctx ->
            val logFile = File(ctx.filesDir, "v2hoor_crashes.log")
            if (logFile.exists() && logFile.length() > 0) {
                // Log file size for monitoring
                val sizeKB = logFile.length() / 1024
                if (sizeKB > 100) { // If log file is larger than 100KB
                    CrashHandler.logWarning("ErrorMonitor", "📊 Log file size: ${sizeKB}KB - Consider clearing old logs")
                }
            }
        }
    }
    
    fun logActivityTransition(from: String, to: String) {
        CrashHandler.logInfo("Navigation", "🔄 Transitioning from $from to $to")
    }
    
    fun logUserAction(action: String, details: String = "") {
        val message = if (details.isNotEmpty()) "$action - $details" else action
        CrashHandler.logInfo("UserAction", "👆 $message")
    }
    
    fun logNetworkError(operation: String, error: String) {
        CrashHandler.logError("Network", "🌐 $operation failed: $error")
    }
    
    fun logDatabaseError(operation: String, error: String) {
        CrashHandler.logError("Database", "💾 $operation failed: $error")
    }
    
    fun logPermissionError(permission: String, reason: String) {
        CrashHandler.logError("Permission", "🔐 $permission denied: $reason")
    }
    
    fun logPerformanceIssue(operation: String, duration: Long) {
        if (duration > 3000) { // More than 3 seconds
            CrashHandler.logWarning("Performance", "⏱️ $operation took ${duration}ms (slow)")
        } else {
            CrashHandler.logInfo("Performance", "⚡ $operation completed in ${duration}ms")
        }
    }
    
    fun getErrorSummary(): String {
        return try {
            context?.let { ctx ->
                val logFile = File(ctx.filesDir, "v2hoor_crashes.log")
                if (logFile.exists()) {
                    val lines = logFile.readLines()
                    val errorCount = lines.count { it.contains("[ERROR]") || it.contains("[FATAL]") }
                    val warningCount = lines.count { it.contains("[WARNING]") }
                    val lastError = lines.lastOrNull { it.contains("[ERROR]") || it.contains("[FATAL]") }
                    
                    """
                    📊 Error Summary:
                    🔴 Errors: $errorCount
                    🟡 Warnings: $warningCount
                    📝 Last Error: ${lastError?.take(100) ?: "None"}
                    📁 Log Size: ${logFile.length() / 1024}KB
                    """.trimIndent()
                } else {
                    "📊 No error log file found"
                }
            } ?: "📊 Context not available"
        } catch (e: Exception) {
            "📊 Error reading log file: ${e.message}"
        }
    }
    
    fun clearOldLogs() {
        try {
            context?.let { ctx ->
                val logFile = File(ctx.filesDir, "v2hoor_crashes.log")
                if (logFile.exists()) {
                    val oldSize = logFile.length()
                    logFile.delete()
                    CrashHandler.logInfo("ErrorMonitor", "🗑️ Cleared log file (was ${oldSize / 1024}KB)")
                }
            }
        } catch (e: Exception) {
            CrashHandler.logError("ErrorMonitor", "Failed to clear logs", e)
        }
    }

    /**
     * Data class for error entries
     */
    data class ErrorEntry(
        val timestamp: String,
        val type: String,
        val message: String,
        val stackTrace: String = ""
    )

    private val errorEntries = mutableListOf<ErrorEntry>()
    private const val MAX_ERROR_ENTRIES = 50

    /**
     * Add error entry to memory
     */
    private fun addErrorEntry(entry: ErrorEntry) {
        synchronized(errorEntries) {
            errorEntries.add(entry)
            if (errorEntries.size > MAX_ERROR_ENTRIES) {
                errorEntries.removeAt(0)
            }
        }
    }

    /**
     * Get recent errors for display
     */
    fun getRecentErrors(): List<ErrorEntry> {
        return synchronized(errorEntries) {
            errorEntries.toList()
        }
    }

    /**
     * Log application error
     */
    fun logApplicationError(message: String, exception: Throwable? = null) {
        val entry = ErrorEntry(
            timestamp = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date()),
            type = "APPLICATION_ERROR",
            message = message,
            stackTrace = exception?.stackTraceToString() ?: ""
        )
        addErrorEntry(entry)
        CrashHandler.logError("Application", message, exception)
    }
}
