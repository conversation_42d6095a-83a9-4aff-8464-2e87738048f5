// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.Switch;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEditServerBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button buttonCancel;

  @NonNull
  public final Button buttonSave;

  @NonNull
  public final TextInputEditText editTextCity;

  @NonNull
  public final TextInputEditText editTextCountry;

  @NonNull
  public final TextInputEditText editTextMaxUsers;

  @NonNull
  public final TextInputEditText editTextPriority;

  @NonNull
  public final TextInputEditText editTextServerConfig;

  @NonNull
  public final TextInputEditText editTextServerName;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Switch switchServerActive;

  @NonNull
  public final Toolbar toolbar;

  private ActivityEditServerBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button buttonCancel, @NonNull Button buttonSave,
      @NonNull TextInputEditText editTextCity, @NonNull TextInputEditText editTextCountry,
      @NonNull TextInputEditText editTextMaxUsers, @NonNull TextInputEditText editTextPriority,
      @NonNull TextInputEditText editTextServerConfig,
      @NonNull TextInputEditText editTextServerName, @NonNull ProgressBar progressBar,
      @NonNull Switch switchServerActive, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonSave = buttonSave;
    this.editTextCity = editTextCity;
    this.editTextCountry = editTextCountry;
    this.editTextMaxUsers = editTextMaxUsers;
    this.editTextPriority = editTextPriority;
    this.editTextServerConfig = editTextServerConfig;
    this.editTextServerName = editTextServerName;
    this.progressBar = progressBar;
    this.switchServerActive = switchServerActive;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEditServerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEditServerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_edit_server, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEditServerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonCancel;
      Button buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.buttonSave;
      Button buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.editTextCity;
      TextInputEditText editTextCity = ViewBindings.findChildViewById(rootView, id);
      if (editTextCity == null) {
        break missingId;
      }

      id = R.id.editTextCountry;
      TextInputEditText editTextCountry = ViewBindings.findChildViewById(rootView, id);
      if (editTextCountry == null) {
        break missingId;
      }

      id = R.id.editTextMaxUsers;
      TextInputEditText editTextMaxUsers = ViewBindings.findChildViewById(rootView, id);
      if (editTextMaxUsers == null) {
        break missingId;
      }

      id = R.id.editTextPriority;
      TextInputEditText editTextPriority = ViewBindings.findChildViewById(rootView, id);
      if (editTextPriority == null) {
        break missingId;
      }

      id = R.id.editTextServerConfig;
      TextInputEditText editTextServerConfig = ViewBindings.findChildViewById(rootView, id);
      if (editTextServerConfig == null) {
        break missingId;
      }

      id = R.id.editTextServerName;
      TextInputEditText editTextServerName = ViewBindings.findChildViewById(rootView, id);
      if (editTextServerName == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.switchServerActive;
      Switch switchServerActive = ViewBindings.findChildViewById(rootView, id);
      if (switchServerActive == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityEditServerBinding((CoordinatorLayout) rootView, buttonCancel, buttonSave,
          editTextCity, editTextCountry, editTextMaxUsers, editTextPriority, editTextServerConfig,
          editTextServerName, progressBar, switchServerActive, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
