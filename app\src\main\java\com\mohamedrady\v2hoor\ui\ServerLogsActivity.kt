package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityServerLogsBinding

class ServerLogsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityServerLogsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityServerLogsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.server_logs)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
