// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class PreferenceWithHelpLinkBinding implements ViewBinding {
  @NonNull
  private final Button rootView;

  private PreferenceWithHelpLinkBinding(@NonNull Button rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public Button getRoot() {
    return rootView;
  }

  @NonNull
  public static PreferenceWithHelpLinkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PreferenceWithHelpLinkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.preference_with_help_link, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PreferenceWithHelpLinkBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new PreferenceWithHelpLinkBinding((Button) rootView);
  }
}
