package com.mohamedrady.v2hoor.util

import android.content.Context
import android.util.Log
import com.mohamedrady.v2hoor.AppConfig

/**
 * Global crash handler to catch and log unhandled exceptions
 */
class CrashHandler private constructor() : Thread.UncaughtExceptionHandler {
    
    private var defaultHandler: Thread.UncaughtExceptionHandler? = null
    private var context: Context? = null
    
    companion object {
        @Volatile
        private var instance: CrashHandler? = null
        
        fun getInstance(): CrashHandler {
            return instance ?: synchronized(this) {
                instance ?: CrashHandler().also { instance = it }
            }
        }
        
        fun init(context: Context) {
            val crashHandler = getInstance()
            crashHandler.context = context.applicationContext
            crashHandler.defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            Thread.setDefaultUncaughtExceptionHandler(crashHandler)
        }
    }
    
    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            // Log the crash
            Log.e(AppConfig.TAG, "Uncaught exception in thread ${thread.name}", exception)
            
            // Log stack trace
            val stackTrace = Log.getStackTraceString(exception)
            Log.e(AppConfig.TAG, "Stack trace: $stackTrace")
            
            // Log additional info
            Log.e(AppConfig.TAG, "Thread: ${thread.name}")
            Log.e(AppConfig.TAG, "Exception: ${exception.javaClass.simpleName}")
            Log.e(AppConfig.TAG, "Message: ${exception.message}")
            
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Error in crash handler", e)
        } finally {
            // Call the default handler
            defaultHandler?.uncaughtException(thread, exception)
        }
    }
}
