package com.mohamedrady.v2hoor.util

import android.content.Context
import android.os.Build
import android.util.Log
import com.mohamedrady.v2hoor.AppConfig
import java.io.File
import java.io.FileWriter
import java.io.PrintWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * Global crash handler to catch and log unhandled exceptions
 */
class CrashHandler private constructor() : Thread.UncaughtExceptionHandler {

    private var defaultHandler: Thread.UncaughtExceptionHandler? = null
    private var context: Context? = null

    companion object {
        @Volatile
        private var instance: CrashHandler? = null
        private const val CRASH_LOG_FILE = "v2hoor_crashes.log"

        fun getInstance(): CrashHandler {
            return instance ?: synchronized(this) {
                instance ?: CrashHandler().also { instance = it }
            }
        }

        fun init(context: Context) {
            val crashHandler = getInstance()
            crashHandler.context = context.applicationContext
            crashHandler.defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            Thread.setDefaultUncaughtExceptionHandler(crashHandler)

            // Log initialization
            Log.i(AppConfig.TAG, "🔧 CrashHandler initialized successfully")
        }

        fun logError(tag: String, message: String, throwable: Throwable? = null) {
            Log.e(AppConfig.TAG, "❌ [$tag] $message", throwable)
            getInstance().writeToFile("ERROR", tag, message, throwable)
        }

        fun logWarning(tag: String, message: String) {
            Log.w(AppConfig.TAG, "⚠️ [$tag] $message")
            getInstance().writeToFile("WARNING", tag, message, null)
        }

        fun logInfo(tag: String, message: String) {
            Log.i(AppConfig.TAG, "ℹ️ [$tag] $message")
            getInstance().writeToFile("INFO", tag, message, null)
        }
    }

    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

            // Log to Android Log
            Log.e(AppConfig.TAG, "💥 FATAL CRASH at $timestamp")
            Log.e(AppConfig.TAG, "🧵 Thread: ${thread.name}")
            Log.e(AppConfig.TAG, "🚨 Exception: ${exception.javaClass.simpleName}")
            Log.e(AppConfig.TAG, "📝 Message: ${exception.message}")
            Log.e(AppConfig.TAG, "📱 Device: ${Build.MANUFACTURER} ${Build.MODEL}")
            Log.e(AppConfig.TAG, "🤖 Android: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
            Log.e(AppConfig.TAG, "📋 Stack trace:", exception)

            // Write to file
            writeToFile("FATAL", "CRASH", "Uncaught exception in thread ${thread.name}", exception)

        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "💀 Error in crash handler itself", e)
        } finally {
            // Call the default handler
            defaultHandler?.uncaughtException(thread, exception)
        }
    }

    private fun writeToFile(level: String, tag: String, message: String, throwable: Throwable?) {
        try {
            context?.let { ctx ->
                val logFile = File(ctx.filesDir, CRASH_LOG_FILE)
                val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())

                FileWriter(logFile, true).use { writer ->
                    PrintWriter(writer).use { printer ->
                        printer.println("[$timestamp] [$level] [$tag] $message")
                        throwable?.let {
                            printer.println("Exception: ${it.javaClass.simpleName}")
                            printer.println("Message: ${it.message}")
                            it.printStackTrace(printer)
                        }
                        printer.println("---")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to write to crash log file", e)
        }
    }

    /**
     * Data class for log entries
     */
    data class LogEntry(
        val timestamp: String,
        val level: String,
        val tag: String,
        val message: String,
        val exception: String? = null
    )

    private val logEntries = mutableListOf<LogEntry>()
    private val MAX_LOG_ENTRIES = 100

    /**
     * Add log entry to memory
     */
    private fun addLogEntry(entry: LogEntry) {
        synchronized(logEntries) {
            logEntries.add(entry)
            if (logEntries.size > MAX_LOG_ENTRIES) {
                logEntries.removeAt(0)
            }
        }
    }

    /**
     * Get current timestamp
     */
    private fun getCurrentTimestamp(): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
    }

    /**
     * Log error with exception
     */
    fun logError(tag: String, message: String, exception: Throwable? = null) {
        val logEntry = LogEntry(
            timestamp = getCurrentTimestamp(),
            level = "ERROR",
            tag = tag,
            message = message,
            exception = exception?.stackTraceToString()
        )

        addLogEntry(logEntry)
        Log.e(AppConfig.TAG, "[$tag] $message", exception)
        writeToFile("ERROR", tag, message, exception)
    }

    /**
     * Log warning
     */
    fun logWarning(tag: String, message: String) {
        val logEntry = LogEntry(
            timestamp = getCurrentTimestamp(),
            level = "WARNING",
            tag = tag,
            message = message
        )

        addLogEntry(logEntry)
        Log.w(AppConfig.TAG, "[$tag] $message")
        writeToFile("WARNING", tag, message, null)
    }

    /**
     * Log info
     */
    fun logInfo(tag: String, message: String) {
        val logEntry = LogEntry(
            timestamp = getCurrentTimestamp(),
            level = "INFO",
            tag = tag,
            message = message
        )

        addLogEntry(logEntry)
        Log.i(AppConfig.TAG, "[$tag] $message")
        writeToFile("INFO", tag, message, null)
    }

    /**
     * Get recent logs for display
     */
    fun getRecentLogs(): List<LogEntry> {
        return synchronized(logEntries) {
            logEntries.toList()
        }
    }

    /**
     * Read logs from file
     */
    fun readLogsFromFile(): String {
        return try {
            context?.let { ctx ->
                val logFile = File(ctx.filesDir, CRASH_LOG_FILE)
                if (logFile.exists()) {
                    logFile.readText()
                } else {
                    "لا توجد سجلات محفوظة"
                }
            } ?: "لا يمكن الوصول للسجلات"
        } catch (e: Exception) {
            "خطأ في قراءة السجلات: ${e.message}"
        }
    }
}
