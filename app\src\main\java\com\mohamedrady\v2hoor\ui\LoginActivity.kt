package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityLoginBinding
import com.mohamedrady.v2hoor.service.SupabaseService
import com.mohamedrady.v2hoor.handler.MmkvManager
import kotlinx.coroutines.launch

class LoginActivity : BaseActivity() {
    private lateinit var binding: ActivityLoginBinding
    private val supabaseService = SupabaseService.getInstance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        checkIfUserLoggedIn()
    }

    private fun setupUI() {
        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.title_login)

        // Set up click listeners
        binding.btnLogin.setOnClickListener {
            performLogin()
        }

        binding.btnRegister.setOnClickListener {
            performRegister()
        }

        binding.tvForgotPassword.setOnClickListener {
            // TODO: Implement forgot password functionality
            Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
        }

        binding.btnSkipLogin.setOnClickListener {
            skipLogin()
        }
    }

    private fun checkIfUserLoggedIn() {
        if (supabaseService.isUserLoggedIn()) {
            navigateToMain()
        }
    }

    private fun performLogin() {
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()

        if (validateInput(email, password)) {
            showLoading(true)
            lifecycleScope.launch {
                val result = supabaseService.signIn(email, password)
                showLoading(false)
                
                if (result.isSuccess) {
                    Toast.makeText(this@LoginActivity, getString(R.string.login_success), Toast.LENGTH_SHORT).show()
                    saveLoginState()
                    navigateToMain()
                } else {
                    val error = result.exceptionOrNull()?.message ?: getString(R.string.login_failed)
                    Toast.makeText(this@LoginActivity, error, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun performRegister() {
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()

        if (validateInput(email, password)) {
            showLoading(true)
            lifecycleScope.launch {
                val result = supabaseService.signUp(email, password)
                showLoading(false)
                
                if (result.isSuccess) {
                    Toast.makeText(this@LoginActivity, getString(R.string.register_success), Toast.LENGTH_SHORT).show()
                    saveLoginState()
                    navigateToMain()
                } else {
                    val error = result.exceptionOrNull()?.message ?: getString(R.string.register_failed)
                    Toast.makeText(this@LoginActivity, error, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun validateInput(email: String, password: String): Boolean {
        if (email.isEmpty()) {
            binding.etEmail.error = getString(R.string.error_email_required)
            return false
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.error = getString(R.string.error_invalid_email)
            return false
        }

        if (password.isEmpty()) {
            binding.etPassword.error = getString(R.string.error_password_required)
            return false
        }

        if (password.length < 6) {
            binding.etPassword.error = getString(R.string.error_password_too_short)
            return false
        }

        return true
    }

    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.btnLogin.isEnabled = !show
        binding.btnRegister.isEnabled = !show
        binding.btnSkipLogin.isEnabled = !show
    }

    private fun saveLoginState() {
        MmkvManager.encodeSettings("user_logged_in", true)
    }

    private fun skipLogin() {
        MmkvManager.encodeSettings("user_logged_in", false)
        navigateToMain()
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
