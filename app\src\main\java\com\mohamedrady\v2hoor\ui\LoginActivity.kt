package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityLoginBinding
import com.mohamedrady.v2hoor.service.FirebaseAuthService
import com.mohamedrady.v2hoor.handler.MmkvManager
import kotlinx.coroutines.launch

class LoginActivity : BaseActivity() {
    private lateinit var binding: ActivityLoginBinding
    private val firebaseAuth = FirebaseAuthService.getInstance()
    private lateinit var googleSignInClient: GoogleSignInClient

    private val googleSignInLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
        try {
            val account = task.getResult(ApiException::class.java)
            account.idToken?.let { idToken ->
                signInWithGoogle(idToken)
            }
        } catch (e: ApiException) {
            Toast.makeText(this, getString(R.string.google_sign_in_failed), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupGoogleSignIn()
        setupUI()
        checkIfUserLoggedIn()
    }

    private fun setupGoogleSignIn() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(getString(R.string.default_web_client_id))
            .requestEmail()
            .build()

        googleSignInClient = GoogleSignIn.getClient(this, gso)
    }

    private fun setupUI() {
        // Set up toolbar
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            supportActionBar?.title = getString(R.string.title_login)
        } catch (e: Exception) {
            // Handle case where action bar is already set
            binding.toolbar.title = getString(R.string.title_login)
        }

        // Set up password toggle
        setupPasswordToggle()

        // Set up click listeners
        binding.btnLogin.setOnClickListener {
            performLogin()
        }

        binding.btnGoogleSignIn.setOnClickListener {
            signInWithGoogle()
        }

        binding.tvForgotPassword.setOnClickListener {
            showForgotPasswordDialog()
        }
    }

    private fun setupPasswordToggle() {
        binding.ivTogglePassword.setOnClickListener {
            if (binding.etPassword.transformationMethod == PasswordTransformationMethod.getInstance()) {
                // Show password
                binding.etPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                binding.ivTogglePassword.setImageResource(R.drawable.ic_visibility)
            } else {
                // Hide password
                binding.etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                binding.ivTogglePassword.setImageResource(R.drawable.ic_visibility_off)
            }
            // Move cursor to end
            binding.etPassword.setSelection(binding.etPassword.text.length)
        }
    }

    private fun checkIfUserLoggedIn() {
        if (firebaseAuth.isUserLoggedIn()) {
            navigateToMain()
        }
    }

    private fun performLogin() {
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()

        if (validateInput(email, password)) {
            showLoading(true)
            lifecycleScope.launch {
                val result = firebaseAuth.signIn(email, password)
                showLoading(false)
                
                if (result.isSuccess) {
                    Toast.makeText(this@LoginActivity, getString(R.string.login_success), Toast.LENGTH_SHORT).show()
                    saveLoginState()
                    navigateToMain()
                } else {
                    val error = result.exceptionOrNull()?.message ?: getString(R.string.login_failed)
                    Toast.makeText(this@LoginActivity, error, Toast.LENGTH_LONG).show()
                }
            }
        }
    }



    private fun signInWithGoogle() {
        val signInIntent = googleSignInClient.signInIntent
        googleSignInLauncher.launch(signInIntent)
    }

    private fun signInWithGoogle(idToken: String) {
        showLoading(true)
        lifecycleScope.launch {
            val result = firebaseAuth.signInWithGoogle(idToken)
            showLoading(false)
            
            if (result.isSuccess) {
                Toast.makeText(this@LoginActivity, getString(R.string.login_success), Toast.LENGTH_SHORT).show()
                saveLoginState()
                navigateToMain()
            } else {
                val error = result.exceptionOrNull()?.message ?: getString(R.string.google_sign_in_failed)
                Toast.makeText(this@LoginActivity, error, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun showForgotPasswordDialog() {
        val email = binding.etEmail.text.toString().trim()
        if (email.isEmpty()) {
            Toast.makeText(this, getString(R.string.enter_email_for_reset), Toast.LENGTH_SHORT).show()
            return
        }

        showLoading(true)
        lifecycleScope.launch {
            val result = firebaseAuth.sendPasswordResetEmail(email)
            showLoading(false)
            
            if (result.isSuccess) {
                Toast.makeText(this@LoginActivity, getString(R.string.password_reset_sent), Toast.LENGTH_LONG).show()
            } else {
                val error = result.exceptionOrNull()?.message ?: getString(R.string.password_reset_failed)
                Toast.makeText(this@LoginActivity, error, Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun validateInput(email: String, password: String): Boolean {
        if (email.isEmpty()) {
            binding.etEmail.error = getString(R.string.error_email_required)
            return false
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.error = getString(R.string.error_invalid_email)
            return false
        }

        if (password.isEmpty()) {
            binding.etPassword.error = getString(R.string.error_password_required)
            return false
        }

        if (password.length < 6) {
            binding.etPassword.error = getString(R.string.error_password_too_short)
            return false
        }

        return true
    }

    private fun showLoading(show: Boolean) {
        if (show) {
            binding.progressBar.visibility = View.VISIBLE
            binding.btnLogin.text = ""
            binding.btnLogin.isEnabled = false
            binding.btnGoogleSignIn.isEnabled = false
        } else {
            binding.progressBar.visibility = View.GONE
            binding.btnLogin.text = getString(R.string.login)
            binding.btnLogin.isEnabled = true
            binding.btnGoogleSignIn.isEnabled = true
        }
    }

    private fun saveLoginState() {
        MmkvManager.encodeSettings("user_logged_in", true)
    }



    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
