<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".ui.AdminPanelActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Admin Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_admin_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/admin_info_title"
                        android:textColor="@color/primary_color"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_admin_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="<EMAIL>"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_admin_level"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="مدير عام"
                        android:textColor="@color/primary_color"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_permissions_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="الصلاحيات: 9/9"
                        android:textSize="12sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Server Management Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/server_management_section"
                android:textColor="@color/primary_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="vertical">

                <!-- Server Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_server_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_server"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/manage_servers"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_arrow_forward"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Add Server Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_add_server"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_add"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/add_server"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_arrow_forward"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Server Update Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_server_update"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/ic_refresh"
                                app:tint="@color/primary_color" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="16dp"
                                android:layout_weight="1"
                                android:text="@string/update_servers_manually"
                                android:textSize="16sp" />

                            <ProgressBar
                                android:id="@+id/progress_bar"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_gravity="center_vertical"
                                android:visibility="gone" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_update_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="اضغط لتحديث السيرفرات من قاعدة البيانات"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Server Logs Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_server_logs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_list"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/server_logs"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_arrow_forward"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- User Management Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/user_management_section"
                android:textColor="@color/primary_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="vertical">

                <!-- User Management Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_user_management"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_people"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/manage_users"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_arrow_forward"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Promote User Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_promote_user"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_star"
                            app:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="@string/promote_user"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_arrow_forward"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- System Settings Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/system_settings_section"
                android:textColor="@color/primary_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- System Settings Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_system_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_settings"
                        app:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="@string/system_settings"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_arrow_forward"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Debug Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:text="أدوات التشخيص"
                android:textColor="@color/primary_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- View Logs Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_view_logs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_description_24dp"
                        app:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="عرض سجلات التطبيق"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_arrow_forward"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
