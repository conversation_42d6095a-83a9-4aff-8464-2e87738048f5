package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.NestedScrollView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityLogViewerBinding
import com.mohamedrady.v2hoor.util.CrashHandler
import com.mohamedrady.v2hoor.util.ErrorMonitor
import java.io.File

class LogViewerActivity : AppCompatActivity() {
    private lateinit var binding: ActivityLogViewerBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLogViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        loadLogs()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "عرض السجلات"
        }
    }

    private fun loadLogs() {
        try {
            val logs = StringBuilder()

            // Add basic log information
            logs.append("=== سجلات التطبيق ===\n")
            logs.append("تم تحميل عارض السجلات بنجاح\n")
            logs.append("الوقت: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}\n\n")

            // Add error monitor summary
            logs.append("=== ملخص الأخطاء ===\n")
            try {
                val errorSummary = ErrorMonitor.getErrorSummary()
                logs.append(errorSummary)
                logs.append("\n\n")
            } catch (e: Exception) {
                logs.append("خطأ في تحميل ملخص الأخطاء: ${e.message}\n\n")
            }

            // Add system information
            logs.append("=== معلومات النظام ===\n")
            logs.append("Package: ${packageName}\n")
            try {
                val packageInfo = packageManager.getPackageInfo(packageName, 0)
                logs.append("Version: ${packageInfo.versionName}\n")
                logs.append("Build: ${packageInfo.versionCode}\n")
            } catch (e: Exception) {
                logs.append("خطأ في الحصول على معلومات الحزمة: ${e.message}\n")
            }
            logs.append("Android: ${android.os.Build.VERSION.RELEASE}\n")
            logs.append("Device: ${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}\n")
            logs.append("SDK: ${android.os.Build.VERSION.SDK_INT}\n")
            logs.append("\n")

            // Add log file information
            logs.append("=== معلومات ملف السجل ===\n")
            try {
                val logFile = File(filesDir, "v2hoor_crashes.log")
                if (logFile.exists()) {
                    logs.append("حجم الملف: ${logFile.length() / 1024}KB\n")
                    logs.append("آخر تعديل: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(logFile.lastModified()))}\n")

                    // Read last few lines
                    val lines = logFile.readLines()
                    logs.append("عدد الأسطر: ${lines.size}\n")
                    logs.append("\n--- آخر 10 أسطر من السجل ---\n")
                    lines.takeLast(10).forEach { line ->
                        logs.append("$line\n")
                    }
                } else {
                    logs.append("ملف السجل غير موجود\n")
                }
            } catch (e: Exception) {
                logs.append("خطأ في قراءة ملف السجل: ${e.message}\n")
            }

            // Display logs
            binding.textViewLogs.text = logs.toString()

            // Auto scroll to bottom
            binding.scrollView.post {
                binding.scrollView.fullScroll(NestedScrollView.FOCUS_DOWN)
            }

        } catch (e: Exception) {
            binding.textViewLogs.text = "خطأ في تحميل السجلات: ${e.message}"
            android.util.Log.e("LogViewer", "Failed to load logs", e)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
