// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecyclerMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout infoContainer;

  @NonNull
  public final LinearLayout itemBg;

  @NonNull
  public final LinearLayout layoutEdit;

  @NonNull
  public final LinearLayout layoutIndicator;

  @NonNull
  public final LinearLayout layoutMore;

  @NonNull
  public final LinearLayout layoutRemove;

  @NonNull
  public final LinearLayout layoutShare;

  @NonNull
  public final ConstraintLayout layoutSubscription;

  @NonNull
  public final TextView tvName;

  @NonNull
  public final TextView tvStatistics;

  @NonNull
  public final TextView tvSubscription;

  @NonNull
  public final TextView tvTestResult;

  @NonNull
  public final TextView tvType;

  private ItemRecyclerMainBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout infoContainer, @NonNull LinearLayout itemBg,
      @NonNull LinearLayout layoutEdit, @NonNull LinearLayout layoutIndicator,
      @NonNull LinearLayout layoutMore, @NonNull LinearLayout layoutRemove,
      @NonNull LinearLayout layoutShare, @NonNull ConstraintLayout layoutSubscription,
      @NonNull TextView tvName, @NonNull TextView tvStatistics, @NonNull TextView tvSubscription,
      @NonNull TextView tvTestResult, @NonNull TextView tvType) {
    this.rootView = rootView;
    this.infoContainer = infoContainer;
    this.itemBg = itemBg;
    this.layoutEdit = layoutEdit;
    this.layoutIndicator = layoutIndicator;
    this.layoutMore = layoutMore;
    this.layoutRemove = layoutRemove;
    this.layoutShare = layoutShare;
    this.layoutSubscription = layoutSubscription;
    this.tvName = tvName;
    this.tvStatistics = tvStatistics;
    this.tvSubscription = tvSubscription;
    this.tvTestResult = tvTestResult;
    this.tvType = tvType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecyclerMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecyclerMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recycler_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecyclerMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.info_container;
      LinearLayout infoContainer = ViewBindings.findChildViewById(rootView, id);
      if (infoContainer == null) {
        break missingId;
      }

      LinearLayout itemBg = (LinearLayout) rootView;

      id = R.id.layout_edit;
      LinearLayout layoutEdit = ViewBindings.findChildViewById(rootView, id);
      if (layoutEdit == null) {
        break missingId;
      }

      id = R.id.layout_indicator;
      LinearLayout layoutIndicator = ViewBindings.findChildViewById(rootView, id);
      if (layoutIndicator == null) {
        break missingId;
      }

      id = R.id.layout_more;
      LinearLayout layoutMore = ViewBindings.findChildViewById(rootView, id);
      if (layoutMore == null) {
        break missingId;
      }

      id = R.id.layout_remove;
      LinearLayout layoutRemove = ViewBindings.findChildViewById(rootView, id);
      if (layoutRemove == null) {
        break missingId;
      }

      id = R.id.layout_share;
      LinearLayout layoutShare = ViewBindings.findChildViewById(rootView, id);
      if (layoutShare == null) {
        break missingId;
      }

      id = R.id.layout_subscription;
      ConstraintLayout layoutSubscription = ViewBindings.findChildViewById(rootView, id);
      if (layoutSubscription == null) {
        break missingId;
      }

      id = R.id.tv_name;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      id = R.id.tv_statistics;
      TextView tvStatistics = ViewBindings.findChildViewById(rootView, id);
      if (tvStatistics == null) {
        break missingId;
      }

      id = R.id.tv_subscription;
      TextView tvSubscription = ViewBindings.findChildViewById(rootView, id);
      if (tvSubscription == null) {
        break missingId;
      }

      id = R.id.tv_test_result;
      TextView tvTestResult = ViewBindings.findChildViewById(rootView, id);
      if (tvTestResult == null) {
        break missingId;
      }

      id = R.id.tv_type;
      TextView tvType = ViewBindings.findChildViewById(rootView, id);
      if (tvType == null) {
        break missingId;
      }

      return new ItemRecyclerMainBinding((LinearLayout) rootView, infoContainer, itemBg, layoutEdit,
          layoutIndicator, layoutMore, layoutRemove, layoutShare, layoutSubscription, tvName,
          tvStatistics, tvSubscription, tvTestResult, tvType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
