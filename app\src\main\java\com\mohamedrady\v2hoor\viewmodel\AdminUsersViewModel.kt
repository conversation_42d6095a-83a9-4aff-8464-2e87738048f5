package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.UserProfile
import com.mohamedrady.v2hoor.service.UserManagementService
import kotlinx.coroutines.launch

/**
 * ViewModel for admin users management
 */
class AdminUsersViewModel : ViewModel() {

    private val userManagementService = UserManagementService.getInstance()

    private val _users = MutableLiveData<List<UserProfile>>()
    val users: LiveData<List<UserProfile>> = _users

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _statistics = MutableLiveData<Map<String, Any>>()
    val statistics: LiveData<Map<String, Any>> = _statistics

    private var allUsers: List<UserProfile> = emptyList()

    /**
     * Load all users
     */
    fun loadUsers() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val result = userManagementService.getAllUsers()
                result.fold(
                    onSuccess = { usersList ->
                        allUsers = usersList
                        _users.value = usersList
                        loadStatistics()
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تحميل المستخدمين: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ غير متوقع: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Search users by query
     */
    fun searchUsers(query: String) {
        viewModelScope.launch {
            if (query.isBlank()) {
                _users.value = allUsers
                return@launch
            }

            _isLoading.value = true
            
            try {
                val result = userManagementService.searchUsers(query)
                result.fold(
                    onSuccess = { searchResults ->
                        _users.value = searchResults
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في البحث: ${exception.message}"
                        // Fallback to local search
                        val localResults = allUsers.filter { user ->
                            user.email?.contains(query, ignoreCase = true) == true ||
                            user.displayName?.contains(query, ignoreCase = true) == true
                        }
                        _users.value = localResults
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في البحث: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Update user status
     */
    fun updateUserStatus(userId: String, isActive: Boolean) {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val result = userManagementService.updateUserStatus(userId, isActive)
                result.fold(
                    onSuccess = {
                        // Update local data
                        val updatedUsers = allUsers.map { user ->
                            if (user.uid == userId) {
                                user.copy(isActive = isActive)
                            } else {
                                user
                            }
                        }
                        allUsers = updatedUsers
                        _users.value = updatedUsers
                        loadStatistics()
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تحديث حالة المستخدم: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في تحديث المستخدم: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Delete user
     */
    fun deleteUser(userId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val result = userManagementService.deleteUser(userId)
                result.fold(
                    onSuccess = {
                        // Remove from local data
                        val updatedUsers = allUsers.filter { it.uid != userId }
                        allUsers = updatedUsers
                        _users.value = updatedUsers
                        loadStatistics()
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في حذف المستخدم: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في حذف المستخدم: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load user statistics
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val result = userManagementService.getUserStatistics()
                result.fold(
                    onSuccess = { stats ->
                        _statistics.value = stats
                    },
                    onFailure = { exception ->
                        // Calculate statistics locally if service fails
                        val localStats = calculateLocalStatistics()
                        _statistics.value = localStats
                    }
                )
            } catch (e: Exception) {
                val localStats = calculateLocalStatistics()
                _statistics.value = localStats
            }
        }
    }

    /**
     * Calculate statistics from local data
     */
    private fun calculateLocalStatistics(): Map<String, Any> {
        val totalUsers = allUsers.size
        val activeUsers = allUsers.count { it.isActive }
        val premiumUsers = allUsers.count { it.subscriptionType != "free" }
        
        return mapOf(
            "totalUsers" to totalUsers,
            "activeUsers" to activeUsers,
            "inactiveUsers" to (totalUsers - activeUsers),
            "premiumUsers" to premiumUsers,
            "freeUsers" to (totalUsers - premiumUsers)
        )
    }

    /**
     * Refresh data
     */
    fun refresh() {
        loadUsers()
    }

    /**
     * Clear error
     */
    fun clearError() {
        _error.value = null
    }
}
