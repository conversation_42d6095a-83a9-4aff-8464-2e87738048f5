<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/padding_spacing_dp16"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/lay_stream_security"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"

        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_stream_security" />

        <Spinner
            android:id="@+id/sp_stream_security"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/streamsecurityxs"
            android:nextFocusDown="@+id/et_sni" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_sni"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_sni" />

        <EditText
            android:id="@+id/et_sni"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:nextFocusDown="@+id/sp_stream_fingerprint" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_stream_fingerprint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_stream_fingerprint" />

        <Spinner
            android:id="@+id/sp_stream_fingerprint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/streamsecurity_utls"
            android:nextFocusDown="@+id/sp_stream_alpn" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_stream_alpn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_stream_alpn" />

        <Spinner
            android:id="@+id/sp_stream_alpn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/streamsecurity_alpn"
            android:nextFocusDown="@+id/sp_allow_insecure" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_allow_insecure"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_allow_insecure" />

        <Spinner
            android:id="@+id/sp_allow_insecure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/allowinsecures" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_public_key"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_public_key" />

        <EditText
            android:id="@+id/et_public_key"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:nextFocusDown="@+id/sp_stream_fingerprint" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_short_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_short_id" />

        <EditText
            android:id="@+id/et_short_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:nextFocusDown="@+id/sp_stream_fingerprint" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_spider_x"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_spider_x" />

        <EditText
            android:id="@+id/et_spider_x"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:nextFocusDown="@+id/sp_stream_fingerprint" />

    </LinearLayout>
</LinearLayout>