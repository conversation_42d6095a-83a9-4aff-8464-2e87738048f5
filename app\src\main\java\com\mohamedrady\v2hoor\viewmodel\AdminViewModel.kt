package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.service.AdminPermissionService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AdminViewModel : ViewModel() {

    private val adminService = AdminPermissionService.getInstance()

    private val _adminLevel = MutableStateFlow(AdminPermissionService.AdminLevel.NONE)
    val adminLevel: StateFlow<AdminPermissionService.AdminLevel> = _adminLevel.asStateFlow()

    private val _permissions = MutableStateFlow(AdminPermissionService.AdminPermissions())
    val permissions: StateFlow<AdminPermissionService.AdminPermissions> = _permissions.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadAdminInfo()
    }

    private fun loadAdminInfo() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val level = adminService.getCurrentUserAdminLevel()
                val perms = adminService.getCurrentUserPermissions()
                
                _adminLevel.value = level
                _permissions.value = perms
            } catch (e: Exception) {
                // Handle error
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun refreshAdminInfo() {
        loadAdminInfo()
    }

    fun promoteUser(userEmail: String, level: AdminPermissionService.AdminLevel) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                adminService.promoteUserToAdmin(userEmail, level)
                // Refresh admin info after promotion
                loadAdminInfo()
            } catch (e: Exception) {
                // Handle error
            } finally {
                _isLoading.value = false
            }
        }
    }
}
