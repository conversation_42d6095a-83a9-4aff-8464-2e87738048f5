package com.mohamedrady.v2hoor.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemUserServerBinding
import com.mohamedrady.v2hoor.dto.AdminServerConfig

/**
 * Adapter for displaying user's servers
 */
class UserServersAdapter(
    private val onRemoveServer: (AdminServerConfig) -> Unit
) : ListAdapter<AdminServerConfig, UserServersAdapter.ServerViewHolder>(ServerDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServerViewHolder {
        val binding = ItemUserServerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ServerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ServerViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ServerViewHolder(
        private val binding: ItemUserServerBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(server: AdminServerConfig) {
            binding.apply {
                // Server basic info
                textViewServerName.text = server.name
                textViewServerLocation.text = server.getLocationText()
                textViewServerType.text = server.getServerTypeText()
                
                // Server status
                val statusColor = if (server.isActive) {
                    ContextCompat.getColor(root.context, R.color.green)
                } else {
                    ContextCompat.getColor(root.context, R.color.red)
                }
                textViewServerStatus.text = server.getStatusText()
                textViewServerStatus.setTextColor(statusColor)
                
                // Server usage
                val usagePercentage = server.getUsagePercentage()
                textViewServerUsage.text = "الاستخدام: ${server.currentUsers}/${server.maxUsers} ($usagePercentage%)"
                
                // Usage progress bar
                progressBarUsage.progress = usagePercentage
                progressBarUsage.progressTintList = ContextCompat.getColorStateList(
                    root.context,
                    when {
                        usagePercentage >= 90 -> R.color.red
                        usagePercentage >= 70 -> R.color.orange
                        else -> R.color.green
                    }
                )
                
                // Priority
                textViewServerPriority.text = "الأولوية: ${server.priority}"
                
                // Creation date
                textViewCreationDate.text = "تم الإنشاء: ${server.getFormattedCreationDate()}"
                
                // Remove button
                buttonRemoveServer.setOnClickListener { onRemoveServer(server) }
                
                // Card background based on status
                cardView.setCardBackgroundColor(
                    if (server.isActive) {
                        ContextCompat.getColor(root.context, R.color.white)
                    } else {
                        ContextCompat.getColor(root.context, R.color.light_gray)
                    }
                )
                
                // Full server indicator
                if (server.isFull()) {
                    textViewFullIndicator.visibility = android.view.View.VISIBLE
                    textViewFullIndicator.text = "ممتلئ"
                    textViewFullIndicator.setBackgroundColor(
                        ContextCompat.getColor(root.context, R.color.red)
                    )
                } else {
                    textViewFullIndicator.visibility = android.view.View.GONE
                }
            }
        }
    }

    private class ServerDiffCallback : DiffUtil.ItemCallback<AdminServerConfig>() {
        override fun areItemsTheSame(oldItem: AdminServerConfig, newItem: AdminServerConfig): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: AdminServerConfig, newItem: AdminServerConfig): Boolean {
            return oldItem == newItem
        }
    }
}
