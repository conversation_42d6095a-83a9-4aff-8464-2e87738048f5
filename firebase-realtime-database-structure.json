{"users": {"USER_UID_PLACEHOLDER": {"servers": {"server1": {"id": "server1", "name": "US Server 1", "remarks": "United States - New York", "server": "us1.example.com", "server_port": 443, "protocol": "vmess", "uuid": "12345678-1234-1234-1234-123456789abc", "alter_id": 0, "security": "auto", "network": "ws", "header_type": "none", "request_host": "us1.example.com", "path": "/v2ray", "tls": "tls", "sni": "us1.example.com", "alpn": "", "fingerprint": "", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "US", "city": "New York", "flag": "🇺🇸", "is_active": true, "priority": 1, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 0, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER", "tags": ["premium", "fast"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}, "server2": {"id": "server2", "name": "UK Server 1", "remarks": "United Kingdom - London", "server": "uk1.example.com", "server_port": 443, "protocol": "vless", "uuid": "*************-4321-4321-cba987654321", "alter_id": 0, "security": "tls", "network": "tcp", "header_type": "none", "request_host": "", "path": "", "tls": "tls", "sni": "uk1.example.com", "alpn": "h2,http/1.1", "fingerprint": "chrome", "public_key": "", "short_id": "", "spider_x": "", "flow": "xtls-rprx-vision", "encryption": "none", "country": "UK", "city": "London", "flag": "🇬🇧", "is_active": true, "priority": 2, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 1735689600000, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER", "tags": ["premium", "streaming"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}, "server3": {"id": "server3", "name": "Germany Server 1", "remarks": "Germany - Frankfurt", "server": "de1.example.com", "server_port": 443, "protocol": "trojan", "uuid": "abcdef12-3456-7890-abcd-ef1234567890", "alter_id": 0, "security": "tls", "network": "tcp", "header_type": "none", "request_host": "", "path": "", "tls": "tls", "sni": "de1.example.com", "alpn": "h2,http/1.1", "fingerprint": "chrome", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "DE", "city": "Frankfurt", "flag": "🇩🇪", "is_active": true, "priority": 3, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 1735689600000, "valid_until": 0, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER", "tags": ["premium", "gaming"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}, "server4": {"id": "server4", "name": "Japan Server 1", "remarks": "Japan - Tokyo", "server": "jp1.example.com", "server_port": 443, "protocol": "shadowsocks", "uuid": "fedcba09-8765-4321-fedc-ba0987654321", "alter_id": 0, "security": "aes-256-gcm", "network": "tcp", "header_type": "none", "request_host": "", "path": "", "tls": "", "sni": "", "alpn": "", "fingerprint": "", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "JP", "city": "Tokyo", "flag": "🇯🇵", "is_active": true, "priority": 4, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 0, "valid_until": 0, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER", "tags": ["basic", "asia"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": ""}}, "server5_expired": {"id": "server5_expired", "name": "Expired Server", "remarks": "This server is expired for testing", "server": "expired.example.com", "server_port": 443, "protocol": "vmess", "uuid": "expired12-3456-7890-abcd-ef1234567890", "alter_id": 0, "security": "auto", "network": "ws", "header_type": "none", "request_host": "expired.example.com", "path": "/v2ray", "tls": "tls", "sni": "expired.example.com", "alpn": "", "fingerprint": "", "public_key": "", "short_id": "", "spider_x": "", "flow": "", "encryption": "none", "country": "XX", "city": "Test", "flag": "❌", "is_active": false, "priority": 999, "created_at": 1704067200000, "updated_at": 1704067200000, "expires_at": 1704153600000, "valid_until": 1704153600000, "last_sync": 0, "config_version": 1, "subscription_id": "", "user_id": "USER_UID_PLACEHOLDER", "tags": ["expired", "test"], "custom_config": "", "test_result": {"ping": -1, "download_speed": 0, "upload_speed": 0, "last_test": 0, "is_online": false, "error_message": "Server expired"}}}}}}