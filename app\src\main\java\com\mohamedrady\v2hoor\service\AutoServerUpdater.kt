package com.mohamedrady.v2hoor.service

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.work.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.handler.MmkvManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * Service for automatically updating servers from Firebase
 */
object AutoServerUpdater {
    
    private const val WORK_NAME = "auto_server_update"
    private const val CHANNEL_ID = "server_update_channel"
    private const val CHANNEL_NAME = "Server Updates"
    private const val NOTIFICATION_ID = 4
    
    /**
     * Schedule automatic server updates
     */
    fun scheduleAutoUpdate(context: Context, intervalMinutes: Long = 30) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val updateRequest = PeriodicWorkRequestBuilder<ServerUpdateWorker>(
            intervalMinutes, TimeUnit.MINUTES,
            15, TimeUnit.MINUTES // Flex interval
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()
        
        WorkManager.getInstance(context)
            .enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                updateRequest
            )
        
        Log.i(AppConfig.TAG, "Scheduled automatic server updates every $intervalMinutes minutes")
    }
    
    /**
     * Cancel automatic server updates
     */
    fun cancelAutoUpdate(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        Log.i(AppConfig.TAG, "Cancelled automatic server updates")
    }
    
    /**
     * Check if auto update is enabled
     */
    fun isAutoUpdateEnabled(): Boolean {
        return MmkvManager.decodeSettingsBool("auto_server_update_enabled", true)
    }
    
    /**
     * Enable/disable auto update
     */
    fun setAutoUpdateEnabled(enabled: Boolean) {
        MmkvManager.encodeSettings("auto_server_update_enabled", enabled)
    }
    
    /**
     * Get auto update interval in minutes
     */
    fun getAutoUpdateInterval(): Long {
        return MmkvManager.decodeSettingsInt("auto_server_update_interval", 30).toLong()
    }
    
    /**
     * Set auto update interval in minutes
     */
    fun setAutoUpdateInterval(intervalMinutes: Int) {
        MmkvManager.encodeSettings("auto_server_update_interval", intervalMinutes)
    }
    
    /**
     * Worker class for updating servers
     */
    class ServerUpdateWorker(
        context: Context,
        params: WorkerParameters
    ) : CoroutineWorker(context, params) {
        
        private val notificationManager = NotificationManagerCompat.from(applicationContext)
        private val firebaseServerService = FirebaseServerService.getInstance()
        
        @SuppressLint("MissingPermission")
        override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
            try {
                Log.i(AppConfig.TAG, "Starting automatic server update")
                
                // Check if auto update is enabled
                if (!isAutoUpdateEnabled()) {
                    Log.d(AppConfig.TAG, "Auto server update is disabled")
                    return@withContext Result.success()
                }
                
                // Create notification channel
                createNotificationChannel()
                
                // Show notification
                showUpdateNotification("جاري تحديث السيرفرات...")
                
                // Import servers from Firebase
                val result = firebaseServerService.importServersToLocal()
                
                if (result.isSuccess) {
                    val count = result.getOrNull() ?: 0
                    Log.i(AppConfig.TAG, "Successfully updated $count servers from Firebase")
                    
                    if (count > 0) {
                        showUpdateNotification("تم تحديث $count سيرفر بنجاح", true)
                        
                        // Store last update time
                        MmkvManager.encodeSettings("last_server_update_time", System.currentTimeMillis())
                        MmkvManager.encodeSettings("last_server_update_count", count)
                    } else {
                        Log.d(AppConfig.TAG, "No new servers to update")
                    }
                } else {
                    val error = result.exceptionOrNull()
                    Log.e(AppConfig.TAG, "Failed to update servers from Firebase", error)
                    showUpdateNotification("فشل في تحديث السيرفرات", true)
                    return@withContext Result.retry()
                }
                
                // Hide notification after delay
                kotlinx.coroutines.delay(3000)
                hideUpdateNotification()
                
                Result.success()
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Error in server update worker", e)
                showUpdateNotification("خطأ في تحديث السيرفرات", true)
                Result.failure()
            }
        }
        
        private fun createNotificationChannel() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "إشعارات تحديث السيرفرات"
                    setShowBadge(false)
                }
                
                val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
            }
        }
        
        @SuppressLint("MissingPermission")
        private fun showUpdateNotification(message: String, autoCancel: Boolean = false) {
            try {
                val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
                    .setContentTitle("V2HoorVPN")
                    .setContentText(message)
                    .setSmallIcon(R.drawable.ic_stat_name)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setCategory(NotificationCompat.CATEGORY_SERVICE)
                    .setAutoCancel(autoCancel)
                    .setOngoing(!autoCancel)
                    .build()
                
                notificationManager.notify(NOTIFICATION_ID, notification)
            } catch (e: Exception) {
                Log.w(AppConfig.TAG, "Failed to show update notification", e)
            }
        }
        
        private fun hideUpdateNotification() {
            try {
                notificationManager.cancel(NOTIFICATION_ID)
            } catch (e: Exception) {
                Log.w(AppConfig.TAG, "Failed to hide update notification", e)
            }
        }
    }
    
    /**
     * Manually trigger server update
     */
    suspend fun manualUpdate(context: Context): Result<Int> {
        return withContext(Dispatchers.IO) {
            try {
                Log.i(AppConfig.TAG, "Starting manual server update")
                
                val firebaseServerService = FirebaseServerService.getInstance()
                val result = firebaseServerService.importServersToLocal()
                
                if (result.isSuccess) {
                    val count = result.getOrNull() ?: 0
                    Log.i(AppConfig.TAG, "Manually updated $count servers from Firebase")
                    
                    // Store last update time
                    MmkvManager.encodeSettings("last_server_update_time", System.currentTimeMillis())
                    MmkvManager.encodeSettings("last_server_update_count", count)
                    
                    Result.success(count)
                } else {
                    Log.e(AppConfig.TAG, "Failed to manually update servers", result.exceptionOrNull())
                    result
                }
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Error in manual server update", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get last update information
     */
    fun getLastUpdateInfo(): Pair<Long, Int> {
        val lastTime = MmkvManager.decodeSettingsLong("last_server_update_time", 0L)
        val lastCount = MmkvManager.decodeSettingsInt("last_server_update_count", 0)
        return Pair(lastTime, lastCount)
    }
    
    /**
     * Initialize auto updater
     */
    fun initialize(context: Context) {
        if (isAutoUpdateEnabled()) {
            val interval = getAutoUpdateInterval()
            scheduleAutoUpdate(context, interval)
            Log.i(AppConfig.TAG, "Auto server updater initialized with $interval minute interval")
        } else {
            Log.i(AppConfig.TAG, "Auto server updater is disabled")
        }
    }
}
