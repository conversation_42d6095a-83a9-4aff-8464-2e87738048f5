-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:251:9-262:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:255:13-31
	android:authorities
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:253:13-68
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:254:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:252:13-67
provider#androidx.core.content.FileProvider
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:264:9-272:20
	android:grantUriPermissions
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:268:13-47
	android:authorities
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:266:13-57
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:267:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:265:13-62
manifest
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-276:12
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-276:12
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-276:12
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:1-276:12
MERGED from [libv2ray.aar] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f337e6bd729dd233e6a77c49fb23c73\transformed\libv2ray\AndroidManifest.xml:1:1-2:50
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6d3a33a828cd5a5ef3dcaea266c63b3c\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1586d6a1fa7e7bf9ffc9892b585ca703\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1a797b307386fe5e1d81e9a530121bdc\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.GrenderG:Toasty:1.5.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\428de9e0a656a87b6ccc03275ba78f5d\transformed\Toasty-1.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.coil-kt.coil3:coil-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\60a8b95821fc4c439acc00d2d6e3fb80\transformed\coil-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt.coil3:coil-core-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\35fcaa4ffa5548f8d54b405197a0aa94\transformed\coil-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a7564cb1aee56c9120bba62630a9ad04\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20b75010a2726d1adf4745ce03168231\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\082507c0f8acc2f43fb9efc345edc657\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\117969e0f84ec5c8f8ece35a7bfd2182\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fccbc3c129cda66264bedebcfa1a58f7\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e46a2c86fa2dfbd75ecf8247f016ddfe\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6168aafd8aa1313de7d53dbb6f8061c\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3ac2fdd0e2dd36e7f0a9d3276b6e0d7\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\18a2d325b7b784122ddaa47777f51e5e\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36dfa41e9b6c9ba312e620d43aff84a9\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a074c130173d2b089fe0719b1df021c4\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\91bfb72427e7f99269b4976bece04c46\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4024e1a8f7f1e9268e16d2edee0fe971\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.blacksquircle.ui:editorkit:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c1355500b3ce602f057edff43e6ac8e1\transformed\editorkit-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41e3c6af36dae4afe3a77f6300b16747\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3483fb42ca87a2b47f34bf3940a7a30d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29b816507d1c57b904be6c8a6ef22e7d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c9bd2b72d077a15b3d2988ced0621d7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\81eb338dc5244a5e8fc0d1ebb073faa4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d0282b3c0bd193ba56d23bda803884\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9466f0ff17be596184441bcdd1e0d850\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4c13fe5296a9aa4c553b984b1de0788a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64d787e4155fd34087b785a9d2dd0a45\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e4533f195c9d954dc8814282fbbc50f\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d7b6c3843511c7c6eabbb1ee9aec696d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eb0bcb4f3b9e3d457d23a7044116fbc4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7fad29612fb5e9a544f9936dc6a0f1ee\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efd38e3db447d88e5433414961b7f6b2\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\218ebe6bd9e84070247ab313c17cae32\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba7248595ee390762997a1fbb784ec3a\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\de0c5edbd8703928d695edd0a578b20c\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5460c8fdb46d585512b4d4a391c9dd04\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aac0d5a258a394599ba80ad0e4d5ffc3\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b3cb60e784c4e1c1984f118ab42a193\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2815f71f5a10b83ab83146d764633e65\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfbaed1c0137ec3663a75596d35ef4d6\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9dc04be46d88c949d01b277259a6ca9\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\854948da409e0afb0faa8c8f79072c2c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\badd494bc1a010e68a2de9dde556469a\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b832a6afd14f44f7c22206ddf4046b3\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\99c37b367453bbe322ba687a89bbd062\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4cf991a78c85b4a3c99385d23d35039f\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b0108352df81bc5bd7cad76434b1682d\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73103705007b53ba01cf65c8c83c985c\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3226d2f85c3c5523b8d351b07433b1a7\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\398a42a895c0528708dc1e4ff9d1b8be\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc02ac5da09814fef822b96ceb4c0a20\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfeaddb6121d6da3f6f4b16e9e92245c\transformed\flexbox-3.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f7e676337c22ad3d61f724a3bce5f5a\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\866c593d7001f472a68cab6aa13f5d2c\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cd1c0e2182409d1c2f4ec5c9011e937a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efdf12ae1e96814813e83372d238adaf\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\367da3c5eaa6a0c443a41b889da4d85d\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae917e7f3f694441f864ba157c3aaf81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fbc1894866bc714f6eacaa9f6b2a1a01\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\16398d17f3b61d35545878fd3f7c5fa3\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80af5af572852fc6dc87fd7ebe2e0821\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\350a6a12fe1dbdb3742f006e3897763f\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\51d308a7a41694b68b5e593d04ad8ee6\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6cb91685a35e4f0db1e06ec24a01b71f\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.tencent:mmkv-static:1.3.12] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7c8db3c7fd6ffdc31b9b051d4b711403\transformed\mmkv-static-1.3.12\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\373c6582bbe4e63da320526aaa7d6a77\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f25530a2d979b08be27665879d2bf895\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3dab1241f79a44a1027f6b1218aa7601\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b6b1a35945081c296427f2f37c9c426\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\19576446e6d9b854879e07e17a1cfd95\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c268fb3481a6c3ee361e5f6d1c3d646\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a27ad89502fa9e3e509919dce6d2a1d5\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d75b20eb51cad3fb15046d0fd305080\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\28938efecd4375a1add6d8f510494286\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d344db9601bd35d78672ddfe6f25ff8e\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:2:11-69
	tools:ignore
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:4:5-43
supports-screens
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:6:5-11:40
	android:largeScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:8:9-36
	android:smallScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:10:9-36
	android:normalScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:9:9-37
	android:xlargeScreens
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:11:9-37
	android:anyDensity
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:7:9-34
uses-sdk
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:13:5-15:66
MERGED from [libv2ray.aar] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f337e6bd729dd233e6a77c49fb23c73\transformed\libv2ray\AndroidManifest.xml:2:1-39
MERGED from [libv2ray.aar] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2f337e6bd729dd233e6a77c49fb23c73\transformed\libv2ray\AndroidManifest.xml:2:1-39
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:6:5-44
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6d3a33a828cd5a5ef3dcaea266c63b3c\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6d3a33a828cd5a5ef3dcaea266c63b3c\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1586d6a1fa7e7bf9ffc9892b585ca703\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1586d6a1fa7e7bf9ffc9892b585ca703\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1a797b307386fe5e1d81e9a530121bdc\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1a797b307386fe5e1d81e9a530121bdc\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.GrenderG:Toasty:1.5.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\428de9e0a656a87b6ccc03275ba78f5d\transformed\Toasty-1.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.GrenderG:Toasty:1.5.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\428de9e0a656a87b6ccc03275ba78f5d\transformed\Toasty-1.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.coil-kt.coil3:coil-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\60a8b95821fc4c439acc00d2d6e3fb80\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\60a8b95821fc4c439acc00d2d6e3fb80\transformed\coil-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\35fcaa4ffa5548f8d54b405197a0aa94\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt.coil3:coil-core-android:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\35fcaa4ffa5548f8d54b405197a0aa94\transformed\coil-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a7564cb1aee56c9120bba62630a9ad04\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a7564cb1aee56c9120bba62630a9ad04\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20b75010a2726d1adf4745ce03168231\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20b75010a2726d1adf4745ce03168231\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\082507c0f8acc2f43fb9efc345edc657\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\082507c0f8acc2f43fb9efc345edc657\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\117969e0f84ec5c8f8ece35a7bfd2182\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\117969e0f84ec5c8f8ece35a7bfd2182\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fccbc3c129cda66264bedebcfa1a58f7\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fccbc3c129cda66264bedebcfa1a58f7\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e46a2c86fa2dfbd75ecf8247f016ddfe\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e46a2c86fa2dfbd75ecf8247f016ddfe\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6168aafd8aa1313de7d53dbb6f8061c\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d6168aafd8aa1313de7d53dbb6f8061c\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3ac2fdd0e2dd36e7f0a9d3276b6e0d7\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3ac2fdd0e2dd36e7f0a9d3276b6e0d7\transformed\fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\18a2d325b7b784122ddaa47777f51e5e\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\18a2d325b7b784122ddaa47777f51e5e\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36dfa41e9b6c9ba312e620d43aff84a9\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\36dfa41e9b6c9ba312e620d43aff84a9\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a074c130173d2b089fe0719b1df021c4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a074c130173d2b089fe0719b1df021c4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\91bfb72427e7f99269b4976bece04c46\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\91bfb72427e7f99269b4976bece04c46\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4024e1a8f7f1e9268e16d2edee0fe971\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4024e1a8f7f1e9268e16d2edee0fe971\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [com.blacksquircle.ui:editorkit:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c1355500b3ce602f057edff43e6ac8e1\transformed\editorkit-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.blacksquircle.ui:editorkit:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c1355500b3ce602f057edff43e6ac8e1\transformed\editorkit-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41e3c6af36dae4afe3a77f6300b16747\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\41e3c6af36dae4afe3a77f6300b16747\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3483fb42ca87a2b47f34bf3940a7a30d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3483fb42ca87a2b47f34bf3940a7a30d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29b816507d1c57b904be6c8a6ef22e7d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29b816507d1c57b904be6c8a6ef22e7d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c9bd2b72d077a15b3d2988ced0621d7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c9bd2b72d077a15b3d2988ced0621d7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\81eb338dc5244a5e8fc0d1ebb073faa4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\81eb338dc5244a5e8fc0d1ebb073faa4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d0282b3c0bd193ba56d23bda803884\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\17d0282b3c0bd193ba56d23bda803884\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9466f0ff17be596184441bcdd1e0d850\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9466f0ff17be596184441bcdd1e0d850\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4c13fe5296a9aa4c553b984b1de0788a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4c13fe5296a9aa4c553b984b1de0788a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64d787e4155fd34087b785a9d2dd0a45\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\64d787e4155fd34087b785a9d2dd0a45\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e4533f195c9d954dc8814282fbbc50f\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1e4533f195c9d954dc8814282fbbc50f\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d7b6c3843511c7c6eabbb1ee9aec696d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d7b6c3843511c7c6eabbb1ee9aec696d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eb0bcb4f3b9e3d457d23a7044116fbc4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\eb0bcb4f3b9e3d457d23a7044116fbc4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7fad29612fb5e9a544f9936dc6a0f1ee\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7fad29612fb5e9a544f9936dc6a0f1ee\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efd38e3db447d88e5433414961b7f6b2\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efd38e3db447d88e5433414961b7f6b2\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\218ebe6bd9e84070247ab313c17cae32\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\218ebe6bd9e84070247ab313c17cae32\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba7248595ee390762997a1fbb784ec3a\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba7248595ee390762997a1fbb784ec3a\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\de0c5edbd8703928d695edd0a578b20c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\de0c5edbd8703928d695edd0a578b20c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5460c8fdb46d585512b4d4a391c9dd04\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5460c8fdb46d585512b4d4a391c9dd04\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aac0d5a258a394599ba80ad0e4d5ffc3\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aac0d5a258a394599ba80ad0e4d5ffc3\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b3cb60e784c4e1c1984f118ab42a193\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b3cb60e784c4e1c1984f118ab42a193\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2815f71f5a10b83ab83146d764633e65\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2815f71f5a10b83ab83146d764633e65\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfbaed1c0137ec3663a75596d35ef4d6\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfbaed1c0137ec3663a75596d35ef4d6\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9dc04be46d88c949d01b277259a6ca9\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a9dc04be46d88c949d01b277259a6ca9\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\854948da409e0afb0faa8c8f79072c2c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\854948da409e0afb0faa8c8f79072c2c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\badd494bc1a010e68a2de9dde556469a\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\badd494bc1a010e68a2de9dde556469a\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b832a6afd14f44f7c22206ddf4046b3\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b832a6afd14f44f7c22206ddf4046b3\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\99c37b367453bbe322ba687a89bbd062\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\99c37b367453bbe322ba687a89bbd062\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4cf991a78c85b4a3c99385d23d35039f\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4cf991a78c85b4a3c99385d23d35039f\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b0108352df81bc5bd7cad76434b1682d\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b0108352df81bc5bd7cad76434b1682d\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73103705007b53ba01cf65c8c83c985c\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\73103705007b53ba01cf65c8c83c985c\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3226d2f85c3c5523b8d351b07433b1a7\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3226d2f85c3c5523b8d351b07433b1a7\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\398a42a895c0528708dc1e4ff9d1b8be\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\398a42a895c0528708dc1e4ff9d1b8be\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc02ac5da09814fef822b96ceb4c0a20\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc02ac5da09814fef822b96ceb4c0a20\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfeaddb6121d6da3f6f4b16e9e92245c\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfeaddb6121d6da3f6f4b16e9e92245c\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f7e676337c22ad3d61f724a3bce5f5a\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f7e676337c22ad3d61f724a3bce5f5a\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\866c593d7001f472a68cab6aa13f5d2c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\866c593d7001f472a68cab6aa13f5d2c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cd1c0e2182409d1c2f4ec5c9011e937a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cd1c0e2182409d1c2f4ec5c9011e937a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efdf12ae1e96814813e83372d238adaf\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efdf12ae1e96814813e83372d238adaf\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\367da3c5eaa6a0c443a41b889da4d85d\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\367da3c5eaa6a0c443a41b889da4d85d\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae917e7f3f694441f864ba157c3aaf81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae917e7f3f694441f864ba157c3aaf81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fbc1894866bc714f6eacaa9f6b2a1a01\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fbc1894866bc714f6eacaa9f6b2a1a01\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\16398d17f3b61d35545878fd3f7c5fa3\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\16398d17f3b61d35545878fd3f7c5fa3\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80af5af572852fc6dc87fd7ebe2e0821\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\80af5af572852fc6dc87fd7ebe2e0821\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\350a6a12fe1dbdb3742f006e3897763f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\350a6a12fe1dbdb3742f006e3897763f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\51d308a7a41694b68b5e593d04ad8ee6\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\51d308a7a41694b68b5e593d04ad8ee6\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6cb91685a35e4f0db1e06ec24a01b71f\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6cb91685a35e4f0db1e06ec24a01b71f\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.tencent:mmkv-static:1.3.12] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7c8db3c7fd6ffdc31b9b051d4b711403\transformed\mmkv-static-1.3.12\AndroidManifest.xml:5:5-7:41
MERGED from [com.tencent:mmkv-static:1.3.12] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7c8db3c7fd6ffdc31b9b051d4b711403\transformed\mmkv-static-1.3.12\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\373c6582bbe4e63da320526aaa7d6a77\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\373c6582bbe4e63da320526aaa7d6a77\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f25530a2d979b08be27665879d2bf895\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f25530a2d979b08be27665879d2bf895\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3dab1241f79a44a1027f6b1218aa7601\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3dab1241f79a44a1027f6b1218aa7601\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b6b1a35945081c296427f2f37c9c426\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9b6b1a35945081c296427f2f37c9c426\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\19576446e6d9b854879e07e17a1cfd95\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\19576446e6d9b854879e07e17a1cfd95\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c268fb3481a6c3ee361e5f6d1c3d646\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8c268fb3481a6c3ee361e5f6d1c3d646\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a27ad89502fa9e3e509919dce6d2a1d5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a27ad89502fa9e3e509919dce6d2a1d5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d75b20eb51cad3fb15046d0fd305080\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9d75b20eb51cad3fb15046d0fd305080\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\28938efecd4375a1add6d8f510494286\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\28938efecd4375a1add6d8f510494286\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d344db9601bd35d78672ddfe6f25ff8e\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d344db9601bd35d78672ddfe6f25ff8e\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
	tools:overrideLibrary
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:15:9-63
	android:targetSdkVersion
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:14:9-35
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
uses-feature#android.hardware.camera
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:17:5-19:36
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:8:5-10:36
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:8:5-10:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:19:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:18:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:20:5-22:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:22:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:21:9-57
uses-feature#android.software.leanback
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:25:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:24:9-49
uses-feature#android.hardware.touchscreen
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:26:5-28:36
	android:required
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:28:9-33
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:27:9-52
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:31:5-33:53
	tools:ignore
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:33:9-50
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:32:9-61
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:34:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:5-79
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:35:22-76
uses-permission#android.permission.INTERNET
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:5-67
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:36:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:5-80
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:37:22-77
uses-permission#android.permission.CAMERA
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:5-65
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:12:5-65
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:12:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:38:22-62
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:5-77
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:39:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:40:5-42:38
	android:minSdkVersion
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:42:9-35
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:41:9-73
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:5-77
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:44:22-74
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:5-76
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:45:22-73
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:46:22-78
application
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:48:5-274:19
INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:48:5-274:19
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:14:5-20:19
MERGED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:14:5-20:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1586d6a1fa7e7bf9ffc9892b585ca703\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1586d6a1fa7e7bf9ffc9892b585ca703\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1a797b307386fe5e1d81e9a530121bdc\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1a797b307386fe5e1d81e9a530121bdc\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:23:5-28:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae917e7f3f694441f864ba157c3aaf81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ae917e7f3f694441f864ba157c3aaf81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\52367d7329549ed3099a55d70c9fd9ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
	android:extractNativeLibs
		INJECTED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:55:9-35
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:53:9-41
	tools:targetApi
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:58:9-28
	android:icon
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:52:9-43
	android:allowBackup
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:50:9-35
	android:banner
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:51:9-43
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:56:9-48
	android:networkSecurityConfig
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:54:9-69
	android:usesCleartextTraffic
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:57:9-44
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:49:9-39
activity#com.mohamedrady.v2hoor.ui.MainActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:60:9-78:20
	android:launchMode
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:63:13-44
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:62:13-36
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:64:13-64
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:61:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:65:13-70:29
action#android.intent.action.MAIN
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:17-69
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:66:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:17-77
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:68:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:17-86
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:69:27-83
intent-filter#action:name:android.service.quicksettings.action.QS_TILE_PREFERENCES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:71:13-73:29
action#android.service.quicksettings.action.QS_TILE_PREFERENCES
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:17-99
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:72:25-96
meta-data#android.app.shortcuts
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:75:13-77:53
	android:resource
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:77:17-50
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:76:17-53
activity#com.mohamedrady.v2hoor.ui.ServerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:79:9-82:60
	android:windowSoftInputMode
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:82:13-57
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:81:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:80:13-46
activity#com.mohamedrady.v2hoor.ui.ServerCustomConfigActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:83:9-86:60
	android:windowSoftInputMode
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:86:13-57
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:85:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:84:13-58
activity#com.mohamedrady.v2hoor.ui.SettingsActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:87:9-89:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:89:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:88:13-48
activity#com.mohamedrady.v2hoor.ui.PerAppProxyActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:90:9-92:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:92:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:91:13-51
activity#com.mohamedrady.v2hoor.ui.ScannerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:93:9-95:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:95:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:94:13-47
activity#com.mohamedrady.v2hoor.ui.LogcatActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:96:9-98:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:98:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:97:13-46
activity#com.mohamedrady.v2hoor.ui.RoutingSettingActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:99:9-101:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:101:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:100:13-54
activity#com.mohamedrady.v2hoor.ui.RoutingEditActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:102:9-104:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:104:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:103:13-51
activity#com.mohamedrady.v2hoor.ui.SubSettingActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:105:9-107:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:107:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:106:13-50
activity#com.mohamedrady.v2hoor.ui.UserAssetActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:108:9-110:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:110:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:109:13-49
activity#com.mohamedrady.v2hoor.ui.UserAssetUrlActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:111:9-113:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:113:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:112:13-52
activity#com.mohamedrady.v2hoor.ui.SubEditActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:115:9-117:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:117:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:116:13-47
activity#com.mohamedrady.v2hoor.ui.ScScannerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:118:9-120:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:120:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:119:13-49
activity#com.mohamedrady.v2hoor.ui.ScSwitchActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:121:9-126:71
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:125:13-51
	android:excludeFromRecents
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:123:13-46
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:124:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:126:13-68
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:122:13-48
activity#com.mohamedrady.v2hoor.ui.UrlSchemeActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:128:9-146:20
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:130:13-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:129:13-49
intent-filter#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:text/plain
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:131:13-135:29
action#android.intent.action.SEND
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:17-69
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:132:25-66
category#android.intent.category.DEFAULT
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:17-76
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:133:27-73
data
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:17-55
	android:host
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:143:23-52
	android:scheme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:142:23-47
	android:mimeType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:134:23-52
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:install-config+data:host:install-sub+data:scheme:v2rayng
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:136:13-145:29
action#android.intent.action.VIEW
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:17-69
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:137:25-66
category#android.intent.category.BROWSABLE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:17-78
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:139:27-75
activity#com.mohamedrady.v2hoor.ui.CheckUpdateActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:147:9-149:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:149:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:148:13-51
activity#com.mohamedrady.v2hoor.ui.AboutActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:150:9-152:40
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:152:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:151:13-45
activity#com.mohamedrady.v2hoor.ui.LoginActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:153:9-156:55
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:155:13-37
	android:theme
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:156:13-52
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:154:13-45
service#com.mohamedrady.v2hoor.service.V2RayVpnService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:158:9-175:19
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:165:13-51
	android:enabled
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:160:13-35
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:163:13-45
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:161:13-37
	android:permission
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:164:13-69
	android:foregroundServiceType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:162:13-55
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:159:13-52
intent-filter#action:name:android.net.VpnService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:166:13-168:29
action#android.net.VpnService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:17-65
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:167:25-62
meta-data#android.net.VpnService.SUPPORTS_ALWAYS_ON
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:169:13-171:40
	android:value
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:171:17-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:170:17-73
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:172:13-174:39
	android:value
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:174:17-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:173:17-76
service#com.mohamedrady.v2hoor.service.V2RayProxyOnlyService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:177:9-186:19
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:182:13-51
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:181:13-45
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:179:13-37
	android:foregroundServiceType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:180:13-55
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:178:13-58
service#com.mohamedrady.v2hoor.service.V2RayTestService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:188:9-191:54
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:191:13-51
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:190:13-37
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:189:13-53
receiver#com.mohamedrady.v2hoor.receiver.WidgetProvider
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:193:9-205:20
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:196:13-51
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:195:13-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:194:13-52
meta-data#android.appwidget.provider
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:197:13-199:63
	android:resource
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:199:17-60
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:198:17-58
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE+action:name:com.mohamedrady.v2hoor.action.activity+action:name:com.mohamedrady.v2hoor.action.widget.click
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:200:13-204:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:17-84
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:201:25-81
action#com.mohamedrady.v2hoor.action.widget.click
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:17-85
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:202:25-82
action#com.mohamedrady.v2hoor.action.activity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:17-81
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:203:25-78
receiver#com.mohamedrady.v2hoor.receiver.BootReceiver
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:206:9-213:20
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:209:13-41
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:208:13-36
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:207:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:210:13-212:29
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:17-79
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:211:25-76
service#com.mohamedrady.v2hoor.service.QSTileService
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:215:9-230:19
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:222:13-51
	android:label
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:220:13-50
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:217:13-36
	android:permission
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:221:13-77
	tools:targetApi
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:223:13-33
	android:icon
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:219:13-50
	android:foregroundServiceType
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:218:13-55
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:216:13-50
intent-filter#action:name:android.service.quicksettings.action.QS_TILE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:224:13-226:29
action#android.service.quicksettings.action.QS_TILE
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:17-87
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:225:25-84
activity#com.mohamedrady.v2hoor.ui.TaskerActivity
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:232:9-239:20
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:234:13-36
	android:icon
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:235:13-47
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:233:13-46
intent-filter#action:name:com.twofortyfouram.locale.intent.action.EDIT_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:236:13-238:29
action#com.twofortyfouram.locale.intent.action.EDIT_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:17-95
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:237:25-92
receiver#com.mohamedrady.v2hoor.receiver.TaskerReceiver
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:241:9-249:20
	android:process
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:244:13-51
	android:exported
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:243:13-36
	tools:ignore
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:245:13-44
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:242:13-52
intent-filter#action:name:com.twofortyfouram.locale.intent.action.FIRE_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:246:13-248:29
action#com.twofortyfouram.locale.intent.action.FIRE_SETTING
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:17-95
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:247:25-92
meta-data#androidx.work.WorkManagerInitializer
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:257:13-260:39
REJECTED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:260:17-36
	android:value
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:259:17-49
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:258:17-68
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:269:13-271:55
	android:resource
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:271:17-52
	android:name
		ADDED from D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\AndroidManifest.xml:270:17-67
activity#io.github.g00fy2.quickie.QRScannerActivity
ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:15:9-19:45
	android:screenOrientation
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:17:13-47
	tools:ignore
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:19:13-42
	android:theme
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:18:13-58
	android:name
		ADDED from [com.github.T8RIN.QuickieExtended:quickie-foss:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e6d251ef110edeab2647bcfbb802f94\transformed\quickie-foss-1.14.0\AndroidManifest.xml:16:13-70
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
	android:enabled
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:31:13-36
	android:exported
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:32:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:33:13-75
	android:name
		ADDED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0d4be1bb5588df96fc97c62a245df03d\transformed\camera-core-1.4.1\AndroidManifest.xml:30:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0980580debf1df927b39854a530d9a9e\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\203a3444784506cffb81a1889529fab2\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
service#androidx.work.multiprocess.RemoteWorkManagerService
ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:24:9-27:63
	android:exported
		ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:26:13-37
	tools:ignore
		ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:27:13-60
	android:name
		ADDED from [androidx.work:work-multiprocess:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f026490c64481d977aaa71edac79894e\transformed\work-multiprocess-2.10.1\AndroidManifest.xml:25:13-79
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\53813636e15577cb39907bbb84d2feb8\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7b6526c202a0a4f13599cc939fc721e7\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\47a4866c91a27d93b70b30e9ac7667a6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.mohamedrady.v2hoor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\addfe77571c545920b761f990220962d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\65f253a289bbf31122307c0238614aae\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9ea98a750e50d067e466b346f03ba534\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0c436176d440af8c6557070dbfac56da\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a9fc2c059c88f3adf114cabd28ffd8a\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\194b7da0db63fea2a64463e6a0ef5556\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
