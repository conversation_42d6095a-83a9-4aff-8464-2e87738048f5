// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdminServerBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button buttonDeleteServer;

  @NonNull
  public final Button buttonEditServer;

  @NonNull
  public final Button buttonToggleStatus;

  @NonNull
  public final Button buttonViewUsers;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final ProgressBar progressBarUsage;

  @NonNull
  public final TextView textViewCreationDate;

  @NonNull
  public final TextView textViewFullIndicator;

  @NonNull
  public final TextView textViewServerLocation;

  @NonNull
  public final TextView textViewServerName;

  @NonNull
  public final TextView textViewServerPriority;

  @NonNull
  public final TextView textViewServerStatus;

  @NonNull
  public final TextView textViewServerType;

  @NonNull
  public final TextView textViewServerUsage;

  private ItemAdminServerBinding(@NonNull CardView rootView, @NonNull Button buttonDeleteServer,
      @NonNull Button buttonEditServer, @NonNull Button buttonToggleStatus,
      @NonNull Button buttonViewUsers, @NonNull CardView cardView,
      @NonNull ProgressBar progressBarUsage, @NonNull TextView textViewCreationDate,
      @NonNull TextView textViewFullIndicator, @NonNull TextView textViewServerLocation,
      @NonNull TextView textViewServerName, @NonNull TextView textViewServerPriority,
      @NonNull TextView textViewServerStatus, @NonNull TextView textViewServerType,
      @NonNull TextView textViewServerUsage) {
    this.rootView = rootView;
    this.buttonDeleteServer = buttonDeleteServer;
    this.buttonEditServer = buttonEditServer;
    this.buttonToggleStatus = buttonToggleStatus;
    this.buttonViewUsers = buttonViewUsers;
    this.cardView = cardView;
    this.progressBarUsage = progressBarUsage;
    this.textViewCreationDate = textViewCreationDate;
    this.textViewFullIndicator = textViewFullIndicator;
    this.textViewServerLocation = textViewServerLocation;
    this.textViewServerName = textViewServerName;
    this.textViewServerPriority = textViewServerPriority;
    this.textViewServerStatus = textViewServerStatus;
    this.textViewServerType = textViewServerType;
    this.textViewServerUsage = textViewServerUsage;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdminServerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdminServerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_admin_server, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdminServerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDeleteServer;
      Button buttonDeleteServer = ViewBindings.findChildViewById(rootView, id);
      if (buttonDeleteServer == null) {
        break missingId;
      }

      id = R.id.buttonEditServer;
      Button buttonEditServer = ViewBindings.findChildViewById(rootView, id);
      if (buttonEditServer == null) {
        break missingId;
      }

      id = R.id.buttonToggleStatus;
      Button buttonToggleStatus = ViewBindings.findChildViewById(rootView, id);
      if (buttonToggleStatus == null) {
        break missingId;
      }

      id = R.id.buttonViewUsers;
      Button buttonViewUsers = ViewBindings.findChildViewById(rootView, id);
      if (buttonViewUsers == null) {
        break missingId;
      }

      CardView cardView = (CardView) rootView;

      id = R.id.progressBarUsage;
      ProgressBar progressBarUsage = ViewBindings.findChildViewById(rootView, id);
      if (progressBarUsage == null) {
        break missingId;
      }

      id = R.id.textViewCreationDate;
      TextView textViewCreationDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewCreationDate == null) {
        break missingId;
      }

      id = R.id.textViewFullIndicator;
      TextView textViewFullIndicator = ViewBindings.findChildViewById(rootView, id);
      if (textViewFullIndicator == null) {
        break missingId;
      }

      id = R.id.textViewServerLocation;
      TextView textViewServerLocation = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerLocation == null) {
        break missingId;
      }

      id = R.id.textViewServerName;
      TextView textViewServerName = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerName == null) {
        break missingId;
      }

      id = R.id.textViewServerPriority;
      TextView textViewServerPriority = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerPriority == null) {
        break missingId;
      }

      id = R.id.textViewServerStatus;
      TextView textViewServerStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerStatus == null) {
        break missingId;
      }

      id = R.id.textViewServerType;
      TextView textViewServerType = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerType == null) {
        break missingId;
      }

      id = R.id.textViewServerUsage;
      TextView textViewServerUsage = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerUsage == null) {
        break missingId;
      }

      return new ItemAdminServerBinding((CardView) rootView, buttonDeleteServer, buttonEditServer,
          buttonToggleStatus, buttonViewUsers, cardView, progressBarUsage, textViewCreationDate,
          textViewFullIndicator, textViewServerLocation, textViewServerName, textViewServerPriority,
          textViewServerStatus, textViewServerType, textViewServerUsage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
