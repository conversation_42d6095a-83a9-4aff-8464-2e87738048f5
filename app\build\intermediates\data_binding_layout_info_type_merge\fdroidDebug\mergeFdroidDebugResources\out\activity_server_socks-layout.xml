<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_server_socks" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_server_socks.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_server_socks_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="62" endOffset="18"/></Target><Target tag="binding_1" include="layout_address_port"><Expressions/><location startLine="14" startOffset="8" endLine="14" endOffset="55"/></Target><Target id="@+id/et_security" view="EditText"><Expressions/><location startLine="28" startOffset="12" endLine="32" endOffset="42"/></Target><Target id="@+id/et_id" view="EditText"><Expressions/><location startLine="47" startOffset="12" endLine="51" endOffset="42"/></Target></Targets></Layout>