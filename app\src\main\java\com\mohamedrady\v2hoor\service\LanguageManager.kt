package com.mohamedrady.v2hoor.service

import android.app.Activity
import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.os.Build
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mohamedrady.v2hoor.AppConfig
import java.util.*

/**
 * Language Manager for Multi-Language Support
 * Handles language switching and locale management
 */
class LanguageManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: LanguageManager? = null
        
        fun getInstance(context: Context): LanguageManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LanguageManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val PREFS_NAME = "language_preferences"
        private const val KEY_SELECTED_LANGUAGE = "selected_language"
        private const val KEY_AUTO_DETECT = "auto_detect_language"
        private const val KEY_RTL_SUPPORT = "rtl_support_enabled"
        
        // Supported languages
        const val LANGUAGE_ARABIC = "ar"
        const val LANGUAGE_ENGLISH = "en"
        const val LANGUAGE_SYSTEM = "system"
    }
    
    data class Language(
        val code: String,
        val name: String,
        val nativeName: String,
        val isRTL: Boolean = false,
        val flag: String = ""
    )
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // LiveData for language changes
    private val _currentLanguage = MutableLiveData<Language>()
    val currentLanguage: LiveData<Language> = _currentLanguage
    
    private val _isRTL = MutableLiveData<Boolean>()
    val isRTL: LiveData<Boolean> = _isRTL
    
    // Available languages
    private val availableLanguages = listOf(
        Language(LANGUAGE_SYSTEM, "System Default", "تلقائي", false, "🌐"),
        Language(LANGUAGE_ARABIC, "Arabic", "العربية", true, "🇪🇬"),
        Language(LANGUAGE_ENGLISH, "English", "English", false, "🇺🇸")
    )
    
    init {
        // Initialize with saved language
        val savedLanguage = getSavedLanguage()
        _currentLanguage.value = savedLanguage
        updateRTLStatus(savedLanguage)
    }
    
    /**
     * Apply language to context
     */
    fun applyLanguage(context: Context, languageCode: String): Context {
        return try {
            val locale = when (languageCode) {
                LANGUAGE_SYSTEM -> getSystemLocale()
                LANGUAGE_ARABIC -> Locale("ar", "EG")
                LANGUAGE_ENGLISH -> Locale("en", "US")
                else -> Locale("en", "US")
            }
            
            saveLanguage(languageCode)
            updateCurrentLanguage(languageCode)
            
            val config = Configuration(context.resources.configuration)
            config.setLocale(locale)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                config.setLayoutDirection(locale)
            }
            
            android.util.Log.i(AppConfig.TAG, "Language applied: $languageCode (${locale.displayName})")
            
            context.createConfigurationContext(config)
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to apply language", e)
            context
        }
    }
    
    /**
     * Change app language
     */
    fun changeLanguage(languageCode: String) {
        try {
            saveLanguage(languageCode)
            updateCurrentLanguage(languageCode)
            
            android.util.Log.i(AppConfig.TAG, "Language changed to: $languageCode")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to change language", e)
        }
    }
    
    /**
     * Get current language
     */
    fun getCurrentLanguage(): Language {
        return _currentLanguage.value ?: getLanguageByCode(getSavedLanguageCode())
    }
    
    /**
     * Get current language code
     */
    fun getCurrentLanguageCode(): String {
        return getCurrentLanguage().code
    }
    
    /**
     * Check if current language is RTL
     */
    fun isCurrentLanguageRTL(): Boolean {
        return _isRTL.value ?: false
    }
    
    /**
     * Get available languages
     */
    fun getAvailableLanguages(): List<Language> {
        return availableLanguages
    }
    
    /**
     * Get language by code
     */
    fun getLanguageByCode(code: String): Language {
        return availableLanguages.find { it.code == code } 
            ?: availableLanguages.first { it.code == LANGUAGE_SYSTEM }
    }
    
    /**
     * Save language preference
     */
    private fun saveLanguage(languageCode: String) {
        prefs.edit()
            .putString(KEY_SELECTED_LANGUAGE, languageCode)
            .apply()
    }
    
    /**
     * Get saved language code
     */
    private fun getSavedLanguageCode(): String {
        return prefs.getString(KEY_SELECTED_LANGUAGE, LANGUAGE_SYSTEM) ?: LANGUAGE_SYSTEM
    }
    
    /**
     * Get saved language
     */
    private fun getSavedLanguage(): Language {
        val code = getSavedLanguageCode()
        return getLanguageByCode(code)
    }
    
    /**
     * Update current language
     */
    private fun updateCurrentLanguage(languageCode: String) {
        val language = getLanguageByCode(languageCode)
        _currentLanguage.postValue(language)
        updateRTLStatus(language)
    }
    
    /**
     * Update RTL status
     */
    private fun updateRTLStatus(language: Language) {
        _isRTL.postValue(language.isRTL)
    }
    
    /**
     * Get system locale
     */
    private fun getSystemLocale(): Locale {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.resources.configuration.locales[0]
        } else {
            @Suppress("DEPRECATION")
            context.resources.configuration.locale
        }
    }
    
    /**
     * Restart activity with new language
     */
    fun restartActivity(activity: Activity) {
        try {
            val intent = activity.intent
            activity.finish()
            activity.startActivity(intent)
            activity.overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
            
            android.util.Log.i(AppConfig.TAG, "Activity restarted for language change")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to restart activity", e)
        }
    }
    
    /**
     * Set auto language detection
     */
    fun setAutoLanguageDetection(enabled: Boolean) {
        prefs.edit()
            .putBoolean(KEY_AUTO_DETECT, enabled)
            .apply()
        
        if (enabled) {
            detectAndApplySystemLanguage()
        }
    }
    
    /**
     * Check if auto detection is enabled
     */
    fun isAutoDetectionEnabled(): Boolean {
        return prefs.getBoolean(KEY_AUTO_DETECT, false)
    }
    
    /**
     * Detect and apply system language
     */
    fun detectAndApplySystemLanguage() {
        if (!isAutoDetectionEnabled()) return
        
        val systemLocale = getSystemLocale()
        val systemLanguageCode = when (systemLocale.language) {
            "ar" -> LANGUAGE_ARABIC
            "en" -> LANGUAGE_ENGLISH
            else -> LANGUAGE_ENGLISH // Default fallback
        }
        
        if (systemLanguageCode != getCurrentLanguageCode()) {
            changeLanguage(systemLanguageCode)
        }
    }
    
    /**
     * Set RTL support
     */
    fun setRTLSupport(enabled: Boolean) {
        prefs.edit()
            .putBoolean(KEY_RTL_SUPPORT, enabled)
            .apply()
    }
    
    /**
     * Check if RTL support is enabled
     */
    fun isRTLSupportEnabled(): Boolean {
        return prefs.getBoolean(KEY_RTL_SUPPORT, true)
    }
    
    /**
     * Get language display name
     */
    fun getLanguageDisplayName(language: Language): String {
        return "${language.flag} ${language.nativeName}"
    }
    
    /**
     * Get language statistics
     */
    fun getLanguageStats(): Map<String, String> {
        val currentLang = getCurrentLanguage()
        return mapOf(
            "current_language" to currentLang.nativeName,
            "language_code" to currentLang.code,
            "is_rtl" to if (isCurrentLanguageRTL()) "نعم" else "لا",
            "auto_detection" to if (isAutoDetectionEnabled()) "مفعل" else "معطل",
            "rtl_support" to if (isRTLSupportEnabled()) "مفعل" else "معطل",
            "system_language" to getSystemLocale().displayLanguage
        )
    }
    
    /**
     * Export language settings
     */
    fun exportLanguageSettings(): Map<String, Any> {
        return mapOf(
            "selected_language" to getCurrentLanguageCode(),
            "auto_detect" to isAutoDetectionEnabled(),
            "rtl_support" to isRTLSupportEnabled()
        )
    }
    
    /**
     * Import language settings
     */
    fun importLanguageSettings(settings: Map<String, Any>) {
        try {
            val languageCode = settings["selected_language"] as? String ?: LANGUAGE_SYSTEM
            val autoDetect = settings["auto_detect"] as? Boolean ?: false
            val rtlSupport = settings["rtl_support"] as? Boolean ?: true
            
            changeLanguage(languageCode)
            setAutoLanguageDetection(autoDetect)
            setRTLSupport(rtlSupport)
            
            android.util.Log.i(AppConfig.TAG, "Language settings imported successfully")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to import language settings", e)
        }
    }
    
    /**
     * Reset to default language
     */
    fun resetToDefault() {
        changeLanguage(LANGUAGE_SYSTEM)
        setAutoLanguageDetection(false)
        setRTLSupport(true)
    }
    
    /**
     * Get localized string
     */
    fun getString(context: Context, resId: Int): String {
        return try {
            val localizedContext = applyLanguage(context, getCurrentLanguageCode())
            localizedContext.getString(resId)
        } catch (e: Exception) {
            context.getString(resId)
        }
    }
    
    /**
     * Get localized string with arguments
     */
    fun getString(context: Context, resId: Int, vararg args: Any): String {
        return try {
            val localizedContext = applyLanguage(context, getCurrentLanguageCode())
            localizedContext.getString(resId, *args)
        } catch (e: Exception) {
            context.getString(resId, *args)
        }
    }
}
