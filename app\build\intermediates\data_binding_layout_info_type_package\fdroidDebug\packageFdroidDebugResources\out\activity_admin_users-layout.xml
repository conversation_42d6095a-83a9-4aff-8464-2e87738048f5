<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_admin_users" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_admin_users.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_admin_users_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="268" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="21" endOffset="54"/></Target><Target id="@+id/textViewTotalUsers" view="TextView"><Expressions/><location startLine="75" startOffset="28" endLine="82" endOffset="58"/></Target><Target id="@+id/textViewActiveUsers" view="TextView"><Expressions/><location startLine="99" startOffset="28" endLine="106" endOffset="58"/></Target><Target id="@+id/textViewInactiveUsers" view="TextView"><Expressions/><location startLine="123" startOffset="28" endLine="130" endOffset="58"/></Target><Target id="@+id/textViewPremiumUsers" view="TextView"><Expressions/><location startLine="147" startOffset="28" endLine="154" endOffset="58"/></Target><Target id="@+id/editTextSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="197" startOffset="24" endLine="203" endOffset="75"/></Target><Target id="@+id/textViewUsersCount" view="TextView"><Expressions/><location startLine="213" startOffset="24" endLine="220" endOffset="53"/></Target><Target id="@+id/buttonRefresh" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="222" startOffset="24" endLine="230" endOffset="52"/></Target><Target id="@+id/recyclerViewUsers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="239" startOffset="12" endLine="244" endOffset="58"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="251" startOffset="4" endLine="256" endOffset="35"/></Target><Target id="@+id/fabAddUser" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="259" startOffset="4" endLine="266" endOffset="33"/></Target></Targets></Layout>