package com.mohamedrady.v2hoor.model

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.mohamedrady.v2hoor.AppConfig

/**
 * Server Cache Manager for local storage of Firebase servers
 */
class ServerCache private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ServerCache? = null
        
        fun getInstance(context: Context): ServerCache {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerCache(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val PREF_NAME = "firebase_server_cache"
        private const val KEY_SERVERS = "cached_servers"
        private const val KEY_LAST_SYNC = "last_sync_timestamp"
        private const val KEY_USER_ID = "cached_user_id"
        private const val CACHE_EXPIRY_HOURS = 24 // Cache expires after 24 hours
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    /**
     * Cache servers for a specific user
     */
    fun cacheServers(userId: String, servers: List<FirebaseServerModel>) {
        try {
            val serversJson = gson.toJson(servers)
            val currentTime = System.currentTimeMillis()
            
            prefs.edit()
                .putString(KEY_SERVERS, serversJson)
                .putLong(KEY_LAST_SYNC, currentTime)
                .putString(KEY_USER_ID, userId)
                .apply()
                
            android.util.Log.i(AppConfig.TAG, "Cached ${servers.size} servers for user: $userId")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to cache servers", e)
        }
    }
    
    /**
     * Get cached servers for a specific user
     */
    fun getCachedServers(userId: String): List<FirebaseServerModel>? {
        return try {
            val cachedUserId = prefs.getString(KEY_USER_ID, "")
            if (cachedUserId != userId) {
                android.util.Log.d(AppConfig.TAG, "Cache is for different user, clearing cache")
                clearCache()
                return null
            }
            
            if (isCacheExpired()) {
                android.util.Log.d(AppConfig.TAG, "Cache is expired, clearing cache")
                clearCache()
                return null
            }
            
            val serversJson = prefs.getString(KEY_SERVERS, null)
            if (serversJson.isNullOrBlank()) {
                return null
            }
            
            val type = object : TypeToken<List<FirebaseServerModel>>() {}.type
            val servers: List<FirebaseServerModel> = gson.fromJson(serversJson, type)
            
            android.util.Log.i(AppConfig.TAG, "Retrieved ${servers.size} cached servers for user: $userId")
            servers
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to get cached servers", e)
            clearCache()
            null
        }
    }
    
    /**
     * Check if cache is expired
     */
    fun isCacheExpired(): Boolean {
        val lastSync = prefs.getLong(KEY_LAST_SYNC, 0)
        val currentTime = System.currentTimeMillis()
        val cacheExpiryTime = CACHE_EXPIRY_HOURS * 60 * 60 * 1000L
        
        return (currentTime - lastSync) > cacheExpiryTime
    }
    
    /**
     * Get last sync timestamp
     */
    fun getLastSyncTime(): Long {
        return prefs.getLong(KEY_LAST_SYNC, 0)
    }
    
    /**
     * Clear all cached data
     */
    fun clearCache() {
        prefs.edit().clear().apply()
        android.util.Log.i(AppConfig.TAG, "Server cache cleared")
    }
    
    /**
     * Clear cache for specific user
     */
    fun clearCacheForUser(userId: String) {
        val cachedUserId = prefs.getString(KEY_USER_ID, "")
        if (cachedUserId == userId) {
            clearCache()
        }
    }
    
    /**
     * Update single server in cache
     */
    fun updateServerInCache(userId: String, serverId: String, updatedServer: FirebaseServerModel) {
        try {
            val cachedServers = getCachedServers(userId)?.toMutableList() ?: return
            
            val index = cachedServers.indexOfFirst { it.id == serverId }
            if (index >= 0) {
                cachedServers[index] = updatedServer
                cacheServers(userId, cachedServers)
                android.util.Log.i(AppConfig.TAG, "Updated server in cache: $serverId")
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to update server in cache", e)
        }
    }
    
    /**
     * Remove server from cache
     */
    fun removeServerFromCache(userId: String, serverId: String) {
        try {
            val cachedServers = getCachedServers(userId)?.toMutableList() ?: return
            
            val removed = cachedServers.removeAll { it.id == serverId }
            if (removed) {
                cacheServers(userId, cachedServers)
                android.util.Log.i(AppConfig.TAG, "Removed server from cache: $serverId")
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to remove server from cache", e)
        }
    }
    
    /**
     * Get cache statistics
     */
    fun getCacheStats(): CacheStats {
        val cachedUserId = prefs.getString(KEY_USER_ID, "")
        val lastSync = prefs.getLong(KEY_LAST_SYNC, 0)
        val serversJson = prefs.getString(KEY_SERVERS, null)
        
        val serverCount = if (serversJson.isNullOrBlank()) {
            0
        } else {
            try {
                val type = object : TypeToken<List<FirebaseServerModel>>() {}.type
                val servers: List<FirebaseServerModel> = gson.fromJson(serversJson, type)
                servers.size
            } catch (e: Exception) {
                0
            }
        }
        
        return CacheStats(
            userId = cachedUserId ?: "",
            serverCount = serverCount,
            lastSyncTime = lastSync,
            isExpired = isCacheExpired()
        )
    }
}

/**
 * Cache statistics data class
 */
data class CacheStats(
    val userId: String,
    val serverCount: Int,
    val lastSyncTime: Long,
    val isExpired: Boolean
) {
    fun getLastSyncTimeFormatted(): String {
        return if (lastSyncTime > 0) {
            val date = java.util.Date(lastSyncTime)
            java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(date)
        } else {
            "Never"
        }
    }
    
    fun getTimeSinceLastSync(): String {
        if (lastSyncTime <= 0) return "Never synced"
        
        val currentTime = System.currentTimeMillis()
        val diffMillis = currentTime - lastSyncTime
        val diffMinutes = diffMillis / (60 * 1000)
        val diffHours = diffMinutes / 60
        val diffDays = diffHours / 24
        
        return when {
            diffDays > 0 -> "$diffDays days ago"
            diffHours > 0 -> "$diffHours hours ago"
            diffMinutes > 0 -> "$diffMinutes minutes ago"
            else -> "Just now"
        }
    }
}
