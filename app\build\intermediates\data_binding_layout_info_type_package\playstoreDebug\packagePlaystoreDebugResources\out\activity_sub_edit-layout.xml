<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_sub_edit" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_sub_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_sub_edit_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="211" endOffset="12"/></Target><Target id="@+id/et_remarks" view="EditText"><Expressions/><location startLine="43" startOffset="16" endLine="47" endOffset="46"/></Target><Target id="@+id/et_url" view="EditText"><Expressions/><location startLine="63" startOffset="16" endLine="72" endOffset="47"/></Target><Target id="@+id/et_filter" view="EditText"><Expressions/><location startLine="87" startOffset="16" endLine="91" endOffset="46"/></Target><Target id="@+id/chk_enable" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="107" startOffset="16" endLine="113" endOffset="54"/></Target><Target id="@+id/auto_update_check" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="130" startOffset="16" endLine="136" endOffset="54"/></Target><Target id="@+id/allow_insecure_url" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="152" startOffset="16" endLine="158" endOffset="54"/></Target><Target id="@+id/et_pre_profile" view="EditText"><Expressions/><location startLine="173" startOffset="16" endLine="178" endOffset="46"/></Target><Target id="@+id/et_next_profile" view="EditText"><Expressions/><location startLine="193" startOffset="16" endLine="198" endOffset="46"/></Target></Targets></Layout>