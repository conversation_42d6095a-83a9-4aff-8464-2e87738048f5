rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isSuperAdmin() {
      return isAuthenticated() && 
             request.auth.token.email == "<EMAIL>";
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             (isSuperAdmin() || 
              firestore.exists(/databases/(default)/documents/user_roles/$(request.auth.uid)) &&
              firestore.get(/databases/(default)/documents/user_roles/$(request.auth.uid)).data.role in ['admin', 'super_admin']);
    }
    
    function hasValidUserRole() {
      return isAuthenticated() && 
             firestore.exists(/databases/(default)/documents/user_roles/$(request.auth.uid)) &&
             firestore.get(/databases/(default)/documents/user_roles/$(request.auth.uid)).data.isActive == true;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidConfigFile() {
      return request.resource.contentType in ['text/plain', 'application/json', 'application/octet-stream'];
    }
    
    function isReasonableSize() {
      return request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // User profile pictures - Users can upload their own
    match /user_profiles/{userId}/{fileName} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isOwner(userId) && isValidImageFile() && isReasonableSize();
      allow delete: if isOwner(userId) || isAdmin();
    }
    
    // Server configuration files - Admin only
    match /server_configs/{configId} {
      allow read: if isAdmin();
      allow write: if isAdmin() && isValidConfigFile() && isReasonableSize();
      allow delete: if isAdmin();
    }
    
    // App assets and resources - Read for authenticated users, write for admins
    match /app_assets/{assetPath=**} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isAdmin() && isReasonableSize();
      allow delete: if isAdmin();
    }
    
    // User data backups - Users can access their own backups
    match /user_backups/{userId}/{fileName} {
      allow read, write: if isOwner(userId) && isReasonableSize();
      allow delete: if isOwner(userId) || isAdmin();
    }
    
    // System backups - Admin only
    match /system_backups/{fileName} {
      allow read, write: if isAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // Log files - Admin only
    match /logs/{logFile} {
      allow read, write: if isAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // Support attachments - Users can upload for their tickets, admins can read all
    match /support_attachments/{ticketId}/{fileName} {
      allow read: if isAdmin() || 
                     (isAuthenticated() && 
                      firestore.exists(/databases/(default)/documents/support_tickets/$(ticketId)) &&
                      firestore.get(/databases/(default)/documents/support_tickets/$(ticketId)).data.userId == request.auth.uid);
      allow write: if isAuthenticated() && hasValidUserRole() && isReasonableSize();
      allow delete: if isAdmin();
    }
    
    // Temporary files - Users can upload temporarily, auto-cleanup
    match /temp/{userId}/{fileName} {
      allow read, write: if isOwner(userId) && isReasonableSize();
      allow delete: if isOwner(userId) || isAdmin();
    }
    
    // Public downloads - Read for authenticated users, write for admins
    match /public_downloads/{fileName} {
      allow read: if isAuthenticated() && hasValidUserRole();
      allow write: if isAdmin() && isReasonableSize();
      allow delete: if isAdmin();
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
