[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_tasker.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_tasker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\layout_address_port.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_address_port.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_socks.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_socks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_routing_setting.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_promote_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_promote_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_user_asset.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\preference_with_help_link.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\preference_with_help_link.xml"}, {"merged": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:/layout/activity_real_time_log.xml", "source": "com.mohamedrady.v2hoor.app-main-79:/layout/activity_real_time_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_bypass_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_user_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_user_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\dialog_config_filter.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\dialog_config_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_sub_setting.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_hysteria2.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_hysteria2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_add_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_add_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_users.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_users.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_admin_servers.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_admin_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_logcat.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_system_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_system_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_trojan.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_trojan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_custom_config.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_custom_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_vless.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_vless.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\widget_switch.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\widget_switch.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_bypass_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_shadowsocks.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_shadowsocks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_logcat.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_footer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_footer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_logs.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_logs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_recycler_user_asset.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_user_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_user_servers.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_sub_setting.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_sub_setting.xml"}, {"merged": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:/layout/activity_admin_panel.xml", "source": "com.mohamedrady.v2hoor.app-main-79:/layout/activity_admin_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\layout_tls_hysteria2.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_tls_hysteria2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\nav_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_admin_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_admin_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\layout_tls.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_tls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_check_update.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_check_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_routing_edit.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_routing_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\layout_transport.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_wireguard.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_wireguard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_user_asset_url.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_asset_url.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_none.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_none.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_admin_users.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_admin_users.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_user_management.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_management.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_sub_edit.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_sub_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_add_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_add_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_admin_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_admin_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_log_viewer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_log_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_server_vmess.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_vmess.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_routing_setting.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_admin_panel.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_admin_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_about.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_user_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\activity_edit_server.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_edit_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:\\layout\\item_qrcode.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_qrcode.xml"}, {"merged": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-74:/layout/item_log_entry.xml", "source": "com.mohamedrady.v2hoor.app-main-79:/layout/item_log_entry.xml"}]