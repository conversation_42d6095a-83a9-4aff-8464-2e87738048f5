package com.mohamedrady.v2hoor.service

import android.content.Context
import android.content.SharedPreferences
import androidx.appcompat.app.AppCompatDelegate
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mohamedrady.v2hoor.AppConfig

/**
 * Theme Manager for Dark/Light Mode
 * Handles theme switching and persistence
 */
class ThemeManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ThemeManager? = null
        
        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ThemeManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val PREFS_NAME = "theme_preferences"
        private const val KEY_THEME_MODE = "theme_mode"
        private const val KEY_AUTO_THEME = "auto_theme"
        private const val KEY_DARK_MODE_SCHEDULE = "dark_mode_schedule"
        private const val KEY_SCHEDULE_START = "schedule_start_hour"
        private const val KEY_SCHEDULE_END = "schedule_end_hour"
    }
    
    enum class ThemeMode(val value: Int, val displayName: String) {
        LIGHT(AppCompatDelegate.MODE_NIGHT_NO, "فاتح"),
        DARK(AppCompatDelegate.MODE_NIGHT_YES, "داكن"),
        SYSTEM(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM, "تلقائي (النظام)"),
        AUTO(AppCompatDelegate.MODE_NIGHT_AUTO_BATTERY, "توفير البطارية");
        
        companion object {
            fun fromValue(value: Int): ThemeMode {
                return values().find { it.value == value } ?: SYSTEM
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // LiveData for theme changes
    private val _currentTheme = MutableLiveData<ThemeMode>()
    val currentTheme: LiveData<ThemeMode> = _currentTheme
    
    private val _isDarkMode = MutableLiveData<Boolean>()
    val isDarkMode: LiveData<Boolean> = _isDarkMode
    
    init {
        // Initialize with saved theme
        val savedTheme = getSavedTheme()
        applyTheme(savedTheme)
        _currentTheme.value = savedTheme
        updateDarkModeStatus()
    }
    
    /**
     * Apply theme to the app
     */
    fun applyTheme(themeMode: ThemeMode) {
        try {
            AppCompatDelegate.setDefaultNightMode(themeMode.value)
            saveTheme(themeMode)
            _currentTheme.postValue(themeMode)
            updateDarkModeStatus()
            
            android.util.Log.i(AppConfig.TAG, "Theme applied: ${themeMode.displayName}")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to apply theme", e)
        }
    }
    
    /**
     * Toggle between light and dark mode
     */
    fun toggleTheme() {
        val currentTheme = getCurrentTheme()
        val newTheme = when (currentTheme) {
            ThemeMode.LIGHT -> ThemeMode.DARK
            ThemeMode.DARK -> ThemeMode.LIGHT
            else -> ThemeMode.LIGHT
        }
        applyTheme(newTheme)
    }
    
    /**
     * Get current theme mode
     */
    fun getCurrentTheme(): ThemeMode {
        return _currentTheme.value ?: getSavedTheme()
    }
    
    /**
     * Check if dark mode is currently active
     */
    fun isDarkModeActive(): Boolean {
        return _isDarkMode.value ?: false
    }
    
    /**
     * Save theme preference
     */
    private fun saveTheme(themeMode: ThemeMode) {
        prefs.edit()
            .putInt(KEY_THEME_MODE, themeMode.value)
            .apply()
    }
    
    /**
     * Get saved theme preference
     */
    private fun getSavedTheme(): ThemeMode {
        val savedValue = prefs.getInt(KEY_THEME_MODE, ThemeMode.SYSTEM.value)
        return ThemeMode.fromValue(savedValue)
    }
    
    /**
     * Update dark mode status based on current configuration
     */
    private fun updateDarkModeStatus() {
        val currentNightMode = context.resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK
        val isDark = currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES
        _isDarkMode.postValue(isDark)
    }
    
    /**
     * Set auto theme based on time schedule
     */
    fun setAutoThemeSchedule(enabled: Boolean, startHour: Int = 22, endHour: Int = 6) {
        prefs.edit()
            .putBoolean(KEY_DARK_MODE_SCHEDULE, enabled)
            .putInt(KEY_SCHEDULE_START, startHour)
            .putInt(KEY_SCHEDULE_END, endHour)
            .apply()
        
        if (enabled) {
            checkAndApplyScheduledTheme()
        }
    }
    
    /**
     * Check if auto theme schedule is enabled
     */
    fun isAutoThemeScheduleEnabled(): Boolean {
        return prefs.getBoolean(KEY_DARK_MODE_SCHEDULE, false)
    }
    
    /**
     * Apply theme based on schedule
     */
    fun checkAndApplyScheduledTheme() {
        if (!isAutoThemeScheduleEnabled()) return
        
        val currentHour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        val startHour = prefs.getInt(KEY_SCHEDULE_START, 22)
        val endHour = prefs.getInt(KEY_SCHEDULE_END, 6)
        
        val shouldBeDark = if (startHour > endHour) {
            // Schedule crosses midnight (e.g., 22:00 to 6:00)
            currentHour >= startHour || currentHour < endHour
        } else {
            // Schedule within same day (e.g., 8:00 to 18:00)
            currentHour in startHour until endHour
        }
        
        val targetTheme = if (shouldBeDark) ThemeMode.DARK else ThemeMode.LIGHT
        if (getCurrentTheme() != targetTheme) {
            applyTheme(targetTheme)
        }
    }
    
    /**
     * Get theme schedule settings
     */
    fun getThemeSchedule(): Pair<Int, Int> {
        val startHour = prefs.getInt(KEY_SCHEDULE_START, 22)
        val endHour = prefs.getInt(KEY_SCHEDULE_END, 6)
        return Pair(startHour, endHour)
    }
    
    /**
     * Get all available themes
     */
    fun getAvailableThemes(): List<ThemeMode> {
        return ThemeMode.values().toList()
    }
    
    /**
     * Get theme display name
     */
    fun getThemeDisplayName(themeMode: ThemeMode): String {
        return themeMode.displayName
    }
    
    /**
     * Reset to default theme
     */
    fun resetToDefault() {
        applyTheme(ThemeMode.SYSTEM)
        setAutoThemeSchedule(false)
    }
    
    /**
     * Export theme settings
     */
    fun exportThemeSettings(): Map<String, Any> {
        return mapOf(
            "theme_mode" to getCurrentTheme().name,
            "auto_schedule_enabled" to isAutoThemeScheduleEnabled(),
            "schedule_start" to getThemeSchedule().first,
            "schedule_end" to getThemeSchedule().second
        )
    }
    
    /**
     * Import theme settings
     */
    fun importThemeSettings(settings: Map<String, Any>) {
        try {
            val themeName = settings["theme_mode"] as? String
            val themeMode = themeName?.let { name ->
                ThemeMode.values().find { it.name == name }
            } ?: ThemeMode.SYSTEM
            
            val autoScheduleEnabled = settings["auto_schedule_enabled"] as? Boolean ?: false
            val scheduleStart = (settings["schedule_start"] as? Number)?.toInt() ?: 22
            val scheduleEnd = (settings["schedule_end"] as? Number)?.toInt() ?: 6
            
            applyTheme(themeMode)
            setAutoThemeSchedule(autoScheduleEnabled, scheduleStart, scheduleEnd)
            
            android.util.Log.i(AppConfig.TAG, "Theme settings imported successfully")
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to import theme settings", e)
        }
    }
    
    /**
     * Get theme statistics
     */
    fun getThemeStats(): Map<String, String> {
        return mapOf(
            "current_theme" to getCurrentTheme().displayName,
            "is_dark_active" to if (isDarkModeActive()) "نعم" else "لا",
            "auto_schedule" to if (isAutoThemeScheduleEnabled()) "مفعل" else "معطل",
            "schedule_time" to if (isAutoThemeScheduleEnabled()) {
                val (start, end) = getThemeSchedule()
                "${String.format("%02d:00", start)} - ${String.format("%02d:00", end)}"
            } else "غير محدد"
        )
    }
}
