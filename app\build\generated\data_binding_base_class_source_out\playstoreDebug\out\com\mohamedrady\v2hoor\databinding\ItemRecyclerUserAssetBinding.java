// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecyclerUserAssetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView assetName;

  @NonNull
  public final TextView assetProperties;

  @NonNull
  public final LinearLayout layoutEdit;

  @NonNull
  public final LinearLayout layoutRemove;

  private ItemRecyclerUserAssetBinding(@NonNull LinearLayout rootView, @NonNull TextView assetName,
      @NonNull TextView assetProperties, @NonNull LinearLayout layoutEdit,
      @NonNull LinearLayout layoutRemove) {
    this.rootView = rootView;
    this.assetName = assetName;
    this.assetProperties = assetProperties;
    this.layoutEdit = layoutEdit;
    this.layoutRemove = layoutRemove;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecyclerUserAssetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecyclerUserAssetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recycler_user_asset, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecyclerUserAssetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.asset_name;
      TextView assetName = ViewBindings.findChildViewById(rootView, id);
      if (assetName == null) {
        break missingId;
      }

      id = R.id.asset_properties;
      TextView assetProperties = ViewBindings.findChildViewById(rootView, id);
      if (assetProperties == null) {
        break missingId;
      }

      id = R.id.layout_edit;
      LinearLayout layoutEdit = ViewBindings.findChildViewById(rootView, id);
      if (layoutEdit == null) {
        break missingId;
      }

      id = R.id.layout_remove;
      LinearLayout layoutRemove = ViewBindings.findChildViewById(rootView, id);
      if (layoutRemove == null) {
        break missingId;
      }

      return new ItemRecyclerUserAssetBinding((LinearLayout) rootView, assetName, assetProperties,
          layoutEdit, layoutRemove);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
