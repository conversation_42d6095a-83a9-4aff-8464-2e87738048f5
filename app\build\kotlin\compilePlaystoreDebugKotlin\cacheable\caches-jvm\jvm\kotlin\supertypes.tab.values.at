/ Header Record For PersistentHashMapValueStorage& %androidx.multidex.MultiDexApplication android.os.Parcelable android.os.Parcelable kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum android.os.Parcelable android.os.Parcelable# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase# "com.mohamedrady.v2hoor.fmt.FmtBase9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration6 5androidx.recyclerview.widget.ItemTouchHelper.Callback- ,com.mohamedrady.v2hoor.plugin.ResolvedPlugin java.util.ArrayList java.io.FileNotFoundException% $com.mohamedrady.v2hoor.plugin.Plugin" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver$ #android.appwidget.AppWidgetProvider kotlin.Enum androidx.work.CoroutineWorker* )android.service.quicksettings.TileService" !android.content.BroadcastReceiver androidx.work.CoroutineWorkerB android.app.Service-com.mohamedrady.v2hoor.service.ServiceControl libv2ray.CoreCallbackHandler" !android.content.BroadcastReceiver android.app.ServiceE android.net.VpnService-com.mohamedrady.v2hoor.service.ServiceControl' &com.mohamedrady.v2hoor.ui.BaseActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity' &com.mohamedrady.v2hoor.ui.BaseActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity' &com.mohamedrady.v2hoor.ui.BaseActivity) (androidx.appcompat.app.AppCompatActivity1 0androidx.viewpager2.adapter.FragmentStateAdapter) (androidx.appcompat.app.AppCompatActivityo &com.mohamedrady.v2hoor.ui.BaseActivityGandroidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder' &com.mohamedrady.v2hoor.ui.BaseActivity~ &com.mohamedrady.v2hoor.ui.BaseActivityVcom.google.android.material.navigation.NavigationView.OnNavigationItemSelectedListener kotlin.Enumg 1androidx.recyclerview.widget.RecyclerView.Adapter4com.mohamedrady.v2hoor.helper.ItemTouchHelperAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolderu <com.mohamedrady.v2hoor.ui.MainRecyclerAdapter.BaseViewHolder7com.mohamedrady.v2hoor.helper.ItemTouchHelperViewHolder= <com.mohamedrady.v2hoor.ui.MainRecyclerAdapter.BaseViewHolder' &com.mohamedrady.v2hoor.ui.BaseActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder^ ;com.mohamedrady.v2hoor.ui.PerAppProxyAdapter.BaseViewHolder!android.view.View.OnClickListener) (androidx.appcompat.app.AppCompatActivity' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivityg 1androidx.recyclerview.widget.RecyclerView.Adapter4com.mohamedrady.v2hoor.helper.ItemTouchHelperAdapter Fcom.mohamedrady.v2hoor.ui.RoutingSettingRecyclerAdapter.BaseViewHolder7com.mohamedrady.v2hoor.helper.ItemTouchHelperViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity' &com.mohamedrady.v2hoor.ui.BaseActivity- ,androidx.preference.PreferenceFragmentCompat' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivityg 1androidx.recyclerview.widget.RecyclerView.Adapter4com.mohamedrady.v2hoor.helper.ItemTouchHelperAdapter{ Bcom.mohamedrady.v2hoor.ui.SubSettingRecyclerAdapter.BaseViewHolder7com.mohamedrady.v2hoor.helper.ItemTouchHelperViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivity' &com.mohamedrady.v2hoor.ui.BaseActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder' &com.mohamedrady.v2hoor.ui.BaseActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback* )java.lang.Thread.UncaughtExceptionHandler android.content.ContextWrapper androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModelg #androidx.lifecycle.AndroidViewModelBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener androidx.lifecycle.ViewModel