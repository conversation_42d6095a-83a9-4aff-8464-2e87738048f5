<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_user_servers" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_user_servers.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_user_servers_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="150" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/textViewUserServersCount" view="TextView"><Expressions/><location startLine="62" startOffset="24" endLine="69" endOffset="53"/></Target><Target id="@+id/buttonRefresh" view="Button"><Expressions/><location startLine="71" startOffset="24" endLine="78" endOffset="53"/></Target><Target id="@+id/recyclerViewUserServers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="87" startOffset="12" endLine="92" endOffset="59"/></Target><Target id="@+id/layoutEmptyState" view="LinearLayout"><Expressions/><location startLine="95" startOffset="12" endLine="126" endOffset="26"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="133" startOffset="4" endLine="138" endOffset="35"/></Target><Target id="@+id/fabAddServer" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="141" startOffset="4" endLine="148" endOffset="33"/></Target></Targets></Layout>