// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddUserBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button buttonCancel;

  @NonNull
  public final Button buttonSave;

  @NonNull
  public final TextInputEditText editTextCity;

  @NonNull
  public final TextInputEditText editTextCountry;

  @NonNull
  public final TextInputEditText editTextDisplayName;

  @NonNull
  public final TextInputEditText editTextEmail;

  @NonNull
  public final TextInputEditText editTextPhoneNumber;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RadioButton radioButtonFree;

  @NonNull
  public final RadioButton radioButtonPremium;

  @NonNull
  public final RadioGroup radioGroupSubscription;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAddUserBinding(@NonNull CoordinatorLayout rootView, @NonNull Button buttonCancel,
      @NonNull Button buttonSave, @NonNull TextInputEditText editTextCity,
      @NonNull TextInputEditText editTextCountry, @NonNull TextInputEditText editTextDisplayName,
      @NonNull TextInputEditText editTextEmail, @NonNull TextInputEditText editTextPhoneNumber,
      @NonNull ProgressBar progressBar, @NonNull RadioButton radioButtonFree,
      @NonNull RadioButton radioButtonPremium, @NonNull RadioGroup radioGroupSubscription,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonSave = buttonSave;
    this.editTextCity = editTextCity;
    this.editTextCountry = editTextCountry;
    this.editTextDisplayName = editTextDisplayName;
    this.editTextEmail = editTextEmail;
    this.editTextPhoneNumber = editTextPhoneNumber;
    this.progressBar = progressBar;
    this.radioButtonFree = radioButtonFree;
    this.radioButtonPremium = radioButtonPremium;
    this.radioGroupSubscription = radioGroupSubscription;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddUserBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddUserBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_user, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddUserBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonCancel;
      Button buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.buttonSave;
      Button buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.editTextCity;
      TextInputEditText editTextCity = ViewBindings.findChildViewById(rootView, id);
      if (editTextCity == null) {
        break missingId;
      }

      id = R.id.editTextCountry;
      TextInputEditText editTextCountry = ViewBindings.findChildViewById(rootView, id);
      if (editTextCountry == null) {
        break missingId;
      }

      id = R.id.editTextDisplayName;
      TextInputEditText editTextDisplayName = ViewBindings.findChildViewById(rootView, id);
      if (editTextDisplayName == null) {
        break missingId;
      }

      id = R.id.editTextEmail;
      TextInputEditText editTextEmail = ViewBindings.findChildViewById(rootView, id);
      if (editTextEmail == null) {
        break missingId;
      }

      id = R.id.editTextPhoneNumber;
      TextInputEditText editTextPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (editTextPhoneNumber == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.radioButtonFree;
      RadioButton radioButtonFree = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonFree == null) {
        break missingId;
      }

      id = R.id.radioButtonPremium;
      RadioButton radioButtonPremium = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonPremium == null) {
        break missingId;
      }

      id = R.id.radioGroupSubscription;
      RadioGroup radioGroupSubscription = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupSubscription == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAddUserBinding((CoordinatorLayout) rootView, buttonCancel, buttonSave,
          editTextCity, editTextCountry, editTextDisplayName, editTextEmail, editTextPhoneNumber,
          progressBar, radioButtonFree, radioButtonPremium, radioGroupSubscription, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
