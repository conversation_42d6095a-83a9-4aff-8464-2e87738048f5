package com.mohamedrady.v2hoor.dto

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Server configuration for admin management
 */
@Parcelize
data class AdminServerConfig(
    val id: String = "",
    val name: String = "",
    val config: String = "",
    val country: String? = null,
    val city: String? = null,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long? = null,
    val maxUsers: Int = 100,
    val currentUsers: Int = 0,
    val serverType: String = "vmess", // vmess, vless, trojan, shadowsocks
    val priority: Int = 0 // Higher priority servers shown first
) : Parcelable {
    
    /**
     * Get server location display text
     */
    fun getLocationText(): String {
        return when {
            !country.isNullOrBlank() && !city.isNullOrBlank() -> "$city, $country"
            !country.isNullOrBlank() -> country
            !city.isNullOrBlank() -> city
            else -> "موقع غير محدد"
        }
    }
    
    /**
     * Get server type display text
     */
    fun getServerTypeText(): String {
        return when (serverType.lowercase()) {
            "vmess" -> "VMess"
            "vless" -> "VLESS"
            "trojan" -> "Trojan"
            "shadowsocks" -> "Shadowsocks"
            "ss" -> "Shadowsocks"
            else -> serverType.uppercase()
        }
    }
    
    /**
     * Get server status text
     */
    fun getStatusText(): String {
        return if (isActive) "نشط" else "غير نشط"
    }
    
    /**
     * Get usage percentage
     */
    fun getUsagePercentage(): Int {
        return if (maxUsers > 0) {
            ((currentUsers.toFloat() / maxUsers) * 100).toInt()
        } else 0
    }
    
    /**
     * Check if server is full
     */
    fun isFull(): Boolean {
        return currentUsers >= maxUsers
    }
    
    /**
     * Get formatted creation date
     */
    fun getFormattedCreationDate(): String {
        return java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
            .format(java.util.Date(createdAt))
    }
}

/**
 * User-Server assignment data class
 */
@Parcelize
data class UserServerAssignment(
    val id: String = "",
    val userId: String = "",
    val serverId: String = "",
    val isActive: Boolean = true,
    val assignedAt: Long = System.currentTimeMillis(),
    val assignedBy: String? = null,
    val expiresAt: Long? = null
) : Parcelable {
    
    /**
     * Check if assignment is expired
     */
    fun isExpired(): Boolean {
        return expiresAt?.let { it < System.currentTimeMillis() } ?: false
    }
    
    /**
     * Get formatted assignment date
     */
    fun getFormattedAssignmentDate(): String {
        return java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
            .format(java.util.Date(assignedAt))
    }
}
