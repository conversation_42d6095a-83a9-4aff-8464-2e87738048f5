<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_about" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_about.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_about_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="253" endOffset="12"/></Target><Target id="@+id/layout_backup" view="LinearLayout"><Expressions/><location startLine="17" startOffset="12" endLine="55" endOffset="26"/></Target><Target id="@+id/tv_backup_summary" view="TextView"><Expressions/><location startLine="45" startOffset="20" endLine="51" endOffset="88"/></Target><Target id="@+id/layout_share" view="LinearLayout"><Expressions/><location startLine="57" startOffset="12" endLine="79" endOffset="26"/></Target><Target id="@+id/layout_restore" view="LinearLayout"><Expressions/><location startLine="81" startOffset="12" endLine="103" endOffset="26"/></Target><Target id="@+id/layout_soure_ccode" view="LinearLayout"><Expressions/><location startLine="113" startOffset="12" endLine="135" endOffset="26"/></Target><Target id="@+id/layout_oss_licenses" view="LinearLayout"><Expressions/><location startLine="137" startOffset="12" endLine="159" endOffset="26"/></Target><Target id="@+id/layout_feedback" view="LinearLayout"><Expressions/><location startLine="161" startOffset="12" endLine="183" endOffset="26"/></Target><Target id="@+id/layout_tg_channel" view="LinearLayout"><Expressions/><location startLine="186" startOffset="12" endLine="208" endOffset="26"/></Target><Target id="@+id/layout_privacy_policy" view="LinearLayout"><Expressions/><location startLine="210" startOffset="12" endLine="232" endOffset="26"/></Target><Target id="@+id/tv_version" view="TextView"><Expressions/><location startLine="241" startOffset="16" endLine="246" endOffset="84"/></Target></Targets></Layout>