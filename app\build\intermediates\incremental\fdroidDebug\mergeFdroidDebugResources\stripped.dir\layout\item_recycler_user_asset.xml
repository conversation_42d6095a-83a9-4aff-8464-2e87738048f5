<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="@dimen/padding_spacing_dp8">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="@dimen/padding_spacing_dp8">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/asset_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:minLines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead"
                    tools:text="Placeholder.dat" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/asset_properties"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:lines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    tools:text="1MB . 2020.01.01" />


            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/padding_spacing_dp8">

            <ImageView
                android:layout_width="@dimen/image_size_dp24"
                android:layout_height="@dimen/image_size_dp24"
                app:srcCompat="@drawable/ic_edit_24dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_remove"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/padding_spacing_dp8">

            <ImageView
                android:layout_width="@dimen/image_size_dp24"
                android:layout_height="@dimen/image_size_dp24"
                app:srcCompat="@drawable/ic_delete_24dp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
