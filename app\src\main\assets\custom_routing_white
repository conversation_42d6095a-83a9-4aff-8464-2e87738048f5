[{"remarks": "Google cn", "outboundTag": "proxy", "domain": ["domain:googleapis.cn", "domain:gstatic.com"]}, {"remarks": "阻断udp443", "outboundTag": "block", "port": "443", "network": "udp"}, {"remarks": "绕过局域网IP", "outboundTag": "direct", "ip": ["geoip:private"]}, {"remarks": "绕过局域网域名", "outboundTag": "direct", "domain": ["geosite:private"]}, {"remarks": "绕过中国公共DNSIP", "outboundTag": "direct", "ip": ["*********", "*********", "2400:3200::1", "2400:3200:baba::1", "************", "**********", "************", "2402:4e00::", "2402:4e00:1::", "************", "2400:da00::6666", "***************", "***************", "***************", "***************", "***************", "***************", "***********", "***********", "***********", "************", "************", "*************", "*******", "*********", "***********", "************", "2400:7fc0:849e:200::4", "2404:c2c0:85d8:901::4", "************", "***********", "2400:7fc0:849e:200::8", "2404:c2c0:85d8:901::8", "************", "***********"]}, {"remarks": "绕过中国公共DNS域名", "outboundTag": "direct", "domain": ["domain:alidns.com", "domain:doh.pub", "domain:dot.pub", "domain:360.cn", "domain:onedns.net"]}, {"remarks": "绕过中国IP", "outboundTag": "direct", "ip": ["geoip:cn"]}, {"remarks": "绕过中国域名", "outboundTag": "direct", "domain": ["geosite:cn"]}]