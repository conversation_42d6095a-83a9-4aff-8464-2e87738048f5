<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.ServerCustomConfigActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding_spacing_dp8"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_lab_need_inbound"
                android:textAppearance="@style/TextAppearance.AppCompat.Tooltip"
                android:textColor="@color/color_secondary" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding_spacing_dp8"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/title_server"
                android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding_spacing_dp8"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_lab_remarks" />

            <EditText
                android:id="@+id/et_remarks"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_spacing_dp8"
                android:text="@string/server_lab_content"
                android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />

            <com.blacksquircle.ui.editorkit.widget.TextProcessor
                android:id="@+id/editor"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:gravity="top|start" />
        </LinearLayout>

    </LinearLayout>
</ScrollView>
