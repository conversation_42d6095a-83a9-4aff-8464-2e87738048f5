<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_server_details" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_server_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_server_details_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="238" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/textViewServerName" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="70" endOffset="51"/></Target><Target id="@+id/textViewServerLocation" view="TextView"><Expressions/><location startLine="72" startOffset="20" endLine="79" endOffset="64"/></Target><Target id="@+id/textViewServerType" view="TextView"><Expressions/><location startLine="81" startOffset="20" endLine="89" endOffset="57"/></Target><Target id="@+id/textViewServerStatus" view="TextView"><Expressions/><location startLine="91" startOffset="20" endLine="99" endOffset="50"/></Target><Target id="@+id/textViewServerUsage" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="140" endOffset="56"/></Target><Target id="@+id/textViewServerPriority" view="TextView"><Expressions/><location startLine="142" startOffset="20" endLine="148" endOffset="50"/></Target><Target id="@+id/textViewCreationDate" view="TextView"><Expressions/><location startLine="182" startOffset="20" endLine="188" endOffset="67"/></Target><Target id="@+id/textViewServerConfig" view="TextView"><Expressions/><location startLine="221" startOffset="20" endLine="228" endOffset="61"/></Target></Targets></Layout>