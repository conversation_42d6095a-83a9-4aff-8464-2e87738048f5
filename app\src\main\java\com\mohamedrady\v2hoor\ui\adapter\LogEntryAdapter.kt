package com.mohamedrady.v2hoor.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemLogEntryBinding
import com.mohamedrady.v2hoor.service.LogListenerService

class LogEntryAdapter(
    private val onLogClick: (LogListenerService.LogEntry) -> Unit
) : RecyclerView.Adapter<LogEntryAdapter.LogEntryViewHolder>() {

    private val logEntries = mutableListOf<LogListenerService.LogEntry>()

    class LogEntryViewHolder(
        private val binding: ItemLogEntryBinding,
        private val onLogClick: (LogListenerService.LogEntry) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(entry: LogListenerService.LogEntry) {
            binding.apply {
                textViewTimestamp.text = entry.timestamp
                textViewLevel.text = entry.level
                textViewTag.text = entry.tag
                textViewMessage.text = entry.message

                // Set colors based on log level
                val (levelColor, backgroundColor) = when (entry.level) {
                    "E" -> Pair(Color.WHITE, ContextCompat.getColor(root.context, R.color.error_color))
                    "W" -> Pair(Color.BLACK, ContextCompat.getColor(root.context, R.color.warning_color))
                    "I" -> Pair(Color.BLACK, ContextCompat.getColor(root.context, R.color.info_color))
                    "D" -> Pair(Color.BLACK, ContextCompat.getColor(root.context, R.color.debug_color))
                    "V" -> Pair(Color.BLACK, ContextCompat.getColor(root.context, R.color.verbose_color))
                    else -> Pair(Color.BLACK, Color.TRANSPARENT)
                }

                textViewLevel.setTextColor(levelColor)
                textViewLevel.setBackgroundColor(backgroundColor)

                // Set click listener
                root.setOnClickListener {
                    onLogClick(entry)
                }

                // Highlight important logs
                when (entry.level) {
                    "E" -> {
                        root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.error_background))
                    }
                    "W" -> {
                        root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.warning_background))
                    }
                    else -> {
                        root.setBackgroundColor(Color.TRANSPARENT)
                    }
                }

                // Add emoji indicators
                val levelEmoji = when (entry.level) {
                    "E" -> "❌"
                    "W" -> "⚠️"
                    "I" -> "ℹ️"
                    "D" -> "🐛"
                    "V" -> "📝"
                    else -> "📄"
                }
                textViewLevel.text = "$levelEmoji ${entry.level}"

                // Truncate long messages
                if (entry.message.length > 100) {
                    textViewMessage.text = "${entry.message.take(100)}..."
                }

                // Show PID/TID if available
                if (entry.pid.isNotEmpty() || entry.tid.isNotEmpty()) {
                    textViewTag.text = "${entry.tag} (${entry.pid}:${entry.tid})"
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LogEntryViewHolder {
        val binding = ItemLogEntryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return LogEntryViewHolder(binding, onLogClick)
    }

    override fun onBindViewHolder(holder: LogEntryViewHolder, position: Int) {
        holder.bind(logEntries[position])
    }

    override fun getItemCount(): Int = logEntries.size

    fun addLogEntry(entry: LogListenerService.LogEntry) {
        logEntries.add(entry)
        notifyItemInserted(logEntries.size - 1)
        
        // Limit the number of entries to prevent memory issues
        if (logEntries.size > 1000) {
            logEntries.removeAt(0)
            notifyItemRemoved(0)
        }
    }

    fun setLogs(logs: List<LogListenerService.LogEntry>) {
        logEntries.clear()
        logEntries.addAll(logs)
        notifyDataSetChanged()
    }

    fun clearLogs() {
        logEntries.clear()
        notifyDataSetChanged()
    }

    fun filterLogs(predicate: (LogListenerService.LogEntry) -> Boolean) {
        val filteredLogs = logEntries.filter(predicate)
        logEntries.clear()
        logEntries.addAll(filteredLogs)
        notifyDataSetChanged()
    }
}
