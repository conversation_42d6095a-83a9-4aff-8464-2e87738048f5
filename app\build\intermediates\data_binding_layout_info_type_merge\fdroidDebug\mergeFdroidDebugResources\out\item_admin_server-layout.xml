<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_admin_server" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_admin_server.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardView"><Targets><Target id="@+id/cardView" tag="layout/item_admin_server_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="208" endOffset="35"/></Target><Target id="@+id/textViewServerName" view="TextView"><Expressions/><location startLine="30" startOffset="16" endLine="37" endOffset="47"/></Target><Target id="@+id/textViewServerLocation" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="60"/></Target><Target id="@+id/textViewServerStatus" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="66" endOffset="46"/></Target><Target id="@+id/textViewFullIndicator" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="80" endOffset="48"/></Target><Target id="@+id/textViewServerType" view="TextView"><Expressions/><location startLine="99" startOffset="16" endLine="106" endOffset="40"/></Target><Target id="@+id/textViewServerPriority" view="TextView"><Expressions/><location startLine="108" startOffset="16" endLine="115" endOffset="46"/></Target><Target id="@+id/textViewServerUsage" view="TextView"><Expressions/><location startLine="125" startOffset="16" endLine="131" endOffset="58"/></Target><Target id="@+id/progressBarUsage" view="ProgressBar"><Expressions/><location startLine="133" startOffset="16" endLine="141" endOffset="57"/></Target><Target id="@+id/textViewCreationDate" view="TextView"><Expressions/><location startLine="148" startOffset="8" endLine="155" endOffset="55"/></Target><Target id="@+id/buttonDeleteServer" view="Button"><Expressions/><location startLine="165" startOffset="12" endLine="173" endOffset="41"/></Target><Target id="@+id/buttonViewUsers" view="Button"><Expressions/><location startLine="175" startOffset="12" endLine="183" endOffset="41"/></Target><Target id="@+id/buttonEditServer" view="Button"><Expressions/><location startLine="185" startOffset="12" endLine="193" endOffset="41"/></Target><Target id="@+id/buttonToggleStatus" view="Button"><Expressions/><location startLine="195" startOffset="12" endLine="202" endOffset="41"/></Target></Targets></Layout>