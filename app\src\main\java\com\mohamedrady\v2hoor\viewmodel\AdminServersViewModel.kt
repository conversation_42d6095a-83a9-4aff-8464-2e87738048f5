package com.mohamedrady.v2hoor.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mohamedrady.v2hoor.dto.AdminServerConfig
import com.mohamedrady.v2hoor.service.ServerManagementService
import kotlinx.coroutines.launch

/**
 * ViewModel for admin servers management
 */
class AdminServersViewModel : ViewModel() {

    private val serverManagementService = ServerManagementService.getInstance()

    private val _servers = MutableLiveData<List<AdminServerConfig>>()
    val servers: LiveData<List<AdminServerConfig>> = _servers

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _statistics = MutableLiveData<Map<String, Any>>()
    val statistics: LiveData<Map<String, Any>> = _statistics

    private var allServers: List<AdminServerConfig> = emptyList()

    /**
     * Load all servers
     */
    fun loadServers() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val result = serverManagementService.getAllServers()
                result.fold(
                    onSuccess = { serversList ->
                        allServers = serversList
                        _servers.value = serversList
                        loadStatistics()
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تحميل السيرفرات: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ غير متوقع: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Search servers by query
     */
    fun searchServers(query: String) {
        if (query.isBlank()) {
            _servers.value = allServers
            return
        }

        val filteredServers = allServers.filter { server ->
            server.name.contains(query, ignoreCase = true) ||
            server.country?.contains(query, ignoreCase = true) == true ||
            server.city?.contains(query, ignoreCase = true) == true ||
            server.serverType.contains(query, ignoreCase = true)
        }

        _servers.value = filteredServers
    }

    /**
     * Update server
     */
    fun updateServer(server: AdminServerConfig) {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val result = serverManagementService.createOrUpdateServer(server)
                result.fold(
                    onSuccess = {
                        // Update local data
                        val updatedServers = allServers.map { existing ->
                            if (existing.id == server.id) server else existing
                        }
                        allServers = updatedServers
                        _servers.value = updatedServers
                        loadStatistics()
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في تحديث السيرفر: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في تحديث السيرفر: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Delete server
     */
    fun deleteServer(serverId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val result = serverManagementService.deleteServer(serverId)
                result.fold(
                    onSuccess = {
                        // Remove from local data
                        val updatedServers = allServers.filter { it.id != serverId }
                        allServers = updatedServers
                        _servers.value = updatedServers
                        loadStatistics()
                    },
                    onFailure = { exception ->
                        _error.value = "فشل في حذف السيرفر: ${exception.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "خطأ في حذف السيرفر: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load server statistics
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val result = serverManagementService.getServerStatistics()
                result.fold(
                    onSuccess = { stats ->
                        _statistics.value = stats
                    },
                    onFailure = { exception ->
                        // Calculate statistics locally if service fails
                        val localStats = calculateLocalStatistics()
                        _statistics.value = localStats
                    }
                )
            } catch (e: Exception) {
                val localStats = calculateLocalStatistics()
                _statistics.value = localStats
            }
        }
    }

    /**
     * Calculate statistics from local data
     */
    private fun calculateLocalStatistics(): Map<String, Any> {
        val totalServers = allServers.size
        val activeServers = allServers.count { it.isActive }
        
        return mapOf(
            "totalServers" to totalServers,
            "activeServers" to activeServers,
            "inactiveServers" to (totalServers - activeServers),
            "totalAssignments" to 0 // This would need to be calculated from assignments
        )
    }

    /**
     * Refresh data
     */
    fun refresh() {
        loadServers()
    }

    /**
     * Clear error
     */
    fun clearError() {
        _error.value = null
    }
}
