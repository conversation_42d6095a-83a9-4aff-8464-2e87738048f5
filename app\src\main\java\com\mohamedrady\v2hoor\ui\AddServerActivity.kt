package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityAddServerBinding

class AddServerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddServerBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddServerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.add_server)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
