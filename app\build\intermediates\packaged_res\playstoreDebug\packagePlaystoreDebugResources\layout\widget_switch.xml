<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_switch"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layout_background"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center">

        <ImageView
            android:id="@+id/image_switch"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:padding="@dimen/padding_spacing_dp16"
            app:srcCompat="@drawable/ic_stat_name" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textAppearance="@style/TextAppearance.AppCompat.Small"
        android:textColor="@android:color/white" />
</LinearLayout>
