package com.mohamedrady.v2hoor.ui

import android.app.AlertDialog
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.ArrayAdapter
import android.widget.EditText
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivitySecuritySettingsBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.service.SecurityManager
import kotlinx.coroutines.launch

/**
 * Security Settings Activity
 * Manages app security features like biometric auth, PIN, and auto-lock
 */
class SecuritySettingsActivity : BaseActivity() {
    
    private lateinit var binding: ActivitySecuritySettingsBinding
    private lateinit var securityManager: SecurityManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("SecuritySettings", "🔒 Starting SecuritySettingsActivity")
        
        binding = ActivitySecuritySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Initialize security manager
        securityManager = SecurityManager.getInstance(this)
        
        setupToolbar()
        setupClickListeners()
        setupObservers()
        loadCurrentSettings()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.security_settings)
        }
    }
    
    private fun setupClickListeners() {
        // Biometric authentication toggle
        binding.switchBiometric.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked && !securityManager.isBiometricAvailable()) {
                binding.switchBiometric.isChecked = false
                toastError("المصادقة البيومترية غير متاحة على هذا الجهاز")
                return@setOnCheckedChangeListener
            }
            
            securityManager.enableBiometricAuth(isChecked)
            updateBiometricStatus()
            toast(if (isChecked) "تم تفعيل المصادقة البيومترية" else "تم إيقاف المصادقة البيومترية")
        }
        
        // PIN authentication toggle
        binding.switchPin.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                showSetPinDialog()
            } else {
                showDisablePinDialog()
            }
        }
        
        // Change PIN
        binding.btnChangePin.setOnClickListener {
            showChangePinDialog()
        }
        
        // Test biometric
        binding.btnTestBiometric.setOnClickListener {
            testBiometricAuthentication()
        }
        
        // Auto-lock toggle
        binding.switchAutoLock.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                showAutoLockTimeoutDialog()
            } else {
                securityManager.setAutoLock(false)
                updateAutoLockStatus()
                toast("تم إيقاف القفل التلقائي")
            }
        }
        
        // Auto-lock timeout
        binding.btnAutoLockTimeout.setOnClickListener {
            showAutoLockTimeoutDialog()
        }
        
        // Reset security settings
        binding.btnResetSecurity.setOnClickListener {
            showResetSecurityDialog()
        }
        
        // Security info
        binding.btnSecurityInfo.setOnClickListener {
            showSecurityInfoDialog()
        }
    }
    
    private fun setupObservers() {
        // Observe app lock status
        securityManager.isAppLocked.observe(this) { isLocked ->
            updateLockStatus(isLocked)
        }
        
        // Observe authentication requirement
        securityManager.authenticationRequired.observe(this) { required ->
            if (required) {
                // Handle authentication requirement
                handleAuthenticationRequired()
            }
        }
    }
    
    private fun loadCurrentSettings() {
        // Load biometric settings
        binding.switchBiometric.isChecked = securityManager.isBiometricEnabled()
        updateBiometricStatus()
        
        // Load PIN settings
        binding.switchPin.isChecked = securityManager.isPinEnabled()
        updatePinStatus()
        
        // Load auto-lock settings
        binding.switchAutoLock.isChecked = securityManager.isAutoLockEnabled()
        updateAutoLockStatus()
        
        // Load security stats
        updateSecurityStats()
    }
    
    private fun updateBiometricStatus() {
        val isAvailable = securityManager.isBiometricAvailable()
        val isEnabled = securityManager.isBiometricEnabled()
        
        binding.tvBiometricStatus.text = when {
            !isAvailable -> "غير متاح على هذا الجهاز"
            isEnabled -> "مفعل ونشط"
            else -> "متاح ولكن معطل"
        }
        
        binding.btnTestBiometric.isEnabled = isEnabled
        binding.switchBiometric.isEnabled = isAvailable
    }
    
    private fun updatePinStatus() {
        val isEnabled = securityManager.isPinEnabled()
        
        binding.tvPinStatus.text = if (isEnabled) "مفعل ونشط" else "معطل"
        binding.btnChangePin.isEnabled = isEnabled
    }
    
    private fun updateAutoLockStatus() {
        val isEnabled = securityManager.isAutoLockEnabled()
        val timeout = securityManager.getAutoLockTimeout()
        
        binding.tvAutoLockStatus.text = if (isEnabled) {
            "مفعل - ${timeout.displayName}"
        } else {
            "معطل"
        }
        
        binding.btnAutoLockTimeout.isEnabled = isEnabled
        binding.btnAutoLockTimeout.text = "المهلة الزمنية: ${timeout.displayName}"
    }
    
    private fun updateLockStatus(isLocked: Boolean) {
        binding.tvLockStatus.text = if (isLocked) "التطبيق مقفل" else "التطبيق مفتوح"
    }
    
    private fun updateSecurityStats() {
        val stats = securityManager.getSecurityStats()
        
        binding.tvStatsAuthMethod.text = "طريقة المصادقة: ${stats["current_auth_method"]}"
        binding.tvStatsBiometric.text = "البيومترية: ${stats["biometric_enabled"]}"
        binding.tvStatsPin.text = "رقم PIN: ${stats["pin_enabled"]}"
        binding.tvStatsAutoLock.text = "القفل التلقائي: ${stats["auto_lock_enabled"]}"
        binding.tvStatsFailedAttempts.text = "المحاولات الفاشلة: ${stats["failed_attempts"]}"
    }
    
    private fun showSetPinDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_set_pin, null)
        val etPin = dialogView.findViewById<EditText>(R.id.et_pin)
        val etConfirmPin = dialogView.findViewById<EditText>(R.id.et_confirm_pin)
        
        AlertDialog.Builder(this)
            .setTitle("تعيين رقم PIN")
            .setMessage("أدخل رقم PIN مكون من 4-6 أرقام")
            .setView(dialogView)
            .setPositiveButton("تعيين") { _, _ ->
                val pin = etPin.text.toString()
                val confirmPin = etConfirmPin.text.toString()
                
                if (pin.length < 4) {
                    toastError("رقم PIN يجب أن يكون 4 أرقام على الأقل")
                    binding.switchPin.isChecked = false
                    return@setPositiveButton
                }
                
                if (pin != confirmPin) {
                    toastError("أرقام PIN غير متطابقة")
                    binding.switchPin.isChecked = false
                    return@setPositiveButton
                }
                
                if (securityManager.setPinCode(pin)) {
                    updatePinStatus()
                    toastSuccess("تم تعيين رقم PIN بنجاح")
                } else {
                    binding.switchPin.isChecked = false
                    toastError("فشل في تعيين رقم PIN")
                }
            }
            .setNegativeButton("إلغاء") { _, _ ->
                binding.switchPin.isChecked = false
            }
            .setCancelable(false)
            .show()
    }
    
    private fun showChangePinDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_change_pin, null)
        val etCurrentPin = dialogView.findViewById<EditText>(R.id.et_current_pin)
        val etNewPin = dialogView.findViewById<EditText>(R.id.et_new_pin)
        val etConfirmPin = dialogView.findViewById<EditText>(R.id.et_confirm_pin)
        
        AlertDialog.Builder(this)
            .setTitle("تغيير رقم PIN")
            .setView(dialogView)
            .setPositiveButton("تغيير") { _, _ ->
                val currentPin = etCurrentPin.text.toString()
                val newPin = etNewPin.text.toString()
                val confirmPin = etConfirmPin.text.toString()
                
                if (!securityManager.verifyPin(currentPin)) {
                    toastError("رقم PIN الحالي غير صحيح")
                    return@setPositiveButton
                }
                
                if (newPin.length < 4) {
                    toastError("رقم PIN الجديد يجب أن يكون 4 أرقام على الأقل")
                    return@setPositiveButton
                }
                
                if (newPin != confirmPin) {
                    toastError("أرقام PIN الجديدة غير متطابقة")
                    return@setPositiveButton
                }
                
                if (securityManager.setPinCode(newPin)) {
                    toastSuccess("تم تغيير رقم PIN بنجاح")
                } else {
                    toastError("فشل في تغيير رقم PIN")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showDisablePinDialog() {
        AlertDialog.Builder(this)
            .setTitle("إيقاف رقم PIN")
            .setMessage("هل أنت متأكد من إيقاف حماية رقم PIN؟")
            .setPositiveButton("إيقاف") { _, _ ->
                securityManager.disablePinAuth()
                updatePinStatus()
                toast("تم إيقاف رقم PIN")
            }
            .setNegativeButton("إلغاء") { _, _ ->
                binding.switchPin.isChecked = true
            }
            .show()
    }
    
    private fun showAutoLockTimeoutDialog() {
        val timeouts = SecurityManager.LockTimeout.values()
        val timeoutNames = timeouts.map { it.displayName }.toTypedArray()
        val currentTimeout = securityManager.getAutoLockTimeout()
        val currentIndex = timeouts.indexOf(currentTimeout)
        
        AlertDialog.Builder(this)
            .setTitle("اختيار مهلة القفل التلقائي")
            .setSingleChoiceItems(timeoutNames, currentIndex) { dialog, which ->
                val selectedTimeout = timeouts[which]
                securityManager.setAutoLock(true, selectedTimeout)
                updateAutoLockStatus()
                toast("تم تعيين مهلة القفل التلقائي: ${selectedTimeout.displayName}")
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء") { _, _ ->
                if (!securityManager.isAutoLockEnabled()) {
                    binding.switchAutoLock.isChecked = false
                }
            }
            .show()
    }
    
    private fun testBiometricAuthentication() {
        if (!securityManager.isBiometricEnabled()) {
            toastError("المصادقة البيومترية غير مفعلة")
            return
        }
        
        securityManager.showBiometricPrompt(
            activity = this,
            title = "اختبار المصادقة البيومترية",
            subtitle = "اختبر المصادقة البيومترية للتأكد من عملها",
            onSuccess = {
                toastSuccess("نجح اختبار المصادقة البيومترية!")
            },
            onError = { error ->
                toastError("فشل اختبار المصادقة البيومترية: $error")
            }
        )
    }
    
    private fun showResetSecurityDialog() {
        AlertDialog.Builder(this)
            .setTitle("إعادة تعيين إعدادات الأمان")
            .setMessage("سيتم إيقاف جميع إعدادات الأمان وحذف أرقام PIN. هل أنت متأكد؟")
            .setPositiveButton("إعادة تعيين") { _, _ ->
                securityManager.resetSecuritySettings()
                loadCurrentSettings()
                toastSuccess("تم إعادة تعيين إعدادات الأمان")
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showSecurityInfoDialog() {
        val stats = securityManager.getSecurityStats()
        val info = buildString {
            appendLine("معلومات الأمان:")
            appendLine()
            stats.forEach { (key, value) ->
                appendLine("$key: $value")
            }
        }
        
        AlertDialog.Builder(this)
            .setTitle("معلومات الأمان")
            .setMessage(info)
            .setPositiveButton("موافق", null)
            .show()
    }
    
    private fun handleAuthenticationRequired() {
        // This would typically show an authentication screen
        // For now, we'll just log it
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("SecuritySettings", "Authentication required")
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Check security status when activity resumes
        securityManager.checkLockStatus()
        updateSecurityStats()
    }
}
