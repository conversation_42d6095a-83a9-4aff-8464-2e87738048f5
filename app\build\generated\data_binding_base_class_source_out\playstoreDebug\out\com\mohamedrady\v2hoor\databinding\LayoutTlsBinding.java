// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutTlsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText etPublicKey;

  @NonNull
  public final EditText etShortId;

  @NonNull
  public final EditText etSni;

  @NonNull
  public final EditText etSpiderX;

  @NonNull
  public final LinearLayout layAllowInsecure;

  @NonNull
  public final LinearLayout layPublicKey;

  @NonNull
  public final LinearLayout layShortId;

  @NonNull
  public final LinearLayout laySni;

  @NonNull
  public final LinearLayout laySpiderX;

  @NonNull
  public final LinearLayout layStreamAlpn;

  @NonNull
  public final LinearLayout layStreamFingerprint;

  @NonNull
  public final LinearLayout layStreamSecurity;

  @NonNull
  public final Spinner spAllowInsecure;

  @NonNull
  public final Spinner spStreamAlpn;

  @NonNull
  public final Spinner spStreamFingerprint;

  @NonNull
  public final Spinner spStreamSecurity;

  private LayoutTlsBinding(@NonNull LinearLayout rootView, @NonNull EditText etPublicKey,
      @NonNull EditText etShortId, @NonNull EditText etSni, @NonNull EditText etSpiderX,
      @NonNull LinearLayout layAllowInsecure, @NonNull LinearLayout layPublicKey,
      @NonNull LinearLayout layShortId, @NonNull LinearLayout laySni,
      @NonNull LinearLayout laySpiderX, @NonNull LinearLayout layStreamAlpn,
      @NonNull LinearLayout layStreamFingerprint, @NonNull LinearLayout layStreamSecurity,
      @NonNull Spinner spAllowInsecure, @NonNull Spinner spStreamAlpn,
      @NonNull Spinner spStreamFingerprint, @NonNull Spinner spStreamSecurity) {
    this.rootView = rootView;
    this.etPublicKey = etPublicKey;
    this.etShortId = etShortId;
    this.etSni = etSni;
    this.etSpiderX = etSpiderX;
    this.layAllowInsecure = layAllowInsecure;
    this.layPublicKey = layPublicKey;
    this.layShortId = layShortId;
    this.laySni = laySni;
    this.laySpiderX = laySpiderX;
    this.layStreamAlpn = layStreamAlpn;
    this.layStreamFingerprint = layStreamFingerprint;
    this.layStreamSecurity = layStreamSecurity;
    this.spAllowInsecure = spAllowInsecure;
    this.spStreamAlpn = spStreamAlpn;
    this.spStreamFingerprint = spStreamFingerprint;
    this.spStreamSecurity = spStreamSecurity;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutTlsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutTlsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_tls, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutTlsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_public_key;
      EditText etPublicKey = ViewBindings.findChildViewById(rootView, id);
      if (etPublicKey == null) {
        break missingId;
      }

      id = R.id.et_short_id;
      EditText etShortId = ViewBindings.findChildViewById(rootView, id);
      if (etShortId == null) {
        break missingId;
      }

      id = R.id.et_sni;
      EditText etSni = ViewBindings.findChildViewById(rootView, id);
      if (etSni == null) {
        break missingId;
      }

      id = R.id.et_spider_x;
      EditText etSpiderX = ViewBindings.findChildViewById(rootView, id);
      if (etSpiderX == null) {
        break missingId;
      }

      id = R.id.lay_allow_insecure;
      LinearLayout layAllowInsecure = ViewBindings.findChildViewById(rootView, id);
      if (layAllowInsecure == null) {
        break missingId;
      }

      id = R.id.lay_public_key;
      LinearLayout layPublicKey = ViewBindings.findChildViewById(rootView, id);
      if (layPublicKey == null) {
        break missingId;
      }

      id = R.id.lay_short_id;
      LinearLayout layShortId = ViewBindings.findChildViewById(rootView, id);
      if (layShortId == null) {
        break missingId;
      }

      id = R.id.lay_sni;
      LinearLayout laySni = ViewBindings.findChildViewById(rootView, id);
      if (laySni == null) {
        break missingId;
      }

      id = R.id.lay_spider_x;
      LinearLayout laySpiderX = ViewBindings.findChildViewById(rootView, id);
      if (laySpiderX == null) {
        break missingId;
      }

      id = R.id.lay_stream_alpn;
      LinearLayout layStreamAlpn = ViewBindings.findChildViewById(rootView, id);
      if (layStreamAlpn == null) {
        break missingId;
      }

      id = R.id.lay_stream_fingerprint;
      LinearLayout layStreamFingerprint = ViewBindings.findChildViewById(rootView, id);
      if (layStreamFingerprint == null) {
        break missingId;
      }

      id = R.id.lay_stream_security;
      LinearLayout layStreamSecurity = ViewBindings.findChildViewById(rootView, id);
      if (layStreamSecurity == null) {
        break missingId;
      }

      id = R.id.sp_allow_insecure;
      Spinner spAllowInsecure = ViewBindings.findChildViewById(rootView, id);
      if (spAllowInsecure == null) {
        break missingId;
      }

      id = R.id.sp_stream_alpn;
      Spinner spStreamAlpn = ViewBindings.findChildViewById(rootView, id);
      if (spStreamAlpn == null) {
        break missingId;
      }

      id = R.id.sp_stream_fingerprint;
      Spinner spStreamFingerprint = ViewBindings.findChildViewById(rootView, id);
      if (spStreamFingerprint == null) {
        break missingId;
      }

      id = R.id.sp_stream_security;
      Spinner spStreamSecurity = ViewBindings.findChildViewById(rootView, id);
      if (spStreamSecurity == null) {
        break missingId;
      }

      return new LayoutTlsBinding((LinearLayout) rootView, etPublicKey, etShortId, etSni, etSpiderX,
          layAllowInsecure, layPublicKey, layShortId, laySni, laySpiderX, layStreamAlpn,
          layStreamFingerprint, layStreamSecurity, spAllowInsecure, spStreamAlpn,
          spStreamFingerprint, spStreamSecurity);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
