<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/view_height_dp160"
    android:background="@drawable/nav_header_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/padding_spacing_dp16">

    <!-- User Avatar -->
    <ImageView
        android:id="@+id/iv_user_avatar"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/user_avatar_background"
        android:padding="8dp"
        android:src="@drawable/ic_user_default"
        android:contentDescription="@string/user_avatar" />

    <!-- App Name -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:fontFamily="@font/montserrat_thin"
        android:gravity="center"
        android:text="@string/app_name"
        android:textAppearance="@style/TextAppearance.AppCompat.Display1" />

    <!-- User Name -->
    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center"
        android:text="@string/guest_user"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/white" />

    <!-- User Email -->
    <TextView
        android:id="@+id/tv_user_email"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:text=""
        android:textSize="12sp"
        android:textColor="@android:color/white"
        android:alpha="0.8" />

</LinearLayout>
