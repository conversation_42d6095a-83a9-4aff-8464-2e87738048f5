<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_admin_users" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_admin_users.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_admin_users_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="266" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="21" endOffset="54"/></Target><Target id="@+id/textViewTotalUsers" view="TextView"><Expressions/><location startLine="75" startOffset="28" endLine="82" endOffset="58"/></Target><Target id="@+id/textViewActiveUsers" view="TextView"><Expressions/><location startLine="99" startOffset="28" endLine="106" endOffset="58"/></Target><Target id="@+id/textViewInactiveUsers" view="TextView"><Expressions/><location startLine="123" startOffset="28" endLine="130" endOffset="58"/></Target><Target id="@+id/textViewPremiumUsers" view="TextView"><Expressions/><location startLine="147" startOffset="28" endLine="152" endOffset="58"/></Target><Target id="@+id/editTextSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="195" startOffset="24" endLine="201" endOffset="75"/></Target><Target id="@+id/textViewUsersCount" view="TextView"><Expressions/><location startLine="211" startOffset="24" endLine="218" endOffset="53"/></Target><Target id="@+id/buttonRefresh" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="220" startOffset="24" endLine="228" endOffset="52"/></Target><Target id="@+id/recyclerViewUsers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="237" startOffset="12" endLine="242" endOffset="58"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="249" startOffset="4" endLine="254" endOffset="35"/></Target><Target id="@+id/fabAddUser" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="257" startOffset="4" endLine="264" endOffset="33"/></Target></Targets></Layout>