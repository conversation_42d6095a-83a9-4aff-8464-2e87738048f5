<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- Timestamp -->
    <TextView
        android:id="@+id/textViewTimestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="12-25 10:30:45"
        android:textSize="10sp"
        android:fontFamily="monospace"
        android:textColor="@color/text_secondary"
        android:layout_marginEnd="8dp"
        android:minWidth="80dp" />

    <!-- Log Level -->
    <TextView
        android:id="@+id/textViewLevel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="I"
        android:textSize="10sp"
        android:fontFamily="monospace"
        android:textStyle="bold"
        android:padding="2dp"
        android:layout_marginEnd="8dp"
        android:minWidth="24dp"
        android:gravity="center" />

    <!-- Tag -->
    <TextView
        android:id="@+id/textViewTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="V2HoorVPN"
        android:textSize="10sp"
        android:fontFamily="monospace"
        android:textStyle="bold"
        android:textColor="@color/primary_color"
        android:layout_marginEnd="8dp"
        android:maxWidth="100dp"
        android:ellipsize="end"
        android:singleLine="true" />

    <!-- Message -->
    <TextView
        android:id="@+id/textViewMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Log message here"
        android:textSize="11sp"
        android:fontFamily="monospace"
        android:textColor="@color/text_primary"
        android:maxLines="2"
        android:ellipsize="end" />

</LinearLayout>
