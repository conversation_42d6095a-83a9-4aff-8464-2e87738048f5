<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".ui.PerAppProxyActivity">

    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/pb_waiting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="invisible"
        app:indicatorColor="@color/color_fab_active" />

    <LinearLayout
        android:id="@+id/header_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_spacing_dp16">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/container_per_app_proxy"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_per_app_proxy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="2"
                        android:text="@string/per_app_proxy_settings_enable"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/colorAccent"
                        app:theme="@style/BrandedSwitch" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_bypass_apps"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_bypass_apps"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/switch_bypass_apps_mode"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/colorAccent"
                        app:theme="@style/BrandedSwitch" />

                    <LinearLayout
                        android:id="@+id/layout_switch_bypass_apps_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:padding="@dimen/padding_spacing_dp8">

                        <ImageView
                            android:layout_width="@dimen/image_size_dp24"
                            android:layout_height="@dimen/image_size_dp24"
                            app:srcCompat="@drawable/ic_about_24dp" />

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>


        </LinearLayout>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager" />

</LinearLayout>