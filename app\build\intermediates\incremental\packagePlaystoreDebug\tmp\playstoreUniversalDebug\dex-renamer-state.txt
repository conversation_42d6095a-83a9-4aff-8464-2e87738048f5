#Thu Jul 10 00:08:18 EEST 2025
base.0=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeExtDexPlaystoreDebug\\classes.dex
base.1=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\0\\classes.dex
base.10=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\desugar_lib_dex\\playstoreDebug\\l8DexDesugarLibPlaystoreDebug\\classes1000.dex
base.11=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeExtDexPlaystoreDebug\\classes2.dex
base.12=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeExtDexPlaystoreDebug\\classes3.dex
base.2=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\10\\classes.dex
base.3=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\11\\classes.dex
base.4=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\12\\classes.dex
base.5=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\13\\classes.dex
base.6=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\3\\classes.dex
base.7=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\4\\classes.dex
base.8=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\7\\classes.dex
base.9=D\:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\intermediates\\dex\\playstoreDebug\\mergeProjectDexPlaystoreDebug\\8\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.10=classes1000.dex
path.11=classes2.dex
path.12=classes3.dex
path.2=10/classes.dex
path.3=11/classes.dex
path.4=12/classes.dex
path.5=13/classes.dex
path.6=3/classes.dex
path.7=4/classes.dex
path.8=7/classes.dex
path.9=8/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
