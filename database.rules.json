{"rules": {"users": {"$uid": {".read": "auth != null && (auth.uid == $uid || auth.token.email == '<EMAIL>' || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && (auth.uid == $uid || auth.token.email == '<EMAIL>' || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".validate": "newData.hasChildren(['uid', 'email', 'name', 'role'])", "uid": {".validate": "newData.isString() && newData.val() == $uid"}, "email": {".validate": "newData.isString() && newData.val().length > 0"}, "name": {".validate": "newData.isString() && newData.val().length > 0"}, "role": {".validate": "newData.isString() && (newData.val() == 'admin' || newData.val() == 'user')"}, "subscription_end": {".validate": "newData.isString()"}, "is_active": {".validate": "newData.isBoolean()"}, "created_at": {".validate": "newData.isNumber() && newData.val() > 0"}, "updated_at": {".validate": "newData.isNumber() && newData.val() > 0"}, "last_login": {".validate": "newData.isNumber() && newData.val() >= 0"}, "subscription_type": {".validate": "newData.isString()"}, "max_devices": {".validate": "newData.isNumber() && newData.val() > 0"}, "data_limit_gb": {".validate": "newData.isNumber() && newData.val() >= 0"}, "data_used_gb": {".validate": "newData.isNumber() && newData.val() >= 0"}, "notes": {".validate": "newData.isString()"}, "created_by": {".validate": "newData.isString()"}, "avatar_url": {".validate": "newData.isString()"}, "servers": {"$serverId": {".validate": "newData.hasChildren(['id', 'name', 'server', 'server_port', 'protocol', 'uuid']) && newData.child('user_id').val() == auth.uid", "id": {".validate": "newData.isString() && newData.val().length > 0"}, "name": {".validate": "newData.isString() && newData.val().length > 0"}, "remarks": {".validate": "newData.isString()"}, "server": {".validate": "newData.isString() && newData.val().length > 0"}, "server_port": {".validate": "newData.isNumber() && newData.val() > 0 && newData.val() <= 65535"}, "protocol": {".validate": "newData.isString() && (newData.val() == 'vmess' || newData.val() == 'vless' || newData.val() == 'trojan' || newData.val() == 'shadowsocks' || newData.val() == 'socks' || newData.val() == 'http')"}, "uuid": {".validate": "newData.isString() && newData.val().length > 0"}, "alter_id": {".validate": "newData.isNumber() && newData.val() >= 0"}, "security": {".validate": "newData.isString()"}, "network": {".validate": "newData.isString()"}, "header_type": {".validate": "newData.isString()"}, "request_host": {".validate": "newData.isString()"}, "path": {".validate": "newData.isString()"}, "tls": {".validate": "newData.isString()"}, "sni": {".validate": "newData.isString()"}, "alpn": {".validate": "newData.isString()"}, "fingerprint": {".validate": "newData.isString()"}, "public_key": {".validate": "newData.isString()"}, "short_id": {".validate": "newData.isString()"}, "spider_x": {".validate": "newData.isString()"}, "flow": {".validate": "newData.isString()"}, "encryption": {".validate": "newData.isString()"}, "country": {".validate": "newData.isString()"}, "city": {".validate": "newData.isString()"}, "flag": {".validate": "newData.isString()"}, "is_active": {".validate": "newData.isBoolean()"}, "priority": {".validate": "newData.isNumber() && newData.val() >= 0"}, "created_at": {".validate": "newData.isNumber() && newData.val() > 0"}, "updated_at": {".validate": "newData.isNumber() && newData.val() > 0"}, "expires_at": {".validate": "newData.isNumber() && newData.val() >= 0"}, "valid_until": {".validate": "newData.isNumber() && newData.val() >= 0"}, "last_sync": {".validate": "newData.isNumber() && newData.val() >= 0"}, "config_version": {".validate": "newData.isNumber() && newData.val() > 0"}, "subscription_id": {".validate": "newData.isString()"}, "user_id": {".validate": "newData.isString() && newData.val() == auth.uid"}, "tags": {".validate": "newData.hasChildren() || newData.val() == null", "$tag": {".validate": "newData.isString()"}}, "custom_config": {".validate": "newData.isString()"}, "test_result": {"ping": {".validate": "newData.isNumber()"}, "download_speed": {".validate": "newData.isNumber() && newData.val() >= 0"}, "upload_speed": {".validate": "newData.isNumber() && newData.val() >= 0"}, "last_test": {".validate": "newData.isNumber() && newData.val() >= 0"}, "is_online": {".validate": "newData.isBoolean()"}, "error_message": {".validate": "newData.isString()"}}}}}}, "admin": {".read": "auth != null && (auth.token.email == '<EMAIL>' || auth.token.admin == true)", ".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.token.admin == true)", "users": {"$uid": {"servers": {"$serverId": {".validate": "newData.hasChildren(['id', 'name', 'server', 'server_port', 'protocol', 'uuid'])"}}}}, "global_servers": {"$serverId": {".validate": "newData.hasChildren(['id', 'name', 'server', 'server_port', 'protocol', 'uuid'])"}}, "server_assignments": {"$uid": {"$serverId": {".validate": "newData.isBoolean()"}}}}, ".read": false, ".write": false}}