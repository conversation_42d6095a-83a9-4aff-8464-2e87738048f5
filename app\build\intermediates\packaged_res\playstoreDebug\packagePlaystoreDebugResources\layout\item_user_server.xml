<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Server Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewServerName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="سيرفر أمريكا" />

                <TextView
                    android:id="@+id/textViewServerLocation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="نيويورك, الولايات المتحدة" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:id="@+id/textViewServerStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_status_badge"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:text="نشط"
                    android:textColor="@color/green"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewFullIndicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/bg_role_badge"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:text="ممتلئ"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <!-- Server Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewServerType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/blue"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="VMess" />

                <TextView
                    android:id="@+id/textViewServerPriority"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="الأولوية: 5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewServerUsage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="الاستخدام: 45/100 (45%)" />

                <ProgressBar
                    android:id="@+id/progressBarUsage"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="4dp"
                    android:max="100"
                    android:progress="45"
                    android:progressTint="@color/green" />

            </LinearLayout>

        </LinearLayout>

        <!-- Creation Date -->
        <TextView
            android:id="@+id/textViewCreationDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="11sp"
            tools:text="تم الإنشاء: 15/01/2024 14:30" />

        <!-- Action Button -->
        <Button
            android:id="@+id/buttonRemoveServer"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_gravity="end"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_button_danger"
            android:text="إزالة"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
