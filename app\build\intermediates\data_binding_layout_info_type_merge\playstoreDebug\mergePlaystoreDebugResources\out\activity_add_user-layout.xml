<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_user" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_add_user.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_add_user_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="253" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/editTextEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="70" startOffset="24" endLine="74" endOffset="66"/></Target><Target id="@+id/editTextDisplayName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="86" startOffset="24" endLine="90" endOffset="64"/></Target><Target id="@+id/editTextPhoneNumber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="102" startOffset="24" endLine="106" endOffset="55"/></Target><Target id="@+id/editTextCountry" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="124" startOffset="28" endLine="128" endOffset="58"/></Target><Target id="@+id/editTextCity" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="141" startOffset="28" endLine="145" endOffset="58"/></Target><Target id="@+id/radioGroupSubscription" view="RadioGroup"><Expressions/><location startLine="183" startOffset="20" endLine="207" endOffset="32"/></Target><Target id="@+id/radioButtonFree" view="RadioButton"><Expressions/><location startLine="189" startOffset="24" endLine="196" endOffset="53"/></Target><Target id="@+id/radioButtonPremium" view="RadioButton"><Expressions/><location startLine="198" startOffset="24" endLine="205" endOffset="53"/></Target><Target id="@+id/buttonCancel" view="Button"><Expressions/><location startLine="220" startOffset="16" endLine="228" endOffset="45"/></Target><Target id="@+id/buttonSave" view="Button"><Expressions/><location startLine="230" startOffset="16" endLine="237" endOffset="45"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="246" startOffset="4" endLine="251" endOffset="35"/></Target></Targets></Layout>