{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergePlaystoreDebugResources-63:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1586d6a1fa7e7bf9ffc9892b585ca703\\transformed\\material-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1143,1208,1307,1383,1448,1538,1602,1668,1722,1791,1851,1905,2022,2082,2144,2198,2270,2400,2487,2567,2663,2747,2839,2978,3047,3125,3256,3344,3424,3478,3529,3595,3667,3744,3815,3897,3969,4046,4119,4190,4295,4383,4455,4547,4643,4717,4791,4887,4939,5021,5088,5175,5262,5324,5388,5451,5519,5625,5732,5830,5947,6005,6060,6139,6222,6297", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "373,448,523,601,693,776,868,996,1077,1138,1203,1302,1378,1443,1533,1597,1663,1717,1786,1846,1900,2017,2077,2139,2193,2265,2395,2482,2562,2658,2742,2834,2973,3042,3120,3251,3339,3419,3473,3524,3590,3662,3739,3810,3892,3964,4041,4114,4185,4290,4378,4450,4542,4638,4712,4786,4882,4934,5016,5083,5170,5257,5319,5383,5446,5514,5620,5727,5825,5942,6000,6055,6134,6217,6292,6368"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,52,53,55,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3140,3215,3290,3368,3460,4280,4372,4500,4761,4822,4975,5394,5470,5535,5625,5689,5755,5809,5878,5938,5992,6109,6169,6231,6285,6357,6487,6574,6654,6750,6834,6926,7065,7134,7212,7343,7431,7511,7565,7616,7682,7754,7831,7902,7984,8056,8133,8206,8277,8382,8470,8542,8634,8730,8804,8878,8974,9026,9108,9175,9262,9349,9411,9475,9538,9606,9712,9819,9917,10034,10092,10225,10663,10746,10821", "endLines": "7,35,36,37,38,39,47,48,49,52,53,55,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,125,126,127", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "423,3210,3285,3363,3455,3538,4367,4495,4576,4817,4882,5069,5465,5530,5620,5684,5750,5804,5873,5933,5987,6104,6164,6226,6280,6352,6482,6569,6649,6745,6829,6921,7060,7129,7207,7338,7426,7506,7560,7611,7677,7749,7826,7897,7979,8051,8128,8201,8272,8377,8465,8537,8629,8725,8799,8873,8969,9021,9103,9170,9257,9344,9406,9470,9533,9601,9707,9814,9912,10029,10087,10142,10299,10741,10816,10892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6168aafd8aa1313de7d53dbb6f8061c\\transformed\\appcompat-1.7.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,10580", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,10658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\addfe77571c545920b761f990220962d\\transformed\\core-1.16.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3741,3842,3940,4050,4158,10897", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3634,3736,3837,3935,4045,4153,4275,10993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\de0c5edbd8703928d695edd0a578b20c\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4654,5074,5177,5292", "endColumns": "106,102,114,101", "endOffsets": "4756,5172,5287,5389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\082507c0f8acc2f43fb9efc345edc657\\transformed\\preference-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,491,660,744", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "173,261,339,486,655,739,820"}, "to": {"startLines": "50,54,119,121,129,130,131", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4581,4887,10147,10304,10998,11167,11251", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "4649,4970,10220,10446,11162,11246,11327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\194b7da0db63fea2a64463e6a0ef5556\\transformed\\zxing-android-embedded-4.3.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,315", "endColumns": "58,46,153,120", "endOffsets": "109,156,310,431"}, "to": {"startLines": "132,133,134,135", "startColumns": "4,4,4,4", "startOffsets": "11332,11391,11438,11592", "endColumns": "58,46,153,120", "endOffsets": "11386,11433,11587,11708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5e6d251ef110edeab2647bcfbb802f94\\transformed\\quickie-foss-1.14.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endColumns": "58,69", "endOffsets": "109,179"}, "to": {"startLines": "122,123", "startColumns": "4,4", "startOffsets": "10451,10510", "endColumns": "58,69", "endOffsets": "10505,10575"}}]}]}