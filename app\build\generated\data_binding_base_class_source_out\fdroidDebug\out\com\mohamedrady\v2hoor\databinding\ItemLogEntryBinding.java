// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLogEntryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView textViewLevel;

  @NonNull
  public final TextView textViewMessage;

  @NonNull
  public final TextView textViewTag;

  @NonNull
  public final TextView textViewTimestamp;

  private ItemLogEntryBinding(@NonNull LinearLayout rootView, @NonNull TextView textViewLevel,
      @NonNull TextView textViewMessage, @NonNull TextView textViewTag,
      @NonNull TextView textViewTimestamp) {
    this.rootView = rootView;
    this.textViewLevel = textViewLevel;
    this.textViewMessage = textViewMessage;
    this.textViewTag = textViewTag;
    this.textViewTimestamp = textViewTimestamp;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLogEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLogEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_log_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLogEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.textViewLevel;
      TextView textViewLevel = ViewBindings.findChildViewById(rootView, id);
      if (textViewLevel == null) {
        break missingId;
      }

      id = R.id.textViewMessage;
      TextView textViewMessage = ViewBindings.findChildViewById(rootView, id);
      if (textViewMessage == null) {
        break missingId;
      }

      id = R.id.textViewTag;
      TextView textViewTag = ViewBindings.findChildViewById(rootView, id);
      if (textViewTag == null) {
        break missingId;
      }

      id = R.id.textViewTimestamp;
      TextView textViewTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (textViewTimestamp == null) {
        break missingId;
      }

      return new ItemLogEntryBinding((LinearLayout) rootView, textViewLevel, textViewMessage,
          textViewTag, textViewTimestamp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
