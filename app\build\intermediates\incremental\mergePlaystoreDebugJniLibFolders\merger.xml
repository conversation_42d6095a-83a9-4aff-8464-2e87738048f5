<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\main\jniLibs"/><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs"><file name="arm64-v8a/libhysteria2.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\arm64-v8a\libhysteria2.so"/><file name="arm64-v8a/libpdnsd.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\arm64-v8a\libpdnsd.so"/><file name="arm64-v8a/libproxychains4.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\arm64-v8a\libproxychains4.so"/><file name="arm64-v8a/libssr-local.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\arm64-v8a\libssr-local.so"/><file name="arm64-v8a/libtun2socks.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\arm64-v8a\libtun2socks.so"/><file name="armeabi-v7a/libhysteria2.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\armeabi-v7a\libhysteria2.so"/><file name="armeabi-v7a/libpdnsd.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\armeabi-v7a\libpdnsd.so"/><file name="armeabi-v7a/libproxychains4.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\armeabi-v7a\libproxychains4.so"/><file name="armeabi-v7a/libssr-local.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\armeabi-v7a\libssr-local.so"/><file name="armeabi-v7a/libtun2socks.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\armeabi-v7a\libtun2socks.so"/><file name="libv2ray.aar" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\libv2ray.aar"/><file name="x86/libhysteria2.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86\libhysteria2.so"/><file name="x86/libpdnsd.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86\libpdnsd.so"/><file name="x86/libproxychains4.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86\libproxychains4.so"/><file name="x86/libssr-local.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86\libssr-local.so"/><file name="x86/libtun2socks.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86\libtun2socks.so"/><file name="x86_64/libhysteria2.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86_64\libhysteria2.so"/><file name="x86_64/libpdnsd.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86_64\libpdnsd.so"/><file name="x86_64/libproxychains4.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86_64\libproxychains4.so"/><file name="x86_64/libssr-local.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86_64\libssr-local.so"/><file name="x86_64/libtun2socks.so" path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\libs\x86_64\libtun2socks.so"/></source></dataSet><dataSet config="playstore" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstore\jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\debug\jniLibs"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\work\v2rayNG-1.10.7\V2HoorVPN\app\src\playstoreDebug\jniLibs"/></dataSet></merger>