// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityUserAssetUrlBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText etRemarks;

  @NonNull
  public final EditText etUrl;

  private ActivityUserAssetUrlBinding(@NonNull ScrollView rootView, @NonNull EditText etRemarks,
      @NonNull EditText etUrl) {
    this.rootView = rootView;
    this.etRemarks = etRemarks;
    this.etUrl = etUrl;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityUserAssetUrlBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityUserAssetUrlBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_user_asset_url, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityUserAssetUrlBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_remarks;
      EditText etRemarks = ViewBindings.findChildViewById(rootView, id);
      if (etRemarks == null) {
        break missingId;
      }

      id = R.id.et_url;
      EditText etUrl = ViewBindings.findChildViewById(rootView, id);
      if (etUrl == null) {
        break missingId;
      }

      return new ActivityUserAssetUrlBinding((ScrollView) rootView, etRemarks, etUrl);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
