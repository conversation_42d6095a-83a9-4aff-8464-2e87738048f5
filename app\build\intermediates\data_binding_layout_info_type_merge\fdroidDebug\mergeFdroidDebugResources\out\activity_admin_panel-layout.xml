<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_admin_panel" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_admin_panel.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_admin_panel_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="461" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="59"/></Target><Target id="@+id/card_admin_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="35" startOffset="12" endLine="85" endOffset="63"/></Target><Target id="@+id/tv_admin_email" view="TextView"><Expressions/><location startLine="57" startOffset="20" endLine="63" endOffset="49"/></Target><Target id="@+id/tv_admin_level" view="TextView"><Expressions/><location startLine="65" startOffset="20" endLine="73" endOffset="50"/></Target><Target id="@+id/tv_permissions_count" view="TextView"><Expressions/><location startLine="75" startOffset="20" endLine="81" endOffset="49"/></Target><Target id="@+id/card_server_management" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="104" startOffset="16" endLine="145" endOffset="67"/></Target><Target id="@+id/card_add_server" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="148" startOffset="16" endLine="189" endOffset="67"/></Target><Target id="@+id/card_server_update" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="192" startOffset="16" endLine="249" endOffset="67"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="229" startOffset="28" endLine="234" endOffset="59"/></Target><Target id="@+id/tv_update_status" view="TextView"><Expressions/><location startLine="238" startOffset="24" endLine="245" endOffset="53"/></Target><Target id="@+id/card_server_logs" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="252" startOffset="16" endLine="293" endOffset="67"/></Target><Target id="@+id/card_user_management" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="314" startOffset="16" endLine="355" endOffset="67"/></Target><Target id="@+id/card_promote_user" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="358" startOffset="16" endLine="399" endOffset="67"/></Target><Target id="@+id/card_system_settings" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="414" startOffset="12" endLine="455" endOffset="63"/></Target></Targets></Layout>