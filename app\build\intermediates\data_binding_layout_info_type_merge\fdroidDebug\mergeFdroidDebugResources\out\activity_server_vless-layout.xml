<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_server_vless" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_server_vless.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_server_vless_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="86" endOffset="18"/></Target><Target tag="binding_1" include="layout_address_port"><Expressions/><location startLine="14" startOffset="8" endLine="14" endOffset="55"/></Target><Target tag="binding_1" include="layout_transport"><Expressions/><location startLine="75" startOffset="8" endLine="75" endOffset="52"/></Target><Target tag="binding_1" include="layout_tls"><Expressions/><location startLine="77" startOffset="8" endLine="77" endOffset="46"/></Target><Target id="@+id/et_id" view="EditText"><Expressions/><location startLine="27" startOffset="12" endLine="31" endOffset="42"/></Target><Target id="@+id/sp_flow" view="Spinner"><Expressions/><location startLine="46" startOffset="12" endLine="52" endOffset="48"/></Target><Target id="@+id/et_security" view="EditText"><Expressions/><location startLine="66" startOffset="12" endLine="71" endOffset="37"/></Target></Targets></Layout>