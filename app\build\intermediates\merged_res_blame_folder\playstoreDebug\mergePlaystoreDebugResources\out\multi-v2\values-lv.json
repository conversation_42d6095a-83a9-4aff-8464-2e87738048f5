{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergePlaystoreDebugResources-63:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\addfe77571c545920b761f990220962d\\transformed\\core-1.16.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3704,3802,3904,4004,4105,4212,4320,11149", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3797,3899,3999,4100,4207,4315,4430,11245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6168aafd8aa1313de7d53dbb6f8061c\\transformed\\appcompat-1.7.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,10829", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,10907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1586d6a1fa7e7bf9ffc9892b585ca703\\transformed\\material-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1149,1214,1308,1381,1442,1567,1633,1701,1762,1834,1894,1948,2068,2128,2190,2244,2321,2451,2538,2615,2705,2788,2870,3011,3091,3176,3303,3394,3470,3524,3577,3643,3717,3798,3869,3949,4022,4099,4176,4250,4360,4453,4528,4618,4709,4781,4859,4950,5004,5087,5155,5239,5326,5388,5452,5515,5587,5697,5810,5913,6022,6080,6137,6214,6299,6377", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1144,1209,1303,1376,1437,1562,1628,1696,1757,1829,1889,1943,2063,2123,2185,2239,2316,2446,2533,2610,2700,2783,2865,3006,3086,3171,3298,3389,3465,3519,3572,3638,3712,3793,3864,3944,4017,4094,4171,4245,4355,4448,4523,4613,4704,4776,4854,4945,4999,5082,5150,5234,5321,5383,5447,5510,5582,5692,5805,5908,6017,6075,6132,6209,6294,6372,6446"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,51,52,54,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,4435,4536,4670,4925,4986,5149,5560,5633,5694,5819,5885,5953,6014,6086,6146,6200,6320,6380,6442,6496,6573,6703,6790,6867,6957,7040,7122,7263,7343,7428,7555,7646,7722,7776,7829,7895,7969,8050,8121,8201,8274,8351,8428,8502,8612,8705,8780,8870,8961,9033,9111,9202,9256,9339,9407,9491,9578,9640,9704,9767,9839,9949,10062,10165,10274,10332,10469,10912,10997,11075", "endLines": "6,34,35,36,37,38,46,47,48,51,52,54,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,119,124,125,126", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "369,3340,3425,3506,3611,3699,4531,4665,4748,4981,5046,5238,5628,5689,5814,5880,5948,6009,6081,6141,6195,6315,6375,6437,6491,6568,6698,6785,6862,6952,7035,7117,7258,7338,7423,7550,7641,7717,7771,7824,7890,7964,8045,8116,8196,8269,8346,8423,8497,8607,8700,8775,8865,8956,9028,9106,9197,9251,9334,9402,9486,9573,9635,9699,9762,9834,9944,10057,10160,10269,10327,10384,10541,10992,11070,11144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\082507c0f8acc2f43fb9efc345edc657\\transformed\\preference-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,272,352,498,667,752", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "169,267,347,493,662,747,829"}, "to": {"startLines": "49,53,118,120,128,129,130", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4753,5051,10389,10546,11250,11419,11504", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "4817,5144,10464,10687,11414,11499,11581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5e6d251ef110edeab2647bcfbb802f94\\transformed\\quickie-foss-1.14.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,123", "endColumns": "67,68", "endOffsets": "118,187"}, "to": {"startLines": "121,122", "startColumns": "4,4", "startOffsets": "10692,10760", "endColumns": "67,68", "endOffsets": "10755,10824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\de0c5edbd8703928d695edd0a578b20c\\transformed\\browser-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "50,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4822,5243,5342,5458", "endColumns": "102,98,115,101", "endOffsets": "4920,5337,5453,5555"}}]}]}