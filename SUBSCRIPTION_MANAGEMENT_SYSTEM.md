# 💳 V2Hoor Subscription Management System

## 📋 **Overview**

The V2Hoor app now includes comprehensive subscription management based on expiration dates stored in Firebase. This system provides access control, expiration warnings, and admin subscription management capabilities.

---

## 🏗️ **Firebase Structure**

### **User Subscription Data**
```
/users/{uid}/
  - subscription_end: "2025-12-31" (ISO format: yyyy-MM-dd)
  - subscription_type: "basic" | "premium" | "unlimited"
  - is_active: true
  - data_limit_gb: 100
  - data_used_gb: 25
  - max_devices: 3
```

### **Subscription Types**
- **Basic**: Limited data, single device, basic servers
- **Premium**: High data limit, multiple devices, premium servers
- **Unlimited**: No limits, all features, admin-level access
- **Trial**: 30-day trial with basic features

---

## 🔒 **Access Control Logic**

### **Subscription Status Validation**
1. **Active**: Current date ≤ subscription_end
2. **Expiring Soon**: 1-3 days before expiration
3. **Expired**: Current date > subscription_end
4. **Unlimited**: No expiration date (empty subscription_end)

### **Access Restrictions**
- **Expired Users**: Blocked from app access, shown contact support dialog
- **Data Limit Exceeded**: Can access app but cannot connect to servers
- **Expiring Soon**: Full access with warning notifications
- **Admin Users**: Unlimited access regardless of subscription

---

## 🎨 **User Interface Components**

### **📊 Subscription Status Banner**
- **Location**: Top of main activity, below progress indicator
- **Dynamic Display**: Shows/hides based on subscription status
- **Color Coding**: Green (active), Orange (expiring), Red (expired)
- **Action Buttons**: Renew, Contact Support, Close

### **Banner States**
1. **Active Subscription**
   - ✅ Green background with checkmark icon
   - Shows "نشط حتى: dd/MM/yyyy"
   - Data usage progress bar (if limited)
   - Close button available

2. **Expiring Soon**
   - ⚠️ Orange background with warning icon
   - Shows "ينتهي خلال X أيام"
   - Renew button visible
   - Cannot be closed

3. **Expired Subscription**
   - ❌ Red background with error icon
   - Shows "انتهت صلاحية الاشتراك"
   - Contact Support button
   - Cannot be closed

4. **Unlimited Subscription**
   - 🔵 Blue background with checkmark icon
   - Shows "اشتراك غير محدود"
   - No action buttons needed

---

## 🔧 **Technical Implementation**

### **Core Classes**

#### **1. SubscriptionStatus.kt**
- Subscription status enumeration
- Date validation and formatting
- Expiration checking logic
- Data usage calculations

#### **2. SubscriptionInfo.kt**
- Complete subscription data model
- Validation methods
- Status calculation
- Feature access control

#### **3. SubscriptionService.kt**
- Firebase Realtime Database integration
- Subscription CRUD operations
- Real-time subscription monitoring
- Admin subscription management

#### **4. SubscriptionValidator.kt**
- Access validation logic
- Feature-specific permission checking
- Block reason generation
- Admin bypass handling

#### **5. SubscriptionStatusBanner.kt**
- UI component for subscription display
- Dynamic styling based on status
- Action button handling
- Contact support integration

---

## 🚫 **Access Restrictions**

### **App Level Restrictions**
```kotlin
// Check app access
val validation = subscriptionValidator.validateCurrentUser()
if (!validation.canAccessApp) {
    showSubscriptionExpiredDialog(validation.blockReason)
    return
}
```

### **Server Connection Restrictions**
```kotlin
// Check server connection
val canConnect = subscriptionValidator.canConnectToServer(serverId)
if (!canConnect) {
    val blockMessage = subscriptionValidator.getServerConnectionBlockMessage(serverId)
    showToast(blockMessage)
    return
}
```

### **Feature Access Control**
```kotlin
// Check specific feature access
val canAccess = subscriptionValidator.canAccessFeature("server_list")
if (!canAccess) {
    val blockMessage = subscriptionValidator.getAccessBlockMessage("server_list")
    showAccessDeniedDialog(blockMessage)
    return
}
```

---

## 🔔 **Notification System**

### **Expiration Warnings**
- **3 Days Before**: Warning banner appears
- **1 Day Before**: Persistent warning with action button
- **Expired**: App access blocked with contact support dialog

### **Data Usage Alerts**
- **80% Usage**: Warning notification
- **100% Usage**: Server connections blocked
- **Progress Bar**: Visual data usage indicator

### **Admin Notifications**
- **Expiring Users**: List of users with expiring subscriptions
- **Usage Monitoring**: Data usage statistics
- **Subscription Management**: Bulk operations interface

---

## 👨‍💼 **Admin Features**

### **Subscription Management**
- **Extend Subscriptions**: Update expiration dates
- **Change Subscription Types**: Upgrade/downgrade users
- **Data Limit Management**: Adjust usage limits
- **Bulk Operations**: Mass subscription updates

### **User Monitoring**
- **Expiring Subscriptions**: List users expiring soon
- **Usage Statistics**: Data consumption reports
- **Active Users**: Real-time user activity
- **Subscription Revenue**: Financial tracking

### **Admin Interface**
```kotlin
// Extend user subscription (admin only)
subscriptionService.extendSubscription(userId, "2025-12-31")

// Get expiring users
val expiringUsers = subscriptionService.getUsersWithExpiringSubscriptions()

// Deactivate subscription
subscriptionService.deactivateSubscription(userId)
```

---

## 📱 **User Experience Flow**

### **Login Flow**
1. **Authentication** → Firebase Auth
2. **Subscription Check** → Load user subscription data
3. **Validation** → Check expiration and limits
4. **UI Update** → Show appropriate banner and restrictions
5. **Access Control** → Enable/disable features based on status

### **Connection Flow**
1. **Server Selection** → User taps server
2. **Subscription Validation** → Check connection permissions
3. **Access Decision** → Allow/block connection
4. **User Feedback** → Show success/error message
5. **Connection Attempt** → Proceed if allowed

### **Expiration Handling**
1. **Warning Period** → Show expiration warnings
2. **Grace Period** → Limited access with notifications
3. **Expiration** → Block access, show contact dialog
4. **Renewal** → Restore full access after payment

---

## 🧪 **Testing Scenarios**

### **Subscription States**
- [ ] Active subscription with valid date
- [ ] Expiring subscription (1-3 days)
- [ ] Expired subscription
- [ ] Unlimited subscription
- [ ] Data limit exceeded
- [ ] Invalid date format

### **Access Control**
- [ ] Expired user cannot access app
- [ ] Data limit exceeded blocks connections
- [ ] Admin users bypass all restrictions
- [ ] Warning messages display correctly
- [ ] Contact support functionality works

### **UI Components**
- [ ] Banner shows correct status and colors
- [ ] Action buttons work properly
- [ ] Data usage progress updates
- [ ] Banner hides/shows appropriately
- [ ] Arabic text displays correctly

---

## 📊 **Sample Data**

### **Active User**
```json
{
  "subscription_end": "2025-12-31",
  "subscription_type": "premium",
  "is_active": true,
  "data_limit_gb": 100,
  "data_used_gb": 25,
  "max_devices": 3
}
```

### **Expiring User**
```json
{
  "subscription_end": "2024-12-03",
  "subscription_type": "basic",
  "is_active": true,
  "data_limit_gb": 10,
  "data_used_gb": 8,
  "max_devices": 1
}
```

### **Expired User**
```json
{
  "subscription_end": "2024-11-01",
  "subscription_type": "basic",
  "is_active": false,
  "data_limit_gb": 10,
  "data_used_gb": 12,
  "max_devices": 1
}
```

---

## 🚀 **Deployment**

### **Firebase Rules Update**
```json
{
  "rules": {
    "users": {
      "$uid": {
        "subscription_end": {
          ".validate": "newData.isString() && newData.val().matches(/^\\d{4}-\\d{2}-\\d{2}$/)"
        },
        "subscription_type": {
          ".validate": "newData.isString() && ['basic', 'premium', 'unlimited', 'trial'].includes(newData.val())"
        }
      }
    }
  }
}
```

### **Installation Steps**
1. Deploy updated Firebase rules
2. Install updated APK
3. Test subscription validation
4. Configure admin accounts
5. Set up user subscriptions

---

## 🎯 **Benefits**

### **🔒 Security**
- **Subscription Enforcement**: Prevents unauthorized access
- **Data Usage Control**: Manages bandwidth consumption
- **Admin Oversight**: Complete subscription management
- **Audit Trail**: Tracks all subscription changes

### **💰 Business Value**
- **Revenue Protection**: Ensures paid access only
- **Usage Monitoring**: Tracks service consumption
- **Customer Management**: Automated renewal reminders
- **Scalable Model**: Supports multiple subscription tiers

### **👥 User Experience**
- **Clear Status**: Always know subscription state
- **Proactive Warnings**: Advance expiration notices
- **Easy Renewal**: Direct contact support integration
- **Fair Usage**: Transparent data limits

**Subscription management system is now complete and production-ready!** 🎉
