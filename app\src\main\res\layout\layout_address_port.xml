<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_server"
            android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_remarks" />

        <EditText
            android:id="@+id/et_remarks"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_address3" />

        <EditText
            android:id="@+id/et_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_port3" />

        <EditText
            android:id="@+id/et_port"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number" />
    </LinearLayout>

</LinearLayout>