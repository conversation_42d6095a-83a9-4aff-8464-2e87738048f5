package com.mohamedrady.v2hoor.service

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.SubscriptionInfo
import com.mohamedrady.v2hoor.model.SubscriptionStatus
import com.mohamedrady.v2hoor.model.UserProfile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Subscription Management Service
 * Handles subscription validation, expiration checking, and access control
 */
class SubscriptionService private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: SubscriptionService? = null
        
        fun getInstance(context: Context): SubscriptionService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SubscriptionService(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val USERS_PATH = "users"
        private const val DATE_FORMAT = "yyyy-MM-dd"
    }
    
    private val database: DatabaseReference = FirebaseDatabase.getInstance().reference
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val userRoleService = UserRoleService.getInstance(context)
    
    // LiveData for subscription status
    private val _currentSubscription = MutableLiveData<SubscriptionInfo?>()
    val currentSubscription: LiveData<SubscriptionInfo?> = _currentSubscription
    
    private val _subscriptionStatus = MutableLiveData<SubscriptionStatus>()
    val subscriptionStatus: LiveData<SubscriptionStatus> = _subscriptionStatus
    
    private val _accessBlocked = MutableLiveData<Boolean>()
    val accessBlocked: LiveData<Boolean> = _accessBlocked
    
    /**
     * Initialize subscription service
     */
    suspend fun initialize(): Result<SubscriptionInfo?> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    android.util.Log.w(AppConfig.TAG, "No authenticated user found")
                    return@withContext Result.failure(Exception("User not authenticated"))
                }
                
                // Load subscription info from user profile
                val subscriptionInfo = loadUserSubscription(currentUser.uid)
                
                if (subscriptionInfo != null) {
                    // Update LiveData
                    _currentSubscription.postValue(subscriptionInfo)
                    _subscriptionStatus.postValue(subscriptionInfo.getStatus())
                    _accessBlocked.postValue(!subscriptionInfo.canConnect())
                    
                    android.util.Log.i(AppConfig.TAG, "Subscription initialized: ${subscriptionInfo.getStatus().displayName}")
                    Result.success(subscriptionInfo)
                } else {
                    // Create default subscription if not exists
                    val defaultSubscription = createDefaultSubscription()
                    val updateResult = updateUserSubscription(currentUser.uid, defaultSubscription)
                    
                    if (updateResult.isSuccess) {
                        _currentSubscription.postValue(defaultSubscription)
                        _subscriptionStatus.postValue(defaultSubscription.getStatus())
                        _accessBlocked.postValue(!defaultSubscription.canConnect())
                        
                        android.util.Log.i(AppConfig.TAG, "Created default subscription")
                        Result.success(defaultSubscription)
                    } else {
                        Result.failure(updateResult.exceptionOrNull() ?: Exception("Failed to create default subscription"))
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to initialize subscription service", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Load user subscription from Firebase
     */
    private suspend fun loadUserSubscription(uid: String): SubscriptionInfo? {
        return suspendCancellableCoroutine { continuation ->
            val userRef = database.child(USERS_PATH).child(uid)
            
            val listener = object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    try {
                        val subscriptionEnd = snapshot.child("subscription_end").getValue(String::class.java) ?: ""
                        val subscriptionType = snapshot.child("subscription_type").getValue(String::class.java) ?: "basic"
                        val isActive = snapshot.child("is_active").getValue(Boolean::class.java) ?: true
                        val dataLimitGb = snapshot.child("data_limit_gb").getValue(Long::class.java) ?: 0
                        val dataUsedGb = snapshot.child("data_used_gb").getValue(Long::class.java) ?: 0
                        val maxDevices = snapshot.child("max_devices").getValue(Int::class.java) ?: 1
                        val notes = snapshot.child("notes").getValue(String::class.java) ?: ""
                        
                        val subscriptionInfo = SubscriptionInfo(
                            subscriptionEnd = subscriptionEnd,
                            subscriptionType = subscriptionType,
                            isActive = isActive,
                            dataLimitGb = dataLimitGb,
                            dataUsedGb = dataUsedGb,
                            maxDevices = maxDevices,
                            features = getDefaultFeatures(subscriptionType),
                            notes = notes
                        )
                        
                        continuation.resume(subscriptionInfo)
                    } catch (e: Exception) {
                        android.util.Log.e(AppConfig.TAG, "Error parsing subscription data", e)
                        continuation.resumeWithException(e)
                    }
                }
                
                override fun onCancelled(error: DatabaseError) {
                    android.util.Log.e(AppConfig.TAG, "Firebase query cancelled: ${error.message}")
                    continuation.resumeWithException(error.toException())
                }
            }
            
            userRef.addListenerForSingleValueEvent(listener)
            
            continuation.invokeOnCancellation {
                userRef.removeEventListener(listener)
            }
        }
    }
    
    /**
     * Update user subscription in Firebase
     */
    suspend fun updateUserSubscription(uid: String, subscriptionInfo: SubscriptionInfo): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Validate date format
                if (!subscriptionInfo.isValidDateFormat()) {
                    return@withContext Result.failure(Exception("Invalid date format. Use yyyy-MM-dd"))
                }
                
                suspendCancellableCoroutine<Unit> { continuation ->
                    val userRef = database.child(USERS_PATH).child(uid)
                    
                    val updates = mapOf(
                        "subscription_end" to subscriptionInfo.subscriptionEnd,
                        "subscription_type" to subscriptionInfo.subscriptionType,
                        "is_active" to subscriptionInfo.isActive,
                        "data_limit_gb" to subscriptionInfo.dataLimitGb,
                        "data_used_gb" to subscriptionInfo.dataUsedGb,
                        "max_devices" to subscriptionInfo.maxDevices,
                        "updated_at" to System.currentTimeMillis()
                    )
                    
                    userRef.updateChildren(updates)
                        .addOnSuccessListener {
                            android.util.Log.i(AppConfig.TAG, "Subscription updated for user: $uid")
                            
                            // Update LiveData if it's current user
                            if (uid == auth.currentUser?.uid) {
                                _currentSubscription.postValue(subscriptionInfo)
                                _subscriptionStatus.postValue(subscriptionInfo.getStatus())
                                _accessBlocked.postValue(!subscriptionInfo.canConnect())
                            }
                            
                            continuation.resume(Unit)
                        }
                        .addOnFailureListener { exception ->
                            android.util.Log.e(AppConfig.TAG, "Failed to update subscription", exception)
                            continuation.resumeWithException(exception)
                        }
                }
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Check if current user can access the app
     */
    fun canAccessApp(): Boolean {
        val subscription = _currentSubscription.value ?: return false
        return subscription.isSubscriptionActive()
    }
    
    /**
     * Check if current user can connect to servers
     */
    fun canConnectToServers(): Boolean {
        val subscription = _currentSubscription.value ?: return false
        return subscription.canConnect()
    }
    
    /**
     * Get current subscription info
     */
    fun getCurrentSubscription(): SubscriptionInfo? {
        return _currentSubscription.value
    }
    
    /**
     * Get current subscription status
     */
    fun getCurrentStatus(): SubscriptionStatus {
        return _subscriptionStatus.value ?: SubscriptionStatus.UNKNOWN
    }
    
    /**
     * Check if access is blocked
     */
    fun isAccessBlocked(): Boolean {
        return _accessBlocked.value ?: true
    }
    
    /**
     * Get access block reason
     */
    fun getAccessBlockReason(): String? {
        return _currentSubscription.value?.getConnectionBlockReason()
    }
    
    /**
     * Extend subscription (admin only)
     */
    suspend fun extendSubscription(uid: String, newEndDate: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!userRoleService.isCurrentUserAdmin()) {
                    return@withContext Result.failure(Exception("Access denied: Admin role required"))
                }
                
                // Validate date format
                val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
                dateFormat.isLenient = false
                try {
                    dateFormat.parse(newEndDate)
                } catch (e: Exception) {
                    return@withContext Result.failure(Exception("Invalid date format. Use yyyy-MM-dd"))
                }
                
                // Load current subscription
                val currentSubscription = loadUserSubscription(uid)
                if (currentSubscription == null) {
                    return@withContext Result.failure(Exception("User subscription not found"))
                }
                
                // Update subscription
                val updatedSubscription = currentSubscription.withNewEndDate(newEndDate)
                updateUserSubscription(uid, updatedSubscription)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Deactivate subscription (admin only)
     */
    suspend fun deactivateSubscription(uid: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!userRoleService.isCurrentUserAdmin()) {
                    return@withContext Result.failure(Exception("Access denied: Admin role required"))
                }
                
                // Load current subscription
                val currentSubscription = loadUserSubscription(uid)
                if (currentSubscription == null) {
                    return@withContext Result.failure(Exception("User subscription not found"))
                }
                
                // Deactivate subscription
                val deactivatedSubscription = currentSubscription.deactivated()
                updateUserSubscription(uid, deactivatedSubscription)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update data usage
     */
    suspend fun updateDataUsage(additionalGb: Long): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUser ?: return@withContext Result.failure(Exception("User not authenticated"))
                val currentSubscription = _currentSubscription.value ?: return@withContext Result.failure(Exception("No subscription found"))
                
                val updatedSubscription = currentSubscription.withDataUsage(currentSubscription.dataUsedGb + additionalGb)
                updateUserSubscription(currentUser.uid, updatedSubscription)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get users with expiring subscriptions (admin only)
     */
    suspend fun getUsersWithExpiringSubscriptions(): Result<List<UserProfile>> {
        return withContext(Dispatchers.IO) {
            try {
                // Check admin permission
                if (!userRoleService.isCurrentUserAdmin()) {
                    return@withContext Result.failure(Exception("Access denied: Admin role required"))
                }
                
                val allUsers = userRoleService.loadAllUsers()
                if (allUsers.isSuccess) {
                    val users = allUsers.getOrNull() ?: emptyList()
                    val expiringUsers = users.filter { user ->
                        val subscription = SubscriptionInfo(
                            subscriptionEnd = user.subscriptionEnd,
                            subscriptionType = user.subscriptionType,
                            isActive = user.isActive
                        )
                        subscription.isExpiringSoon() || !subscription.isSubscriptionActive()
                    }
                    Result.success(expiringUsers)
                } else {
                    Result.failure(allUsers.exceptionOrNull() ?: Exception("Failed to load users"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Create default subscription
     */
    private fun createDefaultSubscription(): SubscriptionInfo {
        // Create 30-day trial subscription
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, 30)
        val endDate = SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(calendar.time)
        
        return SubscriptionInfo.createBasic(endDate).copy(
            subscriptionType = "trial",
            notes = "اشتراك تجريبي لمدة 30 يوم"
        )
    }
    
    /**
     * Get default features for subscription type
     */
    private fun getDefaultFeatures(subscriptionType: String): List<String> {
        return when (subscriptionType.lowercase()) {
            "unlimited" -> listOf("unlimited_data", "unlimited_devices", "premium_servers", "priority_support")
            "premium" -> listOf("premium_servers", "high_speed", "multiple_devices")
            "basic" -> listOf("basic_servers")
            "trial" -> listOf("basic_servers")
            else -> emptyList()
        }
    }
    
    /**
     * Refresh subscription data
     */
    suspend fun refreshSubscription(): Result<SubscriptionInfo?> {
        return initialize()
    }
    
    /**
     * Clear subscription data
     */
    fun clearSubscriptionData() {
        _currentSubscription.postValue(null)
        _subscriptionStatus.postValue(SubscriptionStatus.UNKNOWN)
        _accessBlocked.postValue(true)
        android.util.Log.i(AppConfig.TAG, "Subscription data cleared")
    }
}
