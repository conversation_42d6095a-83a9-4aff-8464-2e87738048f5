{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-62:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6168aafd8aa1313de7d53dbb6f8061c\\transformed\\appcompat-1.7.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,41,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2572,2796,2911,3018,3131", "endLines": "4,5,6,7,21,35,36,37,40,44,45,46,47,51", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2567,2791,2906,3013,3126,3356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5e6d251ef110edeab2647bcfbb802f94\\transformed\\quickie-foss-1.14.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "10", "endOffsets": "365"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "3511", "endLines": "60", "endColumns": "10", "endOffsets": "3821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1586d6a1fa7e7bf9ffc9892b585ca703\\transformed\\material-1.12.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,9,13,16,19,22,25,28,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,413,574,784,991,1198,1401,1603,1868", "endLines": "4,8,12,15,18,21,24,27,31,35", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,408,569,779,986,1193,1396,1598,1863,2136"}, "to": {"startLines": "61,64,68,72,75,78,81,84,87,91", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3826,4042,4184,4345,4555,4762,4969,5172,5374,5639", "endLines": "63,67,71,74,77,80,83,86,90,94", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "4037,4179,4340,4550,4757,4964,5167,5369,5634,5907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\53813636e15577cb39907bbb84d2feb8\\transformed\\work-runtime-2.10.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3dab1241f79a44a1027f6b1218aa7601\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "3361", "endLines": "54", "endColumns": "12", "endOffsets": "3506"}}]}]}