package com.mohamedrady.v2hoor.service

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.dto.AdminServerConfig
import com.mohamedrady.v2hoor.dto.UserServerAssignment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Service for managing servers and user-server assignments
 */
class ServerManagementService private constructor() {

    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()

    companion object {
        @Volatile
        private var INSTANCE: ServerManagementService? = null

        // Collections
        private const val SERVERS_COLLECTION = "servers"
        private const val USER_SERVERS_COLLECTION = "user_servers"

        fun getInstance(): ServerManagementService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerManagementService().also { INSTANCE = it }
            }
        }
    }

    /**
     * Get servers for current user
     */
    suspend fun getUserServers(): Result<List<AdminServerConfig>> {
        return withContext(Dispatchers.IO) {
            try {
                val user = auth.currentUser ?: return@withContext Result.failure(Exception("User not authenticated"))
                
                // Get user-server assignments
                val assignmentsSnapshot = firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("userId", user.uid)
                    .whereEqualTo("isActive", true)
                    .get()
                    .await()

                val serverIds = assignmentsSnapshot.documents.mapNotNull { 
                    it.getString("serverId") 
                }

                if (serverIds.isEmpty()) {
                    return@withContext Result.success(emptyList())
                }

                // Get server details
                val servers = mutableListOf<AdminServerConfig>()
                for (serverId in serverIds) {
                    try {
                        val serverDoc = firestore.collection(SERVERS_COLLECTION)
                            .document(serverId)
                            .get()
                            .await()

                        if (serverDoc.exists()) {
                            val server = AdminServerConfig(
                                id = serverDoc.id,
                                name = serverDoc.getString("name") ?: "خادم غير معروف",
                                config = serverDoc.getString("config") ?: "",
                                country = serverDoc.getString("country"),
                                city = serverDoc.getString("city"),
                                isActive = serverDoc.getBoolean("isActive") ?: true,
                                createdAt = serverDoc.getLong("createdAt") ?: 0L,
                                updatedAt = serverDoc.getLong("updatedAt"),
                                maxUsers = serverDoc.getLong("maxUsers")?.toInt() ?: 100,
                                currentUsers = serverDoc.getLong("currentUsers")?.toInt() ?: 0,
                                serverType = serverDoc.getString("serverType") ?: "vmess",
                                priority = serverDoc.getLong("priority")?.toInt() ?: 0
                            )
                            servers.add(server)
                        }
                    } catch (e: Exception) {
                        Log.e(AppConfig.TAG, "Error loading server $serverId", e)
                    }
                }

                // Sort by priority
                servers.sortByDescending { it.priority }
                
                Result.success(servers)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get user servers", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get all servers (admin only)
     */
    suspend fun getAllServers(): Result<List<AdminServerConfig>> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val querySnapshot = firestore.collection(SERVERS_COLLECTION)
                    .orderBy("priority", Query.Direction.DESCENDING)
                    .orderBy("createdAt", Query.Direction.DESCENDING)
                    .get()
                    .await()

                val servers = querySnapshot.documents.mapNotNull { doc ->
                    try {
                        AdminServerConfig(
                            id = doc.id,
                            name = doc.getString("name") ?: "خادم غير معروف",
                            config = doc.getString("config") ?: "",
                            country = doc.getString("country"),
                            city = doc.getString("city"),
                            isActive = doc.getBoolean("isActive") ?: true,
                            createdAt = doc.getLong("createdAt") ?: 0L,
                            updatedAt = doc.getLong("updatedAt"),
                            maxUsers = doc.getLong("maxUsers")?.toInt() ?: 100,
                            currentUsers = doc.getLong("currentUsers")?.toInt() ?: 0,
                            serverType = doc.getString("serverType") ?: "vmess",
                            priority = doc.getLong("priority")?.toInt() ?: 0
                        )
                    } catch (e: Exception) {
                        Log.e(AppConfig.TAG, "Error parsing server document", e)
                        null
                    }
                }

                Result.success(servers)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get all servers", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Create or update server (admin only)
     */
    suspend fun createOrUpdateServer(server: AdminServerConfig): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val serverData = mapOf(
                    "name" to server.name,
                    "config" to server.config,
                    "country" to server.country,
                    "city" to server.city,
                    "isActive" to server.isActive,
                    "createdAt" to (server.createdAt.takeIf { it > 0 } ?: System.currentTimeMillis()),
                    "updatedAt" to System.currentTimeMillis(),
                    "maxUsers" to server.maxUsers,
                    "currentUsers" to server.currentUsers,
                    "serverType" to server.serverType,
                    "priority" to server.priority
                )

                val docRef = if (server.id.isNotEmpty()) {
                    // Update existing server
                    firestore.collection(SERVERS_COLLECTION)
                        .document(server.id)
                        .set(serverData)
                        .await()
                    server.id
                } else {
                    // Create new server
                    val docRef = firestore.collection(SERVERS_COLLECTION)
                        .add(serverData)
                        .await()
                    docRef.id
                }

                Log.i(AppConfig.TAG, "Server created/updated: $docRef")
                Result.success(docRef)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to create/update server", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Delete server (admin only)
     */
    suspend fun deleteServer(serverId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                // Delete server document
                firestore.collection(SERVERS_COLLECTION)
                    .document(serverId)
                    .delete()
                    .await()

                // Delete all user-server assignments for this server
                val assignmentsSnapshot = firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("serverId", serverId)
                    .get()
                    .await()

                assignmentsSnapshot.documents.forEach { doc ->
                    doc.reference.delete()
                }

                Log.i(AppConfig.TAG, "Server deleted: $serverId")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to delete server", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Assign server to user (admin only)
     */
    suspend fun assignServerToUser(userId: String, serverId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                // Check if assignment already exists
                val existingAssignment = firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("userId", userId)
                    .whereEqualTo("serverId", serverId)
                    .get()
                    .await()

                if (existingAssignment.documents.isNotEmpty()) {
                    // Update existing assignment
                    existingAssignment.documents.first().reference
                        .update(
                            mapOf(
                                "isActive" to true,
                                "assignedAt" to System.currentTimeMillis()
                            )
                        )
                        .await()
                } else {
                    // Create new assignment
                    val assignmentData = mapOf(
                        "userId" to userId,
                        "serverId" to serverId,
                        "isActive" to true,
                        "assignedAt" to System.currentTimeMillis(),
                        "assignedBy" to auth.currentUser?.uid
                    )

                    firestore.collection(USER_SERVERS_COLLECTION)
                        .add(assignmentData)
                        .await()
                }

                Log.i(AppConfig.TAG, "Server assigned to user: $serverId -> $userId")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to assign server to user", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Remove server from user (admin only)
     */
    suspend fun removeServerFromUser(userId: String, serverId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val assignmentsSnapshot = firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("userId", userId)
                    .whereEqualTo("serverId", serverId)
                    .get()
                    .await()

                assignmentsSnapshot.documents.forEach { doc ->
                    doc.reference.delete()
                }

                Log.i(AppConfig.TAG, "Server removed from user: $serverId -> $userId")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to remove server from user", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get users assigned to a server (admin only)
     */
    suspend fun getServerUsers(serverId: String): Result<List<String>> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val assignmentsSnapshot = firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("serverId", serverId)
                    .whereEqualTo("isActive", true)
                    .get()
                    .await()

                val userIds = assignmentsSnapshot.documents.mapNotNull { 
                    it.getString("userId") 
                }

                Result.success(userIds)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get server users", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Get server statistics
     */
    suspend fun getServerStatistics(): Result<Map<String, Any>> {
        return withContext(Dispatchers.IO) {
            try {
                val adminService = AdminPermissionService.getInstance()
                if (!adminService.isAdmin()) {
                    return@withContext Result.failure(Exception("Access denied"))
                }

                val serversSnapshot = firestore.collection(SERVERS_COLLECTION).get().await()
                val totalServers = serversSnapshot.size()
                val activeServers = serversSnapshot.documents.count { 
                    it.getBoolean("isActive") ?: true 
                }

                val assignmentsSnapshot = firestore.collection(USER_SERVERS_COLLECTION)
                    .whereEqualTo("isActive", true)
                    .get()
                    .await()
                val totalAssignments = assignmentsSnapshot.size()

                val stats = mapOf(
                    "totalServers" to totalServers,
                    "activeServers" to activeServers,
                    "inactiveServers" to (totalServers - activeServers),
                    "totalAssignments" to totalAssignments
                )

                Result.success(stats)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get server statistics", e)
                Result.failure(e)
            }
        }
    }
}
