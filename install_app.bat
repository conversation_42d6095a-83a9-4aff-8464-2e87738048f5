@echo off
echo ========================================
echo   Installing V2Hoor App
echo ========================================
echo.

REM Check if device is connected
echo Checking for connected devices...
adb devices
if %errorlevel% neq 0 (
    echo ERROR: ADB not found or no devices connected!
    echo Please make sure:
    echo 1. Android device is connected via USB
    echo 2. USB Debugging is enabled
    echo 3. ADB is installed and in PATH
    pause
    exit /b 1
)

echo.
echo Installing V2Hoor app...
adb install -r "app\build\outputs\apk\fdroid\debug\v2hoor_1.10.7-fdroid_universal.apk"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   Installation Successful!
    echo ========================================
    echo.
    echo Starting V2Hoor app...
    adb shell am start -n com.mohamedrady.v2hoor/.ui.LoginActivity
    
    echo.
    echo App started! You can now test the login functionality.
    echo.
    echo Super Admin Account:
    echo Email: <EMAIL>
    echo Password: [Your Firebase password]
    echo.
) else (
    echo.
    echo ========================================
    echo   Installation Failed!
    echo ========================================
    echo.
    echo Please check:
    echo 1. Device is properly connected
    echo 2. USB Debugging is enabled
    echo 3. Developer options are enabled
    echo.
)

pause
