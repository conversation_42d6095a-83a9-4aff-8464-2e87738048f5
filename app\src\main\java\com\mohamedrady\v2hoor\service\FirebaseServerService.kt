package com.mohamedrady.v2hoor.service

import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.handler.AngConfigManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Service for managing VPN servers in Firebase Firestore
 */
class FirebaseServerService private constructor() {

    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()

    companion object {
        @Volatile
        private var INSTANCE: FirebaseServerService? = null

        private const val PUBLIC_SERVERS_COLLECTION = "public_servers"

        fun getInstance(): FirebaseServerService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseServerService().also { INSTANCE = it }
            }
        }
    }

    /**
     * Data class for server information in Firestore
     */
    data class FirebaseServer(
        val id: String = "",
        val name: String = "",
        val config: String = "",
        val type: String = "",
        val country: String = "",
        val city: String = "",
        val isActive: Boolean = true,
        val priority: Int = 0
    )

    /**
     * Get public servers
     */
    suspend fun getPublicServers(): Result<List<FirebaseServer>> {
        return withContext(Dispatchers.IO) {
            try {
                val snapshot = firestore.collection(PUBLIC_SERVERS_COLLECTION)
                    .whereEqualTo("isActive", true)
                    .get()
                    .await()

                val servers = snapshot.documents.mapNotNull { doc ->
                    try {
                        doc.toObject(FirebaseServer::class.java)?.copy(id = doc.id)
                    } catch (e: Exception) {
                        Log.w(AppConfig.TAG, "Failed to parse server document: ${doc.id}", e)
                        null
                    }
                }

                Result.success(servers)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get public servers", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Import servers from Firebase to local storage
     */
    suspend fun importServersToLocal(): Result<Int> {
        return withContext(Dispatchers.IO) {
            try {
                val servers = getPublicServers().getOrThrow()
                var importedCount = 0
                
                for (server in servers) {
                    try {
                        val (count, countSub) = AngConfigManager.importBatchConfig(server.config, "firebase_${server.id}", false)
                        if (count > 0) {
                            importedCount += count
                            Log.d(AppConfig.TAG, "Imported server: ${server.name}")
                        }
                    } catch (e: Exception) {
                        Log.w(AppConfig.TAG, "Failed to import server: ${server.name}", e)
                    }
                }
                
                Log.i(AppConfig.TAG, "Imported $importedCount servers from Firebase")
                Result.success(importedCount)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to import servers from Firebase", e)
                Result.failure(e)
            }
        }
    }
}
