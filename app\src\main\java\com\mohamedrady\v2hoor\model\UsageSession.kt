package com.mohamedrady.v2hoor.model

import com.google.firebase.database.PropertyName
import java.text.SimpleDateFormat
import java.util.*

/**
 * Usage Session Model
 * Represents a single VPN connection session
 */
data class UsageSession(
    @PropertyName("session_id")
    var sessionId: String = "",
    
    @PropertyName("user_id")
    var userId: String = "",
    
    @PropertyName("server_id")
    var serverId: String = "",
    
    @PropertyName("server_name")
    var serverName: String = "",
    
    @PropertyName("server_location")
    var serverLocation: String = "",
    
    @PropertyName("start_time")
    var startTime: Long = 0,
    
    @PropertyName("end_time")
    var endTime: Long = 0,
    
    @PropertyName("duration_seconds")
    var durationSeconds: Long = 0,
    
    @PropertyName("bytes_sent")
    var bytesSent: Long = 0,
    
    @PropertyName("bytes_received")
    var bytesReceived: Long = 0,
    
    @PropertyName("connection_type")
    var connectionType: String = "unknown", // wifi, mobile, ethernet
    
    @PropertyName("app_version")
    var appVersion: String = "",
    
    @PropertyName("device_model")
    var deviceModel: String = "",
    
    @PropertyName("os_version")
    var osVersion: String = "",
    
    @PropertyName("disconnect_reason")
    var disconnectReason: String = "user", // user, error, timeout, network
    
    @PropertyName("quality_rating")
    var qualityRating: Int = 0, // 1-5 stars
    
    @PropertyName("notes")
    var notes: String = "",
    
    @PropertyName("created_at")
    var createdAt: Long = System.currentTimeMillis(),
    
    @PropertyName("updated_at")
    var updatedAt: Long = System.currentTimeMillis()
) {
    constructor() : this(
        sessionId = "",
        userId = "",
        serverId = "",
        serverName = "",
        serverLocation = "",
        startTime = 0,
        endTime = 0,
        durationSeconds = 0,
        bytesSent = 0,
        bytesReceived = 0,
        connectionType = "unknown",
        appVersion = "",
        deviceModel = "",
        osVersion = "",
        disconnectReason = "user",
        qualityRating = 0,
        notes = "",
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
    
    /**
     * Check if session is active
     */
    fun isActive(): Boolean {
        return startTime > 0 && endTime == 0L
    }
    
    /**
     * Get session duration in seconds
     */
    fun getDurationSeconds(): Long {
        return if (isActive()) {
            (System.currentTimeMillis() - startTime) / 1000
        } else {
            durationSeconds
        }
    }
    
    /**
     * Get session duration in minutes
     */
    fun getDurationMinutes(): Long {
        return getDurationSeconds() / 60
    }
    
    /**
     * Get session duration in hours
     */
    fun getDurationHours(): Double {
        return getDurationSeconds() / 3600.0
    }
    
    /**
     * Get formatted duration
     */
    fun getFormattedDuration(): String {
        val totalSeconds = getDurationSeconds()
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes, seconds)
            minutes > 0 -> String.format("%d:%02d", minutes, seconds)
            else -> "${seconds}s"
        }
    }
    
    /**
     * Get total bytes transferred
     */
    fun getTotalBytes(): Long {
        return bytesSent + bytesReceived
    }
    
    /**
     * Get formatted data usage
     */
    fun getFormattedDataUsage(): String {
        val totalBytes = getTotalBytes()
        return when {
            totalBytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", totalBytes / (1024.0 * 1024.0 * 1024.0))
            totalBytes >= 1024 * 1024 -> String.format("%.2f MB", totalBytes / (1024.0 * 1024.0))
            totalBytes >= 1024 -> String.format("%.2f KB", totalBytes / 1024.0)
            else -> "$totalBytes B"
        }
    }
    
    /**
     * Get formatted start time
     */
    fun getFormattedStartTime(): String {
        return if (startTime > 0) {
            val date = Date(startTime)
            SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(date)
        } else {
            "غير محدد"
        }
    }
    
    /**
     * Get formatted end time
     */
    fun getFormattedEndTime(): String {
        return if (endTime > 0) {
            val date = Date(endTime)
            SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(date)
        } else if (isActive()) {
            "نشط"
        } else {
            "غير محدد"
        }
    }
    
    /**
     * Get session date
     */
    fun getSessionDate(): String {
        return if (startTime > 0) {
            val date = Date(startTime)
            SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(date)
        } else {
            "غير محدد"
        }
    }
    
    /**
     * Get connection type display name
     */
    fun getConnectionTypeDisplayName(): String {
        return when (connectionType.lowercase()) {
            "wifi" -> "واي فاي"
            "mobile" -> "بيانات الجوال"
            "ethernet" -> "إيثرنت"
            else -> "غير معروف"
        }
    }
    
    /**
     * Get disconnect reason display name
     */
    fun getDisconnectReasonDisplayName(): String {
        return when (disconnectReason.lowercase()) {
            "user" -> "قطع المستخدم"
            "error" -> "خطأ في الاتصال"
            "timeout" -> "انتهاء المهلة"
            "network" -> "مشكلة في الشبكة"
            "server" -> "مشكلة في السيرفر"
            else -> "غير معروف"
        }
    }
    
    /**
     * Get quality rating stars
     */
    fun getQualityStars(): String {
        return "★".repeat(qualityRating) + "☆".repeat(5 - qualityRating)
    }
    
    /**
     * Get session summary
     */
    fun getSummary(): String {
        val parts = mutableListOf<String>()
        parts.add(getFormattedDuration())
        parts.add(getFormattedDataUsage())
        if (serverName.isNotBlank()) parts.add(serverName)
        return parts.joinToString(" • ")
    }
    
    /**
     * Calculate average speed (bytes per second)
     */
    fun getAverageSpeed(): Double {
        val duration = getDurationSeconds()
        return if (duration > 0) {
            getTotalBytes().toDouble() / duration
        } else {
            0.0
        }
    }
    
    /**
     * Get formatted average speed
     */
    fun getFormattedAverageSpeed(): String {
        val speed = getAverageSpeed()
        return when {
            speed >= 1024 * 1024 -> String.format("%.2f MB/s", speed / (1024.0 * 1024.0))
            speed >= 1024 -> String.format("%.2f KB/s", speed / 1024.0)
            else -> String.format("%.0f B/s", speed)
        }
    }
    
    /**
     * Check if session is from today
     */
    fun isFromToday(): Boolean {
        val today = Calendar.getInstance()
        val sessionDate = Calendar.getInstance().apply { timeInMillis = startTime }
        
        return today.get(Calendar.YEAR) == sessionDate.get(Calendar.YEAR) &&
               today.get(Calendar.DAY_OF_YEAR) == sessionDate.get(Calendar.DAY_OF_YEAR)
    }
    
    /**
     * Check if session is from this week
     */
    fun isFromThisWeek(): Boolean {
        val today = Calendar.getInstance()
        val sessionDate = Calendar.getInstance().apply { timeInMillis = startTime }
        
        return today.get(Calendar.YEAR) == sessionDate.get(Calendar.YEAR) &&
               today.get(Calendar.WEEK_OF_YEAR) == sessionDate.get(Calendar.WEEK_OF_YEAR)
    }
    
    /**
     * Check if session is from this month
     */
    fun isFromThisMonth(): Boolean {
        val today = Calendar.getInstance()
        val sessionDate = Calendar.getInstance().apply { timeInMillis = startTime }
        
        return today.get(Calendar.YEAR) == sessionDate.get(Calendar.YEAR) &&
               today.get(Calendar.MONTH) == sessionDate.get(Calendar.MONTH)
    }
    
    /**
     * Validate session data
     */
    fun isValid(): Boolean {
        return sessionId.isNotBlank() &&
               userId.isNotBlank() &&
               serverId.isNotBlank() &&
               startTime > 0
    }
    
    /**
     * End the session
     */
    fun endSession(reason: String = "user") {
        if (isActive()) {
            endTime = System.currentTimeMillis()
            durationSeconds = (endTime - startTime) / 1000
            disconnectReason = reason
            updatedAt = System.currentTimeMillis()
        }
    }
    
    companion object {
        /**
         * Create new session
         */
        fun createSession(
            userId: String,
            serverId: String,
            serverName: String,
            serverLocation: String = "",
            connectionType: String = "unknown"
        ): UsageSession {
            return UsageSession(
                sessionId = UUID.randomUUID().toString(),
                userId = userId,
                serverId = serverId,
                serverName = serverName,
                serverLocation = serverLocation,
                startTime = System.currentTimeMillis(),
                connectionType = connectionType,
                appVersion = getAppVersion(),
                deviceModel = getDeviceModel(),
                osVersion = getOSVersion()
            )
        }
        
        private fun getAppVersion(): String {
            return try {
                "1.0.0" // TODO: Get actual app version
            } catch (e: Exception) {
                "unknown"
            }
        }
        
        private fun getDeviceModel(): String {
            return try {
                "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
            } catch (e: Exception) {
                "unknown"
            }
        }
        
        private fun getOSVersion(): String {
            return try {
                "Android ${android.os.Build.VERSION.RELEASE}"
            } catch (e: Exception) {
                "unknown"
            }
        }
    }
}
