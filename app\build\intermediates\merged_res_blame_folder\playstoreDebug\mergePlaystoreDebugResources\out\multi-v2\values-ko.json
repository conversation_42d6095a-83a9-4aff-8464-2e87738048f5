{"logs": [{"outputFile": "com.mohamedrady.v2hoor.app-mergePlaystoreDebugResources-63:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1586d6a1fa7e7bf9ffc9892b585ca703\\transformed\\material-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,917,979,1060,1122,1179,1266,1326,1384,1442,1501,1558,1612,1707,1763,1820,1874,1940,2044,2119,2191,2272,2350,2427,2548,2613,2678,2778,2857,2932,2982,3033,3099,3163,3233,3304,3375,3443,3514,3586,3656,3749,3829,3903,3983,4065,4137,4202,4274,4322,4395,4459,4534,4611,4673,4737,4800,4867,4951,5029,5109,5187,5241,5296,5368,5445,5518", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "244,309,373,442,516,595,678,784,859,912,974,1055,1117,1174,1261,1321,1379,1437,1496,1553,1607,1702,1758,1815,1869,1935,2039,2114,2186,2267,2345,2422,2543,2608,2673,2773,2852,2927,2977,3028,3094,3158,3228,3299,3370,3438,3509,3581,3651,3744,3824,3898,3978,4060,4132,4197,4269,4317,4390,4454,4529,4606,4668,4732,4795,4862,4946,5024,5104,5182,5236,5291,5363,5440,5513,5584"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,53,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2879,2944,3008,3077,3151,3907,3990,4096,4323,4376,4519,4890,4952,5009,5096,5156,5214,5272,5331,5388,5442,5537,5593,5650,5704,5770,5874,5949,6021,6102,6180,6257,6378,6443,6508,6608,6687,6762,6812,6863,6929,6993,7063,7134,7205,7273,7344,7416,7486,7579,7659,7733,7813,7895,7967,8032,8104,8152,8225,8289,8364,8441,8503,8567,8630,8697,8781,8859,8939,9017,9071,9200,9605,9682,9755", "endLines": "5,33,34,35,36,37,45,46,47,50,51,53,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,118,123,124,125", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "294,2939,3003,3072,3146,3225,3985,4091,4166,4371,4433,4595,4947,5004,5091,5151,5209,5267,5326,5383,5437,5532,5588,5645,5699,5765,5869,5944,6016,6097,6175,6252,6373,6438,6503,6603,6682,6757,6807,6858,6924,6988,7058,7129,7200,7268,7339,7411,7481,7574,7654,7728,7808,7890,7962,8027,8099,8147,8220,8284,8359,8436,8498,8562,8625,8692,8776,8854,8934,9012,9066,9121,9267,9677,9750,9821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\de0c5edbd8703928d695edd0a578b20c\\transformed\\browser-1.8.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "49,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4237,4600,4693,4796", "endColumns": "85,92,102,93", "endOffsets": "4318,4688,4791,4885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\addfe77571c545920b761f990220962d\\transformed\\core-1.16.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "38,39,40,41,42,43,44,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3230,3322,3422,3516,3613,3709,3807,9826", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3317,3417,3511,3608,3704,3802,3902,9922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d6168aafd8aa1313de7d53dbb6f8061c\\transformed\\appcompat-1.7.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,9526", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,9600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\082507c0f8acc2f43fb9efc345edc657\\transformed\\preference-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,326,458,627,709", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "166,247,321,453,622,704,780"}, "to": {"startLines": "48,52,117,119,127,128,129", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4171,4438,9126,9272,9927,10096,10178", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "4232,4514,9195,9399,10091,10173,10249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\194b7da0db63fea2a64463e6a0ef5556\\transformed\\zxing-android-embedded-4.3.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,106,153,260", "endColumns": "50,46,106,71", "endOffsets": "101,148,255,327"}, "to": {"startLines": "130,131,132,133", "startColumns": "4,4,4,4", "startOffsets": "10254,10305,10352,10459", "endColumns": "50,46,106,71", "endOffsets": "10300,10347,10454,10526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\5e6d251ef110edeab2647bcfbb802f94\\transformed\\quickie-foss-1.14.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,117", "endColumns": "61,59", "endOffsets": "112,172"}, "to": {"startLines": "120,121", "startColumns": "4,4", "startOffsets": "9404,9466", "endColumns": "61,59", "endOffsets": "9461,9521"}}]}]}