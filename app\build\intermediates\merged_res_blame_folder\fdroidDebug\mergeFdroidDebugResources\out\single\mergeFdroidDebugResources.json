[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_sub_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_shadowsocks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_shadowsocks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_outline_filter_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_outline_filter_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xxxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\xml_cache_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\xml\\cache_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_cloud_download_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_cloud_download_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_about_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_about_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_vless.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_vless.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_rounded_corner_active.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_rounded_corner_active.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_sub_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_sub_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_fab_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_fab_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_feedback_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_feedback_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_select_all_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_select_all_24dp.xml"}, {"merged": "com.mohamedrady.v2hoor.app-fdroidDebug-75:/drawable_login_background.xml.flat", "source": "com.mohamedrady.v2hoor.app-main-79:/drawable/login_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_stop_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_stop_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_routing_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_routing_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\xml_pref_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\xml\\pref_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-mdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-mdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xhdpi_ic_banner.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xhdpi\\ic_banner.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_delete_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_delete_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_delete_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_delete_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_outline_filter_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_outline_filter_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_share_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_share_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_tasker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_tasker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_asset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xhdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xhdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_play_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_play_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_sub_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_image_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_image_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_add_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_add_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_privacy_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_privacy_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_trojan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_trojan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\xml_app_widget_provider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\xml\\app_widget_provider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxhdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxhdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_lock_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_lock_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_wireguard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_wireguard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_edit_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_edit_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_logcat_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_logcat_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_add_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_add_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\raw_licenses.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\raw\\licenses.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_routing_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxxhdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxxhdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_promotion_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_promotion_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_logcat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_backup_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_backup_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_privacy_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_privacy_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_description_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_description_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\color_color_highlight_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\color\\color_highlight_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_source_code_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_source_code_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_action_sub_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\action_sub_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_restore_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_restore_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_logcat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_scan_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_scan_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-anydpi-v26_ic_banner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-anydpi-v26\\ic_banner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_routing_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_routing_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_file_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_file_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_action_done.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_action_done.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_dialog_config_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\dialog_config_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_subscriptions_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_subscriptions_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_feedback_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_feedback_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_custom_divider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\custom_divider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_file_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_file_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\font_montserrat_thin.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\font\\montserrat_thin.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxhdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxhdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_scan_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_scan_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_per_apps_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_per_apps_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_user_asset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_stop_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_stop_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_layout_address_port.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_address_port.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_drawer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_backup_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_backup_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-hdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-hdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_routing_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xhdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xhdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_share_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_share_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_check_update.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_check_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_widget_switch.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\widget_switch.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_routing_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_routing_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_qrcode.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_qrcode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-mdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-mdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-mdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-mdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xhdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xhdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-mdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-mdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_logcat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_logcat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxhdpi_ic_stat_name_black.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxhdpi\\ic_stat_name_black.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_qu_scan_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_qu_scan_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_more_vert_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_more_vert_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_play_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_play_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_nav_header_bg.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\nav_header_bg.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_telegram_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_telegram_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_scanner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_google.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_google.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_per_apps_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_per_apps_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxxhdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxxhdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_subscriptions_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_subscriptions_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_restore_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_restore_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_promotion_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_promotion_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxxhdpi_ic_stat_direct.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxxhdpi\\ic_stat_direct.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_bypass_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_vmess.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_vmess.xml"}, {"merged": "com.mohamedrady.v2hoor.app-fdroidDebug-75:/layout_activity_login.xml.flat", "source": "com.mohamedrady.v2hoor.app-main-79:/layout/activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_cloud_download_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_cloud_download_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-mdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-mdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_hysteria2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_hysteria2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_layout_tls.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_tls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxhdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxhdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_check_update_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_check_update_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-hdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-hdpi\\ic_stat_name.png"}, {"merged": "com.mohamedrady.v2hoor.app-fdroidDebug-75:/drawable_login_button_background.xml.flat", "source": "com.mohamedrady.v2hoor.app-main-79:/drawable/login_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_nav_header_bg.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\nav_header_bg.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xxxhdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xxxhdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_layout_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_user_asset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_user_asset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_action_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\action_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_custom_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_custom_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_qu_switch_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_qu_switch_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_source_code_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_source_code_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_telegram_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_telegram_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_save_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_save_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_preference_with_help_link.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\preference_with_help_link.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-xhdpi_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-xhdpi\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_action_done.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_action_done.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_save_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_save_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_edit_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_edit_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_image_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_image_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_layout_tls_hysteria2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\layout_tls_hysteria2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_about_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_about_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_license_24px.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\license_24px.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_settings_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_settings_24dp.xml"}, {"merged": "com.mohamedrady.v2hoor.app-fdroidDebug-75:/drawable_google_button_background.xml.flat", "source": "com.mohamedrady.v2hoor.app-main-79:/drawable/google_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-hdpi_ic_stat_proxy.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-hdpi\\ic_stat_proxy.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_none.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_none.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_select_all_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_select_all_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_lock_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_lock_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_description_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_description_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_server_socks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_server_socks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xhdpi_ic_banner_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xhdpi\\ic_banner_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\menu_menu_bypass_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\menu\\menu_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\xml_shortcuts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\xml\\shortcuts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_rounded_corner_inactive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_rounded_corner_inactive.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_check_update_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_check_update_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable_ic_settings_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable\\ic_settings_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_logcat_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_logcat_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_fab_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_fab_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_bypass_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_bypass_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_item_recycler_footer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\item_recycler_footer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_routing_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_routing_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\layout_activity_user_asset_url.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\layout\\activity_user_asset_url.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\mipmap-xxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-fdroidDebug-75:\\drawable-night_ic_more_vert_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-main-79:\\drawable-night\\ic_more_vert_24dp.xml"}]