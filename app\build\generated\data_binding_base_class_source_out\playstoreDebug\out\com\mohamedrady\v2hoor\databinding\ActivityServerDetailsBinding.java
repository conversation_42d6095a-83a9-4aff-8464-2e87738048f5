// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityServerDetailsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextView textViewCreationDate;

  @NonNull
  public final TextView textViewServerConfig;

  @NonNull
  public final TextView textViewServerLocation;

  @NonNull
  public final TextView textViewServerName;

  @NonNull
  public final TextView textViewServerPriority;

  @NonNull
  public final TextView textViewServerStatus;

  @NonNull
  public final TextView textViewServerType;

  @NonNull
  public final TextView textViewServerUsage;

  @NonNull
  public final Toolbar toolbar;

  private ActivityServerDetailsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull TextView textViewCreationDate, @NonNull TextView textViewServerConfig,
      @NonNull TextView textViewServerLocation, @NonNull TextView textViewServerName,
      @NonNull TextView textViewServerPriority, @NonNull TextView textViewServerStatus,
      @NonNull TextView textViewServerType, @NonNull TextView textViewServerUsage,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.textViewCreationDate = textViewCreationDate;
    this.textViewServerConfig = textViewServerConfig;
    this.textViewServerLocation = textViewServerLocation;
    this.textViewServerName = textViewServerName;
    this.textViewServerPriority = textViewServerPriority;
    this.textViewServerStatus = textViewServerStatus;
    this.textViewServerType = textViewServerType;
    this.textViewServerUsage = textViewServerUsage;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityServerDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityServerDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_server_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityServerDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.textViewCreationDate;
      TextView textViewCreationDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewCreationDate == null) {
        break missingId;
      }

      id = R.id.textViewServerConfig;
      TextView textViewServerConfig = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerConfig == null) {
        break missingId;
      }

      id = R.id.textViewServerLocation;
      TextView textViewServerLocation = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerLocation == null) {
        break missingId;
      }

      id = R.id.textViewServerName;
      TextView textViewServerName = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerName == null) {
        break missingId;
      }

      id = R.id.textViewServerPriority;
      TextView textViewServerPriority = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerPriority == null) {
        break missingId;
      }

      id = R.id.textViewServerStatus;
      TextView textViewServerStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerStatus == null) {
        break missingId;
      }

      id = R.id.textViewServerType;
      TextView textViewServerType = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerType == null) {
        break missingId;
      }

      id = R.id.textViewServerUsage;
      TextView textViewServerUsage = ViewBindings.findChildViewById(rootView, id);
      if (textViewServerUsage == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityServerDetailsBinding((CoordinatorLayout) rootView, textViewCreationDate,
          textViewServerConfig, textViewServerLocation, textViewServerName, textViewServerPriority,
          textViewServerStatus, textViewServerType, textViewServerUsage, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
