<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_tls_hysteria2" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\layout_tls_hysteria2.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_tls_hysteria2_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="94" endOffset="14"/></Target><Target id="@+id/lay_stream_security" view="LinearLayout"><Expressions/><location startLine="7" startOffset="4" endLine="29" endOffset="18"/></Target><Target id="@+id/sp_stream_security" view="Spinner"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="49"/></Target><Target id="@+id/lay_sni" view="LinearLayout"><Expressions/><location startLine="31" startOffset="4" endLine="50" endOffset="18"/></Target><Target id="@+id/et_sni" view="EditText"><Expressions/><location startLine="43" startOffset="8" endLine="48" endOffset="64"/></Target><Target id="@+id/lay_allow_insecure" view="LinearLayout"><Expressions/><location startLine="53" startOffset="4" endLine="91" endOffset="18"/></Target><Target id="@+id/sp_allow_insecure" view="Spinner"><Expressions/><location startLine="64" startOffset="8" endLine="70" endOffset="53"/></Target><Target id="@+id/et_pinsha256" view="EditText"><Expressions/><location startLine="84" startOffset="12" endLine="88" endOffset="42"/></Target></Targets></Layout>