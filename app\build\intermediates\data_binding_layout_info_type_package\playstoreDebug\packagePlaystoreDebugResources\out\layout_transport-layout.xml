<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_transport" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\layout_transport.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_transport_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="14"/></Target><Target id="@+id/sp_network" view="Spinner"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="47"/></Target><Target id="@+id/sp_header_type_title" view="TextView"><Expressions/><location startLine="45" startOffset="8" endLine="49" endOffset="57"/></Target><Target id="@+id/sp_header_type" view="Spinner"><Expressions/><location startLine="51" startOffset="8" endLine="56" endOffset="70"/></Target><Target id="@+id/tv_request_host" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="69" endOffset="60"/></Target><Target id="@+id/et_request_host" view="EditText"><Expressions/><location startLine="71" startOffset="8" endLine="75" endOffset="38"/></Target><Target id="@+id/tv_path" view="TextView"><Expressions/><location startLine="85" startOffset="8" endLine="89" endOffset="52"/></Target><Target id="@+id/et_path" view="EditText"><Expressions/><location startLine="91" startOffset="8" endLine="95" endOffset="38"/></Target><Target id="@+id/layout_extra" view="LinearLayout"><Expressions/><location startLine="98" startOffset="4" endLine="118" endOffset="18"/></Target><Target id="@+id/et_extra" view="EditText"><Expressions/><location startLine="110" startOffset="8" endLine="117" endOffset="34"/></Target></Targets></Layout>