package com.mohamedrady.v2hoor.service

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.firebase.database.DatabaseReference
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.FirebaseServerModel
import com.mohamedrady.v2hoor.model.ServerCache
import kotlinx.coroutines.*

/**
 * Server Sync Manager
 * Handles server synchronization, refresh, and real-time updates
 */
class ServerSyncManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ServerSyncManager? = null
        
        fun getInstance(context: Context): ServerSyncManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerSyncManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val firebaseService = FirebaseRealtimeServerService.getInstance(context)
    private val serverCache = ServerCache.getInstance(context)
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // LiveData for UI updates
    private val _servers = MutableLiveData<List<FirebaseServerModel>>()
    val servers: LiveData<List<FirebaseServerModel>> = _servers
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _syncStatus = MutableLiveData<SyncStatus>()
    val syncStatus: LiveData<SyncStatus> = _syncStatus
    
    private val _expiredServers = MutableLiveData<List<FirebaseServerModel>>()
    val expiredServers: LiveData<List<FirebaseServerModel>> = _expiredServers
    
    private val _serverWarnings = MutableLiveData<List<Pair<FirebaseServerModel, String>>>()
    val serverWarnings: LiveData<List<Pair<FirebaseServerModel, String>>> = _serverWarnings
    
    // Real-time listener reference
    private var realtimeListener: DatabaseReference? = null
    
    /**
     * Initialize server sync manager
     */
    fun initialize() {
        android.util.Log.i(AppConfig.TAG, "Initializing ServerSyncManager")
        
        // Load cached servers first
        loadCachedServers()
        
        // Start real-time listening
        startRealtimeSync()
        
        // Load fresh servers from Firebase
        refreshServers()
    }
    
    /**
     * Load cached servers
     */
    private fun loadCachedServers() {
        scope.launch {
            try {
                val result = firebaseService.loadUserServers(forceRefresh = false)
                if (result.isSuccess) {
                    val serverList = result.getOrNull() ?: emptyList()
                    _servers.value = serverList
                    updateExpiredServers(serverList)
                    updateServerWarnings(serverList)
                    
                    android.util.Log.i(AppConfig.TAG, "Loaded ${serverList.size} cached servers")
                }
            } catch (e: Exception) {
                android.util.Log.e(AppConfig.TAG, "Failed to load cached servers", e)
            }
        }
    }
    
    /**
     * Start real-time synchronization
     */
    private fun startRealtimeSync() {
        try {
            realtimeListener = firebaseService.listenForServerUpdates { updatedServers ->
                scope.launch {
                    _servers.value = updatedServers
                    updateExpiredServers(updatedServers)
                    updateServerWarnings(updatedServers)
                    
                    _syncStatus.value = SyncStatus.SUCCESS
                    android.util.Log.i(AppConfig.TAG, "Real-time sync: ${updatedServers.size} servers updated")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to start real-time sync", e)
            _syncStatus.value = SyncStatus.ERROR
        }
    }
    
    /**
     * Refresh servers from Firebase
     */
    fun refreshServers() {
        scope.launch {
            try {
                _isLoading.value = true
                _syncStatus.value = SyncStatus.SYNCING
                
                android.util.Log.i(AppConfig.TAG, "Refreshing servers from Firebase...")
                
                val result = firebaseService.refreshServers()
                if (result.isSuccess) {
                    val serverList = result.getOrNull() ?: emptyList()
                    _servers.value = serverList
                    updateExpiredServers(serverList)
                    updateServerWarnings(serverList)
                    
                    _syncStatus.value = SyncStatus.SUCCESS
                    android.util.Log.i(AppConfig.TAG, "Successfully refreshed ${serverList.size} servers")
                    
                    // Import servers to V2rayNG
                    importServersToV2rayNG()
                } else {
                    _syncStatus.value = SyncStatus.ERROR
                    android.util.Log.e(AppConfig.TAG, "Failed to refresh servers", result.exceptionOrNull())
                }
            } catch (e: Exception) {
                _syncStatus.value = SyncStatus.ERROR
                android.util.Log.e(AppConfig.TAG, "Error during server refresh", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Import servers to V2rayNG format
     */
    private suspend fun importServersToV2rayNG() {
        try {
            val result = firebaseService.importServersToV2rayNG()
            if (result.isSuccess) {
                val importedCount = result.getOrNull() ?: 0
                android.util.Log.i(AppConfig.TAG, "Imported $importedCount servers to V2rayNG")
            } else {
                android.util.Log.w(AppConfig.TAG, "Failed to import servers to V2rayNG", result.exceptionOrNull())
            }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Error importing servers to V2rayNG", e)
        }
    }
    
    /**
     * Update expired servers list
     */
    private suspend fun updateExpiredServers(servers: List<FirebaseServerModel>) {
        val expired = servers.filter { it.isExpired() }
        _expiredServers.value = expired
        
        if (expired.isNotEmpty()) {
            android.util.Log.w(AppConfig.TAG, "Found ${expired.size} expired servers")
        }
    }
    
    /**
     * Update server warnings list
     */
    private suspend fun updateServerWarnings(servers: List<FirebaseServerModel>) {
        val warnings = servers.mapNotNull { server ->
            val warning = server.getExpirationWarning()
            if (warning != null) {
                Pair(server, warning)
            } else {
                null
            }
        }
        _serverWarnings.value = warnings
        
        if (warnings.isNotEmpty()) {
            android.util.Log.w(AppConfig.TAG, "Found ${warnings.size} servers with warnings")
        }
    }
    
    /**
     * Force sync servers
     */
    fun forceSyncServers() {
        scope.launch {
            try {
                _isLoading.value = true
                _syncStatus.value = SyncStatus.SYNCING
                
                // Clear cache first
                firebaseService.clearCache()
                
                // Load fresh servers
                val result = firebaseService.loadUserServers(forceRefresh = true)
                if (result.isSuccess) {
                    val serverList = result.getOrNull() ?: emptyList()
                    _servers.value = serverList
                    updateExpiredServers(serverList)
                    updateServerWarnings(serverList)
                    
                    _syncStatus.value = SyncStatus.SUCCESS
                    android.util.Log.i(AppConfig.TAG, "Force sync completed: ${serverList.size} servers")
                    
                    // Import servers to V2rayNG
                    importServersToV2rayNG()
                } else {
                    _syncStatus.value = SyncStatus.ERROR
                    android.util.Log.e(AppConfig.TAG, "Force sync failed", result.exceptionOrNull())
                }
            } catch (e: Exception) {
                _syncStatus.value = SyncStatus.ERROR
                android.util.Log.e(AppConfig.TAG, "Error during force sync", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Get server by ID
     */
    fun getServerById(serverId: String): FirebaseServerModel? {
        return _servers.value?.find { it.id == serverId }
    }
    
    /**
     * Get cache statistics
     */
    fun getCacheStats(): com.mohamedrady.v2hoor.model.CacheStats? {
        return firebaseService.getCacheStats()
    }
    
    /**
     * Check if servers are available
     */
    fun hasServers(): Boolean {
        return _servers.value?.isNotEmpty() == true
    }
    
    /**
     * Get active servers count
     */
    fun getActiveServersCount(): Int {
        return _servers.value?.count { it.isActive && !it.isExpired() } ?: 0
    }
    
    /**
     * Get expired servers count
     */
    fun getExpiredServersCount(): Int {
        return _expiredServers.value?.size ?: 0
    }
    
    /**
     * Get servers with warnings count
     */
    fun getWarningServersCount(): Int {
        return _serverWarnings.value?.size ?: 0
    }
    
    /**
     * Stop real-time synchronization
     */
    fun stopRealtimeSync() {
        firebaseService.stopListeningForUpdates(realtimeListener)
        realtimeListener = null
        android.util.Log.i(AppConfig.TAG, "Stopped real-time sync")
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stopRealtimeSync()
        scope.cancel()
        android.util.Log.i(AppConfig.TAG, "ServerSyncManager cleaned up")
    }
    
    /**
     * Restart synchronization
     */
    fun restart() {
        cleanup()
        initialize()
    }
}

/**
 * Sync status enum
 */
enum class SyncStatus {
    IDLE,
    SYNCING,
    SUCCESS,
    ERROR
}
