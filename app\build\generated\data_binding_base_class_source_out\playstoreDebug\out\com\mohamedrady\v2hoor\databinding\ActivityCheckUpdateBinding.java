// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCheckUpdateBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final SwitchCompat checkPreRelease;

  @NonNull
  public final LinearLayout layoutCheckUpdate;

  @NonNull
  public final TextView tvVersion;

  private ActivityCheckUpdateBinding(@NonNull ScrollView rootView,
      @NonNull SwitchCompat checkPreRelease, @NonNull LinearLayout layoutCheckUpdate,
      @NonNull TextView tvVersion) {
    this.rootView = rootView;
    this.checkPreRelease = checkPreRelease;
    this.layoutCheckUpdate = layoutCheckUpdate;
    this.tvVersion = tvVersion;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCheckUpdateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCheckUpdateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_check_update, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCheckUpdateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.check_pre_release;
      SwitchCompat checkPreRelease = ViewBindings.findChildViewById(rootView, id);
      if (checkPreRelease == null) {
        break missingId;
      }

      id = R.id.layout_check_update;
      LinearLayout layoutCheckUpdate = ViewBindings.findChildViewById(rootView, id);
      if (layoutCheckUpdate == null) {
        break missingId;
      }

      id = R.id.tv_version;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      return new ActivityCheckUpdateBinding((ScrollView) rootView, checkPreRelease,
          layoutCheckUpdate, tvVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
