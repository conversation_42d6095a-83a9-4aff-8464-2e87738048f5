package com.mohamedrady.v2hoor.service

import android.content.Context
import android.content.SharedPreferences
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mohamedrady.v2hoor.AppConfig
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec

/**
 * Security Manager for App Protection
 * Handles biometric authentication, PIN protection, and encrypted storage
 */
class SecurityManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: SecurityManager? = null
        
        fun getInstance(context: Context): SecurityManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SecurityManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val PREFS_NAME = "security_preferences"
        private const val KEY_BIOMETRIC_ENABLED = "biometric_enabled"
        private const val KEY_PIN_ENABLED = "pin_enabled"
        private const val KEY_PIN_HASH = "pin_hash"
        private const val KEY_AUTO_LOCK_ENABLED = "auto_lock_enabled"
        private const val KEY_AUTO_LOCK_TIMEOUT = "auto_lock_timeout"
        private const val KEY_LAST_UNLOCK_TIME = "last_unlock_time"
        private const val KEY_FAILED_ATTEMPTS = "failed_attempts"
        private const val KEY_LOCK_TIMESTAMP = "lock_timestamp"
        
        private const val KEYSTORE_ALIAS = "V2HoorSecurityKey"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val MAX_FAILED_ATTEMPTS = 5
        private const val LOCKOUT_DURATION = 5 * 60 * 1000L // 5 minutes
    }
    
    enum class AuthenticationMethod {
        NONE, PIN, BIOMETRIC, BOTH
    }
    
    enum class LockTimeout(val minutes: Int, val displayName: String) {
        IMMEDIATE(0, "فوري"),
        ONE_MINUTE(1, "دقيقة واحدة"),
        FIVE_MINUTES(5, "5 دقائق"),
        FIFTEEN_MINUTES(15, "15 دقيقة"),
        THIRTY_MINUTES(30, "30 دقيقة"),
        ONE_HOUR(60, "ساعة واحدة"),
        NEVER(-1, "أبداً")
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // LiveData for security state
    private val _isAppLocked = MutableLiveData<Boolean>()
    val isAppLocked: LiveData<Boolean> = _isAppLocked
    
    private val _authenticationRequired = MutableLiveData<Boolean>()
    val authenticationRequired: LiveData<Boolean> = _authenticationRequired
    
    init {
        // Initialize security state
        checkLockStatus()
    }
    
    /**
     * Check if biometric authentication is available
     */
    fun isBiometricAvailable(): Boolean {
        val biometricManager = BiometricManager.from(context)
        return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            BiometricManager.BIOMETRIC_SUCCESS -> true
            else -> false
        }
    }
    
    /**
     * Enable biometric authentication
     */
    fun enableBiometricAuth(enabled: Boolean) {
        prefs.edit()
            .putBoolean(KEY_BIOMETRIC_ENABLED, enabled)
            .apply()
        
        android.util.Log.i(AppConfig.TAG, "Biometric authentication ${if (enabled) "enabled" else "disabled"}")
    }
    
    /**
     * Check if biometric authentication is enabled
     */
    fun isBiometricEnabled(): Boolean {
        return prefs.getBoolean(KEY_BIOMETRIC_ENABLED, false) && isBiometricAvailable()
    }
    
    /**
     * Set PIN code
     */
    fun setPinCode(pin: String): Boolean {
        return try {
            val pinHash = hashPin(pin)
            prefs.edit()
                .putString(KEY_PIN_HASH, pinHash)
                .putBoolean(KEY_PIN_ENABLED, true)
                .apply()
            
            android.util.Log.i(AppConfig.TAG, "PIN code set successfully")
            true
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to set PIN code", e)
            false
        }
    }
    
    /**
     * Verify PIN code
     */
    fun verifyPin(pin: String): Boolean {
        val storedHash = prefs.getString(KEY_PIN_HASH, null) ?: return false
        val inputHash = hashPin(pin)
        
        val isValid = storedHash == inputHash
        
        if (isValid) {
            resetFailedAttempts()
            updateLastUnlockTime()
        } else {
            incrementFailedAttempts()
        }
        
        return isValid
    }
    
    /**
     * Check if PIN is enabled
     */
    fun isPinEnabled(): Boolean {
        return prefs.getBoolean(KEY_PIN_ENABLED, false) && 
               prefs.getString(KEY_PIN_HASH, null) != null
    }
    
    /**
     * Disable PIN authentication
     */
    fun disablePinAuth() {
        prefs.edit()
            .putBoolean(KEY_PIN_ENABLED, false)
            .remove(KEY_PIN_HASH)
            .apply()
        
        android.util.Log.i(AppConfig.TAG, "PIN authentication disabled")
    }
    
    /**
     * Set auto-lock settings
     */
    fun setAutoLock(enabled: Boolean, timeout: LockTimeout = LockTimeout.FIVE_MINUTES) {
        prefs.edit()
            .putBoolean(KEY_AUTO_LOCK_ENABLED, enabled)
            .putInt(KEY_AUTO_LOCK_TIMEOUT, timeout.minutes)
            .apply()
        
        android.util.Log.i(AppConfig.TAG, "Auto-lock ${if (enabled) "enabled" else "disabled"} with ${timeout.minutes} minutes timeout")
    }
    
    /**
     * Check if auto-lock is enabled
     */
    fun isAutoLockEnabled(): Boolean {
        return prefs.getBoolean(KEY_AUTO_LOCK_ENABLED, false)
    }
    
    /**
     * Get auto-lock timeout
     */
    fun getAutoLockTimeout(): LockTimeout {
        val minutes = prefs.getInt(KEY_AUTO_LOCK_TIMEOUT, LockTimeout.FIVE_MINUTES.minutes)
        return LockTimeout.values().find { it.minutes == minutes } ?: LockTimeout.FIVE_MINUTES
    }
    
    /**
     * Check if app should be locked
     */
    fun shouldLockApp(): Boolean {
        if (!isAutoLockEnabled()) return false
        
        val timeout = getAutoLockTimeout()
        if (timeout == LockTimeout.NEVER) return false
        
        val lastUnlockTime = prefs.getLong(KEY_LAST_UNLOCK_TIME, 0)
        val currentTime = System.currentTimeMillis()
        val timeoutMillis = timeout.minutes * 60 * 1000L
        
        return (currentTime - lastUnlockTime) > timeoutMillis
    }
    
    /**
     * Lock the app
     */
    fun lockApp() {
        prefs.edit()
            .putLong(KEY_LOCK_TIMESTAMP, System.currentTimeMillis())
            .apply()
        
        _isAppLocked.postValue(true)
        _authenticationRequired.postValue(true)
        
        android.util.Log.i(AppConfig.TAG, "App locked")
    }
    
    /**
     * Unlock the app
     */
    fun unlockApp() {
        updateLastUnlockTime()
        resetFailedAttempts()
        
        _isAppLocked.postValue(false)
        _authenticationRequired.postValue(false)
        
        android.util.Log.i(AppConfig.TAG, "App unlocked")
    }
    
    /**
     * Check current lock status
     */
    fun checkLockStatus() {
        val hasAuthMethod = isPinEnabled() || isBiometricEnabled()
        val shouldLock = hasAuthMethod && shouldLockApp()
        
        _isAppLocked.postValue(shouldLock)
        _authenticationRequired.postValue(shouldLock)
    }
    
    /**
     * Get current authentication method
     */
    fun getCurrentAuthMethod(): AuthenticationMethod {
        return when {
            isPinEnabled() && isBiometricEnabled() -> AuthenticationMethod.BOTH
            isPinEnabled() -> AuthenticationMethod.PIN
            isBiometricEnabled() -> AuthenticationMethod.BIOMETRIC
            else -> AuthenticationMethod.NONE
        }
    }
    
    /**
     * Show biometric prompt
     */
    fun showBiometricPrompt(
        activity: FragmentActivity,
        title: String = "المصادقة البيومترية",
        subtitle: String = "استخدم بصمة الإصبع أو الوجه للمتابعة",
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        if (!isBiometricAvailable()) {
            onError("المصادقة البيومترية غير متاحة")
            return
        }
        
        val executor = ContextCompat.getMainExecutor(context)
        val biometricPrompt = BiometricPrompt(activity, executor, object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                unlockApp()
                onSuccess()
            }
            
            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                onError(errString.toString())
            }
            
            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                incrementFailedAttempts()
                onError("فشل في المصادقة")
            }
        })
        
        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setNegativeButtonText("إلغاء")
            .build()
        
        biometricPrompt.authenticate(promptInfo)
    }
    
    /**
     * Get failed attempts count
     */
    fun getFailedAttempts(): Int {
        return prefs.getInt(KEY_FAILED_ATTEMPTS, 0)
    }
    
    /**
     * Check if app is locked due to failed attempts
     */
    fun isLockedDueToFailedAttempts(): Boolean {
        val failedAttempts = getFailedAttempts()
        if (failedAttempts < MAX_FAILED_ATTEMPTS) return false
        
        val lockTimestamp = prefs.getLong(KEY_LOCK_TIMESTAMP, 0)
        val currentTime = System.currentTimeMillis()
        
        return (currentTime - lockTimestamp) < LOCKOUT_DURATION
    }
    
    /**
     * Get remaining lockout time
     */
    fun getRemainingLockoutTime(): Long {
        if (!isLockedDueToFailedAttempts()) return 0
        
        val lockTimestamp = prefs.getLong(KEY_LOCK_TIMESTAMP, 0)
        val currentTime = System.currentTimeMillis()
        val elapsed = currentTime - lockTimestamp
        
        return maxOf(0, LOCKOUT_DURATION - elapsed)
    }
    
    /**
     * Update last unlock time
     */
    private fun updateLastUnlockTime() {
        prefs.edit()
            .putLong(KEY_LAST_UNLOCK_TIME, System.currentTimeMillis())
            .apply()
    }
    
    /**
     * Increment failed attempts
     */
    private fun incrementFailedAttempts() {
        val currentAttempts = getFailedAttempts()
        val newAttempts = currentAttempts + 1
        
        prefs.edit()
            .putInt(KEY_FAILED_ATTEMPTS, newAttempts)
            .apply()
        
        if (newAttempts >= MAX_FAILED_ATTEMPTS) {
            prefs.edit()
                .putLong(KEY_LOCK_TIMESTAMP, System.currentTimeMillis())
                .apply()
        }
        
        android.util.Log.w(AppConfig.TAG, "Failed authentication attempt: $newAttempts")
    }
    
    /**
     * Reset failed attempts
     */
    private fun resetFailedAttempts() {
        prefs.edit()
            .putInt(KEY_FAILED_ATTEMPTS, 0)
            .remove(KEY_LOCK_TIMESTAMP)
            .apply()
    }
    
    /**
     * Hash PIN code
     */
    private fun hashPin(pin: String): String {
        return try {
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val hash = digest.digest(pin.toByteArray())
            hash.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            android.util.Log.e(AppConfig.TAG, "Failed to hash PIN", e)
            ""
        }
    }
    
    /**
     * Get security statistics
     */
    fun getSecurityStats(): Map<String, String> {
        return mapOf(
            "biometric_available" to if (isBiometricAvailable()) "متاح" else "غير متاح",
            "biometric_enabled" to if (isBiometricEnabled()) "مفعل" else "معطل",
            "pin_enabled" to if (isPinEnabled()) "مفعل" else "معطل",
            "auto_lock_enabled" to if (isAutoLockEnabled()) "مفعل" else "معطل",
            "auto_lock_timeout" to getAutoLockTimeout().displayName,
            "current_auth_method" to getCurrentAuthMethod().name,
            "failed_attempts" to getFailedAttempts().toString(),
            "is_locked" to if (_isAppLocked.value == true) "مقفل" else "مفتوح"
        )
    }
    
    /**
     * Reset all security settings
     */
    fun resetSecuritySettings() {
        prefs.edit().clear().apply()
        _isAppLocked.postValue(false)
        _authenticationRequired.postValue(false)
        
        android.util.Log.i(AppConfig.TAG, "Security settings reset")
    }
}
