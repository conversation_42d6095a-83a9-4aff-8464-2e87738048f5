// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAdminUsersBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button buttonRefresh;

  @NonNull
  public final SearchView editTextSearch;

  @NonNull
  public final FloatingActionButton fabAddUser;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewUsers;

  @NonNull
  public final TextView textViewActiveUsers;

  @NonNull
  public final TextView textViewInactiveUsers;

  @NonNull
  public final TextView textViewPremiumUsers;

  @NonNull
  public final TextView textViewTotalUsers;

  @NonNull
  public final TextView textViewUsersCount;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAdminUsersBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button buttonRefresh, @NonNull SearchView editTextSearch,
      @NonNull FloatingActionButton fabAddUser, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewUsers, @NonNull TextView textViewActiveUsers,
      @NonNull TextView textViewInactiveUsers, @NonNull TextView textViewPremiumUsers,
      @NonNull TextView textViewTotalUsers, @NonNull TextView textViewUsersCount,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonRefresh = buttonRefresh;
    this.editTextSearch = editTextSearch;
    this.fabAddUser = fabAddUser;
    this.progressBar = progressBar;
    this.recyclerViewUsers = recyclerViewUsers;
    this.textViewActiveUsers = textViewActiveUsers;
    this.textViewInactiveUsers = textViewInactiveUsers;
    this.textViewPremiumUsers = textViewPremiumUsers;
    this.textViewTotalUsers = textViewTotalUsers;
    this.textViewUsersCount = textViewUsersCount;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAdminUsersBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAdminUsersBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_admin_users, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAdminUsersBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonRefresh;
      Button buttonRefresh = ViewBindings.findChildViewById(rootView, id);
      if (buttonRefresh == null) {
        break missingId;
      }

      id = R.id.editTextSearch;
      SearchView editTextSearch = ViewBindings.findChildViewById(rootView, id);
      if (editTextSearch == null) {
        break missingId;
      }

      id = R.id.fabAddUser;
      FloatingActionButton fabAddUser = ViewBindings.findChildViewById(rootView, id);
      if (fabAddUser == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewUsers;
      RecyclerView recyclerViewUsers = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewUsers == null) {
        break missingId;
      }

      id = R.id.textViewActiveUsers;
      TextView textViewActiveUsers = ViewBindings.findChildViewById(rootView, id);
      if (textViewActiveUsers == null) {
        break missingId;
      }

      id = R.id.textViewInactiveUsers;
      TextView textViewInactiveUsers = ViewBindings.findChildViewById(rootView, id);
      if (textViewInactiveUsers == null) {
        break missingId;
      }

      id = R.id.textViewPremiumUsers;
      TextView textViewPremiumUsers = ViewBindings.findChildViewById(rootView, id);
      if (textViewPremiumUsers == null) {
        break missingId;
      }

      id = R.id.textViewTotalUsers;
      TextView textViewTotalUsers = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotalUsers == null) {
        break missingId;
      }

      id = R.id.textViewUsersCount;
      TextView textViewUsersCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewUsersCount == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAdminUsersBinding((CoordinatorLayout) rootView, buttonRefresh,
          editTextSearch, fabAddUser, progressBar, recyclerViewUsers, textViewActiveUsers,
          textViewInactiveUsers, textViewPremiumUsers, textViewTotalUsers, textViewUsersCount,
          toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
