package com.mohamedrady.v2hoor.model

import com.google.firebase.database.PropertyName
import com.google.firebase.database.ServerValue
import java.util.*

/**
 * Firebase Server Model for Realtime Database
 * Represents a V2Ray server configuration stored in Firebase
 */
data class FirebaseServerModel(
    @PropertyName("id")
    var id: String = "",
    
    @PropertyName("name")
    var name: String = "",
    
    @PropertyName("remarks")
    var remarks: String = "",
    
    @PropertyName("server")
    var server: String = "",
    
    @PropertyName("server_port")
    var serverPort: Int = 0,
    
    @PropertyName("protocol")
    var protocol: String = "vmess", // vmess, vless, trojan, shadowsocks
    
    @PropertyName("uuid")
    var uuid: String = "",
    
    @PropertyName("alter_id")
    var alterId: Int = 0,
    
    @PropertyName("security")
    var security: String = "auto", // auto, aes-128-gcm, chacha20-poly1305, none
    
    @PropertyName("network")
    var network: String = "tcp", // tcp, kcp, ws, h2, quic, grpc
    
    @PropertyName("header_type")
    var headerType: String = "none",
    
    @PropertyName("request_host")
    var requestHost: String = "",
    
    @PropertyName("path")
    var path: String = "",
    
    @PropertyName("tls")
    var tls: String = "", // tls, xtls, reality
    
    @PropertyName("sni")
    var sni: String = "",
    
    @PropertyName("alpn")
    var alpn: String = "",
    
    @PropertyName("fingerprint")
    var fingerprint: String = "",
    
    @PropertyName("public_key")
    var publicKey: String = "",
    
    @PropertyName("short_id")
    var shortId: String = "",
    
    @PropertyName("spider_x")
    var spiderX: String = "",
    
    @PropertyName("flow")
    var flow: String = "",
    
    @PropertyName("encryption")
    var encryption: String = "none",
    
    @PropertyName("country")
    var country: String = "",
    
    @PropertyName("city")
    var city: String = "",
    
    @PropertyName("flag")
    var flag: String = "",
    
    @PropertyName("is_active")
    var isActive: Boolean = true,
    
    @PropertyName("priority")
    var priority: Int = 0,
    
    @PropertyName("created_at")
    var createdAt: Long = 0,
    
    @PropertyName("updated_at")
    var updatedAt: Long = 0,
    
    @PropertyName("expires_at")
    var expiresAt: Long = 0, // 0 means never expires
    
    @PropertyName("valid_until")
    var validUntil: Long = 0, // 0 means always valid
    
    @PropertyName("last_sync")
    var lastSync: Long = 0,
    
    @PropertyName("config_version")
    var configVersion: Int = 1,
    
    @PropertyName("subscription_id")
    var subscriptionId: String = "",
    
    @PropertyName("user_id")
    var userId: String = "",
    
    @PropertyName("tags")
    var tags: List<String> = emptyList(),
    
    @PropertyName("custom_config")
    var customConfig: String = "", // Raw V2Ray JSON config if needed
    
    @PropertyName("test_result")
    var testResult: ServerTestResult? = null
) {
    constructor() : this(
        id = "",
        name = "",
        remarks = "",
        server = "",
        serverPort = 0,
        protocol = "vmess",
        uuid = "",
        alterId = 0,
        security = "auto",
        network = "tcp",
        headerType = "none",
        requestHost = "",
        path = "",
        tls = "",
        sni = "",
        alpn = "",
        fingerprint = "",
        publicKey = "",
        shortId = "",
        spiderX = "",
        flow = "",
        encryption = "none",
        country = "",
        city = "",
        flag = "",
        isActive = true,
        priority = 0,
        createdAt = 0,
        updatedAt = 0,
        expiresAt = 0,
        validUntil = 0,
        lastSync = 0,
        configVersion = 1,
        subscriptionId = "",
        userId = "",
        tags = emptyList(),
        customConfig = "",
        testResult = null
    )
    
    /**
     * Check if server is expired
     */
    fun isExpired(): Boolean {
        val currentTime = System.currentTimeMillis()
        return (expiresAt > 0 && currentTime > expiresAt) || 
               (validUntil > 0 && currentTime > validUntil)
    }
    
    /**
     * Check if server is valid and active
     */
    fun isValid(): Boolean {
        return isActive && !isExpired() && server.isNotBlank() && serverPort > 0
    }
    
    /**
     * Get expiration warning message
     */
    fun getExpirationWarning(): String? {
        val currentTime = System.currentTimeMillis()
        val warningThreshold = 24 * 60 * 60 * 1000L // 24 hours
        
        return when {
            expiresAt > 0 && (expiresAt - currentTime) in 0..warningThreshold -> {
                val hoursLeft = (expiresAt - currentTime) / (60 * 60 * 1000)
                "Server expires in $hoursLeft hours"
            }
            validUntil > 0 && (validUntil - currentTime) in 0..warningThreshold -> {
                val hoursLeft = (validUntil - currentTime) / (60 * 60 * 1000)
                "Server validity expires in $hoursLeft hours"
            }
            else -> null
        }
    }
    
    /**
     * Generate V2Ray compatible config string
     */
    fun toV2RayConfig(): String {
        return when (protocol.lowercase()) {
            "vmess" -> generateVmessConfig()
            "vless" -> generateVlessConfig()
            "trojan" -> generateTrojanConfig()
            "shadowsocks" -> generateShadowsocksConfig()
            else -> customConfig.ifBlank { generateVmessConfig() }
        }
    }
    
    private fun generateVmessConfig(): String {
        val vmessJson = mapOf(
            "v" to "2",
            "ps" to remarks.ifBlank { name },
            "add" to server,
            "port" to serverPort.toString(),
            "id" to uuid,
            "aid" to alterId.toString(),
            "scy" to security,
            "net" to network,
            "type" to headerType,
            "host" to requestHost,
            "path" to path,
            "tls" to tls,
            "sni" to sni,
            "alpn" to alpn,
            "fp" to fingerprint
        )
        
        val jsonString = com.google.gson.Gson().toJson(vmessJson)
        val encodedConfig = android.util.Base64.encodeToString(
            jsonString.toByteArray(), 
            android.util.Base64.NO_WRAP
        )
        return "vmess://$encodedConfig"
    }
    
    private fun generateVlessConfig(): String {
        val params = mutableListOf<String>()
        if (encryption.isNotBlank()) params.add("encryption=$encryption")
        if (security.isNotBlank()) params.add("security=$security")
        if (sni.isNotBlank()) params.add("sni=$sni")
        if (alpn.isNotBlank()) params.add("alpn=$alpn")
        if (fingerprint.isNotBlank()) params.add("fp=$fingerprint")
        if (publicKey.isNotBlank()) params.add("pbk=$publicKey")
        if (shortId.isNotBlank()) params.add("sid=$shortId")
        if (spiderX.isNotBlank()) params.add("spx=$spiderX")
        if (flow.isNotBlank()) params.add("flow=$flow")
        if (headerType.isNotBlank()) params.add("type=$headerType")
        if (requestHost.isNotBlank()) params.add("host=$requestHost")
        if (path.isNotBlank()) params.add("path=$path")
        
        val paramString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        val remarksPart = if (remarks.isNotBlank()) "#${java.net.URLEncoder.encode(remarks, "UTF-8")}" else ""
        
        return "vless://$uuid@$server:$serverPort$paramString$remarksPart"
    }
    
    private fun generateTrojanConfig(): String {
        val params = mutableListOf<String>()
        if (security.isNotBlank()) params.add("security=$security")
        if (sni.isNotBlank()) params.add("sni=$sni")
        if (alpn.isNotBlank()) params.add("alpn=$alpn")
        if (fingerprint.isNotBlank()) params.add("fp=$fingerprint")
        if (headerType.isNotBlank()) params.add("type=$headerType")
        if (requestHost.isNotBlank()) params.add("host=$requestHost")
        if (path.isNotBlank()) params.add("path=$path")
        
        val paramString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        val remarksPart = if (remarks.isNotBlank()) "#${java.net.URLEncoder.encode(remarks, "UTF-8")}" else ""
        
        return "trojan://$uuid@$server:$serverPort$paramString$remarksPart"
    }
    
    private fun generateShadowsocksConfig(): String {
        val method = security.ifBlank { "aes-256-gcm" }
        val password = uuid
        val userInfo = "$method:$password"
        val encodedUserInfo = android.util.Base64.encodeToString(
            userInfo.toByteArray(), 
            android.util.Base64.NO_WRAP
        )
        val remarksPart = if (remarks.isNotBlank()) "#${java.net.URLEncoder.encode(remarks, "UTF-8")}" else ""
        
        return "ss://$encodedUserInfo@$server:$serverPort$remarksPart"
    }
}

/**
 * Server test result model
 */
data class ServerTestResult(
    @PropertyName("ping")
    var ping: Long = -1,
    
    @PropertyName("download_speed")
    var downloadSpeed: Long = 0,
    
    @PropertyName("upload_speed")
    var uploadSpeed: Long = 0,
    
    @PropertyName("last_test")
    var lastTest: Long = 0,
    
    @PropertyName("is_online")
    var isOnline: Boolean = false,
    
    @PropertyName("error_message")
    var errorMessage: String = ""
) {
    constructor() : this(-1, 0, 0, 0, false, "")
}
