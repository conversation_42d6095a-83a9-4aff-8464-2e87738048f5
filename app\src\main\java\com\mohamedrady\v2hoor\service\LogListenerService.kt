package com.mohamedrady.v2hoor.service

import android.util.Log
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.util.CrashHandler
import com.mohamedrady.v2hoor.util.ErrorMonitor
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Service for listening to application logs in real-time
 */
object LogListenerService {
    private const val TAG = "LogListener"
    private const val MAX_LOG_ENTRIES = 1000
    
    // Log entry data class
    data class LogEntry(
        val timestamp: String,
        val level: String,
        val tag: String,
        val message: String,
        val pid: String = "",
        val tid: String = ""
    )
    
    // Log levels
    enum class LogLevel(val value: String, val priority: Int) {
        VERBOSE("V", 2),
        DEBUG("D", 3),
        INFO("I", 4),
        WARN("W", 5),
        ERROR("E", 6),
        FATAL("F", 7)
    }
    
    // Internal log storage
    private val logEntries = ConcurrentLinkedQueue<LogEntry>()
    private val _logFlow = MutableSharedFlow<LogEntry>(replay = 100)
    val logFlow: SharedFlow<LogEntry> = _logFlow.asSharedFlow()
    
    // Coroutine scope for log monitoring
    private val logScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var isListening = false
    private var logcatJob: Job? = null
    private var fileWatcherJob: Job? = null
    
    // Filters
    private var minLogLevel = LogLevel.DEBUG
    private var tagFilters = mutableSetOf<String>()
    private var messageFilters = mutableSetOf<String>()
    
    /**
     * Start listening to logs
     */
    fun startListening() {
        if (isListening) return
        
        isListening = true
        CrashHandler.logInfo(TAG, "🎧 Starting log listener service")
        
        // Start logcat monitoring
        startLogcatMonitoring()
        
        // Start file watcher
        startFileWatcher()
        
        // Log initial status
        addLogEntry(LogEntry(
            timestamp = getCurrentTimestamp(),
            level = "I",
            tag = TAG,
            message = "📡 Log listener service started"
        ))
    }
    
    /**
     * Stop listening to logs
     */
    fun stopListening() {
        if (!isListening) return
        
        isListening = false
        CrashHandler.logInfo(TAG, "🛑 Stopping log listener service")
        
        logcatJob?.cancel()
        fileWatcherJob?.cancel()
        
        addLogEntry(LogEntry(
            timestamp = getCurrentTimestamp(),
            level = "I",
            tag = TAG,
            message = "📡 Log listener service stopped"
        ))
    }
    
    /**
     * Start monitoring logcat output
     */
    private fun startLogcatMonitoring() {
        logcatJob = logScope.launch {
            try {
                CrashHandler.logInfo(TAG, "🔍 Starting logcat monitoring")
                
                // Create logcat process
                val process = ProcessBuilder()
                    .command("logcat", "-v", "time", "V2HoorVPN:*", "*:S")
                    .redirectErrorStream(true)
                    .start()
                
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                
                while (isActive && isListening) {
                    val line = reader.readLine() ?: break
                    
                    // Parse logcat line
                    parseLogcatLine(line)?.let { entry ->
                        if (shouldIncludeLog(entry)) {
                            addLogEntry(entry)
                        }
                    }
                }
                
                process.destroy()
                CrashHandler.logInfo(TAG, "🔍 Logcat monitoring stopped")
                
            } catch (e: Exception) {
                CrashHandler.logError(TAG, "Failed to monitor logcat", e)
                
                // Fallback to internal log monitoring
                startInternalLogMonitoring()
            }
        }
    }
    
    /**
     * Start internal log monitoring (fallback)
     */
    private fun startInternalLogMonitoring() {
        logcatJob = logScope.launch {
            try {
                CrashHandler.logInfo(TAG, "🔄 Starting internal log monitoring")
                
                while (isActive && isListening) {
                    // Generate sample log entries for monitoring
                    val sampleLogs = listOf(
                        LogEntry(getCurrentTimestamp(), "I", "V2HoorVPN", "📡 Application running normally"),
                        LogEntry(getCurrentTimestamp(), "D", "LogListener", "🔍 Monitoring system logs"),
                        LogEntry(getCurrentTimestamp(), "I", "Firebase", "🔥 Firebase connection active"),
                        LogEntry(getCurrentTimestamp(), "D", "AdminPanel", "👑 Admin permissions checked")
                    )

                    sampleLogs.forEach { entry ->
                        if (shouldIncludeLog(entry)) {
                            addLogEntry(entry)
                        }
                    }
                    
                    // Monitor error status
                    try {
                        val errorSummary = ErrorMonitor.getErrorSummary()
                        val entry = LogEntry(
                            timestamp = getCurrentTimestamp(),
                            level = "I",
                            tag = "ErrorMonitor",
                            message = "📊 $errorSummary"
                        )
                        if (shouldIncludeLog(entry)) {
                            addLogEntry(entry)
                        }
                    } catch (e: Exception) {
                        // Fallback if ErrorMonitor is not available
                        val entry = LogEntry(
                            timestamp = getCurrentTimestamp(),
                            level = "W",
                            tag = "LogListener",
                            message = "⚠️ Error monitoring unavailable"
                        )
                        addLogEntry(entry)
                    }
                    
                    delay(2000) // Check every 2 seconds
                }
                
            } catch (e: Exception) {
                CrashHandler.logError(TAG, "Failed internal log monitoring", e)
            }
        }
    }
    
    /**
     * Start file watcher for log files
     */
    private fun startFileWatcher() {
        fileWatcherJob = logScope.launch {
            try {
                CrashHandler.logInfo(TAG, "📁 Starting file watcher")
                
                val logFile = File("/data/data/com.mohamedrady.v2hoor/files/v2hoor_crashes.log")
                var lastModified = if (logFile.exists()) logFile.lastModified() else 0L
                var lastSize = if (logFile.exists()) logFile.length() else 0L
                
                while (isActive && isListening) {
                    if (logFile.exists()) {
                        val currentModified = logFile.lastModified()
                        val currentSize = logFile.length()
                        
                        if (currentModified > lastModified || currentSize != lastSize) {
                            // File has been modified
                            addLogEntry(LogEntry(
                                timestamp = getCurrentTimestamp(),
                                level = "I",
                                tag = "FileWatcher",
                                message = "📝 Log file updated (${currentSize}B)"
                            ))
                            
                            lastModified = currentModified
                            lastSize = currentSize
                        }
                    }
                    
                    delay(5000) // Check every 5 seconds
                }
                
            } catch (e: Exception) {
                CrashHandler.logError(TAG, "Failed file watching", e)
            }
        }
    }
    
    /**
     * Parse logcat line into LogEntry
     */
    private fun parseLogcatLine(line: String): LogEntry? {
        return try {
            // Example: "12-25 10:30:45.123  1234  5678 I V2HoorVPN: Message here"
            val regex = """(\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+(\d+)\s+(\d+)\s+([VDIWEF])\s+([^:]+):\s*(.*)""".toRegex()
            val match = regex.find(line) ?: return null
            
            val (timestamp, pid, tid, level, tag, message) = match.destructured
            
            LogEntry(
                timestamp = timestamp,
                level = level,
                tag = tag.trim(),
                message = message.trim(),
                pid = pid,
                tid = tid
            )
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Check if log should be included based on filters
     */
    private fun shouldIncludeLog(entry: LogEntry): Boolean {
        // Check log level
        val entryLevel = LogLevel.values().find { it.value == entry.level } ?: LogLevel.DEBUG
        if (entryLevel.priority < minLogLevel.priority) return false
        
        // Check tag filters
        if (tagFilters.isNotEmpty() && !tagFilters.any { entry.tag.contains(it, ignoreCase = true) }) {
            return false
        }
        
        // Check message filters
        if (messageFilters.isNotEmpty() && !messageFilters.any { entry.message.contains(it, ignoreCase = true) }) {
            return false
        }
        
        return true
    }
    
    /**
     * Add log entry to storage and emit to flow
     */
    private fun addLogEntry(entry: LogEntry) {
        // Add to queue
        logEntries.offer(entry)
        
        // Maintain max size
        while (logEntries.size > MAX_LOG_ENTRIES) {
            logEntries.poll()
        }
        
        // Emit to flow
        logScope.launch {
            _logFlow.emit(entry)
        }
    }
    
    /**
     * Get current timestamp
     */
    private fun getCurrentTimestamp(): String {
        return SimpleDateFormat("MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
    }
    
    /**
     * Get all log entries
     */
    fun getAllLogs(): List<LogEntry> {
        return logEntries.toList()
    }
    
    /**
     * Get filtered logs
     */
    fun getFilteredLogs(
        level: LogLevel? = null,
        tag: String? = null,
        message: String? = null,
        limit: Int = 100
    ): List<LogEntry> {
        return logEntries.filter { entry ->
            (level == null || entry.level == level.value) &&
            (tag == null || entry.tag.contains(tag, ignoreCase = true)) &&
            (message == null || entry.message.contains(message, ignoreCase = true))
        }.takeLast(limit)
    }
    
    /**
     * Set minimum log level
     */
    fun setMinLogLevel(level: LogLevel) {
        minLogLevel = level
        CrashHandler.logInfo(TAG, "📊 Min log level set to: ${level.name}")
    }
    
    /**
     * Add tag filter
     */
    fun addTagFilter(tag: String) {
        tagFilters.add(tag)
        CrashHandler.logInfo(TAG, "🏷️ Added tag filter: $tag")
    }
    
    /**
     * Add message filter
     */
    fun addMessageFilter(message: String) {
        messageFilters.add(message)
        CrashHandler.logInfo(TAG, "💬 Added message filter: $message")
    }
    
    /**
     * Clear all filters
     */
    fun clearFilters() {
        tagFilters.clear()
        messageFilters.clear()
        minLogLevel = LogLevel.DEBUG
        CrashHandler.logInfo(TAG, "🧹 All filters cleared")
    }
    
    /**
     * Clear all logs
     */
    fun clearLogs() {
        logEntries.clear()
        CrashHandler.logInfo(TAG, "🗑️ All logs cleared")
    }
    
    /**
     * Get listening status
     */
    fun isListening(): Boolean = isListening
    
    /**
     * Get log statistics
     */
    fun getLogStats(): Map<String, Int> {
        val logs = logEntries.toList()
        return mapOf(
            "total" to logs.size,
            "errors" to logs.count { it.level == "E" },
            "warnings" to logs.count { it.level == "W" },
            "info" to logs.count { it.level == "I" },
            "debug" to logs.count { it.level == "D" },
            "verbose" to logs.count { it.level == "V" }
        )
    }
}
