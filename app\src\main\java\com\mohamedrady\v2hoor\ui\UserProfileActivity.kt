package com.mohamedrady.v2hoor.ui

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.EditText
import androidx.lifecycle.lifecycleScope
import com.google.firebase.auth.EmailAuthProvider
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityUserProfileBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.service.FirebaseAuthService
import com.mohamedrady.v2hoor.service.SubscriptionService
import com.mohamedrady.v2hoor.service.ThemeManager
import com.mohamedrady.v2hoor.service.LanguageManager
import com.mohamedrady.v2hoor.service.UsageStatsService
import kotlinx.coroutines.launch

/**
 * User Profile Activity
 * Manages user profile information and settings
 */
class UserProfileActivity : BaseActivity() {
    
    private lateinit var binding: ActivityUserProfileBinding
    private lateinit var auth: FirebaseAuth
    private lateinit var authService: FirebaseAuthService
    private lateinit var subscriptionService: SubscriptionService
    private lateinit var themeManager: ThemeManager
    private lateinit var languageManager: LanguageManager
    private lateinit var usageStatsService: UsageStatsService
    
    private var currentUser: FirebaseUser? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        com.mohamedrady.v2hoor.util.CrashHandler.logInfo("UserProfile", "👤 Starting UserProfileActivity")
        
        binding = ActivityUserProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Initialize services
        auth = FirebaseAuth.getInstance()
        authService = FirebaseAuthService.getInstance()
        subscriptionService = SubscriptionService.getInstance(this)
        themeManager = ThemeManager.getInstance(this)
        languageManager = LanguageManager.getInstance(this)
        usageStatsService = UsageStatsService.getInstance(this)
        
        currentUser = auth.currentUser
        
        setupToolbar()
        setupClickListeners()
        setupObservers()
        loadUserProfile()
        checkUserAuthentication()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.user_profile)
        }
    }
    
    private fun setupClickListeners() {
        // Edit profile
        binding.btnEditProfile.setOnClickListener {
            showEditProfileDialog()
        }
        
        // Change password
        binding.btnChangePassword.setOnClickListener {
            showChangePasswordDialog()
        }
        
        // Theme settings
        binding.btnThemeSettings.setOnClickListener {
            startActivity(Intent(this, ThemeSettingsActivity::class.java))
        }
        
        // Language settings
        binding.btnLanguageSettings.setOnClickListener {
            startActivity(Intent(this, LanguageSettingsActivity::class.java))
        }
        
        // Usage statistics
        binding.btnUsageStats.setOnClickListener {
            startActivity(Intent(this, UsageStatsActivity::class.java))
        }
        
        // Logout
        binding.btnLogout.setOnClickListener {
            showLogoutDialog()
        }
        
        // Delete account
        binding.btnDeleteAccount.setOnClickListener {
            showDeleteAccountDialog()
        }
        
        // Refresh profile
        binding.btnRefreshProfile.setOnClickListener {
            refreshUserProfile()
        }
    }
    
    private fun setupObservers() {
        // Observe subscription changes
        subscriptionService.currentSubscription.observe(this) { subscription ->
            updateSubscriptionDisplay(subscription)
        }
        
        // Observe theme changes
        themeManager.currentTheme.observe(this) { theme ->
            binding.tvCurrentTheme.text = "المظهر: ${themeManager.getThemeDisplayName(theme)}"
        }
        
        // Observe language changes
        languageManager.currentLanguage.observe(this) { language ->
            binding.tvCurrentLanguage.text = "اللغة: ${language.nativeName}"
        }
        
        // Observe usage stats
        usageStatsService.usageStats.observe(this) { stats ->
            updateUsageStatsDisplay(stats)
        }
    }
    
    private fun checkUserAuthentication() {
        if (currentUser == null) {
            toastError("المستخدم غير مسجل الدخول")
            finish()
            return
        }
    }
    
    private fun loadUserProfile() {
        currentUser?.let { user ->
            // Basic user info
            binding.tvUserName.text = user.displayName ?: "مستخدم غير معروف"
            binding.tvUserEmail.text = user.email ?: "بريد إلكتروني غير محدد"
            binding.tvUserId.text = "معرف المستخدم: ${user.uid.take(8)}..."
            
            // Account info
            binding.tvAccountCreated.text = "تاريخ الإنشاء: ${getFormattedDate(user.metadata?.creationTimestamp)}"
            binding.tvLastSignIn.text = "آخر دخول: ${getFormattedDate(user.metadata?.lastSignInTimestamp)}"
            binding.tvEmailVerified.text = "البريد مؤكد: ${if (user.isEmailVerified) "نعم" else "لا"}"
            
            // Load additional data
            loadSubscriptionInfo()
            loadUsageStats()
        }
    }
    
    private fun loadSubscriptionInfo() {
        lifecycleScope.launch {
            try {
                val result = subscriptionService.initialize()
                if (result.isSuccess) {
                    val subscription = result.getOrNull()
                    updateSubscriptionDisplay(subscription)
                }
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserProfile", "Failed to load subscription", e)
            }
        }
    }
    
    private fun loadUsageStats() {
        lifecycleScope.launch {
            try {
                usageStatsService.loadUsageStats()
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserProfile", "Failed to load usage stats", e)
            }
        }
    }
    
    private fun updateSubscriptionDisplay(subscription: com.mohamedrady.v2hoor.model.SubscriptionInfo?) {
        if (subscription != null) {
            binding.tvSubscriptionStatus.text = "حالة الاشتراك: ${subscription.getStatusMessage()}"
            binding.tvSubscriptionEnd.text = "انتهاء الاشتراك: ${subscription.getFormattedEndDate()}"
            binding.tvSubscriptionType.text = "نوع الاشتراك: ${subscription.subscriptionType}"
            
            // Show subscription card
            binding.cardSubscription.visibility = View.VISIBLE
        } else {
            binding.cardSubscription.visibility = View.GONE
        }
    }
    
    private fun updateUsageStatsDisplay(stats: UsageStatsService.UsageStats) {
        binding.tvTotalSessions.text = "إجمالي الجلسات: ${stats.totalSessions}"
        binding.tvTotalDuration.text = "إجمالي الوقت: ${formatDuration(stats.totalDuration)}"
        binding.tvTotalDataUsage.text = "إجمالي البيانات: ${formatBytes(stats.totalDataUsage)}"
        binding.tvTodaySessions.text = "جلسات اليوم: ${stats.todaySessions}"
    }
    
    private fun showEditProfileDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_edit_profile, null)
        val etName = dialogView.findViewById<EditText>(R.id.et_name)
        val etEmail = dialogView.findViewById<EditText>(R.id.et_email)
        
        // Pre-fill current values
        etName.setText(currentUser?.displayName ?: "")
        etEmail.setText(currentUser?.email ?: "")
        
        AlertDialog.Builder(this)
            .setTitle("تعديل الملف الشخصي")
            .setView(dialogView)
            .setPositiveButton("حفظ") { _, _ ->
                val newName = etName.text.toString().trim()
                val newEmail = etEmail.text.toString().trim()
                updateProfile(newName, newEmail)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showChangePasswordDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_change_password, null)
        val etCurrentPassword = dialogView.findViewById<EditText>(R.id.et_current_password)
        val etNewPassword = dialogView.findViewById<EditText>(R.id.et_new_password)
        val etConfirmPassword = dialogView.findViewById<EditText>(R.id.et_confirm_password)
        
        AlertDialog.Builder(this)
            .setTitle("تغيير كلمة المرور")
            .setView(dialogView)
            .setPositiveButton("تغيير") { _, _ ->
                val currentPassword = etCurrentPassword.text.toString()
                val newPassword = etNewPassword.text.toString()
                val confirmPassword = etConfirmPassword.text.toString()
                
                if (newPassword == confirmPassword) {
                    changePassword(currentPassword, newPassword)
                } else {
                    toastError("كلمات المرور غير متطابقة")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showLogoutDialog() {
        AlertDialog.Builder(this)
            .setTitle("تسجيل الخروج")
            .setMessage("هل أنت متأكد من تسجيل الخروج؟")
            .setPositiveButton("تسجيل الخروج") { _, _ ->
                logout()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showDeleteAccountDialog() {
        AlertDialog.Builder(this)
            .setTitle("حذف الحساب")
            .setMessage("تحذير: سيتم حذف حسابك وجميع بياناتك نهائياً. هذا الإجراء لا يمكن التراجع عنه.")
            .setPositiveButton("حذف") { _, _ ->
                showDeleteConfirmationDialog()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun showDeleteConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_delete_account_confirmation, null)
        val etPassword = dialogView.findViewById<EditText>(R.id.et_password_confirmation)
        
        AlertDialog.Builder(this)
            .setTitle("تأكيد حذف الحساب")
            .setMessage("أدخل كلمة المرور لتأكيد حذف الحساب")
            .setView(dialogView)
            .setPositiveButton("حذف نهائياً") { _, _ ->
                val password = etPassword.text.toString()
                deleteAccount(password)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun updateProfile(name: String, email: String) {
        lifecycleScope.launch {
            try {
                binding.progressBar.visibility = View.VISIBLE
                
                // Update display name
                if (name != currentUser?.displayName) {
                    val profileUpdates = com.google.firebase.auth.UserProfileChangeRequest.Builder()
                        .setDisplayName(name)
                        .build()
                    
                    currentUser?.updateProfile(profileUpdates)
                }
                
                // Update email if changed
                if (email != currentUser?.email) {
                    currentUser?.updateEmail(email)
                }
                
                toastSuccess("تم تحديث الملف الشخصي بنجاح")
                loadUserProfile()
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserProfile", "Failed to update profile", e)
                toastError("فشل في تحديث الملف الشخصي")
            } finally {
                binding.progressBar.visibility = View.GONE
            }
        }
    }
    
    private fun changePassword(currentPassword: String, newPassword: String) {
        lifecycleScope.launch {
            try {
                binding.progressBar.visibility = View.VISIBLE
                
                // Re-authenticate user
                val email = currentUser?.email ?: return@launch
                val credential = EmailAuthProvider.getCredential(email, currentPassword)
                
                currentUser?.reauthenticate(credential)?.addOnCompleteListener { reauthTask ->
                    if (reauthTask.isSuccessful) {
                        // Update password
                        currentUser?.updatePassword(newPassword)?.addOnCompleteListener { updateTask ->
                            binding.progressBar.visibility = View.GONE
                            if (updateTask.isSuccessful) {
                                toastSuccess("تم تغيير كلمة المرور بنجاح")
                            } else {
                                toastError("فشل في تغيير كلمة المرور")
                            }
                        }
                    } else {
                        binding.progressBar.visibility = View.GONE
                        toastError("كلمة المرور الحالية غير صحيحة")
                    }
                }
            } catch (e: Exception) {
                binding.progressBar.visibility = View.GONE
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserProfile", "Failed to change password", e)
                toastError("فشل في تغيير كلمة المرور")
            }
        }
    }
    
    private fun logout() {
        lifecycleScope.launch {
            try {
                // End any active usage session
                usageStatsService.getCurrentSession()?.let { session ->
                    usageStatsService.endSession(session.sessionId, "logout")
                }
                
                // Sign out from Firebase
                auth.signOut()
                
                // Clear local data
                clearLocalData()
                
                // Navigate to login
                val intent = Intent(this@UserProfileActivity, LoginActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
                finish()
                
                toastSuccess("تم تسجيل الخروج بنجاح")
            } catch (e: Exception) {
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserProfile", "Failed to logout", e)
                toastError("فشل في تسجيل الخروج")
            }
        }
    }
    
    private fun deleteAccount(password: String) {
        lifecycleScope.launch {
            try {
                binding.progressBar.visibility = View.VISIBLE
                
                // Re-authenticate user
                val email = currentUser?.email ?: return@launch
                val credential = EmailAuthProvider.getCredential(email, password)
                
                currentUser?.reauthenticate(credential)?.addOnCompleteListener { reauthTask ->
                    if (reauthTask.isSuccessful) {
                        // Delete user account
                        currentUser?.delete()?.addOnCompleteListener { deleteTask ->
                            binding.progressBar.visibility = View.GONE
                            if (deleteTask.isSuccessful) {
                                // Clear local data
                                clearLocalData()
                                
                                // Navigate to login
                                val intent = Intent(this@UserProfileActivity, LoginActivity::class.java)
                                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                                startActivity(intent)
                                finish()
                                
                                toastSuccess("تم حذف الحساب بنجاح")
                            } else {
                                toastError("فشل في حذف الحساب")
                            }
                        }
                    } else {
                        binding.progressBar.visibility = View.GONE
                        toastError("كلمة المرور غير صحيحة")
                    }
                }
            } catch (e: Exception) {
                binding.progressBar.visibility = View.GONE
                com.mohamedrady.v2hoor.util.CrashHandler.logError("UserProfile", "Failed to delete account", e)
                toastError("فشل في حذف الحساب")
            }
        }
    }
    
    private fun refreshUserProfile() {
        currentUser?.reload()?.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                loadUserProfile()
                toastSuccess("تم تحديث الملف الشخصي")
            } else {
                toastError("فشل في تحديث الملف الشخصي")
            }
        }
    }
    
    private fun clearLocalData() {
        // Clear any cached data, preferences, etc.
        // This will be implemented based on your app's data storage
    }
    
    private fun getFormattedDate(timestamp: Long?): String {
        return if (timestamp != null && timestamp > 0) {
            val date = java.util.Date(timestamp)
            java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault()).format(date)
        } else {
            "غير محدد"
        }
    }
    
    private fun formatDuration(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        return when {
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m"
            else -> "${seconds}s"
        }
    }
    
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
