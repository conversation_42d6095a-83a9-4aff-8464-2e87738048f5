// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogConfigFilterBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final EditText etKeyword;

  @NonNull
  public final Spinner spSubscriptionId;

  private DialogConfigFilterBinding(@NonNull ScrollView rootView, @NonNull EditText etKeyword,
      @NonNull Spinner spSubscriptionId) {
    this.rootView = rootView;
    this.etKeyword = etKeyword;
    this.spSubscriptionId = spSubscriptionId;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogConfigFilterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogConfigFilterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_config_filter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogConfigFilterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_keyword;
      EditText etKeyword = ViewBindings.findChildViewById(rootView, id);
      if (etKeyword == null) {
        break missingId;
      }

      id = R.id.sp_subscriptionId;
      Spinner spSubscriptionId = ViewBindings.findChildViewById(rootView, id);
      if (spSubscriptionId == null) {
        break missingId;
      }

      return new DialogConfigFilterBinding((ScrollView) rootView, etKeyword, spSubscriptionId);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
