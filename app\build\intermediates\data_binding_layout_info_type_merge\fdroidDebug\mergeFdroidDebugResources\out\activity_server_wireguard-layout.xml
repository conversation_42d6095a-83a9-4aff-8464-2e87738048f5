<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_server_wireguard" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\activity_server_wireguard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_server_wireguard_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="12"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="144" endOffset="18"/></Target><Target tag="binding_1" include="layout_address_port"><Expressions/><location startLine="14" startOffset="8" endLine="14" endOffset="55"/></Target><Target id="@+id/et_id" view="EditText"><Expressions/><location startLine="27" startOffset="12" endLine="31" endOffset="42"/></Target><Target id="@+id/et_public_key" view="EditText"><Expressions/><location startLine="47" startOffset="12" endLine="51" endOffset="42"/></Target><Target id="@+id/et_preshared_key" view="EditText"><Expressions/><location startLine="67" startOffset="12" endLine="71" endOffset="42"/></Target><Target id="@+id/et_reserved1" view="EditText"><Expressions/><location startLine="91" startOffset="16" endLine="95" endOffset="46"/></Target><Target id="@+id/et_local_address" view="EditText"><Expressions/><location startLine="111" startOffset="12" endLine="115" endOffset="42"/></Target><Target id="@+id/et_local_mtu" view="EditText"><Expressions/><location startLine="129" startOffset="12" endLine="133" endOffset="44"/></Target></Targets></Layout>