<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_log_entry" modulePackage="com.mohamedrady.v2hoor" filePath="app\src\main\res\layout\item_log_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_log_entry_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="64" endOffset="14"/></Target><Target id="@+id/textViewTimestamp" view="TextView"><Expressions/><location startLine="11" startOffset="4" endLine="20" endOffset="33"/></Target><Target id="@+id/textViewLevel" view="TextView"><Expressions/><location startLine="23" startOffset="4" endLine="34" endOffset="34"/></Target><Target id="@+id/textViewTag" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="49" endOffset="35"/></Target><Target id="@+id/textViewMessage" view="TextView"><Expressions/><location startLine="52" startOffset="4" endLine="62" endOffset="33"/></Target></Targets></Layout>