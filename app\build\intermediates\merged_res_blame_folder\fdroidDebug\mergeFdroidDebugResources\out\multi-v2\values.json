{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-73:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e09aa481a2483f3d3bc09954c2742862\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2181", "startColumns": "4", "startOffsets": "136504", "endColumns": "42", "endOffsets": "136542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3301c563da9d289aac88c6ca3529e20c\\transformed\\recyclerview-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "697,1388,1389,1390,1399,1400,1401,2110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "31852,77317,77376,77424,78138,78213,78289,132796", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "31903,77371,77419,77475,78208,78284,78356,132857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\be0b7d272e566bfd154d1bf5d5ba0c43\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2105,2143,2186", "startColumns": "4,4,4", "startOffsets": "132564,134529,136754", "endColumns": "56,64,63", "endOffsets": "132616,134589,136813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\19bfc16374171ba7a26e435a1ff1fa3b\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "294,10161", "startColumns": "4,4", "startOffsets": "11766,697331", "endLines": "294,10163", "endColumns": "60,12", "endOffsets": "11822,697471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2302,2303", "startColumns": "4,4", "startOffsets": "144398,144480", "endColumns": "81,83", "endOffsets": "144475,144559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\cb2a86983ad6559594debb17aca21969\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2109,2130", "startColumns": "4,4", "startOffsets": "132742,133890", "endColumns": "53,66", "endOffsets": "132791,133952"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1398,2040,2041,2042,2058,2059,2060,2061", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "78091,128616,128668,128718,129654,129704,129752,129800", "endColumns": "46,51,49,49,49,47,47,47", "endOffsets": "78133,128663,128713,128763,129699,129747,129795,129843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\fbfe99676c41211a4bfd510557294d9b\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2136,2183", "startColumns": "4,4", "startOffsets": "134210,136590", "endColumns": "41,59", "endOffsets": "134247,136645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,850,851,852,853,855,856,857,858,859,860,865,866,928,929,930,931,937,938,939,940,944,945,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1197,1198,1199,1200,1201,1202,1203,1204,1211,1212,1214,1215,1216,1217,1219,1220,1221,1222,1225,1226,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1386,1387,1391,1392,1393,1394,1395,1396,1397,2050,2051,2052,2053,2054,2055,2056,2057,2099,2100,2101,2102,2108,2134,2135,2144,2178,2196,2197,2200,2201,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2597,2833,2834,2835,2836,2837,2884,2892,2893,2897,2901,2912,2917,2946,2953,2957,2961,2966,2970,2974,2978,2982,2986,2990,2996,3000,3006,3010,3016,3020,3025,3029,3032,3036,3042,3046,3052,3056,3062,3065,3069,3073,3077,3081,3085,3086,3087,3088,3091,3094,3097,3100,3104,3105,3106,3107,3148,3151,3153,3155,3157,3162,3163,3167,3173,3177,3178,3180,3192,3193,3197,3203,3207,3284,3285,3289,3316,3320,3321,3325,5127,5299,5325,5496,5522,5553,5561,5567,5583,5605,5610,5615,5625,5634,5643,5647,5654,5673,5680,5681,5690,5693,5696,5700,5704,5708,5711,5712,5717,5722,5732,5737,5744,5750,5751,5754,5758,5763,5765,5767,5770,5773,5775,5779,5782,5789,5792,5795,5799,5801,5805,5807,5809,5811,5815,5823,5831,5843,5849,5858,5861,5872,5875,5876,5881,5882,6382,6451,6525,6526,6536,6545,6701,6703,6707,6710,6713,6716,6719,6722,6725,6728,6732,6735,6738,6741,6745,6748,6752,6903,6904,6905,6906,6907,6908,6909,6910,6911,6912,6913,6914,6915,6916,6917,6918,6919,6920,6921,6922,6923,6925,6927,6928,6929,6930,6931,6932,6933,6934,6936,6937,6939,6940,6942,6944,6945,6947,6948,6949,6950,6951,6952,6954,6955,6956,6957,6958,7243,7245,7247,7249,7250,7251,7252,7253,7254,7255,7256,7257,7258,7259,7260,7261,7263,7264,7265,7266,7267,7268,7269,7271,7275,7458,7459,7460,7461,7462,7463,7467,7468,7469,8010,8012,8014,8016,8018,8020,8021,8022,8023,8025,8027,8029,8030,8031,8032,8033,8034,8035,8036,8037,8038,8039,8040,8043,8044,8045,8046,8048,8050,8051,8053,8054,8056,8058,8060,8061,8062,8063,8064,8065,8066,8067,8068,8069,8070,8071,8073,8074,8075,8076,8078,8079,8080,8081,8082,8084,8086,8088,8090,8091,8092,8093,8094,8095,8096,8097,8098,8099,8100,8101,8102,8103,8104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12942,17487,17792,36391,38574,38629,39086,39150,39220,39281,39356,39432,39509,39798,39883,39965,40041,40156,40233,40311,40417,40523,40602,40931,40988,45114,45188,45263,45328,45638,45698,45759,45831,46035,46102,59621,59680,59739,59798,59857,59916,59970,60024,60077,60131,60185,60239,64888,64962,65041,65114,65188,65259,65331,65403,65764,65821,65934,66007,66081,66155,66277,66349,66422,66492,66659,66719,67622,67691,67760,67830,67904,67980,68044,68121,68197,68274,68339,68408,68485,68560,68629,68697,68774,68840,68901,68998,69063,69132,69231,69302,69361,69419,69476,69535,69599,69670,69742,69814,69886,69958,70025,70093,70161,70220,70283,70347,70437,70528,70588,70654,70721,70787,70857,70921,70974,71041,71102,71169,71282,71340,71403,71468,71533,71608,71681,71753,71797,71844,71890,71939,72000,72061,72122,72184,72248,72312,72376,72441,72504,72564,72625,72691,72750,72810,72872,72943,73003,77144,77230,77480,77570,77657,77745,77827,77910,78000,129201,129253,129311,129356,129422,129486,129543,129600,132248,132305,132353,132402,132708,134114,134161,134594,136379,137267,137331,137521,137581,142297,142371,142441,142519,142573,142643,142728,142776,142822,142883,142946,143012,143076,143147,143210,143275,143339,143400,143461,143513,143586,143660,143729,143804,143878,143952,144093,176747,195479,195557,195647,195735,195831,198756,199338,199427,199674,199955,200621,200906,202715,203192,203414,203636,203912,204139,204369,204599,204829,205059,205286,205705,205931,206356,206586,207014,207233,207516,207724,207855,208082,208508,208733,209160,209381,209806,209926,210202,210503,210827,211118,211432,211569,211700,211805,212047,212214,212418,212626,212897,213009,213121,213226,215319,215533,215679,215819,215905,216253,216341,216587,217005,217254,217336,217434,218091,218191,218443,218867,219122,225012,225101,225338,227362,227604,227706,227959,363528,374209,375725,386420,387948,389705,390331,390751,392012,393277,393533,393769,394316,394810,395415,395613,396193,397561,397936,398054,398592,398749,398945,399218,399474,399644,399785,399849,400214,400581,401257,401521,401859,402212,402306,402492,402798,403060,403185,403312,403551,403762,403881,404074,404251,404706,404887,405009,405268,405381,405568,405670,405777,405906,406181,406689,407185,408062,408356,408926,409075,409807,409979,410063,410399,410491,442795,448026,453741,453803,454381,454965,463163,463276,463505,463665,463817,463988,464154,464323,464490,464653,464896,465066,465239,465410,465684,465883,466088,475586,475670,475766,475862,475960,476060,476162,476264,476366,476468,476570,476670,476766,476878,477007,477130,477261,477392,477490,477604,477698,477838,477972,478068,478180,478280,478396,478492,478604,478704,478844,478980,479144,479274,479432,479582,479723,479867,480002,480114,480264,480392,480520,480656,480788,480918,481048,481160,498818,498964,499108,499246,499312,499402,499478,499582,499672,499774,499882,499990,500090,500170,500262,500360,500470,500522,500600,500706,500798,500902,501012,501134,501297,515005,515085,515185,515275,515385,515475,515716,515810,515916,555795,555895,556007,556121,556237,556353,556447,556561,556673,556775,556895,557017,557099,557203,557323,557449,557547,557641,557729,557841,557957,558079,558191,558366,558482,558568,558660,558772,558896,558963,559089,559157,559285,559429,559557,559626,559721,559836,559949,560048,560157,560268,560379,560480,560585,560685,560815,560906,561029,561123,561235,561321,561425,561521,561609,561727,561831,561935,562061,562149,562257,562357,562447,562557,562641,562743,562827,562881,562945,563051,563137,563247,563331", "endLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,850,851,852,853,855,856,857,858,859,860,865,866,928,929,930,931,937,938,939,940,944,945,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1197,1198,1199,1200,1201,1202,1203,1204,1211,1212,1214,1215,1216,1217,1219,1220,1221,1222,1225,1226,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1386,1387,1391,1392,1393,1394,1395,1396,1397,2050,2051,2052,2053,2054,2055,2056,2057,2099,2100,2101,2102,2108,2134,2135,2144,2178,2196,2197,2200,2201,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2597,2833,2834,2835,2836,2837,2891,2892,2896,2900,2904,2916,2922,2952,2956,2960,2965,2969,2973,2977,2981,2985,2989,2995,2999,3005,3009,3015,3019,3024,3028,3031,3035,3041,3045,3051,3055,3061,3064,3068,3072,3076,3080,3084,3085,3086,3087,3090,3093,3096,3099,3103,3104,3105,3106,3107,3150,3152,3154,3156,3161,3162,3166,3172,3176,3177,3179,3191,3192,3196,3202,3206,3207,3284,3288,3315,3319,3320,3324,3352,5298,5324,5495,5521,5552,5560,5566,5582,5604,5609,5614,5624,5633,5642,5646,5653,5672,5679,5680,5689,5692,5695,5699,5703,5707,5710,5711,5716,5721,5731,5736,5743,5749,5750,5753,5757,5762,5764,5766,5769,5772,5774,5778,5781,5788,5791,5794,5798,5800,5804,5806,5808,5810,5814,5822,5830,5842,5848,5857,5860,5871,5874,5875,5880,5881,5886,6450,6520,6525,6535,6544,6545,6702,6706,6709,6712,6715,6718,6721,6724,6727,6731,6734,6737,6740,6744,6747,6751,6755,6903,6904,6905,6906,6907,6908,6909,6910,6911,6912,6913,6914,6915,6916,6917,6918,6919,6920,6921,6922,6924,6926,6927,6928,6929,6930,6931,6932,6933,6935,6936,6938,6939,6941,6943,6944,6946,6947,6948,6949,6950,6951,6953,6954,6955,6956,6957,6958,7244,7246,7248,7249,7250,7251,7252,7253,7254,7255,7256,7257,7258,7259,7260,7262,7263,7264,7265,7266,7267,7268,7270,7274,7278,7458,7459,7460,7461,7462,7466,7467,7468,7469,8011,8013,8015,8017,8019,8020,8021,8022,8024,8026,8028,8029,8030,8031,8032,8033,8034,8035,8036,8037,8038,8039,8042,8043,8044,8045,8047,8049,8050,8052,8053,8055,8057,8059,8060,8061,8062,8063,8064,8065,8066,8067,8068,8069,8070,8072,8073,8074,8075,8077,8078,8079,8080,8081,8083,8085,8087,8089,8090,8091,8092,8093,8094,8095,8096,8097,8098,8099,8100,8101,8102,8103,8104", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "12992,17527,17836,36427,38624,38686,39145,39215,39276,39351,39427,39504,39582,39878,39960,40036,40112,40228,40306,40412,40518,40597,40677,40983,41041,45183,45258,45323,45389,45693,45754,45826,45899,46097,46165,59675,59734,59793,59852,59911,59965,60019,60072,60126,60180,60234,60288,64957,65036,65109,65183,65254,65326,65398,65471,65816,65874,66002,66076,66150,66225,66344,66417,66487,66558,66714,66775,67686,67755,67825,67899,67975,68039,68116,68192,68269,68334,68403,68480,68555,68624,68692,68769,68835,68896,68993,69058,69127,69226,69297,69356,69414,69471,69530,69594,69665,69737,69809,69881,69953,70020,70088,70156,70215,70278,70342,70432,70523,70583,70649,70716,70782,70852,70916,70969,71036,71097,71164,71277,71335,71398,71463,71528,71603,71676,71748,71792,71839,71885,71934,71995,72056,72117,72179,72243,72307,72371,72436,72499,72559,72620,72686,72745,72805,72867,72938,72998,73066,77225,77312,77565,77652,77740,77822,77905,77995,78086,129248,129306,129351,129417,129481,129538,129595,129649,132300,132348,132397,132448,132737,134156,134205,134635,136406,137326,137388,137576,137633,142366,142436,142514,142568,142638,142723,142771,142817,142878,142941,143007,143071,143142,143205,143270,143334,143395,143456,143508,143581,143655,143724,143799,143873,143947,144088,144158,176795,195552,195642,195730,195826,195916,199333,199422,199669,199950,200202,200901,201294,203187,203409,203631,203907,204134,204364,204594,204824,205054,205281,205700,205926,206351,206581,207009,207228,207511,207719,207850,208077,208503,208728,209155,209376,209801,209921,210197,210498,210822,211113,211427,211564,211695,211800,212042,212209,212413,212621,212892,213004,213116,213221,213338,215528,215674,215814,215900,216248,216336,216582,217000,217249,217331,217429,218086,218186,218438,218862,219117,219211,225096,225333,227357,227599,227701,227954,230110,374204,375720,386415,387943,389700,390326,390746,392007,393272,393528,393764,394311,394805,395410,395608,396188,397556,397931,398049,398587,398744,398940,399213,399469,399639,399780,399844,400209,400576,401252,401516,401854,402207,402301,402487,402793,403055,403180,403307,403546,403757,403876,404069,404246,404701,404882,405004,405263,405376,405563,405665,405772,405901,406176,406684,407180,408057,408351,408921,409070,409802,409974,410058,410394,410486,410764,448021,453392,453798,454376,454960,455051,463271,463500,463660,463812,463983,464149,464318,464485,464648,464891,465061,465234,465405,465679,465878,466083,466413,475665,475761,475857,475955,476055,476157,476259,476361,476463,476565,476665,476761,476873,477002,477125,477256,477387,477485,477599,477693,477833,477967,478063,478175,478275,478391,478487,478599,478699,478839,478975,479139,479269,479427,479577,479718,479862,479997,480109,480259,480387,480515,480651,480783,480913,481043,481155,481295,498959,499103,499241,499307,499397,499473,499577,499667,499769,499877,499985,500085,500165,500257,500355,500465,500517,500595,500701,500793,500897,501007,501129,501292,501449,515080,515180,515270,515380,515470,515711,515805,515911,516003,555890,556002,556116,556232,556348,556442,556556,556668,556770,556890,557012,557094,557198,557318,557444,557542,557636,557724,557836,557952,558074,558186,558361,558477,558563,558655,558767,558891,558958,559084,559152,559280,559424,559552,559621,559716,559831,559944,560043,560152,560263,560374,560475,560580,560680,560810,560901,561024,561118,561230,561316,561420,561516,561604,561722,561826,561930,562056,562144,562252,562352,562442,562552,562636,562738,562822,562876,562940,563046,563132,563242,563326,563446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "659,847,848,867,868,1191,1192,1330,1331,1332,1333,1334,1335,1336,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2111,2112,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2169,2266,2318,2319,2320,2321,2322,2323,2324,2671,6959,6960,6964,6965,6969,8105,8106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30242,39587,39659,41046,41111,64545,64614,73504,73574,73642,73714,73784,73845,73919,127638,127699,127760,127822,127886,127948,128009,128077,128177,128237,128303,128376,128445,128502,128554,130125,130197,130273,130338,130397,130456,130516,130576,130636,130696,130756,130816,130876,130936,130996,131056,131115,131175,131235,131295,131355,131415,131475,131535,131595,131655,131715,131774,131834,131894,131953,132012,132071,132130,132189,132862,132897,134640,134695,134758,134813,134871,134927,134985,135046,135109,135166,135217,135275,135325,135386,135443,135509,135543,135925,141907,146304,146371,146443,146512,146581,146655,146727,181982,481300,481417,481618,481728,481929,563451,563523", "endLines": "659,847,848,867,868,1191,1192,1330,1331,1332,1333,1334,1335,1336,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2111,2112,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2169,2266,2318,2319,2320,2321,2322,2323,2324,2671,6959,6963,6964,6968,6969,8105,8106", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "30297,39654,39742,41106,41172,64609,64672,73569,73637,73709,73779,73840,73914,73987,127694,127755,127817,127881,127943,128004,128072,128172,128232,128298,128371,128440,128497,128549,128611,130192,130268,130333,130392,130451,130511,130571,130631,130691,130751,130811,130871,130931,130991,131051,131110,131170,131230,131290,131350,131410,131470,131530,131590,131650,131710,131769,131829,131889,131948,132007,132066,132125,132184,132243,132892,132927,134690,134753,134808,134866,134922,134980,135041,135104,135161,135212,135270,135320,135381,135438,135504,135538,135573,135955,141972,146366,146438,146507,146576,146650,146722,146810,182048,481412,481613,481723,481924,482053,563518,563585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2204,2337", "startColumns": "4,4", "startOffsets": "137773,148232", "endColumns": "67,166", "endOffsets": "137836,148394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\97daf22df7e5c6e3b5afccf9cf12b867\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,870,871,872,873,1326,1327,1328,2905,6280,6282,6285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7641,41227,41288,41350,41412,73279,73338,73395,200207,436053,436117,436243", "endLines": "220,870,871,872,873,1326,1327,1328,2911,6281,6284,6287", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "7688,41283,41345,41407,41471,73333,73390,73444,200616,436112,436238,436366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\700cabc0e517e16b3b1a2efd32cecc35\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "727,732,733,734,2103", "startColumns": "4,4,4,4,4", "startOffsets": "33364,33543,33603,33655,132453", "endLines": "731,732,733,734,2103", "endColumns": "11,59,51,44,59", "endOffsets": "33538,33598,33650,33695,132508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c5c1f58a58ca000404c5b280db793ba2\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2106,2107,2129,2138,2139,2170,2171,2172,2173,2174,2175,2176,2177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "132621,132661,133847,134295,134350,135960,136005,136059,136115,136167,136219,136268,136329", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "132656,132703,133885,134345,134392,136000,136054,136110,136162,136214,136263,136324,136374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b67b39daef2398884ed6af353040af9d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2304", "startColumns": "4", "startOffsets": "144564", "endColumns": "82", "endOffsets": "144642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\07e10290a8de075a2af92b4127d3fcd4\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "187,194,196,202,203,211,213,214,221,227,229,230,231,232,233,290,291,292,293,295,299,300,301,304,314,324,352,353,358,359,364,369,370,371,376,377,382,383,388,389,390,396,397,398,403,409,410,428,429,435,436,437,438,441,444,447,448,451,454,455,456,457,458,461,464,465,466,467,473,478,481,484,485,486,491,492,493,496,499,500,503,506,509,512,513,514,517,520,521,526,527,533,538,541,544,545,546,547,548,549,550,551,552,553,554,555,571,652,653,654,655,660,667,675,676,677,680,685,687,695,696,725,740,777,778,782,783,794,795,796,802,805,811,815,816,817,818,819,828,2116,2180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6151,6435,6547,6782,6843,7134,7241,7291,7693,8000,8120,8175,8235,8300,8359,11545,11597,11658,11720,11827,11960,12012,12062,12223,12630,13053,14995,15054,15251,15308,15503,15684,15738,15795,15987,16045,16241,16297,16491,16548,16599,16821,16873,16928,17118,17334,17384,18285,18341,18547,18608,18668,18738,18871,19002,19130,19198,19327,19453,19515,19578,19646,19713,19836,19961,20028,20093,20158,20447,20628,20749,20870,20936,21003,21213,21282,21348,21473,21599,21666,21792,21919,22044,22171,22227,22292,22418,22541,22606,22814,22881,23169,23349,23469,23589,23654,23716,23778,23842,23904,23963,24023,24084,24145,24204,24264,24924,29867,29918,29967,30015,30302,30594,30902,30949,31009,31115,31295,31407,31742,31796,33268,33951,36281,36332,36541,36593,37029,37088,37142,37380,37558,37760,37899,37945,38000,38045,38089,38437,133081,136459", "endLines": "193,194,200,202,210,211,213,214,221,227,229,230,231,232,233,290,291,292,293,298,299,300,301,313,321,324,352,357,358,363,368,369,370,375,376,381,382,387,388,389,395,396,397,402,408,409,410,428,434,435,436,437,440,443,446,447,450,453,454,455,456,457,460,463,464,465,466,472,477,480,483,484,485,490,491,492,495,498,499,502,505,508,511,512,513,516,519,520,525,526,532,537,540,543,544,545,546,547,548,549,550,551,552,553,554,570,576,652,653,654,655,666,674,675,676,679,684,685,694,695,696,725,740,777,778,782,791,794,795,801,802,810,814,815,816,817,818,827,831,2116,2180", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "6430,6486,6728,6838,7129,7181,7286,7339,7736,8046,8170,8230,8295,8354,8416,11592,11653,11715,11761,11955,12007,12057,12108,12625,12937,13093,15049,15246,15303,15498,15679,15733,15790,15982,16040,16236,16292,16486,16543,16594,16816,16868,16923,17113,17329,17379,17431,18336,18542,18603,18663,18733,18866,18997,19125,19193,19322,19448,19510,19573,19641,19708,19831,19956,20023,20088,20153,20442,20623,20744,20865,20931,20998,21208,21277,21343,21468,21594,21661,21787,21914,22039,22166,22222,22287,22413,22536,22601,22809,22876,23164,23344,23464,23584,23649,23711,23773,23837,23899,23958,24018,24079,24140,24199,24259,24919,25170,29913,29962,30010,30068,30589,30897,30944,31004,31110,31290,31344,31737,31791,31847,33309,33993,36327,36386,36588,36918,37083,37137,37375,37430,37755,37894,37940,37995,38040,38084,38432,38569,133117,136499"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\generated\\res\\processFdroidDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2358,2381,2382,2383,2384,2386,2568", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "150320,152154,152236,152340,152449,152641,174441", "endColumns": "143,81,103,108,119,106,75", "endOffsets": "150459,152231,152335,152444,152564,152743,174512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\59e516bb99fd3e6425d2c9627b07a4fa\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7450", "startColumns": "4", "startOffsets": "514595", "endLines": "7457", "endColumns": "8", "endOffsets": "515000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1206,1207,1208,1209,2571,2572,6697", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "65517,65575,65622,65678,174649,174710,462912", "endLines": "1206,1207,1208,1209,2571,2572,6700", "endColumns": "57,46,55,47,60,61,10", "endOffsets": "65570,65617,65673,65721,174705,174767,463158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ebcc5988bd502c901a17c4d3fc1eb859\\transformed\\zxing-android-embedded-4.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,2187,2188,2189,2190,2191,2192,2193,2194,2195,2308,2359,2393,2394,2395,2396,2397,2398,2399,2400,2401,2829,2830,2831,2832,10164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "67016,67088,67149,67213,67278,67343,67397,67451,67505,67564,136818,136865,136914,136962,137004,137053,137105,137163,137213,144868,150464,153338,153435,153552,153648,153809,153922,154008,154155,154254,195116,195175,195222,195366,697476", "endLines": "1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,2187,2188,2189,2190,2191,2192,2193,2194,2195,2308,2359,2393,2394,2395,2396,2397,2398,2399,2400,2401,2829,2830,2831,2832,10165", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10", "endOffsets": "67083,67144,67208,67273,67338,67392,67446,67500,67559,67617,136860,136909,136957,136999,137048,137100,137158,137208,137262,144929,150529,153430,153547,153643,153804,153917,154003,154150,154249,154396,195170,195217,195361,195474,697571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f68ab7854e90816622d4e41b6bdf2b2c\\transformed\\camera-view-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "415,699", "startColumns": "4,4", "startOffsets": "17640,31954", "endLines": "418,706", "endColumns": "11,11", "endOffsets": "17787,32256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "834,1194,2043,2044,2045,2046,2047,2048,2049,2131,2132,2133,2263,2264,2356,2372,2552,2565,2681,2826,2827,6260,6546,6549,6555,6561,6564,6570,6574,6577,6584,6590,6593,6599,6604,6609,6616,6618,6624,6630,6638,6643,6650,6655,6661,6665,6672,6676,6682,6688,6691,6695,6696", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "38691,64718,128768,128832,128887,128955,129022,129087,129144,133957,134005,134053,141724,141787,150214,151361,172976,174301,182722,194954,195004,434487,455056,455161,455406,455744,455890,456230,456442,456605,457012,457350,457473,457812,458051,458308,458679,458739,459077,459363,459812,460104,460492,460797,461141,461386,461716,461923,462191,462464,462608,462809,462856", "endLines": "834,1194,2043,2044,2045,2046,2047,2048,2049,2131,2132,2133,2263,2264,2356,2372,2552,2567,2681,2826,2827,6276,6548,6554,6560,6563,6569,6573,6576,6583,6589,6592,6598,6603,6608,6615,6617,6623,6629,6637,6642,6649,6654,6660,6664,6671,6675,6681,6687,6690,6694,6695,6696", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "38759,64782,128827,128882,128950,129017,129082,129139,129196,134000,134048,134109,141782,141845,150247,151413,173015,174436,182856,194999,195047,435920,455156,455401,455739,455885,456225,456437,456600,457007,457345,457468,457807,458046,458303,458674,458734,459072,459358,459807,460099,460487,460792,461136,461381,461711,461918,462186,462459,462603,462804,462851,462907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3671adc343b59afebe99d3ec6265d6d7\\transformed\\Toasty-1.5.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "892,934,948,1190,1218,1228,2803", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "42640,45496,46282,64499,66230,66828,193227", "endColumns": "50,44,43,45,46,46,55", "endOffsets": "42686,45536,46321,64540,66272,66870,193278"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,7,14,19,25,34,38,42,50,55,64,69,78,89,94,117,130,139,152,156,166,176,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,301,517,689,878,1163,1298,1445,1702,1845,2129,2297,2557,2919,3102,3896,4404,4681,5058,5182,5484,5796,5953", "endLines": "6,13,18,24,33,37,41,45,54,58,68,77,82,93,100,129,138,151,155,160,170,180,186", "endColumns": "19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19", "endOffsets": "296,512,684,873,1158,1293,1440,1581,1840,1975,2292,2552,2714,3097,3324,4399,4676,5053,5177,5331,5635,5948,6146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d70395873c50a004c47266b87418baa0\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2104", "startColumns": "4", "startOffsets": "132513", "endColumns": "50", "endOffsets": "132559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\8db806c88c82d97fba3e067706ad47ec\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "835,836,837,839", "startColumns": "4,4,4,4", "startOffsets": "38764,38829,38899,39025", "endColumns": "64,69,63,60", "endOffsets": "38824,38894,38958,39081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\aaec5b52f0b87a27b87b977c567045d7\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2184", "startColumns": "4", "startOffsets": "136650", "endColumns": "53", "endOffsets": "136699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c588be67285252bb290c6b2c2549687a\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2185", "startColumns": "4", "startOffsets": "136704", "endColumns": "49", "endOffsets": "136749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f7eb1da5e42044f662b8e59e06978dfd\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7301", "startColumns": "4", "startOffsets": "502596", "endLines": "7304", "endColumns": "12", "endOffsets": "502814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\43f80c422e65d725a8b34fc24966139b\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "323,326,1337", "startColumns": "4,4,4", "startOffsets": "12997,13161,73992", "endColumns": "55,47,51", "endOffsets": "13048,13204,74039"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6900", "startColumns": "4", "startOffsets": "475454", "endLines": "6902", "endColumns": "12", "endOffsets": "475581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "861,862,863,864,1324,1325,2357,2376,2377,2378", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40682,40740,40806,40869,73136,73207,150252,151821,151888,151967", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "40735,40801,40864,40926,73202,73274,150315,151883,151962,152031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "882,883,884,885,886,887,888,889,2329,2330,2331,2332,2333,2334,2335,2336,2338,2339,2340,2341,2342,2343,2344,2345,2346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41864,41954,42034,42124,42214,42294,42375,42455,147192,147297,147478,147603,147710,147890,148013,148129,148399,148587,148692,148873,148998,149173,149321,149384,149446", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "41949,42029,42119,42209,42289,42370,42450,42530,147292,147473,147598,147705,147885,148008,148124,148227,148582,148687,148868,148993,149168,149316,149379,149441,149520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3fac676b4e1c0ee2b6233dfa300c1ec0\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2182", "startColumns": "4", "startOffsets": "136547", "endColumns": "42", "endOffsets": "136585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,717,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1323,1329,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2062,2063,2113,2114,2115,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2137,2140,2141,2142,2162,2163,2164,2165,2166,2167,2168,2179,2198,2199,2202,2203,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2265,2267,2310,2312,2313,2314,2315,2316,2317,2325,2326,2327,2328,2364,2368,2373,2374,2375,2388,2389,2392,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2494,2497,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2558,2559,2560,2561,2562,2598,2601,2602,2668,2669,2838,2842,2846,2850,2854,2855,2923,2931,2938,3108,3111,3121,3130,3139,3208,3209,3210,3211,3217,3218,3219,3220,3221,3222,3228,3229,3230,3231,3232,3237,3238,3242,3243,3249,3253,3254,3255,3256,3266,3267,3268,3272,3273,3279,3283,3353,3356,3357,3362,3363,3366,3367,3368,3369,3633,3640,3901,3907,4171,4178,4439,4445,4508,4590,4642,4724,4786,4868,4932,4984,5066,5074,5080,5091,5095,5099,5112,5887,5903,5910,5916,5933,5946,5966,5983,5992,5997,6004,6024,6037,6054,6060,6066,6073,6077,6083,6097,6100,6110,6111,6112,6160,6164,6168,6172,6173,6174,6177,6193,6200,6214,6259,6288,6294,6298,6302,6307,6314,6320,6321,6324,6328,6333,6346,6350,6355,6360,6365,6368,6371,6374,6378,6521,6522,6523,6524,6756,6757,6758,6759,6760,6761,6762,6763,6764,6765,6766,6767,6768,6769,6770,6771,6772,6773,6774,6778,6782,6786,6790,6794,6798,6802,6803,6804,6805,6806,6807,6808,6809,6813,6817,6818,6822,6823,6826,6830,6833,6836,6839,6843,6846,6849,6853,6857,6861,6865,6868,6869,6870,6871,6874,6878,6881,6884,6887,6890,6893,6896,6970,6973,6974,6977,6980,6981,6984,6985,6986,6990,6991,6996,7003,7010,7017,7024,7031,7038,7045,7052,7059,7068,7077,7086,7093,7102,7111,7114,7117,7118,7119,7120,7121,7122,7123,7124,7125,7126,7127,7128,7129,7133,7138,7143,7146,7147,7148,7149,7150,7158,7166,7167,7175,7179,7187,7195,7203,7211,7219,7220,7228,7236,7237,7240,7279,7281,7286,7288,7293,7297,7305,7306,7307,7308,7312,7316,7317,7321,7322,7323,7324,7325,7326,7327,7328,7329,7330,7331,7335,7336,7337,7338,7342,7343,7344,7345,7349,7353,7354,7358,7359,7360,7365,7366,7367,7368,7369,7370,7371,7372,7373,7374,7375,7376,7377,7378,7379,7380,7381,7382,7383,7384,7385,7389,7390,7391,7397,7398,7402,7404,7405,7410,7411,7412,7413,7414,7415,7419,7420,7421,7427,7428,7432,7434,7438,7442,7446,7470,7471,7472,7473,7476,7479,7482,7485,7488,7493,7497,7500,7501,7506,7510,7515,7521,7527,7532,7536,7541,7545,7549,7590,7591,7592,7593,7594,7598,7599,7600,7601,7605,7609,7613,7617,7621,7625,7629,7633,7639,7640,7681,7695,7700,7726,7733,7736,7747,7752,7755,7758,7813,7819,7820,7823,7826,7829,7832,7835,7838,7841,7845,7848,7849,7850,7858,7866,7869,7874,7879,7884,7889,7893,7897,7898,7906,7907,7908,7909,7910,7918,7923,7928,7929,7930,7931,7956,7962,7967,7970,7974,7977,7981,7991,7994,7999,8002,8006,8107,8115,8129,8142,8146,8161,8172,8175,8186,8191,8195,8230,8231,8232,8244,8252,8260,8268,8276,8296,8299,8326,8331,8351,8354,8357,8364,8377,8386,8389,8409,8419,8423,8427,8440,8444,8448,8452,8458,8462,8479,8487,8491,8495,8499,8502,8506,8510,8514,8524,8531,8538,8542,8568,8578,8603,8612,8632,8642,8646,8656,8681,8691,8694,8701,8708,8715,8716,8717,8718,8719,8726,8730,8736,8742,8743,8756,8757,8758,8761,8764,8767,8770,8773,8776,8779,8782,8785,8788,8791,8794,8797,8800,8803,8806,8809,8812,8815,8818,8821,8822,8830,8838,8839,8852,8862,8866,8871,8876,8880,8883,8887,8891,8894,8898,8901,8905,8910,8915,8918,8925,8929,8933,8942,8947,8952,8953,8957,8960,8964,8977,8982,8990,8994,8998,9015,9019,9024,9042,9049,9053,9083,9086,9089,9092,9095,9098,9101,9120,9126,9134,9141,9153,9161,9166,9174,9178,9196,9203,9219,9223,9231,9234,9239,9240,9241,9242,9246,9250,9254,9258,9293,9296,9300,9304,9338,9341,9345,9349,9358,9364,9367,9377,9381,9382,9389,9393,9400,9401,9402,9405,9410,9415,9416,9420,9435,9454,9458,9459,9471,9481,9482,9494,9499,9523,9526,9532,9535,9544,9552,9556,9559,9562,9565,9569,9572,9589,9593,9596,9611,9614,9622,9627,9634,9639,9640,9645,9646,9652,9658,9664,9696,9707,9724,9731,9735,9738,9751,9760,9764,9769,9773,9777,9781,9785,9789,9793,9797,9802,9805,9817,9822,9831,9834,9841,9842,9846,9855,9861,9865,9866,9870,9891,9897,9901,9905,9906,9924,9925,9926,9927,9928,9933,9936,9937,9943,9944,9956,9968,9975,9976,9981,9986,9987,9991,10005,10010,10016,10022,10028,10033,10039,10045,10046,10052,10067,10072,10081,10090,10093,10107,10112,10123,10127,10136,10145,10146,10153,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197,15198,15199,15200,15201,15202,15203,15204,15205,15206,15207,15208,15209,15210,15211,15212,15213,15214,15215,15216,15217,15218,15219,15220,15221,15222,15223,15224,15225,15226,15227,15228,15229,15230,15231,15232,15233,15234,15235,15236,15237,15238,15239,15240,15241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6491,6733,7186,7344,7400,7460,7521,7586,7741,7791,7841,7894,7952,8051,8421,8469,8540,8612,8684,8757,8824,8873,8927,8964,9015,9075,9122,9178,9227,9285,9339,9400,9456,9507,9567,9623,9686,9735,9791,9847,9897,9956,10011,10073,10120,10174,10230,10282,10337,10391,10445,10499,10548,10606,10660,10717,10773,10820,10873,10929,10989,11052,11111,11173,11223,11277,11331,11379,11436,11489,12113,12167,13098,13209,13271,13327,13387,13440,13501,13580,13661,13733,13812,13892,13968,14046,14115,14191,14268,14339,14412,14488,14566,14635,14711,14788,14852,14923,17436,17532,17585,17841,17908,17961,18013,18063,18121,18186,18234,25175,25242,25308,25366,25435,25493,25562,25632,25705,25779,25847,25914,25984,26050,26123,26183,26259,26319,26379,26454,26522,26588,26656,26716,26775,26832,26898,26960,27017,27085,27158,27228,27290,27351,27419,27481,27551,27620,27676,27735,27797,27859,27926,27983,28044,28105,28166,28227,28283,28339,28395,28451,28509,28567,28625,28683,28740,28797,28854,28911,28970,29029,29087,29170,29253,29326,29380,29449,29505,29586,29667,29738,30073,30126,30184,31349,31908,32261,32321,32375,32445,32515,32580,32646,32711,32779,32848,32916,33046,33099,33158,33216,33314,33700,33752,33798,33848,33904,33998,34056,34114,34176,34239,34301,34360,34420,34485,34551,34616,34678,34740,34802,34864,34926,34988,35054,35121,35187,35250,35314,35377,35445,35506,35568,35630,35693,35757,35820,35884,35962,36021,36087,36167,36228,36432,36490,36923,36968,37435,37499,38963,42691,42765,42836,42902,42976,43045,43116,43189,43260,43328,43401,43477,43547,43625,43693,43759,43820,43889,43953,44019,44087,44153,44216,44284,44355,44420,44493,44556,44637,44701,44767,44837,44907,44977,45047,46528,46585,46643,46702,46762,46821,46880,46939,46998,47057,47116,47175,47234,47293,47352,47412,47473,47535,47596,47657,47718,47779,47840,47901,47961,48022,48083,48143,48204,48265,48326,48387,48448,48509,48570,48631,48692,48753,48814,48882,48951,49021,49090,49159,49228,49297,49366,49435,49504,49573,49642,49711,49771,49832,49894,49955,50016,50077,50138,50199,50260,50321,50382,50443,50504,50566,50629,50693,50756,50819,50882,50945,51008,51071,51134,51197,51260,51323,51384,51446,51509,51571,51633,51695,51757,51819,51881,51943,52005,52067,52129,52186,52272,52352,52442,52537,52629,52721,52811,52894,52987,53074,53171,53262,53363,53450,53553,53642,53741,53833,53933,54017,54111,54199,54297,54380,54471,54565,54664,54766,54864,54964,55051,55151,55237,55333,55421,55502,55593,55689,55782,55875,55966,56051,56145,56234,56332,56425,56527,56615,56719,56810,56910,57003,57104,57189,57284,57373,57472,57557,57649,57744,57844,57947,58046,58149,58238,58339,58426,58523,58611,58707,58799,58899,58989,59087,59172,59261,59350,59443,59530,60293,60359,60435,60504,60583,60656,60736,60816,60893,60961,61039,61115,61186,61267,61340,61423,61498,61583,61656,61737,61818,61892,61976,62046,62124,62194,62274,62352,62424,62506,62576,62653,62733,62818,62906,62990,63077,63151,63229,63307,63378,63459,63550,63633,63729,63827,63934,63999,64065,64118,64194,64260,64347,64423,73071,73449,74044,74098,74177,74255,74328,74393,74456,74522,74593,74664,74734,74796,74865,74931,74991,75058,75125,75181,75232,75285,75337,75391,75462,75525,75584,75646,75705,75778,75845,75915,75975,76038,76113,76185,76281,76352,76408,76479,76536,76593,76659,76723,76794,76851,76904,76967,77019,77077,78361,78430,78496,78555,78638,78697,78754,78821,78891,78965,79027,79096,79166,79265,79362,79461,79547,79633,79714,79789,79878,79969,80053,80112,80158,80224,80281,80348,80405,80487,80552,80618,80741,80825,80946,81011,81073,81171,81245,81328,81417,81481,81560,81634,81696,81792,81857,81916,81972,82028,82088,82195,82242,82302,82363,82427,82488,82548,82606,82649,82698,82750,82801,82853,82902,82951,83016,83082,83142,83203,83259,83318,83367,83415,83473,83530,83632,83689,83764,83812,83863,83925,83990,84042,84116,84179,84242,84310,84360,84422,84482,84539,84599,84648,84716,84822,84924,84993,85064,85120,85169,85269,85340,85450,85541,85623,85721,85777,85878,85988,86087,86150,86256,86333,86445,86572,86684,86811,86881,86995,87126,87223,87291,87409,87512,87630,87691,87765,87832,87937,88059,88133,88200,88310,88409,88482,88579,88701,88819,88937,88998,89120,89237,89305,89411,89513,89593,89664,89760,89827,89901,89975,90061,90151,90229,90306,90406,90477,90598,90719,90783,90908,90982,91106,91230,91297,91406,91534,91646,91725,91803,91904,91975,92097,92219,92284,92410,92522,92628,92696,92795,92899,92962,93028,93112,93225,93338,93456,93534,93606,93742,93878,93963,94103,94241,94379,94521,94603,94689,94766,94839,94948,95059,95187,95315,95447,95577,95707,95841,95930,95992,96088,96155,96272,96393,96490,96572,96659,96746,96877,97008,97143,97220,97297,97408,97522,97596,97705,97817,97884,97957,98022,98124,98220,98324,98392,98457,98551,98623,98733,98839,98912,99003,99105,99208,99303,99410,99515,99637,99759,99885,99944,100002,100126,100250,100378,100496,100614,100736,100822,100919,101053,101187,101267,101405,101537,101669,101805,101880,101956,102059,102133,102246,102327,102384,102445,102504,102564,102622,102683,102741,102791,102840,102907,102966,103025,103074,103145,103229,103299,103370,103450,103519,103582,103650,103716,103784,103849,103915,103992,104070,104176,104282,104378,104507,104596,104723,104789,104859,104945,105011,105094,105168,105266,105362,105458,105556,105665,105760,105849,105911,105971,106036,106093,106174,106228,106285,106382,106492,106553,106668,106789,106884,106976,107069,107125,107184,107233,107325,107374,107428,107482,107536,107590,107644,107699,107809,107919,108027,108137,108247,108357,108467,108575,108681,108785,108889,108993,109088,109183,109276,109369,109473,109579,109683,109787,109880,109973,110066,110159,110267,110373,110479,110585,110682,110777,110872,110967,111073,111179,111285,111391,111489,111584,111680,111777,111842,111946,112004,112068,112129,112191,112251,112316,112378,112446,112504,112567,112630,112697,112772,112845,112911,112963,113016,113068,113125,113209,113304,113389,113470,113550,113627,113706,113783,113857,113931,114002,114082,114154,114229,114294,114355,114415,114490,114564,114637,114707,114779,114849,114922,114986,115056,115102,115171,115223,115308,115391,115448,115514,115581,115647,115728,115803,115859,115912,115973,116031,116081,116130,116179,116228,116290,116342,116387,116468,116519,116573,116626,116680,116731,116780,116846,116897,116958,117019,117081,117131,117172,117249,117308,117367,117426,117487,117543,117599,117666,117727,117792,117847,117912,117981,118049,118127,118196,118256,118327,118401,118466,118538,118608,118675,118759,118828,118895,118965,119028,119095,119163,119246,119325,119415,119492,119560,119627,119705,119762,119819,119887,119953,120009,120069,120128,120182,120232,120282,120330,120392,120443,120516,120596,120676,120740,120807,120878,120936,120997,121063,121122,121189,121249,121309,121372,121440,121501,121568,121646,121716,121765,121822,121891,121952,122040,122128,122216,122304,122391,122478,122565,122652,122710,122784,122854,122910,122981,123046,123108,123183,123256,123346,123412,123478,123539,123603,123665,123723,123794,123877,123936,124007,124073,124138,124199,124258,124329,124395,124460,124543,124619,124694,124775,124835,124904,124974,125043,125098,125154,125210,125271,125329,125385,125444,125498,125553,125615,125672,125766,125835,125936,125987,126057,126120,126176,126234,126293,126347,126433,126517,126587,126656,126726,126841,126962,127029,127096,127171,127238,127297,127351,127405,127459,127512,127564,129848,129985,132932,132981,133031,133122,133170,133226,133284,133346,133401,133459,133530,133594,133653,133715,133781,134252,134397,134441,134486,135578,135629,135676,135721,135772,135823,135874,136411,137393,137459,137638,137701,137841,137898,137952,138007,138065,138120,138179,138235,138304,138373,138442,138512,138575,138638,138701,138764,138829,138894,138959,139024,139087,139151,139215,139279,139330,139408,139486,139557,139629,139702,139774,139840,139906,139974,140042,140108,140175,140249,140312,140369,140429,140494,140561,140626,140683,140744,140802,140906,141016,141125,141229,141307,141372,141439,141505,141575,141622,141674,141850,141977,144985,145215,145346,145530,145708,145946,146135,146815,146913,147028,147113,150821,151048,151418,151507,151664,152798,152951,153279,154921,155108,155204,155294,155390,155480,155646,155769,155892,156062,156168,156283,156398,156500,156606,156723,156955,157037,157210,157378,157526,157685,157840,158013,158130,158247,158415,158527,158641,158813,158989,159147,159280,159392,159538,159690,159822,159965,162362,162540,162676,162772,162908,163003,163170,163263,163355,163542,163698,163876,164040,164222,164539,164721,164903,165093,165325,165515,165692,165854,166011,166121,166304,166441,166645,166829,167013,167173,167331,167515,167742,167945,168116,168336,168558,168713,168913,169097,169200,169390,169531,169696,169867,170067,170271,170473,170638,170843,171042,171241,171438,171529,171678,171828,171912,172061,172206,172358,172499,172665,173358,173436,173737,173903,174058,176800,176958,177122,181566,181789,195921,196198,196470,196748,196993,197055,201299,201750,202206,213343,213491,214005,214442,214876,219216,219301,219422,219521,219926,220023,220140,220227,220350,220451,220857,220956,221075,221168,221275,221618,221725,221970,222091,222500,222748,222848,222953,223072,223581,223728,223847,224098,224231,224646,224900,230115,230362,230487,230895,231016,231244,231365,231498,231645,252367,252859,273330,273754,294521,295015,315531,315957,320798,326215,330306,335737,340479,345856,349840,353832,359223,359770,360203,360959,361189,361432,362599,410769,411673,412257,412730,414160,414904,416097,417151,417629,417922,418305,419820,420585,421728,422169,422610,423206,423480,423891,424907,425085,425838,425975,426066,428260,428526,428848,429058,429167,429286,429470,430588,431058,431809,434392,436371,436747,436975,437231,437490,438066,438420,438542,438681,438973,439233,440161,440447,440850,441252,441595,441807,442008,442221,442510,453397,453470,453557,453642,466418,466530,466636,466759,466891,467014,467144,467268,467401,467532,467657,467774,467894,468026,468154,468268,468386,468499,468620,468808,468995,469176,469359,469543,469708,469890,470010,470130,470238,470348,470460,470568,470678,470843,471009,471161,471326,471427,471547,471718,471879,472042,472203,472370,472489,472606,472786,472968,473149,473332,473487,473632,473754,473889,474052,474245,474371,474523,474665,474835,474991,475163,482058,482253,482345,482518,482680,482775,482944,483038,483127,483370,483459,483752,484168,484588,485009,485435,485852,486268,486685,487103,487517,487987,488460,488932,489343,489814,490286,490476,490682,490788,490896,491002,491114,491228,491340,491454,491570,491684,491792,491902,492010,492272,492651,493055,493202,493310,493420,493528,493642,494051,494465,494581,494999,495240,495670,496105,496515,496937,497347,497469,497878,498294,498416,498634,501454,501522,501866,501946,502302,502452,502819,502895,503007,503097,503359,503624,503732,503884,503992,504068,504180,504270,504372,504480,504588,504688,504796,504881,505047,505151,505279,505366,505533,505611,505725,505817,506081,506348,506458,506611,506721,506805,507194,507292,507400,507494,507624,507732,507854,507990,508098,508218,508352,508474,508602,508744,508870,509010,509136,509254,509386,509484,509594,509894,510006,510124,510588,510704,511007,511133,511229,511630,511740,511864,512002,512112,512234,512546,512670,512800,513276,513404,513719,513857,514019,514235,514391,516008,516076,516160,516264,516467,516656,516857,517050,517255,517568,517780,517946,518062,518308,518524,518837,519263,519725,519962,520114,520374,520518,520660,523892,524006,524126,524242,524336,524657,524756,524874,524975,525254,525539,525818,526100,526353,526612,526865,527121,527545,527621,530871,532226,532670,534524,535099,535307,536317,536697,536863,537004,542024,542450,542562,542697,542850,543047,543218,543401,543576,543763,544035,544193,544277,544381,544868,545424,545582,545801,546032,546255,546490,546712,546978,547116,547715,547829,547967,548079,548203,548774,549269,549815,549960,550053,550145,552072,552642,552940,553129,553335,553528,553738,554622,554767,555159,555317,555534,563590,564022,564897,565517,565714,566662,567427,567550,568323,568544,568744,570721,570821,570911,571597,572350,573115,573878,574653,575866,576031,577644,577965,579028,579238,579408,579978,580873,581506,581672,583158,583774,584010,584231,585189,585454,585719,585966,586380,586616,587901,588350,588537,588786,589028,589204,589445,589678,589903,590498,590973,591497,591758,593109,593584,594810,595280,596328,596780,597024,597481,598726,599209,599359,599914,600366,600766,600919,601064,601207,601277,601705,601993,602497,603006,603122,604024,604146,604258,604435,604701,604971,605237,605505,605761,606021,606277,606535,606787,607043,607295,607549,607781,608017,608269,608525,608777,609031,609263,609497,609609,610261,610716,610840,611932,612747,612943,613267,613656,614008,614249,614463,614762,614954,615269,615476,615822,616122,616523,616742,617155,617392,617762,618486,618841,619110,619250,619504,619648,619925,620917,621326,621958,622304,622672,623746,624109,624509,626017,626602,626920,629455,629649,629867,630093,630305,630504,630711,631915,632210,632767,633157,633789,634266,634511,634998,635244,636440,636837,637843,638065,638488,638679,639058,639146,639254,639362,639675,640000,640319,640650,643353,643541,643802,644051,646635,646827,647092,647345,647877,648285,648484,649068,649303,649427,649839,650053,650455,650558,650688,650863,651115,651311,651451,651645,652656,653725,654013,654143,654920,655577,655723,656429,656667,658207,658357,658774,658939,659625,660095,660291,660382,660466,660610,660844,661011,661939,662225,662385,663000,663159,663487,663714,664226,664588,664667,665006,665111,665476,665847,666208,668082,668711,669787,670211,670464,670616,671664,672401,672604,672850,673097,673315,673557,673878,674142,674447,674670,674981,675170,675885,676154,676648,676874,677314,677473,677757,678502,678867,679172,679330,679568,680887,681285,681513,681733,681875,683165,683271,683401,683539,683663,683951,684120,684220,684505,684619,685502,686257,686696,686820,687066,687259,687393,687584,688363,688581,688872,689151,689468,689690,689985,690268,690372,690713,691529,691845,692406,692912,693117,693903,694308,694969,695158,695709,696275,696395,696797,847294,847389,847482,847545,847627,847720,847813,847900,847998,848089,848180,848268,848352,848448,848548,848654,848757,848858,848962,849068,849167,849273,849375,849482,849591,849702,849833,849953,850069,850187,850286,850393,850509,850628,850756,850845,850940,851017,851106,851197,851290,851364,851461,851556,851654,851753,851857,851953,852055,852158,852258,852361,852446,852547,852645,852735,852830,852917,853023,853125,853219,853310,853404,853480,853572,853661,853764,853875,853958,854044,854139,854236,854332,854420,854521,854622,854725,854831,854929,855026,855121,855219,855322,855422,855525,855630,855748,855864,855959,856052,856137,856233,856327,856419,856502,856606,856711,856811,856912,857017,857117,857218,857317,857419,857513,857620,857722,857825,857918,858014,858116,858219,858315,858417,858520,858617,858720,858818,858922,859027,859124,859232,859346,859461,859569,859683,859798,859900,860005,860113,860223,860339,860456,860551,860648,860747,860852,860958,861057,861162,861268,861368,861474,861575,861682,861801,861900,862005,862107,862209,862309,862412,862507,862611,862696,862800,862904,863002,863106,863212,863310,863415,863513,863626,863720,863809,863898,863981,864072,864155,864253,864343,864439,864528,864622,864710,864806,864891,864999,865100,865201,865299,865405,865496,865595,865692,865790,865886,865979,866089,866187,866282,866392,866484,866584,866683,866770,866874,866979,867078,867185,867292,867391,867500,867592,867703,867814,867925,868029,868144,868260,868387,868507,868602,868697,868794,868893,868985,869084,869176,869275,869361,869455,869558,869654,869757,869853,869956,870053,870151,870254,870347,870437,870538,870621,870712,870797,870889,870992,871087,871183,871276,871370,871449,871556,871647,871746,871839,871942,872046,872147,872248,872352,872446,872550,872654,872767,872873,872979,873087,873204,873305,873413,873513,873616,873721,873828,873924,874003,874093,874177,874269,874342,874434,874523,874615,874700,874797,874890,874985,875084,875181,875272,875363,875455,875550,875657,875765,875867,875964,876061,876154,876241,876325,876422,876519,876612,876699,876790,876889,876988,877083,877172,877253,877352,877456,877553,877658,877755,877839,877938,878042,878139,878244,878341,878439,878540,878646,878745,878852,878951,879050,879141,879230,879319,879401,879494,879585,879696,879797,879897,880009,880122,880220,880328,880422,880522,880611,880703,880814,880924,881019,881135,881261,881387,881506,881634,881759,881884,882002,882129,882238,882347,882460,882583,882706,882822,882947,883044,883152,883274,883390,883506,883615,883703,883804,883893,883994,884081,884169,884266,884358,884464,884564,884640", "endLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,651,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,720,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1323,1329,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2062,2063,2113,2114,2115,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2137,2140,2141,2142,2162,2163,2164,2165,2166,2167,2168,2179,2198,2199,2202,2203,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2265,2270,2310,2312,2313,2314,2315,2316,2317,2325,2326,2327,2328,2366,2368,2373,2374,2375,2388,2389,2392,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2482,2483,2484,2485,2486,2487,2488,2489,2490,2493,2496,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2558,2559,2560,2561,2562,2600,2601,2602,2668,2669,2841,2845,2849,2853,2854,2858,2930,2937,2945,3110,3120,3129,3138,3147,3208,3209,3210,3216,3217,3218,3219,3220,3221,3227,3228,3229,3230,3231,3236,3237,3241,3242,3248,3252,3253,3254,3255,3265,3266,3267,3271,3272,3278,3282,3283,3355,3356,3361,3362,3365,3366,3367,3368,3632,3639,3900,3906,4170,4177,4438,4444,4507,4589,4641,4723,4785,4867,4931,4983,5065,5073,5079,5090,5094,5098,5111,5126,5902,5909,5915,5932,5945,5965,5982,5991,5996,6003,6023,6036,6053,6059,6065,6072,6076,6082,6096,6099,6109,6110,6111,6159,6163,6167,6171,6172,6173,6176,6192,6199,6213,6258,6259,6293,6297,6301,6306,6313,6319,6320,6323,6327,6332,6345,6349,6354,6359,6364,6367,6370,6373,6377,6381,6521,6522,6523,6524,6756,6757,6758,6759,6760,6761,6762,6763,6764,6765,6766,6767,6768,6769,6770,6771,6772,6773,6777,6781,6785,6789,6793,6797,6801,6802,6803,6804,6805,6806,6807,6808,6812,6816,6817,6821,6822,6825,6829,6832,6835,6838,6842,6845,6848,6852,6856,6860,6864,6867,6868,6869,6870,6873,6877,6880,6883,6886,6889,6892,6895,6899,6972,6973,6976,6979,6980,6983,6984,6985,6989,6990,6995,7002,7009,7016,7023,7030,7037,7044,7051,7058,7067,7076,7085,7092,7101,7110,7113,7116,7117,7118,7119,7120,7121,7122,7123,7124,7125,7126,7127,7128,7132,7137,7142,7145,7146,7147,7148,7149,7157,7165,7166,7174,7178,7186,7194,7202,7210,7218,7219,7227,7235,7236,7239,7242,7280,7285,7287,7292,7296,7300,7305,7306,7307,7311,7315,7316,7320,7321,7322,7323,7324,7325,7326,7327,7328,7329,7330,7334,7335,7336,7337,7341,7342,7343,7344,7348,7352,7353,7357,7358,7359,7364,7365,7366,7367,7368,7369,7370,7371,7372,7373,7374,7375,7376,7377,7378,7379,7380,7381,7382,7383,7384,7388,7389,7390,7396,7397,7401,7403,7404,7409,7410,7411,7412,7413,7414,7418,7419,7420,7426,7427,7431,7433,7437,7441,7445,7449,7470,7471,7472,7475,7478,7481,7484,7487,7492,7496,7499,7500,7505,7509,7514,7520,7526,7531,7535,7540,7544,7548,7589,7590,7591,7592,7593,7597,7598,7599,7600,7604,7608,7612,7616,7620,7624,7628,7632,7638,7639,7680,7694,7699,7725,7732,7735,7746,7751,7754,7757,7812,7818,7819,7822,7825,7828,7831,7834,7837,7840,7844,7847,7848,7849,7857,7865,7868,7873,7878,7883,7888,7892,7896,7897,7905,7906,7907,7908,7909,7917,7922,7927,7928,7929,7930,7955,7961,7966,7969,7973,7976,7980,7990,7993,7998,8001,8005,8009,8114,8128,8141,8145,8160,8171,8174,8185,8190,8194,8229,8230,8231,8243,8251,8259,8267,8275,8295,8298,8325,8330,8350,8353,8356,8363,8376,8385,8388,8408,8418,8422,8426,8439,8443,8447,8451,8457,8461,8478,8486,8490,8494,8498,8501,8505,8509,8513,8523,8530,8537,8541,8567,8577,8602,8611,8631,8641,8645,8655,8680,8690,8693,8700,8707,8714,8715,8716,8717,8718,8725,8729,8735,8741,8742,8755,8756,8757,8760,8763,8766,8769,8772,8775,8778,8781,8784,8787,8790,8793,8796,8799,8802,8805,8808,8811,8814,8817,8820,8821,8829,8837,8838,8851,8861,8865,8870,8875,8879,8882,8886,8890,8893,8897,8900,8904,8909,8914,8917,8924,8928,8932,8941,8946,8951,8952,8956,8959,8963,8976,8981,8989,8993,8997,9014,9018,9023,9041,9048,9052,9082,9085,9088,9091,9094,9097,9100,9119,9125,9133,9140,9152,9160,9165,9173,9177,9195,9202,9218,9222,9230,9233,9238,9239,9240,9241,9245,9249,9253,9257,9292,9295,9299,9303,9337,9340,9344,9348,9357,9363,9366,9376,9380,9381,9388,9392,9399,9400,9401,9404,9409,9414,9415,9419,9434,9453,9457,9458,9470,9480,9481,9493,9498,9522,9525,9531,9534,9543,9551,9555,9558,9561,9564,9568,9571,9588,9592,9595,9610,9613,9621,9626,9633,9638,9639,9644,9645,9651,9657,9663,9695,9706,9723,9730,9734,9737,9750,9759,9763,9768,9772,9776,9780,9784,9788,9792,9796,9801,9804,9816,9821,9830,9833,9840,9841,9845,9854,9860,9864,9865,9869,9890,9896,9900,9904,9905,9923,9924,9925,9926,9927,9932,9935,9936,9942,9943,9955,9967,9974,9975,9980,9985,9986,9990,10004,10009,10015,10021,10027,10032,10038,10044,10045,10051,10066,10071,10080,10089,10092,10106,10111,10122,10126,10135,10144,10145,10152,10160,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197,15198,15199,15200,15201,15202,15203,15204,15205,15206,15207,15208,15209,15210,15211,15212,15213,15214,15215,15216,15217,15218,15219,15220,15221,15222,15223,15224,15225,15226,15227,15228,15229,15230,15231,15232,15233,15234,15235,15236,15237,15238,15239,15240,15241", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "6542,6777,7236,7395,7455,7516,7581,7636,7786,7836,7889,7947,7995,8115,8464,8535,8607,8679,8752,8819,8868,8922,8959,9010,9070,9117,9173,9222,9280,9334,9395,9451,9502,9562,9618,9681,9730,9786,9842,9892,9951,10006,10068,10115,10169,10225,10277,10332,10386,10440,10494,10543,10601,10655,10712,10768,10815,10868,10924,10984,11047,11106,11168,11218,11272,11326,11374,11431,11484,11540,12162,12218,13156,13266,13322,13382,13435,13496,13575,13656,13728,13807,13887,13963,14041,14110,14186,14263,14334,14407,14483,14561,14630,14706,14783,14847,14918,14990,17482,17580,17635,17903,17956,18008,18058,18116,18181,18229,18280,25237,25303,25361,25430,25488,25557,25627,25700,25774,25842,25909,25979,26045,26118,26178,26254,26314,26374,26449,26517,26583,26651,26711,26770,26827,26893,26955,27012,27080,27153,27223,27285,27346,27414,27476,27546,27615,27671,27730,27792,27854,27921,27978,28039,28100,28161,28222,28278,28334,28390,28446,28504,28562,28620,28678,28735,28792,28849,28906,28965,29024,29082,29165,29248,29321,29375,29444,29500,29581,29662,29733,29862,30121,30179,30237,31402,31949,32316,32370,32440,32510,32575,32641,32706,32774,32843,32911,33041,33094,33153,33211,33263,33359,33747,33793,33843,33899,33946,34051,34109,34171,34234,34296,34355,34415,34480,34546,34611,34673,34735,34797,34859,34921,34983,35049,35116,35182,35245,35309,35372,35440,35501,35563,35625,35688,35752,35815,35879,35957,36016,36082,36162,36223,36276,36485,36536,36963,37024,37494,37553,39020,42760,42831,42897,42971,43040,43111,43184,43255,43323,43396,43472,43542,43620,43688,43754,43815,43884,43948,44014,44082,44148,44211,44279,44350,44415,44488,44551,44632,44696,44762,44832,44902,44972,45042,45109,46580,46638,46697,46757,46816,46875,46934,46993,47052,47111,47170,47229,47288,47347,47407,47468,47530,47591,47652,47713,47774,47835,47896,47956,48017,48078,48138,48199,48260,48321,48382,48443,48504,48565,48626,48687,48748,48809,48877,48946,49016,49085,49154,49223,49292,49361,49430,49499,49568,49637,49706,49766,49827,49889,49950,50011,50072,50133,50194,50255,50316,50377,50438,50499,50561,50624,50688,50751,50814,50877,50940,51003,51066,51129,51192,51255,51318,51379,51441,51504,51566,51628,51690,51752,51814,51876,51938,52000,52062,52124,52181,52267,52347,52437,52532,52624,52716,52806,52889,52982,53069,53166,53257,53358,53445,53548,53637,53736,53828,53928,54012,54106,54194,54292,54375,54466,54560,54659,54761,54859,54959,55046,55146,55232,55328,55416,55497,55588,55684,55777,55870,55961,56046,56140,56229,56327,56420,56522,56610,56714,56805,56905,56998,57099,57184,57279,57368,57467,57552,57644,57739,57839,57942,58041,58144,58233,58334,58421,58518,58606,58702,58794,58894,58984,59082,59167,59256,59345,59438,59525,59616,60354,60430,60499,60578,60651,60731,60811,60888,60956,61034,61110,61181,61262,61335,61418,61493,61578,61651,61732,61813,61887,61971,62041,62119,62189,62269,62347,62419,62501,62571,62648,62728,62813,62901,62985,63072,63146,63224,63302,63373,63454,63545,63628,63724,63822,63929,63994,64060,64113,64189,64255,64342,64418,64494,73131,73499,74093,74172,74250,74323,74388,74451,74517,74588,74659,74729,74791,74860,74926,74986,75053,75120,75176,75227,75280,75332,75386,75457,75520,75579,75641,75700,75773,75840,75910,75970,76033,76108,76180,76276,76347,76403,76474,76531,76588,76654,76718,76789,76846,76899,76962,77014,77072,77139,78425,78491,78550,78633,78692,78749,78816,78886,78960,79022,79091,79161,79260,79357,79456,79542,79628,79709,79784,79873,79964,80048,80107,80153,80219,80276,80343,80400,80482,80547,80613,80736,80820,80941,81006,81068,81166,81240,81323,81412,81476,81555,81629,81691,81787,81852,81911,81967,82023,82083,82190,82237,82297,82358,82422,82483,82543,82601,82644,82693,82745,82796,82848,82897,82946,83011,83077,83137,83198,83254,83313,83362,83410,83468,83525,83627,83684,83759,83807,83858,83920,83985,84037,84111,84174,84237,84305,84355,84417,84477,84534,84594,84643,84711,84817,84919,84988,85059,85115,85164,85264,85335,85445,85536,85618,85716,85772,85873,85983,86082,86145,86251,86328,86440,86567,86679,86806,86876,86990,87121,87218,87286,87404,87507,87625,87686,87760,87827,87932,88054,88128,88195,88305,88404,88477,88574,88696,88814,88932,88993,89115,89232,89300,89406,89508,89588,89659,89755,89822,89896,89970,90056,90146,90224,90301,90401,90472,90593,90714,90778,90903,90977,91101,91225,91292,91401,91529,91641,91720,91798,91899,91970,92092,92214,92279,92405,92517,92623,92691,92790,92894,92957,93023,93107,93220,93333,93451,93529,93601,93737,93873,93958,94098,94236,94374,94516,94598,94684,94761,94834,94943,95054,95182,95310,95442,95572,95702,95836,95925,95987,96083,96150,96267,96388,96485,96567,96654,96741,96872,97003,97138,97215,97292,97403,97517,97591,97700,97812,97879,97952,98017,98119,98215,98319,98387,98452,98546,98618,98728,98834,98907,98998,99100,99203,99298,99405,99510,99632,99754,99880,99939,99997,100121,100245,100373,100491,100609,100731,100817,100914,101048,101182,101262,101400,101532,101664,101800,101875,101951,102054,102128,102241,102322,102379,102440,102499,102559,102617,102678,102736,102786,102835,102902,102961,103020,103069,103140,103224,103294,103365,103445,103514,103577,103645,103711,103779,103844,103910,103987,104065,104171,104277,104373,104502,104591,104718,104784,104854,104940,105006,105089,105163,105261,105357,105453,105551,105660,105755,105844,105906,105966,106031,106088,106169,106223,106280,106377,106487,106548,106663,106784,106879,106971,107064,107120,107179,107228,107320,107369,107423,107477,107531,107585,107639,107694,107804,107914,108022,108132,108242,108352,108462,108570,108676,108780,108884,108988,109083,109178,109271,109364,109468,109574,109678,109782,109875,109968,110061,110154,110262,110368,110474,110580,110677,110772,110867,110962,111068,111174,111280,111386,111484,111579,111675,111772,111837,111941,111999,112063,112124,112186,112246,112311,112373,112441,112499,112562,112625,112692,112767,112840,112906,112958,113011,113063,113120,113204,113299,113384,113465,113545,113622,113701,113778,113852,113926,113997,114077,114149,114224,114289,114350,114410,114485,114559,114632,114702,114774,114844,114917,114981,115051,115097,115166,115218,115303,115386,115443,115509,115576,115642,115723,115798,115854,115907,115968,116026,116076,116125,116174,116223,116285,116337,116382,116463,116514,116568,116621,116675,116726,116775,116841,116892,116953,117014,117076,117126,117167,117244,117303,117362,117421,117482,117538,117594,117661,117722,117787,117842,117907,117976,118044,118122,118191,118251,118322,118396,118461,118533,118603,118670,118754,118823,118890,118960,119023,119090,119158,119241,119320,119410,119487,119555,119622,119700,119757,119814,119882,119948,120004,120064,120123,120177,120227,120277,120325,120387,120438,120511,120591,120671,120735,120802,120873,120931,120992,121058,121117,121184,121244,121304,121367,121435,121496,121563,121641,121711,121760,121817,121886,121947,122035,122123,122211,122299,122386,122473,122560,122647,122705,122779,122849,122905,122976,123041,123103,123178,123251,123341,123407,123473,123534,123598,123660,123718,123789,123872,123931,124002,124068,124133,124194,124253,124324,124390,124455,124538,124614,124689,124770,124830,124899,124969,125038,125093,125149,125205,125266,125324,125380,125439,125493,125548,125610,125667,125761,125830,125931,125982,126052,126115,126171,126229,126288,126342,126428,126512,126582,126651,126721,126836,126957,127024,127091,127166,127233,127292,127346,127400,127454,127507,127559,127633,129980,130120,132976,133026,133076,133165,133221,133279,133341,133396,133454,133525,133589,133648,133710,133776,133842,134290,134436,134481,134524,135624,135671,135716,135767,135818,135869,135920,136454,137454,137516,137696,137768,137893,137947,138002,138060,138115,138174,138230,138299,138368,138437,138507,138570,138633,138696,138759,138824,138889,138954,139019,139082,139146,139210,139274,139325,139403,139481,139552,139624,139697,139769,139835,139901,139969,140037,140103,140170,140244,140307,140364,140424,140489,140556,140621,140678,140739,140797,140901,141011,141120,141224,141302,141367,141434,141500,141570,141617,141669,141719,141902,142292,145130,145341,145525,145703,145941,146130,146299,146908,147023,147108,147187,150976,151108,151502,151659,151816,152946,153100,153333,155103,155199,155289,155385,155475,155641,155764,155887,156057,156163,156278,156393,156495,156601,156718,156833,157032,157205,157373,157521,157680,157835,158008,158125,158242,158410,158522,158636,158808,158984,159142,159275,159387,159533,159685,159817,159960,160082,162535,162671,162767,162903,162998,163165,163258,163350,163537,163693,163871,164035,164217,164534,164716,164898,165088,165320,165510,165687,165849,166006,166116,166299,166436,166640,166824,167008,167168,167326,167510,167737,167940,168111,168331,168553,168708,168908,169092,169195,169385,169526,169691,169862,170062,170266,170468,170633,170838,171037,171236,171433,171524,171673,171823,171907,172056,172201,172353,172494,172660,172821,173431,173732,173898,174053,174155,176953,177117,177303,181784,181909,196193,196465,196743,196988,197050,197335,201745,202201,202710,213486,214000,214437,214871,215314,219296,219417,219516,219921,220018,220135,220222,220345,220446,220852,220951,221070,221163,221270,221613,221720,221965,222086,222495,222743,222843,222948,223067,223576,223723,223842,224093,224226,224641,224895,225007,230357,230482,230890,231011,231239,231360,231493,231640,252362,252854,273325,273749,294516,295010,315526,315952,320793,326210,330301,335732,340474,345851,349835,353827,359218,359765,360198,360954,361184,361427,362594,363523,411668,412252,412725,414155,414899,416092,417146,417624,417917,418300,419815,420580,421723,422164,422605,423201,423475,423886,424902,425080,425833,425970,426061,428255,428521,428843,429053,429162,429281,429465,430583,431053,431804,434387,434482,436742,436970,437226,437485,438061,438415,438537,438676,438968,439228,440156,440442,440845,441247,441590,441802,442003,442216,442505,442790,453465,453552,453637,453736,466525,466631,466754,466886,467009,467139,467263,467396,467527,467652,467769,467889,468021,468149,468263,468381,468494,468615,468803,468990,469171,469354,469538,469703,469885,470005,470125,470233,470343,470455,470563,470673,470838,471004,471156,471321,471422,471542,471713,471874,472037,472198,472365,472484,472601,472781,472963,473144,473327,473482,473627,473749,473884,474047,474240,474366,474518,474660,474830,474986,475158,475449,482248,482340,482513,482675,482770,482939,483033,483122,483365,483454,483747,484163,484583,485004,485430,485847,486263,486680,487098,487512,487982,488455,488927,489338,489809,490281,490471,490677,490783,490891,490997,491109,491223,491335,491449,491565,491679,491787,491897,492005,492267,492646,493050,493197,493305,493415,493523,493637,494046,494460,494576,494994,495235,495665,496100,496510,496932,497342,497464,497873,498289,498411,498629,498813,501517,501861,501941,502297,502447,502591,502890,503002,503092,503354,503619,503727,503879,503987,504063,504175,504265,504367,504475,504583,504683,504791,504876,505042,505146,505274,505361,505528,505606,505720,505812,506076,506343,506453,506606,506716,506800,507189,507287,507395,507489,507619,507727,507849,507985,508093,508213,508347,508469,508597,508739,508865,509005,509131,509249,509381,509479,509589,509889,510001,510119,510583,510699,511002,511128,511224,511625,511735,511859,511997,512107,512229,512541,512665,512795,513271,513399,513714,513852,514014,514230,514386,514590,516071,516155,516259,516462,516651,516852,517045,517250,517563,517775,517941,518057,518303,518519,518832,519258,519720,519957,520109,520369,520513,520655,523887,524001,524121,524237,524331,524652,524751,524869,524970,525249,525534,525813,526095,526348,526607,526860,527116,527540,527616,530866,532221,532665,534519,535094,535302,536312,536692,536858,536999,542019,542445,542557,542692,542845,543042,543213,543396,543571,543758,544030,544188,544272,544376,544863,545419,545577,545796,546027,546250,546485,546707,546973,547111,547710,547824,547962,548074,548198,548769,549264,549810,549955,550048,550140,552067,552637,552935,553124,553330,553523,553733,554617,554762,555154,555312,555529,555790,564017,564892,565512,565709,566657,567422,567545,568318,568539,568739,570716,570816,570906,571592,572345,573110,573873,574648,575861,576026,577639,577960,579023,579233,579403,579973,580868,581501,581667,583153,583769,584005,584226,585184,585449,585714,585961,586375,586611,587896,588345,588532,588781,589023,589199,589440,589673,589898,590493,590968,591492,591753,593104,593579,594805,595275,596323,596775,597019,597476,598721,599204,599354,599909,600361,600761,600914,601059,601202,601272,601700,601988,602492,603001,603117,604019,604141,604253,604430,604696,604966,605232,605500,605756,606016,606272,606530,606782,607038,607290,607544,607776,608012,608264,608520,608772,609026,609258,609492,609604,610256,610711,610835,611927,612742,612938,613262,613651,614003,614244,614458,614757,614949,615264,615471,615817,616117,616518,616737,617150,617387,617757,618481,618836,619105,619245,619499,619643,619920,620912,621321,621953,622299,622667,623741,624104,624504,626012,626597,626915,629450,629644,629862,630088,630300,630499,630706,631910,632205,632762,633152,633784,634261,634506,634993,635239,636435,636832,637838,638060,638483,638674,639053,639141,639249,639357,639670,639995,640314,640645,643348,643536,643797,644046,646630,646822,647087,647340,647872,648280,648479,649063,649298,649422,649834,650048,650450,650553,650683,650858,651110,651306,651446,651640,652651,653720,654008,654138,654915,655572,655718,656424,656662,658202,658352,658769,658934,659620,660090,660286,660377,660461,660605,660839,661006,661934,662220,662380,662995,663154,663482,663709,664221,664583,664662,665001,665106,665471,665842,666203,668077,668706,669782,670206,670459,670611,671659,672396,672599,672845,673092,673310,673552,673873,674137,674442,674665,674976,675165,675880,676149,676643,676869,677309,677468,677752,678497,678862,679167,679325,679563,680882,681280,681508,681728,681870,683160,683266,683396,683534,683658,683946,684115,684215,684500,684614,685497,686252,686691,686815,687061,687254,687388,687579,688358,688576,688867,689146,689463,689685,689980,690263,690367,690708,691524,691840,692401,692907,693112,693898,694303,694964,695153,695704,696270,696390,696792,697326,847384,847477,847540,847622,847715,847808,847895,847993,848084,848175,848263,848347,848443,848543,848649,848752,848853,848957,849063,849162,849268,849370,849477,849586,849697,849828,849948,850064,850182,850281,850388,850504,850623,850751,850840,850935,851012,851101,851192,851285,851359,851456,851551,851649,851748,851852,851948,852050,852153,852253,852356,852441,852542,852640,852730,852825,852912,853018,853120,853214,853305,853399,853475,853567,853656,853759,853870,853953,854039,854134,854231,854327,854415,854516,854617,854720,854826,854924,855021,855116,855214,855317,855417,855520,855625,855743,855859,855954,856047,856132,856228,856322,856414,856497,856601,856706,856806,856907,857012,857112,857213,857312,857414,857508,857615,857717,857820,857913,858009,858111,858214,858310,858412,858515,858612,858715,858813,858917,859022,859119,859227,859341,859456,859564,859678,859793,859895,860000,860108,860218,860334,860451,860546,860643,860742,860847,860953,861052,861157,861263,861363,861469,861570,861677,861796,861895,862000,862102,862204,862304,862407,862502,862606,862691,862795,862899,862997,863101,863207,863305,863410,863508,863621,863715,863804,863893,863976,864067,864150,864248,864338,864434,864523,864617,864705,864801,864886,864994,865095,865196,865294,865400,865491,865590,865687,865785,865881,865974,866084,866182,866277,866387,866479,866579,866678,866765,866869,866974,867073,867180,867287,867386,867495,867587,867698,867809,867920,868024,868139,868255,868382,868502,868597,868692,868789,868888,868980,869079,869171,869270,869356,869450,869553,869649,869752,869848,869951,870048,870146,870249,870342,870432,870533,870616,870707,870792,870884,870987,871082,871178,871271,871365,871444,871551,871642,871741,871834,871937,872041,872142,872243,872347,872441,872545,872649,872762,872868,872974,873082,873199,873300,873408,873508,873611,873716,873823,873919,873998,874088,874172,874264,874337,874429,874518,874610,874695,874792,874885,874980,875079,875176,875267,875358,875450,875545,875652,875760,875862,875959,876056,876149,876236,876320,876417,876514,876607,876694,876785,876884,876983,877078,877167,877248,877347,877451,877548,877653,877750,877834,877933,878037,878134,878239,878336,878434,878535,878641,878740,878847,878946,879045,879136,879225,879314,879396,879489,879580,879691,879792,879892,880004,880117,880215,880323,880417,880517,880606,880698,880809,880919,881014,881130,881256,881382,881501,881629,881754,881879,881997,882124,882233,882342,882455,882578,882701,882817,882942,883039,883147,883269,883385,883501,883610,883698,883799,883888,883989,884076,884164,884261,884353,884459,884559,884635,884712"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2859,2860,2861,2866,2871,2872,2880,6277", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "197340,197414,197504,197732,198005,198085,198572,435925", "endLines": "2859,2860,2865,2870,2871,2879,2883,6279", "endColumns": "73,89,12,12,79,12,12,12", "endOffsets": "197409,197499,197727,198000,198080,198567,198751,436048"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_banner_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "946", "startColumns": "4", "startOffsets": "46170", "endColumns": "54", "endOffsets": "46220"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,41,-1,-1,45,38,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,42,46,39,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1858,1624,-1,-1,1754,1485,-1,-1,-1,1579,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1670,1805,1531,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58,45,-1,-1,50,45,-1,-1,-1,44,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,52,47,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1912,1665,-1,-1,1800,1526,-1,-1,-1,1619,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1713,1853,1574,-1"}, "to": {"startLines": "849,854,869,874,875,876,877,878,879,880,881,890,891,932,933,935,936,941,942,943,949,950,951,952,1193,1195,1196,1205,1210,1213,1223,1224,1227,1229,1230,1231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39747,40117,41177,41476,41522,41572,41616,41663,41710,41761,41814,42535,42594,45394,45442,45541,45592,45904,45956,45995,46326,46371,46416,46471,64677,64787,64835,65476,65726,65879,66563,66610,66780,66875,66928,66976", "endColumns": "50,38,49,45,49,43,46,46,50,52,49,58,45,47,53,50,45,51,38,39,44,44,54,56,40,47,52,40,37,54,46,48,47,52,47,39", "endOffsets": "39793,40151,41222,41517,41567,41611,41658,41705,41756,41809,41859,42589,42635,45437,45491,45587,45633,45951,45990,46030,46366,46411,46466,46523,64713,64830,64883,65512,65759,65929,66605,66654,66823,66923,66971,67011"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,59,83,101,106,113,161,171,2298,2299,2300,2301,2305,2306,2307,2309,2311,2347,2348,2349,2350,2351,2352,2353,2354,2355,2360,2361,2362,2363,2367,2369,2370,2371,2379,2380,2385,2387,2390,2391,2402,2403,2404,2405,2406,2407,2408,2409,2410,2427,2428,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2550,2551,2553,2554,2555,2556,2557,2563,2564,2569,2570,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2670,2672,2673,2674,2675,2676,2677,2678,2679,2680,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2784,2785,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2828", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1586,1980,2719,3329,3517,3764,5336,5640,144163,144228,144279,144339,144647,144712,144819,144934,145135,149525,149609,149676,149760,149849,149927,149997,150068,150129,150534,150598,150699,150739,150981,151113,151196,151269,152036,152093,152569,152748,153105,153188,154401,154448,154493,154533,154587,154671,154731,154773,154854,156838,156897,160087,160145,160205,160262,160316,160379,160446,160521,160612,160697,160783,160879,160967,161058,161148,161236,161324,161420,161505,161582,161644,161706,161758,161818,161895,161961,162031,162087,162159,162221,162283,172826,172902,173020,173088,173152,173198,173284,174160,174228,174517,174571,174772,174818,174882,174952,175015,175073,175153,175230,175332,175431,175537,175637,175763,175835,175930,176012,176100,176191,176267,176351,176452,176530,176585,176679,177308,177377,177432,177488,177556,177611,177688,177761,177816,177877,177926,177985,178030,178082,178144,178253,178330,178389,178452,178557,178612,178661,178727,178782,178855,178912,178970,179025,179086,179135,179185,179270,179349,179425,179485,179540,179597,179670,179742,179805,179872,179953,180024,180087,180156,180244,180304,180361,180419,180483,180539,180586,180642,180698,180789,180855,180914,181011,181072,181128,181198,181261,181336,181410,181484,181914,182053,182132,182200,182261,182331,182417,182506,182609,182665,182861,183020,183136,183324,183450,183650,183790,183850,183918,183976,184193,184331,184421,184556,184721,184792,184894,185043,185307,185405,185609,185665,185818,185922,185991,186145,186330,186400,186464,186523,186590,186649,186712,186758,186819,186895,186973,187047,187105,187189,187265,187365,187461,187557,187655,187734,187802,187876,187946,188025,188077,188125,188171,188215,188282,188342,188409,188494,188562,188644,188722,188834,188939,189034,189127,189214,189300,189376,189466,189538,189595,189667,189752,189833,189906,189979,190053,190122,190203,190291,190353,190447,190531,190598,190661,190753,190845,190920,190992,191059,191133,191220,191290,191364,191434,191498,191592,191652,191718,191784,191836,191915,191973,192046,192125,192187,192245,192324,192366,192433,192498,192558,192630,192724,192793,192859,192909,192981,193103,193161,193283,193344,193432,193516,193626,193712,193786,193850,193912,193986,194036,194110,194247,194329,194417,194486,194557,194633,194708,194758,194833,194885,195052", "endLines": "49,63,88,105,112,116,165,175,2298,2299,2300,2301,2305,2306,2307,2309,2311,2347,2348,2349,2350,2351,2352,2353,2354,2355,2360,2361,2362,2363,2367,2369,2370,2371,2379,2380,2385,2387,2390,2391,2402,2403,2404,2405,2406,2407,2408,2409,2410,2427,2428,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2550,2551,2553,2554,2555,2556,2557,2563,2564,2569,2570,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2670,2672,2673,2674,2675,2676,2677,2678,2679,2680,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2784,2785,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2828", "endColumns": "19,19,19,19,19,19,19,19,64,50,59,58,64,106,48,50,79,83,66,83,88,77,69,70,60,84,63,100,39,81,66,82,72,91,56,60,71,49,82,90,46,44,39,53,83,59,41,80,66,58,57,57,59,56,53,62,66,74,90,84,85,95,87,90,89,87,87,95,84,76,61,61,51,59,76,65,69,55,71,61,61,78,75,73,67,63,45,85,73,67,72,53,77,45,63,69,62,57,79,76,101,98,105,99,125,71,94,81,87,90,75,83,100,77,54,93,67,68,54,55,67,54,76,72,54,60,48,58,44,51,61,108,76,58,62,104,54,48,65,54,72,56,57,54,60,48,49,84,78,75,59,54,56,72,71,62,66,80,70,62,68,87,59,56,57,63,55,46,55,55,90,65,58,96,60,55,69,62,74,73,73,81,67,78,67,60,69,85,88,102,55,56,158,115,187,125,199,139,59,67,57,216,137,89,134,164,70,101,148,263,97,203,55,152,103,68,23,184,69,63,58,66,58,62,45,60,75,77,73,57,83,75,99,95,95,97,78,67,73,69,78,51,47,45,43,66,59,66,84,67,81,77,111,104,94,92,86,85,75,89,71,56,71,84,80,72,72,73,68,80,87,61,93,83,66,62,91,91,74,71,66,73,86,69,73,69,63,93,59,65,65,51,78,57,72,78,61,57,78,41,66,64,59,71,93,68,65,49,71,121,57,65,60,87,83,109,85,73,63,61,73,49,73,136,81,87,68,70,75,74,49,74,51,68,63", "endOffsets": "1697,2124,2914,3512,3759,3891,5479,5791,144223,144274,144334,144393,144707,144814,144863,144980,145210,149604,149671,149755,149844,149922,149992,150063,150124,150209,150593,150694,150734,150816,151043,151191,151264,151356,152088,152149,152636,152793,153183,153274,154443,154488,154528,154582,154666,154726,154768,154849,154916,156892,156950,160140,160200,160257,160311,160374,160441,160516,160607,160692,160778,160874,160962,161053,161143,161231,161319,161415,161500,161577,161639,161701,161753,161813,161890,161956,162026,162082,162154,162216,162278,162357,172897,172971,173083,173147,173193,173279,173353,174223,174296,174566,174644,174813,174877,174947,175010,175068,175148,175225,175327,175426,175532,175632,175758,175830,175925,176007,176095,176186,176262,176346,176447,176525,176580,176674,176742,177372,177427,177483,177551,177606,177683,177756,177811,177872,177921,177980,178025,178077,178139,178248,178325,178384,178447,178552,178607,178656,178722,178777,178850,178907,178965,179020,179081,179130,179180,179265,179344,179420,179480,179535,179592,179665,179737,179800,179867,179948,180019,180082,180151,180239,180299,180356,180414,180478,180534,180581,180637,180693,180784,180850,180909,181006,181067,181123,181193,181256,181331,181405,181479,181561,181977,182127,182195,182256,182326,182412,182501,182604,182660,182717,183015,183131,183319,183445,183645,183785,183845,183913,183971,184188,184326,184416,184551,184716,184787,184889,185038,185302,185400,185604,185660,185813,185917,185986,186140,186325,186395,186459,186518,186585,186644,186707,186753,186814,186890,186968,187042,187100,187184,187260,187360,187456,187552,187650,187729,187797,187871,187941,188020,188072,188120,188166,188210,188277,188337,188404,188489,188557,188639,188717,188829,188934,189029,189122,189209,189295,189371,189461,189533,189590,189662,189747,189828,189901,189974,190048,190117,190198,190286,190348,190442,190526,190593,190656,190748,190840,190915,190987,191054,191128,191215,191285,191359,191429,191493,191587,191647,191713,191779,191831,191910,191968,192041,192120,192182,192240,192319,192361,192428,192493,192553,192625,192719,192788,192854,192904,192976,193098,193156,193222,193339,193427,193511,193621,193707,193781,193845,193907,193981,194031,194105,194242,194324,194412,194481,194552,194628,194703,194753,194828,194880,194949,195111"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "947", "startColumns": "4", "startOffsets": "46225", "endColumns": "56", "endOffsets": "46277"}}]}, {"outputFile": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-73:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e09aa481a2483f3d3bc09954c2742862\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2181", "startColumns": "4", "startOffsets": "136504", "endColumns": "42", "endOffsets": "136542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3301c563da9d289aac88c6ca3529e20c\\transformed\\recyclerview-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "697,1388,1389,1390,1399,1400,1401,2110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "31852,77317,77376,77424,78138,78213,78289,132796", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "31903,77371,77419,77475,78208,78284,78356,132857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\be0b7d272e566bfd154d1bf5d5ba0c43\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2105,2143,2186", "startColumns": "4,4,4", "startOffsets": "132564,134529,136754", "endColumns": "56,64,63", "endOffsets": "132616,134589,136813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\19bfc16374171ba7a26e435a1ff1fa3b\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "294,10173", "startColumns": "4,4", "startOffsets": "11766,698011", "endLines": "294,10175", "endColumns": "60,12", "endOffsets": "11822,698151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2302,2303", "startColumns": "4,4", "startOffsets": "144398,144480", "endColumns": "81,83", "endOffsets": "144475,144559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\cb2a86983ad6559594debb17aca21969\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2109,2130", "startColumns": "4,4", "startOffsets": "132742,133890", "endColumns": "53,66", "endOffsets": "132791,133952"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1398,2040,2041,2042,2058,2059,2060,2061", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "78091,128616,128668,128718,129654,129704,129752,129800", "endColumns": "46,51,49,49,49,47,47,47", "endOffsets": "78133,128663,128713,128763,129699,129747,129795,129843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\fbfe99676c41211a4bfd510557294d9b\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2136,2183", "startColumns": "4,4", "startOffsets": "134210,136590", "endColumns": "41,59", "endOffsets": "134247,136645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,850,851,852,853,855,856,857,858,859,860,865,866,928,929,930,931,937,938,939,940,944,945,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1197,1198,1199,1200,1201,1202,1203,1204,1211,1212,1214,1215,1216,1217,1219,1220,1221,1222,1225,1226,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1386,1387,1391,1392,1393,1394,1395,1396,1397,2050,2051,2052,2053,2054,2055,2056,2057,2099,2100,2101,2102,2108,2134,2135,2144,2178,2196,2197,2200,2201,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2597,2833,2834,2835,2836,2837,2896,2904,2905,2909,2913,2924,2929,2958,2965,2969,2973,2978,2982,2986,2990,2994,2998,3002,3008,3012,3018,3022,3028,3032,3037,3041,3044,3048,3054,3058,3064,3068,3074,3077,3081,3085,3089,3093,3097,3098,3099,3100,3103,3106,3109,3112,3116,3117,3118,3119,3160,3163,3165,3167,3169,3174,3175,3179,3185,3189,3190,3192,3204,3205,3209,3215,3219,3296,3297,3301,3328,3332,3333,3337,5139,5311,5337,5508,5534,5565,5573,5579,5595,5617,5622,5627,5637,5646,5655,5659,5666,5685,5692,5693,5702,5705,5708,5712,5716,5720,5723,5724,5729,5734,5744,5749,5756,5762,5763,5766,5770,5775,5777,5779,5782,5785,5787,5791,5794,5801,5804,5807,5811,5813,5817,5819,5821,5823,5827,5835,5843,5855,5861,5870,5873,5884,5887,5888,5893,5894,6394,6463,6537,6538,6548,6557,6713,6715,6719,6722,6725,6728,6731,6734,6737,6740,6744,6747,6750,6753,6757,6760,6764,6915,6916,6917,6918,6919,6920,6921,6922,6923,6924,6925,6926,6927,6928,6929,6930,6931,6932,6933,6934,6935,6937,6939,6940,6941,6942,6943,6944,6945,6946,6948,6949,6951,6952,6954,6956,6957,6959,6960,6961,6962,6963,6964,6966,6967,6968,6969,6970,7255,7257,7259,7261,7262,7263,7264,7265,7266,7267,7268,7269,7270,7271,7272,7273,7275,7276,7277,7278,7279,7280,7281,7283,7287,7470,7471,7472,7473,7474,7475,7479,7480,7481,8022,8024,8026,8028,8030,8032,8033,8034,8035,8037,8039,8041,8042,8043,8044,8045,8046,8047,8048,8049,8050,8051,8052,8055,8056,8057,8058,8060,8062,8063,8065,8066,8068,8070,8072,8073,8074,8075,8076,8077,8078,8079,8080,8081,8082,8083,8085,8086,8087,8088,8090,8091,8092,8093,8094,8096,8098,8100,8102,8103,8104,8105,8106,8107,8108,8109,8110,8111,8112,8113,8114,8115,8116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12942,17487,17792,36391,38574,38629,39086,39150,39220,39281,39356,39432,39509,39798,39883,39965,40041,40156,40233,40311,40417,40523,40602,40931,40988,45114,45188,45263,45328,45638,45698,45759,45831,46035,46102,59621,59680,59739,59798,59857,59916,59970,60024,60077,60131,60185,60239,64888,64962,65041,65114,65188,65259,65331,65403,65764,65821,65934,66007,66081,66155,66277,66349,66422,66492,66659,66719,67622,67691,67760,67830,67904,67980,68044,68121,68197,68274,68339,68408,68485,68560,68629,68697,68774,68840,68901,68998,69063,69132,69231,69302,69361,69419,69476,69535,69599,69670,69742,69814,69886,69958,70025,70093,70161,70220,70283,70347,70437,70528,70588,70654,70721,70787,70857,70921,70974,71041,71102,71169,71282,71340,71403,71468,71533,71608,71681,71753,71797,71844,71890,71939,72000,72061,72122,72184,72248,72312,72376,72441,72504,72564,72625,72691,72750,72810,72872,72943,73003,77144,77230,77480,77570,77657,77745,77827,77910,78000,129201,129253,129311,129356,129422,129486,129543,129600,132248,132305,132353,132402,132708,134114,134161,134594,136379,137267,137331,137521,137581,142297,142371,142441,142519,142573,142643,142728,142776,142822,142883,142946,143012,143076,143147,143210,143275,143339,143400,143461,143513,143586,143660,143729,143804,143878,143952,144093,176747,195479,195557,195647,195735,195831,199436,200018,200107,200354,200635,201301,201586,203395,203872,204094,204316,204592,204819,205049,205279,205509,205739,205966,206385,206611,207036,207266,207694,207913,208196,208404,208535,208762,209188,209413,209840,210061,210486,210606,210882,211183,211507,211798,212112,212249,212380,212485,212727,212894,213098,213306,213577,213689,213801,213906,215999,216213,216359,216499,216585,216933,217021,217267,217685,217934,218016,218114,218771,218871,219123,219547,219802,225692,225781,226018,228042,228284,228386,228639,364208,374889,376405,387100,388628,390385,391011,391431,392692,393957,394213,394449,394996,395490,396095,396293,396873,398241,398616,398734,399272,399429,399625,399898,400154,400324,400465,400529,400894,401261,401937,402201,402539,402892,402986,403172,403478,403740,403865,403992,404231,404442,404561,404754,404931,405386,405567,405689,405948,406061,406248,406350,406457,406586,406861,407369,407865,408742,409036,409606,409755,410487,410659,410743,411079,411171,443475,448706,454421,454483,455061,455645,463843,463956,464185,464345,464497,464668,464834,465003,465170,465333,465576,465746,465919,466090,466364,466563,466768,476266,476350,476446,476542,476640,476740,476842,476944,477046,477148,477250,477350,477446,477558,477687,477810,477941,478072,478170,478284,478378,478518,478652,478748,478860,478960,479076,479172,479284,479384,479524,479660,479824,479954,480112,480262,480403,480547,480682,480794,480944,481072,481200,481336,481468,481598,481728,481840,499498,499644,499788,499926,499992,500082,500158,500262,500352,500454,500562,500670,500770,500850,500942,501040,501150,501202,501280,501386,501478,501582,501692,501814,501977,515685,515765,515865,515955,516065,516155,516396,516490,516596,556475,556575,556687,556801,556917,557033,557127,557241,557353,557455,557575,557697,557779,557883,558003,558129,558227,558321,558409,558521,558637,558759,558871,559046,559162,559248,559340,559452,559576,559643,559769,559837,559965,560109,560237,560306,560401,560516,560629,560728,560837,560948,561059,561160,561265,561365,561495,561586,561709,561803,561915,562001,562105,562201,562289,562407,562511,562615,562741,562829,562937,563037,563127,563237,563321,563423,563507,563561,563625,563731,563817,563927,564011", "endLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,850,851,852,853,855,856,857,858,859,860,865,866,928,929,930,931,937,938,939,940,944,945,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1197,1198,1199,1200,1201,1202,1203,1204,1211,1212,1214,1215,1216,1217,1219,1220,1221,1222,1225,1226,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1386,1387,1391,1392,1393,1394,1395,1396,1397,2050,2051,2052,2053,2054,2055,2056,2057,2099,2100,2101,2102,2108,2134,2135,2144,2178,2196,2197,2200,2201,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2597,2833,2834,2835,2836,2837,2903,2904,2908,2912,2916,2928,2934,2964,2968,2972,2977,2981,2985,2989,2993,2997,3001,3007,3011,3017,3021,3027,3031,3036,3040,3043,3047,3053,3057,3063,3067,3073,3076,3080,3084,3088,3092,3096,3097,3098,3099,3102,3105,3108,3111,3115,3116,3117,3118,3119,3162,3164,3166,3168,3173,3174,3178,3184,3188,3189,3191,3203,3204,3208,3214,3218,3219,3296,3300,3327,3331,3332,3336,3364,5310,5336,5507,5533,5564,5572,5578,5594,5616,5621,5626,5636,5645,5654,5658,5665,5684,5691,5692,5701,5704,5707,5711,5715,5719,5722,5723,5728,5733,5743,5748,5755,5761,5762,5765,5769,5774,5776,5778,5781,5784,5786,5790,5793,5800,5803,5806,5810,5812,5816,5818,5820,5822,5826,5834,5842,5854,5860,5869,5872,5883,5886,5887,5892,5893,5898,6462,6532,6537,6547,6556,6557,6714,6718,6721,6724,6727,6730,6733,6736,6739,6743,6746,6749,6752,6756,6759,6763,6767,6915,6916,6917,6918,6919,6920,6921,6922,6923,6924,6925,6926,6927,6928,6929,6930,6931,6932,6933,6934,6936,6938,6939,6940,6941,6942,6943,6944,6945,6947,6948,6950,6951,6953,6955,6956,6958,6959,6960,6961,6962,6963,6965,6966,6967,6968,6969,6970,7256,7258,7260,7261,7262,7263,7264,7265,7266,7267,7268,7269,7270,7271,7272,7274,7275,7276,7277,7278,7279,7280,7282,7286,7290,7470,7471,7472,7473,7474,7478,7479,7480,7481,8023,8025,8027,8029,8031,8032,8033,8034,8036,8038,8040,8041,8042,8043,8044,8045,8046,8047,8048,8049,8050,8051,8054,8055,8056,8057,8059,8061,8062,8064,8065,8067,8069,8071,8072,8073,8074,8075,8076,8077,8078,8079,8080,8081,8082,8084,8085,8086,8087,8089,8090,8091,8092,8093,8095,8097,8099,8101,8102,8103,8104,8105,8106,8107,8108,8109,8110,8111,8112,8113,8114,8115,8116", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "12992,17527,17836,36427,38624,38686,39145,39215,39276,39351,39427,39504,39582,39878,39960,40036,40112,40228,40306,40412,40518,40597,40677,40983,41041,45183,45258,45323,45389,45693,45754,45826,45899,46097,46165,59675,59734,59793,59852,59911,59965,60019,60072,60126,60180,60234,60288,64957,65036,65109,65183,65254,65326,65398,65471,65816,65874,66002,66076,66150,66225,66344,66417,66487,66558,66714,66775,67686,67755,67825,67899,67975,68039,68116,68192,68269,68334,68403,68480,68555,68624,68692,68769,68835,68896,68993,69058,69127,69226,69297,69356,69414,69471,69530,69594,69665,69737,69809,69881,69953,70020,70088,70156,70215,70278,70342,70432,70523,70583,70649,70716,70782,70852,70916,70969,71036,71097,71164,71277,71335,71398,71463,71528,71603,71676,71748,71792,71839,71885,71934,71995,72056,72117,72179,72243,72307,72371,72436,72499,72559,72620,72686,72745,72805,72867,72938,72998,73066,77225,77312,77565,77652,77740,77822,77905,77995,78086,129248,129306,129351,129417,129481,129538,129595,129649,132300,132348,132397,132448,132737,134156,134205,134635,136406,137326,137388,137576,137633,142366,142436,142514,142568,142638,142723,142771,142817,142878,142941,143007,143071,143142,143205,143270,143334,143395,143456,143508,143581,143655,143724,143799,143873,143947,144088,144158,176795,195552,195642,195730,195826,195916,200013,200102,200349,200630,200882,201581,201974,203867,204089,204311,204587,204814,205044,205274,205504,205734,205961,206380,206606,207031,207261,207689,207908,208191,208399,208530,208757,209183,209408,209835,210056,210481,210601,210877,211178,211502,211793,212107,212244,212375,212480,212722,212889,213093,213301,213572,213684,213796,213901,214018,216208,216354,216494,216580,216928,217016,217262,217680,217929,218011,218109,218766,218866,219118,219542,219797,219891,225776,226013,228037,228279,228381,228634,230790,374884,376400,387095,388623,390380,391006,391426,392687,393952,394208,394444,394991,395485,396090,396288,396868,398236,398611,398729,399267,399424,399620,399893,400149,400319,400460,400524,400889,401256,401932,402196,402534,402887,402981,403167,403473,403735,403860,403987,404226,404437,404556,404749,404926,405381,405562,405684,405943,406056,406243,406345,406452,406581,406856,407364,407860,408737,409031,409601,409750,410482,410654,410738,411074,411166,411444,448701,454072,454478,455056,455640,455731,463951,464180,464340,464492,464663,464829,464998,465165,465328,465571,465741,465914,466085,466359,466558,466763,467093,476345,476441,476537,476635,476735,476837,476939,477041,477143,477245,477345,477441,477553,477682,477805,477936,478067,478165,478279,478373,478513,478647,478743,478855,478955,479071,479167,479279,479379,479519,479655,479819,479949,480107,480257,480398,480542,480677,480789,480939,481067,481195,481331,481463,481593,481723,481835,481975,499639,499783,499921,499987,500077,500153,500257,500347,500449,500557,500665,500765,500845,500937,501035,501145,501197,501275,501381,501473,501577,501687,501809,501972,502129,515760,515860,515950,516060,516150,516391,516485,516591,516683,556570,556682,556796,556912,557028,557122,557236,557348,557450,557570,557692,557774,557878,557998,558124,558222,558316,558404,558516,558632,558754,558866,559041,559157,559243,559335,559447,559571,559638,559764,559832,559960,560104,560232,560301,560396,560511,560624,560723,560832,560943,561054,561155,561260,561360,561490,561581,561704,561798,561910,561996,562100,562196,562284,562402,562506,562610,562736,562824,562932,563032,563122,563232,563316,563418,563502,563556,563620,563726,563812,563922,564006,564126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "659,847,848,867,868,1191,1192,1330,1331,1332,1333,1334,1335,1336,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2111,2112,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2169,2266,2318,2319,2320,2321,2322,2323,2324,2671,6971,6972,6976,6977,6981,8117,8118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30242,39587,39659,41046,41111,64545,64614,73504,73574,73642,73714,73784,73845,73919,127638,127699,127760,127822,127886,127948,128009,128077,128177,128237,128303,128376,128445,128502,128554,130125,130197,130273,130338,130397,130456,130516,130576,130636,130696,130756,130816,130876,130936,130996,131056,131115,131175,131235,131295,131355,131415,131475,131535,131595,131655,131715,131774,131834,131894,131953,132012,132071,132130,132189,132862,132897,134640,134695,134758,134813,134871,134927,134985,135046,135109,135166,135217,135275,135325,135386,135443,135509,135543,135925,141907,146304,146371,146443,146512,146581,146655,146727,181982,481980,482097,482298,482408,482609,564131,564203", "endLines": "659,847,848,867,868,1191,1192,1330,1331,1332,1333,1334,1335,1336,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2111,2112,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2169,2266,2318,2319,2320,2321,2322,2323,2324,2671,6971,6975,6976,6980,6981,8117,8118", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "30297,39654,39742,41106,41172,64609,64672,73569,73637,73709,73779,73840,73914,73987,127694,127755,127817,127881,127943,128004,128072,128172,128232,128298,128371,128440,128497,128549,128611,130192,130268,130333,130392,130451,130511,130571,130631,130691,130751,130811,130871,130931,130991,131051,131110,131170,131230,131290,131350,131410,131470,131530,131590,131650,131710,131769,131829,131889,131948,132007,132066,132125,132184,132243,132892,132927,134690,134753,134808,134866,134922,134980,135041,135104,135161,135212,135270,135320,135381,135438,135504,135538,135573,135955,141972,146366,146438,146507,146576,146650,146722,146810,182048,482092,482293,482403,482604,482733,564198,564265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2204,2337", "startColumns": "4,4", "startOffsets": "137773,148232", "endColumns": "67,166", "endOffsets": "137836,148394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\97daf22df7e5c6e3b5afccf9cf12b867\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,870,871,872,873,1326,1327,1328,2917,6292,6294,6297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7641,41227,41288,41350,41412,73279,73338,73395,200887,436733,436797,436923", "endLines": "220,870,871,872,873,1326,1327,1328,2923,6293,6296,6299", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "7688,41283,41345,41407,41471,73333,73390,73444,201296,436792,436918,437046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\700cabc0e517e16b3b1a2efd32cecc35\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "727,732,733,734,2103", "startColumns": "4,4,4,4,4", "startOffsets": "33364,33543,33603,33655,132453", "endLines": "731,732,733,734,2103", "endColumns": "11,59,51,44,59", "endOffsets": "33538,33598,33650,33695,132508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c5c1f58a58ca000404c5b280db793ba2\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2106,2107,2129,2138,2139,2170,2171,2172,2173,2174,2175,2176,2177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "132621,132661,133847,134295,134350,135960,136005,136059,136115,136167,136219,136268,136329", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "132656,132703,133885,134345,134392,136000,136054,136110,136162,136214,136263,136324,136374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b67b39daef2398884ed6af353040af9d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2304", "startColumns": "4", "startOffsets": "144564", "endColumns": "82", "endOffsets": "144642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\07e10290a8de075a2af92b4127d3fcd4\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "187,194,196,202,203,211,213,214,221,227,229,230,231,232,233,290,291,292,293,295,299,300,301,304,314,324,352,353,358,359,364,369,370,371,376,377,382,383,388,389,390,396,397,398,403,409,410,428,429,435,436,437,438,441,444,447,448,451,454,455,456,457,458,461,464,465,466,467,473,478,481,484,485,486,491,492,493,496,499,500,503,506,509,512,513,514,517,520,521,526,527,533,538,541,544,545,546,547,548,549,550,551,552,553,554,555,571,652,653,654,655,660,667,675,676,677,680,685,687,695,696,725,740,777,778,782,783,794,795,796,802,805,811,815,816,817,818,819,828,2116,2180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6151,6435,6547,6782,6843,7134,7241,7291,7693,8000,8120,8175,8235,8300,8359,11545,11597,11658,11720,11827,11960,12012,12062,12223,12630,13053,14995,15054,15251,15308,15503,15684,15738,15795,15987,16045,16241,16297,16491,16548,16599,16821,16873,16928,17118,17334,17384,18285,18341,18547,18608,18668,18738,18871,19002,19130,19198,19327,19453,19515,19578,19646,19713,19836,19961,20028,20093,20158,20447,20628,20749,20870,20936,21003,21213,21282,21348,21473,21599,21666,21792,21919,22044,22171,22227,22292,22418,22541,22606,22814,22881,23169,23349,23469,23589,23654,23716,23778,23842,23904,23963,24023,24084,24145,24204,24264,24924,29867,29918,29967,30015,30302,30594,30902,30949,31009,31115,31295,31407,31742,31796,33268,33951,36281,36332,36541,36593,37029,37088,37142,37380,37558,37760,37899,37945,38000,38045,38089,38437,133081,136459", "endLines": "193,194,200,202,210,211,213,214,221,227,229,230,231,232,233,290,291,292,293,298,299,300,301,313,321,324,352,357,358,363,368,369,370,375,376,381,382,387,388,389,395,396,397,402,408,409,410,428,434,435,436,437,440,443,446,447,450,453,454,455,456,457,460,463,464,465,466,472,477,480,483,484,485,490,491,492,495,498,499,502,505,508,511,512,513,516,519,520,525,526,532,537,540,543,544,545,546,547,548,549,550,551,552,553,554,570,576,652,653,654,655,666,674,675,676,679,684,685,694,695,696,725,740,777,778,782,791,794,795,801,802,810,814,815,816,817,818,827,831,2116,2180", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "6430,6486,6728,6838,7129,7181,7286,7339,7736,8046,8170,8230,8295,8354,8416,11592,11653,11715,11761,11955,12007,12057,12108,12625,12937,13093,15049,15246,15303,15498,15679,15733,15790,15982,16040,16236,16292,16486,16543,16594,16816,16868,16923,17113,17329,17379,17431,18336,18542,18603,18663,18733,18866,18997,19125,19193,19322,19448,19510,19573,19641,19708,19831,19956,20023,20088,20153,20442,20623,20744,20865,20931,20998,21208,21277,21343,21468,21594,21661,21787,21914,22039,22166,22222,22287,22413,22536,22601,22809,22876,23164,23344,23464,23584,23649,23711,23773,23837,23899,23958,24018,24079,24140,24199,24259,24919,25170,29913,29962,30010,30068,30589,30897,30944,31004,31110,31290,31344,31737,31791,31847,33309,33993,36327,36386,36588,36918,37083,37137,37375,37430,37755,37894,37940,37995,38040,38084,38432,38569,133117,136499"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\generated\\res\\processFdroidDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2358,2381,2382,2383,2384,2386,2568", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "150320,152154,152236,152340,152449,152641,174441", "endColumns": "143,81,103,108,119,106,75", "endOffsets": "150459,152231,152335,152444,152564,152743,174512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\59e516bb99fd3e6425d2c9627b07a4fa\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7462", "startColumns": "4", "startOffsets": "515275", "endLines": "7469", "endColumns": "8", "endOffsets": "515680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1206,1207,1208,1209,2571,2572,6709", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "65517,65575,65622,65678,174649,174710,463592", "endLines": "1206,1207,1208,1209,2571,2572,6712", "endColumns": "57,46,55,47,60,61,10", "endOffsets": "65570,65617,65673,65721,174705,174767,463838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ebcc5988bd502c901a17c4d3fc1eb859\\transformed\\zxing-android-embedded-4.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,2187,2188,2189,2190,2191,2192,2193,2194,2195,2308,2359,2393,2394,2395,2396,2397,2398,2399,2400,2401,2829,2830,2831,2832,10176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "67016,67088,67149,67213,67278,67343,67397,67451,67505,67564,136818,136865,136914,136962,137004,137053,137105,137163,137213,144868,150464,153338,153435,153552,153648,153809,153922,154008,154155,154254,195116,195175,195222,195366,698156", "endLines": "1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,2187,2188,2189,2190,2191,2192,2193,2194,2195,2308,2359,2393,2394,2395,2396,2397,2398,2399,2400,2401,2829,2830,2831,2832,10177", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10", "endOffsets": "67083,67144,67208,67273,67338,67392,67446,67500,67559,67617,136860,136909,136957,136999,137048,137100,137158,137208,137262,144929,150529,153430,153547,153643,153804,153917,154003,154150,154249,154396,195170,195217,195361,195474,698251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f68ab7854e90816622d4e41b6bdf2b2c\\transformed\\camera-view-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "415,699", "startColumns": "4,4", "startOffsets": "17640,31954", "endLines": "418,706", "endColumns": "11,11", "endOffsets": "17787,32256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "834,1194,2043,2044,2045,2046,2047,2048,2049,2131,2132,2133,2263,2264,2356,2372,2552,2565,2681,2826,2827,6272,6558,6561,6567,6573,6576,6582,6586,6589,6596,6602,6605,6611,6616,6621,6628,6630,6636,6642,6650,6655,6662,6667,6673,6677,6684,6688,6694,6700,6703,6707,6708", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "38691,64718,128768,128832,128887,128955,129022,129087,129144,133957,134005,134053,141724,141787,150214,151361,172976,174301,182722,194954,195004,435167,455736,455841,456086,456424,456570,456910,457122,457285,457692,458030,458153,458492,458731,458988,459359,459419,459757,460043,460492,460784,461172,461477,461821,462066,462396,462603,462871,463144,463288,463489,463536", "endLines": "834,1194,2043,2044,2045,2046,2047,2048,2049,2131,2132,2133,2263,2264,2356,2372,2552,2567,2681,2826,2827,6288,6560,6566,6572,6575,6581,6585,6588,6595,6601,6604,6610,6615,6620,6627,6629,6635,6641,6649,6654,6661,6666,6672,6676,6683,6687,6693,6699,6702,6706,6707,6708", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "38759,64782,128827,128882,128950,129017,129082,129139,129196,134000,134048,134109,141782,141845,150247,151413,173015,174436,182856,194999,195047,436600,455836,456081,456419,456565,456905,457117,457280,457687,458025,458148,458487,458726,458983,459354,459414,459752,460038,460487,460779,461167,461472,461816,462061,462391,462598,462866,463139,463283,463484,463531,463587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3671adc343b59afebe99d3ec6265d6d7\\transformed\\Toasty-1.5.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "892,934,948,1190,1218,1228,2803", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "42640,45496,46282,64499,66230,66828,193227", "endColumns": "50,44,43,45,46,46,55", "endOffsets": "42686,45536,46321,64540,66272,66870,193278"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,7,14,19,25,34,38,42,50,55,64,69,78,89,94,117,130,139,152,156,166,176,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,301,517,689,878,1163,1298,1445,1702,1845,2129,2297,2557,2919,3102,3896,4404,4681,5058,5182,5484,5796,5953", "endLines": "6,13,18,24,33,37,41,45,54,58,68,77,82,93,100,129,138,151,155,160,170,180,186", "endColumns": "19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19", "endOffsets": "296,512,684,873,1158,1293,1440,1581,1840,1975,2292,2552,2714,3097,3324,4399,4676,5053,5177,5331,5635,5948,6146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d70395873c50a004c47266b87418baa0\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2104", "startColumns": "4", "startOffsets": "132513", "endColumns": "50", "endOffsets": "132559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\8db806c88c82d97fba3e067706ad47ec\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "835,836,837,839", "startColumns": "4,4,4,4", "startOffsets": "38764,38829,38899,39025", "endColumns": "64,69,63,60", "endOffsets": "38824,38894,38958,39081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\aaec5b52f0b87a27b87b977c567045d7\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2184", "startColumns": "4", "startOffsets": "136650", "endColumns": "53", "endOffsets": "136699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c588be67285252bb290c6b2c2549687a\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2185", "startColumns": "4", "startOffsets": "136704", "endColumns": "49", "endOffsets": "136749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f7eb1da5e42044f662b8e59e06978dfd\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7313", "startColumns": "4", "startOffsets": "503276", "endLines": "7316", "endColumns": "12", "endOffsets": "503494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\43f80c422e65d725a8b34fc24966139b\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "323,326,1337", "startColumns": "4,4,4", "startOffsets": "12997,13161,73992", "endColumns": "55,47,51", "endOffsets": "13048,13204,74039"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6912", "startColumns": "4", "startOffsets": "476134", "endLines": "6914", "endColumns": "12", "endOffsets": "476261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "861,862,863,864,1324,1325,2357,2376,2377,2378", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40682,40740,40806,40869,73136,73207,150252,151821,151888,151967", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "40735,40801,40864,40926,73202,73274,150315,151883,151962,152031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "882,883,884,885,886,887,888,889,2329,2330,2331,2332,2333,2334,2335,2336,2338,2339,2340,2341,2342,2343,2344,2345,2346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41864,41954,42034,42124,42214,42294,42375,42455,147192,147297,147478,147603,147710,147890,148013,148129,148399,148587,148692,148873,148998,149173,149321,149384,149446", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "41949,42029,42119,42209,42289,42370,42450,42530,147292,147473,147598,147705,147885,148008,148124,148227,148582,148687,148868,148993,149168,149316,149379,149441,149520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3fac676b4e1c0ee2b6233dfa300c1ec0\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2182", "startColumns": "4", "startOffsets": "136547", "endColumns": "42", "endOffsets": "136585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,717,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1323,1329,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2062,2063,2113,2114,2115,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2137,2140,2141,2142,2162,2163,2164,2165,2166,2167,2168,2179,2198,2199,2202,2203,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2265,2267,2310,2312,2313,2314,2315,2316,2317,2325,2326,2327,2328,2364,2368,2373,2374,2375,2388,2389,2392,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2494,2497,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2558,2559,2560,2561,2562,2598,2601,2602,2668,2669,2838,2842,2846,2850,2854,2855,2935,2943,2950,3120,3123,3133,3142,3151,3220,3221,3222,3223,3229,3230,3231,3232,3233,3234,3240,3241,3242,3243,3244,3249,3250,3254,3255,3261,3265,3266,3267,3268,3278,3279,3280,3284,3285,3291,3295,3365,3368,3369,3374,3375,3378,3379,3380,3381,3645,3652,3913,3919,4183,4190,4451,4457,4520,4602,4654,4736,4798,4880,4944,4996,5078,5086,5092,5103,5107,5111,5124,5899,5915,5922,5928,5945,5958,5978,5995,6004,6009,6016,6036,6049,6066,6072,6078,6085,6089,6095,6109,6112,6122,6123,6124,6172,6176,6180,6184,6185,6186,6189,6205,6212,6226,6271,6300,6306,6310,6314,6319,6326,6332,6333,6336,6340,6345,6358,6362,6367,6372,6377,6380,6383,6386,6390,6533,6534,6535,6536,6768,6769,6770,6771,6772,6773,6774,6775,6776,6777,6778,6779,6780,6781,6782,6783,6784,6785,6786,6790,6794,6798,6802,6806,6810,6814,6815,6816,6817,6818,6819,6820,6821,6825,6829,6830,6834,6835,6838,6842,6845,6848,6851,6855,6858,6861,6865,6869,6873,6877,6880,6881,6882,6883,6886,6890,6893,6896,6899,6902,6905,6908,6982,6985,6986,6989,6992,6993,6996,6997,6998,7002,7003,7008,7015,7022,7029,7036,7043,7050,7057,7064,7071,7080,7089,7098,7105,7114,7123,7126,7129,7130,7131,7132,7133,7134,7135,7136,7137,7138,7139,7140,7141,7145,7150,7155,7158,7159,7160,7161,7162,7170,7178,7179,7187,7191,7199,7207,7215,7223,7231,7232,7240,7248,7249,7252,7291,7293,7298,7300,7305,7309,7317,7318,7319,7320,7324,7328,7329,7333,7334,7335,7336,7337,7338,7339,7340,7341,7342,7343,7347,7348,7349,7350,7354,7355,7356,7357,7361,7365,7366,7370,7371,7372,7377,7378,7379,7380,7381,7382,7383,7384,7385,7386,7387,7388,7389,7390,7391,7392,7393,7394,7395,7396,7397,7401,7402,7403,7409,7410,7414,7416,7417,7422,7423,7424,7425,7426,7427,7431,7432,7433,7439,7440,7444,7446,7450,7454,7458,7482,7483,7484,7485,7488,7491,7494,7497,7500,7505,7509,7512,7513,7518,7522,7527,7533,7539,7544,7548,7553,7557,7561,7602,7603,7604,7605,7606,7610,7611,7612,7613,7617,7621,7625,7629,7633,7637,7641,7645,7651,7652,7693,7707,7712,7738,7745,7748,7759,7764,7767,7770,7825,7831,7832,7835,7838,7841,7844,7847,7850,7853,7857,7860,7861,7862,7870,7878,7881,7886,7891,7896,7901,7905,7909,7910,7918,7919,7920,7921,7922,7930,7935,7940,7941,7942,7943,7968,7974,7979,7982,7986,7989,7993,8003,8006,8011,8014,8018,8119,8127,8141,8154,8158,8173,8184,8187,8198,8203,8207,8242,8243,8244,8256,8264,8272,8280,8288,8308,8311,8338,8343,8363,8366,8369,8376,8389,8398,8401,8421,8431,8435,8439,8452,8456,8460,8464,8470,8474,8491,8499,8503,8507,8511,8514,8518,8522,8526,8536,8543,8550,8554,8580,8590,8615,8624,8644,8654,8658,8668,8693,8703,8706,8713,8720,8727,8728,8729,8730,8731,8738,8742,8748,8754,8755,8768,8769,8770,8773,8776,8779,8782,8785,8788,8791,8794,8797,8800,8803,8806,8809,8812,8815,8818,8821,8824,8827,8830,8833,8834,8842,8850,8851,8864,8874,8878,8883,8888,8892,8895,8899,8903,8906,8910,8913,8917,8922,8927,8930,8937,8941,8945,8954,8959,8964,8965,8969,8972,8976,8989,8994,9002,9006,9010,9027,9031,9036,9054,9061,9065,9095,9098,9101,9104,9107,9110,9113,9132,9138,9146,9153,9165,9173,9178,9186,9190,9208,9215,9231,9235,9243,9246,9251,9252,9253,9254,9258,9262,9266,9270,9305,9308,9312,9316,9350,9353,9357,9361,9370,9376,9379,9389,9393,9394,9401,9405,9412,9413,9414,9417,9422,9427,9428,9432,9447,9466,9470,9471,9483,9493,9494,9506,9511,9535,9538,9544,9547,9556,9564,9568,9571,9574,9577,9581,9584,9601,9605,9608,9623,9626,9634,9639,9646,9651,9652,9657,9658,9664,9670,9676,9708,9719,9736,9743,9747,9750,9763,9772,9776,9781,9785,9789,9793,9797,9801,9805,9809,9814,9817,9829,9834,9843,9846,9853,9854,9858,9867,9873,9877,9878,9882,9903,9909,9913,9917,9918,9936,9937,9938,9939,9940,9945,9948,9949,9955,9956,9968,9980,9987,9988,9993,9998,9999,10003,10017,10022,10028,10034,10040,10045,10051,10057,10058,10064,10079,10084,10093,10102,10105,10119,10124,10135,10139,10148,10157,10158,10165,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197,15198,15199,15200,15201,15202,15203,15204,15205,15206,15207,15208,15209,15210,15211,15212,15213,15214,15215,15216,15217,15218,15219,15220,15221,15222,15223,15224,15225,15226,15227,15228,15229,15230,15231,15232,15233,15234,15235,15236,15237,15238,15239,15240,15241,15242,15243,15244,15245,15246,15247,15248,15249,15250,15251,15252,15253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6491,6733,7186,7344,7400,7460,7521,7586,7741,7791,7841,7894,7952,8051,8421,8469,8540,8612,8684,8757,8824,8873,8927,8964,9015,9075,9122,9178,9227,9285,9339,9400,9456,9507,9567,9623,9686,9735,9791,9847,9897,9956,10011,10073,10120,10174,10230,10282,10337,10391,10445,10499,10548,10606,10660,10717,10773,10820,10873,10929,10989,11052,11111,11173,11223,11277,11331,11379,11436,11489,12113,12167,13098,13209,13271,13327,13387,13440,13501,13580,13661,13733,13812,13892,13968,14046,14115,14191,14268,14339,14412,14488,14566,14635,14711,14788,14852,14923,17436,17532,17585,17841,17908,17961,18013,18063,18121,18186,18234,25175,25242,25308,25366,25435,25493,25562,25632,25705,25779,25847,25914,25984,26050,26123,26183,26259,26319,26379,26454,26522,26588,26656,26716,26775,26832,26898,26960,27017,27085,27158,27228,27290,27351,27419,27481,27551,27620,27676,27735,27797,27859,27926,27983,28044,28105,28166,28227,28283,28339,28395,28451,28509,28567,28625,28683,28740,28797,28854,28911,28970,29029,29087,29170,29253,29326,29380,29449,29505,29586,29667,29738,30073,30126,30184,31349,31908,32261,32321,32375,32445,32515,32580,32646,32711,32779,32848,32916,33046,33099,33158,33216,33314,33700,33752,33798,33848,33904,33998,34056,34114,34176,34239,34301,34360,34420,34485,34551,34616,34678,34740,34802,34864,34926,34988,35054,35121,35187,35250,35314,35377,35445,35506,35568,35630,35693,35757,35820,35884,35962,36021,36087,36167,36228,36432,36490,36923,36968,37435,37499,38963,42691,42765,42836,42902,42976,43045,43116,43189,43260,43328,43401,43477,43547,43625,43693,43759,43820,43889,43953,44019,44087,44153,44216,44284,44355,44420,44493,44556,44637,44701,44767,44837,44907,44977,45047,46528,46585,46643,46702,46762,46821,46880,46939,46998,47057,47116,47175,47234,47293,47352,47412,47473,47535,47596,47657,47718,47779,47840,47901,47961,48022,48083,48143,48204,48265,48326,48387,48448,48509,48570,48631,48692,48753,48814,48882,48951,49021,49090,49159,49228,49297,49366,49435,49504,49573,49642,49711,49771,49832,49894,49955,50016,50077,50138,50199,50260,50321,50382,50443,50504,50566,50629,50693,50756,50819,50882,50945,51008,51071,51134,51197,51260,51323,51384,51446,51509,51571,51633,51695,51757,51819,51881,51943,52005,52067,52129,52186,52272,52352,52442,52537,52629,52721,52811,52894,52987,53074,53171,53262,53363,53450,53553,53642,53741,53833,53933,54017,54111,54199,54297,54380,54471,54565,54664,54766,54864,54964,55051,55151,55237,55333,55421,55502,55593,55689,55782,55875,55966,56051,56145,56234,56332,56425,56527,56615,56719,56810,56910,57003,57104,57189,57284,57373,57472,57557,57649,57744,57844,57947,58046,58149,58238,58339,58426,58523,58611,58707,58799,58899,58989,59087,59172,59261,59350,59443,59530,60293,60359,60435,60504,60583,60656,60736,60816,60893,60961,61039,61115,61186,61267,61340,61423,61498,61583,61656,61737,61818,61892,61976,62046,62124,62194,62274,62352,62424,62506,62576,62653,62733,62818,62906,62990,63077,63151,63229,63307,63378,63459,63550,63633,63729,63827,63934,63999,64065,64118,64194,64260,64347,64423,73071,73449,74044,74098,74177,74255,74328,74393,74456,74522,74593,74664,74734,74796,74865,74931,74991,75058,75125,75181,75232,75285,75337,75391,75462,75525,75584,75646,75705,75778,75845,75915,75975,76038,76113,76185,76281,76352,76408,76479,76536,76593,76659,76723,76794,76851,76904,76967,77019,77077,78361,78430,78496,78555,78638,78697,78754,78821,78891,78965,79027,79096,79166,79265,79362,79461,79547,79633,79714,79789,79878,79969,80053,80112,80158,80224,80281,80348,80405,80487,80552,80618,80741,80825,80946,81011,81073,81171,81245,81328,81417,81481,81560,81634,81696,81792,81857,81916,81972,82028,82088,82195,82242,82302,82363,82427,82488,82548,82606,82649,82698,82750,82801,82853,82902,82951,83016,83082,83142,83203,83259,83318,83367,83415,83473,83530,83632,83689,83764,83812,83863,83925,83990,84042,84116,84179,84242,84310,84360,84422,84482,84539,84599,84648,84716,84822,84924,84993,85064,85120,85169,85269,85340,85450,85541,85623,85721,85777,85878,85988,86087,86150,86256,86333,86445,86572,86684,86811,86881,86995,87126,87223,87291,87409,87512,87630,87691,87765,87832,87937,88059,88133,88200,88310,88409,88482,88579,88701,88819,88937,88998,89120,89237,89305,89411,89513,89593,89664,89760,89827,89901,89975,90061,90151,90229,90306,90406,90477,90598,90719,90783,90908,90982,91106,91230,91297,91406,91534,91646,91725,91803,91904,91975,92097,92219,92284,92410,92522,92628,92696,92795,92899,92962,93028,93112,93225,93338,93456,93534,93606,93742,93878,93963,94103,94241,94379,94521,94603,94689,94766,94839,94948,95059,95187,95315,95447,95577,95707,95841,95930,95992,96088,96155,96272,96393,96490,96572,96659,96746,96877,97008,97143,97220,97297,97408,97522,97596,97705,97817,97884,97957,98022,98124,98220,98324,98392,98457,98551,98623,98733,98839,98912,99003,99105,99208,99303,99410,99515,99637,99759,99885,99944,100002,100126,100250,100378,100496,100614,100736,100822,100919,101053,101187,101267,101405,101537,101669,101805,101880,101956,102059,102133,102246,102327,102384,102445,102504,102564,102622,102683,102741,102791,102840,102907,102966,103025,103074,103145,103229,103299,103370,103450,103519,103582,103650,103716,103784,103849,103915,103992,104070,104176,104282,104378,104507,104596,104723,104789,104859,104945,105011,105094,105168,105266,105362,105458,105556,105665,105760,105849,105911,105971,106036,106093,106174,106228,106285,106382,106492,106553,106668,106789,106884,106976,107069,107125,107184,107233,107325,107374,107428,107482,107536,107590,107644,107699,107809,107919,108027,108137,108247,108357,108467,108575,108681,108785,108889,108993,109088,109183,109276,109369,109473,109579,109683,109787,109880,109973,110066,110159,110267,110373,110479,110585,110682,110777,110872,110967,111073,111179,111285,111391,111489,111584,111680,111777,111842,111946,112004,112068,112129,112191,112251,112316,112378,112446,112504,112567,112630,112697,112772,112845,112911,112963,113016,113068,113125,113209,113304,113389,113470,113550,113627,113706,113783,113857,113931,114002,114082,114154,114229,114294,114355,114415,114490,114564,114637,114707,114779,114849,114922,114986,115056,115102,115171,115223,115308,115391,115448,115514,115581,115647,115728,115803,115859,115912,115973,116031,116081,116130,116179,116228,116290,116342,116387,116468,116519,116573,116626,116680,116731,116780,116846,116897,116958,117019,117081,117131,117172,117249,117308,117367,117426,117487,117543,117599,117666,117727,117792,117847,117912,117981,118049,118127,118196,118256,118327,118401,118466,118538,118608,118675,118759,118828,118895,118965,119028,119095,119163,119246,119325,119415,119492,119560,119627,119705,119762,119819,119887,119953,120009,120069,120128,120182,120232,120282,120330,120392,120443,120516,120596,120676,120740,120807,120878,120936,120997,121063,121122,121189,121249,121309,121372,121440,121501,121568,121646,121716,121765,121822,121891,121952,122040,122128,122216,122304,122391,122478,122565,122652,122710,122784,122854,122910,122981,123046,123108,123183,123256,123346,123412,123478,123539,123603,123665,123723,123794,123877,123936,124007,124073,124138,124199,124258,124329,124395,124460,124543,124619,124694,124775,124835,124904,124974,125043,125098,125154,125210,125271,125329,125385,125444,125498,125553,125615,125672,125766,125835,125936,125987,126057,126120,126176,126234,126293,126347,126433,126517,126587,126656,126726,126841,126962,127029,127096,127171,127238,127297,127351,127405,127459,127512,127564,129848,129985,132932,132981,133031,133122,133170,133226,133284,133346,133401,133459,133530,133594,133653,133715,133781,134252,134397,134441,134486,135578,135629,135676,135721,135772,135823,135874,136411,137393,137459,137638,137701,137841,137898,137952,138007,138065,138120,138179,138235,138304,138373,138442,138512,138575,138638,138701,138764,138829,138894,138959,139024,139087,139151,139215,139279,139330,139408,139486,139557,139629,139702,139774,139840,139906,139974,140042,140108,140175,140249,140312,140369,140429,140494,140561,140626,140683,140744,140802,140906,141016,141125,141229,141307,141372,141439,141505,141575,141622,141674,141850,141977,144985,145215,145346,145530,145708,145946,146135,146815,146913,147028,147113,150821,151048,151418,151507,151664,152798,152951,153279,154921,155108,155204,155294,155390,155480,155646,155769,155892,156062,156168,156283,156398,156500,156606,156723,156955,157037,157210,157378,157526,157685,157840,158013,158130,158247,158415,158527,158641,158813,158989,159147,159280,159392,159538,159690,159822,159965,162362,162540,162676,162772,162908,163003,163170,163263,163355,163542,163698,163876,164040,164222,164539,164721,164903,165093,165325,165515,165692,165854,166011,166121,166304,166441,166645,166829,167013,167173,167331,167515,167742,167945,168116,168336,168558,168713,168913,169097,169200,169390,169531,169696,169867,170067,170271,170473,170638,170843,171042,171241,171438,171529,171678,171828,171912,172061,172206,172358,172499,172665,173358,173436,173737,173903,174058,176800,176958,177122,181566,181789,195921,196198,196470,196748,196993,197055,201979,202430,202886,214023,214171,214685,215122,215556,219896,219981,220102,220201,220606,220703,220820,220907,221030,221131,221537,221636,221755,221848,221955,222298,222405,222650,222771,223180,223428,223528,223633,223752,224261,224408,224527,224778,224911,225326,225580,230795,231042,231167,231575,231696,231924,232045,232178,232325,253047,253539,274010,274434,295201,295695,316211,316637,321478,326895,330986,336417,341159,346536,350520,354512,359903,360450,360883,361639,361869,362112,363279,411449,412353,412937,413410,414840,415584,416777,417831,418309,418602,418985,420500,421265,422408,422849,423290,423886,424160,424571,425587,425765,426518,426655,426746,428940,429206,429528,429738,429847,429966,430150,431268,431738,432489,435072,437051,437427,437655,437911,438170,438746,439100,439222,439361,439653,439913,440841,441127,441530,441932,442275,442487,442688,442901,443190,454077,454150,454237,454322,467098,467210,467316,467439,467571,467694,467824,467948,468081,468212,468337,468454,468574,468706,468834,468948,469066,469179,469300,469488,469675,469856,470039,470223,470388,470570,470690,470810,470918,471028,471140,471248,471358,471523,471689,471841,472006,472107,472227,472398,472559,472722,472883,473050,473169,473286,473466,473648,473829,474012,474167,474312,474434,474569,474732,474925,475051,475203,475345,475515,475671,475843,482738,482933,483025,483198,483360,483455,483624,483718,483807,484050,484139,484432,484848,485268,485689,486115,486532,486948,487365,487783,488197,488667,489140,489612,490023,490494,490966,491156,491362,491468,491576,491682,491794,491908,492020,492134,492250,492364,492472,492582,492690,492952,493331,493735,493882,493990,494100,494208,494322,494731,495145,495261,495679,495920,496350,496785,497195,497617,498027,498149,498558,498974,499096,499314,502134,502202,502546,502626,502982,503132,503499,503575,503687,503777,504039,504304,504412,504564,504672,504748,504860,504950,505052,505160,505268,505368,505476,505561,505727,505831,505959,506046,506213,506291,506405,506497,506761,507028,507138,507291,507401,507485,507874,507972,508080,508174,508304,508412,508534,508670,508778,508898,509032,509154,509282,509424,509550,509690,509816,509934,510066,510164,510274,510574,510686,510804,511268,511384,511687,511813,511909,512310,512420,512544,512682,512792,512914,513226,513350,513480,513956,514084,514399,514537,514699,514915,515071,516688,516756,516840,516944,517147,517336,517537,517730,517935,518248,518460,518626,518742,518988,519204,519517,519943,520405,520642,520794,521054,521198,521340,524572,524686,524806,524922,525016,525337,525436,525554,525655,525934,526219,526498,526780,527033,527292,527545,527801,528225,528301,531551,532906,533350,535204,535779,535987,536997,537377,537543,537684,542704,543130,543242,543377,543530,543727,543898,544081,544256,544443,544715,544873,544957,545061,545548,546104,546262,546481,546712,546935,547170,547392,547658,547796,548395,548509,548647,548759,548883,549454,549949,550495,550640,550733,550825,552752,553322,553620,553809,554015,554208,554418,555302,555447,555839,555997,556214,564270,564702,565577,566197,566394,567342,568107,568230,569003,569224,569424,571401,571501,571591,572277,573030,573795,574558,575333,576546,576711,578324,578645,579708,579918,580088,580658,581553,582186,582352,583838,584454,584690,584911,585869,586134,586399,586646,587060,587296,588581,589030,589217,589466,589708,589884,590125,590358,590583,591178,591653,592177,592438,593789,594264,595490,595960,597008,597460,597704,598161,599406,599889,600039,600594,601046,601446,601599,601744,601887,601957,602385,602673,603177,603686,603802,604704,604826,604938,605115,605381,605651,605917,606185,606441,606701,606957,607215,607467,607723,607975,608229,608461,608697,608949,609205,609457,609711,609943,610177,610289,610941,611396,611520,612612,613427,613623,613947,614336,614688,614929,615143,615442,615634,615949,616156,616502,616802,617203,617422,617835,618072,618442,619166,619521,619790,619930,620184,620328,620605,621597,622006,622638,622984,623352,624426,624789,625189,626697,627282,627600,630135,630329,630547,630773,630985,631184,631391,632595,632890,633447,633837,634469,634946,635191,635678,635924,637120,637517,638523,638745,639168,639359,639738,639826,639934,640042,640355,640680,640999,641330,644033,644221,644482,644731,647315,647507,647772,648025,648557,648965,649164,649748,649983,650107,650519,650733,651135,651238,651368,651543,651795,651991,652131,652325,653336,654405,654693,654823,655600,656257,656403,657109,657347,658887,659037,659454,659619,660305,660775,660971,661062,661146,661290,661524,661691,662619,662905,663065,663680,663839,664167,664394,664906,665268,665347,665686,665791,666156,666527,666888,668762,669391,670467,670891,671144,671296,672344,673081,673284,673530,673777,673995,674237,674558,674822,675127,675350,675661,675850,676565,676834,677328,677554,677994,678153,678437,679182,679547,679852,680010,680248,681567,681965,682193,682413,682555,683845,683951,684081,684219,684343,684631,684800,684900,685185,685299,686182,686937,687376,687500,687746,687939,688073,688264,689043,689261,689552,689831,690148,690370,690665,690948,691052,691393,692209,692525,693086,693592,693797,694583,694988,695649,695838,696389,696955,697075,697477,847974,848069,848162,848225,848307,848400,848493,848580,848678,848769,848860,848948,849032,849128,849228,849334,849437,849538,849642,849748,849847,849953,850055,850162,850271,850382,850513,850633,850749,850867,850966,851073,851189,851308,851436,851525,851620,851697,851786,851877,851970,852044,852141,852236,852334,852433,852537,852633,852735,852838,852938,853041,853126,853227,853325,853415,853510,853597,853703,853805,853899,853990,854084,854160,854252,854341,854444,854555,854638,854724,854819,854916,855012,855100,855201,855302,855405,855511,855609,855706,855801,855899,856002,856102,856205,856310,856428,856544,856639,856732,856817,856913,857007,857099,857182,857286,857391,857491,857592,857697,857797,857898,857997,858099,858193,858300,858402,858505,858598,858694,858796,858899,858995,859097,859200,859297,859400,859498,859602,859707,859804,859912,860026,860141,860249,860363,860478,860580,860685,860793,860903,861019,861136,861231,861328,861427,861532,861638,861737,861842,861948,862048,862154,862255,862362,862481,862580,862685,862787,862889,862989,863092,863187,863291,863376,863480,863584,863682,863786,863892,863990,864095,864193,864306,864400,864489,864578,864661,864752,864835,864933,865023,865119,865208,865302,865390,865486,865571,865679,865780,865881,865979,866085,866176,866275,866372,866470,866566,866659,866769,866867,866962,867072,867164,867264,867363,867450,867554,867659,867758,867865,867972,868071,868180,868272,868383,868494,868605,868709,868824,868940,869067,869187,869282,869377,869474,869573,869665,869764,869856,869955,870041,870135,870238,870334,870437,870533,870636,870733,870831,870934,871027,871117,871218,871301,871392,871477,871569,871672,871767,871863,871956,872050,872129,872236,872327,872426,872519,872622,872726,872827,872928,873032,873126,873230,873334,873447,873553,873659,873767,873884,873985,874093,874193,874296,874401,874508,874604,874683,874773,874857,874949,875022,875114,875203,875295,875380,875477,875570,875665,875764,875861,875952,876043,876135,876230,876337,876445,876547,876644,876741,876834,876921,877005,877102,877199,877292,877379,877470,877569,877668,877763,877852,877933,878032,878136,878233,878338,878435,878519,878618,878722,878819,878924,879021,879119,879220,879326,879425,879532,879631,879730,879821,879910,879999,880081,880174,880265,880376,880477,880577,880689,880802,880900,881008,881102,881202,881291,881383,881494,881604,881699,881815,881941,882067,882186,882314,882439,882564,882682,882809,882918,883027,883140,883263,883386,883502,883627,883724,883832,883954,884070,884186,884295,884383,884484,884573,884674,884761,884849,884946,885038,885144,885244,885320", "endLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,651,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,720,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1323,1329,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2062,2063,2113,2114,2115,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2137,2140,2141,2142,2162,2163,2164,2165,2166,2167,2168,2179,2198,2199,2202,2203,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2265,2270,2310,2312,2313,2314,2315,2316,2317,2325,2326,2327,2328,2366,2368,2373,2374,2375,2388,2389,2392,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2482,2483,2484,2485,2486,2487,2488,2489,2490,2493,2496,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2558,2559,2560,2561,2562,2600,2601,2602,2668,2669,2841,2845,2849,2853,2854,2858,2942,2949,2957,3122,3132,3141,3150,3159,3220,3221,3222,3228,3229,3230,3231,3232,3233,3239,3240,3241,3242,3243,3248,3249,3253,3254,3260,3264,3265,3266,3267,3277,3278,3279,3283,3284,3290,3294,3295,3367,3368,3373,3374,3377,3378,3379,3380,3644,3651,3912,3918,4182,4189,4450,4456,4519,4601,4653,4735,4797,4879,4943,4995,5077,5085,5091,5102,5106,5110,5123,5138,5914,5921,5927,5944,5957,5977,5994,6003,6008,6015,6035,6048,6065,6071,6077,6084,6088,6094,6108,6111,6121,6122,6123,6171,6175,6179,6183,6184,6185,6188,6204,6211,6225,6270,6271,6305,6309,6313,6318,6325,6331,6332,6335,6339,6344,6357,6361,6366,6371,6376,6379,6382,6385,6389,6393,6533,6534,6535,6536,6768,6769,6770,6771,6772,6773,6774,6775,6776,6777,6778,6779,6780,6781,6782,6783,6784,6785,6789,6793,6797,6801,6805,6809,6813,6814,6815,6816,6817,6818,6819,6820,6824,6828,6829,6833,6834,6837,6841,6844,6847,6850,6854,6857,6860,6864,6868,6872,6876,6879,6880,6881,6882,6885,6889,6892,6895,6898,6901,6904,6907,6911,6984,6985,6988,6991,6992,6995,6996,6997,7001,7002,7007,7014,7021,7028,7035,7042,7049,7056,7063,7070,7079,7088,7097,7104,7113,7122,7125,7128,7129,7130,7131,7132,7133,7134,7135,7136,7137,7138,7139,7140,7144,7149,7154,7157,7158,7159,7160,7161,7169,7177,7178,7186,7190,7198,7206,7214,7222,7230,7231,7239,7247,7248,7251,7254,7292,7297,7299,7304,7308,7312,7317,7318,7319,7323,7327,7328,7332,7333,7334,7335,7336,7337,7338,7339,7340,7341,7342,7346,7347,7348,7349,7353,7354,7355,7356,7360,7364,7365,7369,7370,7371,7376,7377,7378,7379,7380,7381,7382,7383,7384,7385,7386,7387,7388,7389,7390,7391,7392,7393,7394,7395,7396,7400,7401,7402,7408,7409,7413,7415,7416,7421,7422,7423,7424,7425,7426,7430,7431,7432,7438,7439,7443,7445,7449,7453,7457,7461,7482,7483,7484,7487,7490,7493,7496,7499,7504,7508,7511,7512,7517,7521,7526,7532,7538,7543,7547,7552,7556,7560,7601,7602,7603,7604,7605,7609,7610,7611,7612,7616,7620,7624,7628,7632,7636,7640,7644,7650,7651,7692,7706,7711,7737,7744,7747,7758,7763,7766,7769,7824,7830,7831,7834,7837,7840,7843,7846,7849,7852,7856,7859,7860,7861,7869,7877,7880,7885,7890,7895,7900,7904,7908,7909,7917,7918,7919,7920,7921,7929,7934,7939,7940,7941,7942,7967,7973,7978,7981,7985,7988,7992,8002,8005,8010,8013,8017,8021,8126,8140,8153,8157,8172,8183,8186,8197,8202,8206,8241,8242,8243,8255,8263,8271,8279,8287,8307,8310,8337,8342,8362,8365,8368,8375,8388,8397,8400,8420,8430,8434,8438,8451,8455,8459,8463,8469,8473,8490,8498,8502,8506,8510,8513,8517,8521,8525,8535,8542,8549,8553,8579,8589,8614,8623,8643,8653,8657,8667,8692,8702,8705,8712,8719,8726,8727,8728,8729,8730,8737,8741,8747,8753,8754,8767,8768,8769,8772,8775,8778,8781,8784,8787,8790,8793,8796,8799,8802,8805,8808,8811,8814,8817,8820,8823,8826,8829,8832,8833,8841,8849,8850,8863,8873,8877,8882,8887,8891,8894,8898,8902,8905,8909,8912,8916,8921,8926,8929,8936,8940,8944,8953,8958,8963,8964,8968,8971,8975,8988,8993,9001,9005,9009,9026,9030,9035,9053,9060,9064,9094,9097,9100,9103,9106,9109,9112,9131,9137,9145,9152,9164,9172,9177,9185,9189,9207,9214,9230,9234,9242,9245,9250,9251,9252,9253,9257,9261,9265,9269,9304,9307,9311,9315,9349,9352,9356,9360,9369,9375,9378,9388,9392,9393,9400,9404,9411,9412,9413,9416,9421,9426,9427,9431,9446,9465,9469,9470,9482,9492,9493,9505,9510,9534,9537,9543,9546,9555,9563,9567,9570,9573,9576,9580,9583,9600,9604,9607,9622,9625,9633,9638,9645,9650,9651,9656,9657,9663,9669,9675,9707,9718,9735,9742,9746,9749,9762,9771,9775,9780,9784,9788,9792,9796,9800,9804,9808,9813,9816,9828,9833,9842,9845,9852,9853,9857,9866,9872,9876,9877,9881,9902,9908,9912,9916,9917,9935,9936,9937,9938,9939,9944,9947,9948,9954,9955,9967,9979,9986,9987,9992,9997,9998,10002,10016,10021,10027,10033,10039,10044,10050,10056,10057,10063,10078,10083,10092,10101,10104,10118,10123,10134,10138,10147,10156,10157,10164,10172,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197,15198,15199,15200,15201,15202,15203,15204,15205,15206,15207,15208,15209,15210,15211,15212,15213,15214,15215,15216,15217,15218,15219,15220,15221,15222,15223,15224,15225,15226,15227,15228,15229,15230,15231,15232,15233,15234,15235,15236,15237,15238,15239,15240,15241,15242,15243,15244,15245,15246,15247,15248,15249,15250,15251,15252,15253", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "6542,6777,7236,7395,7455,7516,7581,7636,7786,7836,7889,7947,7995,8115,8464,8535,8607,8679,8752,8819,8868,8922,8959,9010,9070,9117,9173,9222,9280,9334,9395,9451,9502,9562,9618,9681,9730,9786,9842,9892,9951,10006,10068,10115,10169,10225,10277,10332,10386,10440,10494,10543,10601,10655,10712,10768,10815,10868,10924,10984,11047,11106,11168,11218,11272,11326,11374,11431,11484,11540,12162,12218,13156,13266,13322,13382,13435,13496,13575,13656,13728,13807,13887,13963,14041,14110,14186,14263,14334,14407,14483,14561,14630,14706,14783,14847,14918,14990,17482,17580,17635,17903,17956,18008,18058,18116,18181,18229,18280,25237,25303,25361,25430,25488,25557,25627,25700,25774,25842,25909,25979,26045,26118,26178,26254,26314,26374,26449,26517,26583,26651,26711,26770,26827,26893,26955,27012,27080,27153,27223,27285,27346,27414,27476,27546,27615,27671,27730,27792,27854,27921,27978,28039,28100,28161,28222,28278,28334,28390,28446,28504,28562,28620,28678,28735,28792,28849,28906,28965,29024,29082,29165,29248,29321,29375,29444,29500,29581,29662,29733,29862,30121,30179,30237,31402,31949,32316,32370,32440,32510,32575,32641,32706,32774,32843,32911,33041,33094,33153,33211,33263,33359,33747,33793,33843,33899,33946,34051,34109,34171,34234,34296,34355,34415,34480,34546,34611,34673,34735,34797,34859,34921,34983,35049,35116,35182,35245,35309,35372,35440,35501,35563,35625,35688,35752,35815,35879,35957,36016,36082,36162,36223,36276,36485,36536,36963,37024,37494,37553,39020,42760,42831,42897,42971,43040,43111,43184,43255,43323,43396,43472,43542,43620,43688,43754,43815,43884,43948,44014,44082,44148,44211,44279,44350,44415,44488,44551,44632,44696,44762,44832,44902,44972,45042,45109,46580,46638,46697,46757,46816,46875,46934,46993,47052,47111,47170,47229,47288,47347,47407,47468,47530,47591,47652,47713,47774,47835,47896,47956,48017,48078,48138,48199,48260,48321,48382,48443,48504,48565,48626,48687,48748,48809,48877,48946,49016,49085,49154,49223,49292,49361,49430,49499,49568,49637,49706,49766,49827,49889,49950,50011,50072,50133,50194,50255,50316,50377,50438,50499,50561,50624,50688,50751,50814,50877,50940,51003,51066,51129,51192,51255,51318,51379,51441,51504,51566,51628,51690,51752,51814,51876,51938,52000,52062,52124,52181,52267,52347,52437,52532,52624,52716,52806,52889,52982,53069,53166,53257,53358,53445,53548,53637,53736,53828,53928,54012,54106,54194,54292,54375,54466,54560,54659,54761,54859,54959,55046,55146,55232,55328,55416,55497,55588,55684,55777,55870,55961,56046,56140,56229,56327,56420,56522,56610,56714,56805,56905,56998,57099,57184,57279,57368,57467,57552,57644,57739,57839,57942,58041,58144,58233,58334,58421,58518,58606,58702,58794,58894,58984,59082,59167,59256,59345,59438,59525,59616,60354,60430,60499,60578,60651,60731,60811,60888,60956,61034,61110,61181,61262,61335,61418,61493,61578,61651,61732,61813,61887,61971,62041,62119,62189,62269,62347,62419,62501,62571,62648,62728,62813,62901,62985,63072,63146,63224,63302,63373,63454,63545,63628,63724,63822,63929,63994,64060,64113,64189,64255,64342,64418,64494,73131,73499,74093,74172,74250,74323,74388,74451,74517,74588,74659,74729,74791,74860,74926,74986,75053,75120,75176,75227,75280,75332,75386,75457,75520,75579,75641,75700,75773,75840,75910,75970,76033,76108,76180,76276,76347,76403,76474,76531,76588,76654,76718,76789,76846,76899,76962,77014,77072,77139,78425,78491,78550,78633,78692,78749,78816,78886,78960,79022,79091,79161,79260,79357,79456,79542,79628,79709,79784,79873,79964,80048,80107,80153,80219,80276,80343,80400,80482,80547,80613,80736,80820,80941,81006,81068,81166,81240,81323,81412,81476,81555,81629,81691,81787,81852,81911,81967,82023,82083,82190,82237,82297,82358,82422,82483,82543,82601,82644,82693,82745,82796,82848,82897,82946,83011,83077,83137,83198,83254,83313,83362,83410,83468,83525,83627,83684,83759,83807,83858,83920,83985,84037,84111,84174,84237,84305,84355,84417,84477,84534,84594,84643,84711,84817,84919,84988,85059,85115,85164,85264,85335,85445,85536,85618,85716,85772,85873,85983,86082,86145,86251,86328,86440,86567,86679,86806,86876,86990,87121,87218,87286,87404,87507,87625,87686,87760,87827,87932,88054,88128,88195,88305,88404,88477,88574,88696,88814,88932,88993,89115,89232,89300,89406,89508,89588,89659,89755,89822,89896,89970,90056,90146,90224,90301,90401,90472,90593,90714,90778,90903,90977,91101,91225,91292,91401,91529,91641,91720,91798,91899,91970,92092,92214,92279,92405,92517,92623,92691,92790,92894,92957,93023,93107,93220,93333,93451,93529,93601,93737,93873,93958,94098,94236,94374,94516,94598,94684,94761,94834,94943,95054,95182,95310,95442,95572,95702,95836,95925,95987,96083,96150,96267,96388,96485,96567,96654,96741,96872,97003,97138,97215,97292,97403,97517,97591,97700,97812,97879,97952,98017,98119,98215,98319,98387,98452,98546,98618,98728,98834,98907,98998,99100,99203,99298,99405,99510,99632,99754,99880,99939,99997,100121,100245,100373,100491,100609,100731,100817,100914,101048,101182,101262,101400,101532,101664,101800,101875,101951,102054,102128,102241,102322,102379,102440,102499,102559,102617,102678,102736,102786,102835,102902,102961,103020,103069,103140,103224,103294,103365,103445,103514,103577,103645,103711,103779,103844,103910,103987,104065,104171,104277,104373,104502,104591,104718,104784,104854,104940,105006,105089,105163,105261,105357,105453,105551,105660,105755,105844,105906,105966,106031,106088,106169,106223,106280,106377,106487,106548,106663,106784,106879,106971,107064,107120,107179,107228,107320,107369,107423,107477,107531,107585,107639,107694,107804,107914,108022,108132,108242,108352,108462,108570,108676,108780,108884,108988,109083,109178,109271,109364,109468,109574,109678,109782,109875,109968,110061,110154,110262,110368,110474,110580,110677,110772,110867,110962,111068,111174,111280,111386,111484,111579,111675,111772,111837,111941,111999,112063,112124,112186,112246,112311,112373,112441,112499,112562,112625,112692,112767,112840,112906,112958,113011,113063,113120,113204,113299,113384,113465,113545,113622,113701,113778,113852,113926,113997,114077,114149,114224,114289,114350,114410,114485,114559,114632,114702,114774,114844,114917,114981,115051,115097,115166,115218,115303,115386,115443,115509,115576,115642,115723,115798,115854,115907,115968,116026,116076,116125,116174,116223,116285,116337,116382,116463,116514,116568,116621,116675,116726,116775,116841,116892,116953,117014,117076,117126,117167,117244,117303,117362,117421,117482,117538,117594,117661,117722,117787,117842,117907,117976,118044,118122,118191,118251,118322,118396,118461,118533,118603,118670,118754,118823,118890,118960,119023,119090,119158,119241,119320,119410,119487,119555,119622,119700,119757,119814,119882,119948,120004,120064,120123,120177,120227,120277,120325,120387,120438,120511,120591,120671,120735,120802,120873,120931,120992,121058,121117,121184,121244,121304,121367,121435,121496,121563,121641,121711,121760,121817,121886,121947,122035,122123,122211,122299,122386,122473,122560,122647,122705,122779,122849,122905,122976,123041,123103,123178,123251,123341,123407,123473,123534,123598,123660,123718,123789,123872,123931,124002,124068,124133,124194,124253,124324,124390,124455,124538,124614,124689,124770,124830,124899,124969,125038,125093,125149,125205,125266,125324,125380,125439,125493,125548,125610,125667,125761,125830,125931,125982,126052,126115,126171,126229,126288,126342,126428,126512,126582,126651,126721,126836,126957,127024,127091,127166,127233,127292,127346,127400,127454,127507,127559,127633,129980,130120,132976,133026,133076,133165,133221,133279,133341,133396,133454,133525,133589,133648,133710,133776,133842,134290,134436,134481,134524,135624,135671,135716,135767,135818,135869,135920,136454,137454,137516,137696,137768,137893,137947,138002,138060,138115,138174,138230,138299,138368,138437,138507,138570,138633,138696,138759,138824,138889,138954,139019,139082,139146,139210,139274,139325,139403,139481,139552,139624,139697,139769,139835,139901,139969,140037,140103,140170,140244,140307,140364,140424,140489,140556,140621,140678,140739,140797,140901,141011,141120,141224,141302,141367,141434,141500,141570,141617,141669,141719,141902,142292,145130,145341,145525,145703,145941,146130,146299,146908,147023,147108,147187,150976,151108,151502,151659,151816,152946,153100,153333,155103,155199,155289,155385,155475,155641,155764,155887,156057,156163,156278,156393,156495,156601,156718,156833,157032,157205,157373,157521,157680,157835,158008,158125,158242,158410,158522,158636,158808,158984,159142,159275,159387,159533,159685,159817,159960,160082,162535,162671,162767,162903,162998,163165,163258,163350,163537,163693,163871,164035,164217,164534,164716,164898,165088,165320,165510,165687,165849,166006,166116,166299,166436,166640,166824,167008,167168,167326,167510,167737,167940,168111,168331,168553,168708,168908,169092,169195,169385,169526,169691,169862,170062,170266,170468,170633,170838,171037,171236,171433,171524,171673,171823,171907,172056,172201,172353,172494,172660,172821,173431,173732,173898,174053,174155,176953,177117,177303,181784,181909,196193,196465,196743,196988,197050,197335,202425,202881,203390,214166,214680,215117,215551,215994,219976,220097,220196,220601,220698,220815,220902,221025,221126,221532,221631,221750,221843,221950,222293,222400,222645,222766,223175,223423,223523,223628,223747,224256,224403,224522,224773,224906,225321,225575,225687,231037,231162,231570,231691,231919,232040,232173,232320,253042,253534,274005,274429,295196,295690,316206,316632,321473,326890,330981,336412,341154,346531,350515,354507,359898,360445,360878,361634,361864,362107,363274,364203,412348,412932,413405,414835,415579,416772,417826,418304,418597,418980,420495,421260,422403,422844,423285,423881,424155,424566,425582,425760,426513,426650,426741,428935,429201,429523,429733,429842,429961,430145,431263,431733,432484,435067,435162,437422,437650,437906,438165,438741,439095,439217,439356,439648,439908,440836,441122,441525,441927,442270,442482,442683,442896,443185,443470,454145,454232,454317,454416,467205,467311,467434,467566,467689,467819,467943,468076,468207,468332,468449,468569,468701,468829,468943,469061,469174,469295,469483,469670,469851,470034,470218,470383,470565,470685,470805,470913,471023,471135,471243,471353,471518,471684,471836,472001,472102,472222,472393,472554,472717,472878,473045,473164,473281,473461,473643,473824,474007,474162,474307,474429,474564,474727,474920,475046,475198,475340,475510,475666,475838,476129,482928,483020,483193,483355,483450,483619,483713,483802,484045,484134,484427,484843,485263,485684,486110,486527,486943,487360,487778,488192,488662,489135,489607,490018,490489,490961,491151,491357,491463,491571,491677,491789,491903,492015,492129,492245,492359,492467,492577,492685,492947,493326,493730,493877,493985,494095,494203,494317,494726,495140,495256,495674,495915,496345,496780,497190,497612,498022,498144,498553,498969,499091,499309,499493,502197,502541,502621,502977,503127,503271,503570,503682,503772,504034,504299,504407,504559,504667,504743,504855,504945,505047,505155,505263,505363,505471,505556,505722,505826,505954,506041,506208,506286,506400,506492,506756,507023,507133,507286,507396,507480,507869,507967,508075,508169,508299,508407,508529,508665,508773,508893,509027,509149,509277,509419,509545,509685,509811,509929,510061,510159,510269,510569,510681,510799,511263,511379,511682,511808,511904,512305,512415,512539,512677,512787,512909,513221,513345,513475,513951,514079,514394,514532,514694,514910,515066,515270,516751,516835,516939,517142,517331,517532,517725,517930,518243,518455,518621,518737,518983,519199,519512,519938,520400,520637,520789,521049,521193,521335,524567,524681,524801,524917,525011,525332,525431,525549,525650,525929,526214,526493,526775,527028,527287,527540,527796,528220,528296,531546,532901,533345,535199,535774,535982,536992,537372,537538,537679,542699,543125,543237,543372,543525,543722,543893,544076,544251,544438,544710,544868,544952,545056,545543,546099,546257,546476,546707,546930,547165,547387,547653,547791,548390,548504,548642,548754,548878,549449,549944,550490,550635,550728,550820,552747,553317,553615,553804,554010,554203,554413,555297,555442,555834,555992,556209,556470,564697,565572,566192,566389,567337,568102,568225,568998,569219,569419,571396,571496,571586,572272,573025,573790,574553,575328,576541,576706,578319,578640,579703,579913,580083,580653,581548,582181,582347,583833,584449,584685,584906,585864,586129,586394,586641,587055,587291,588576,589025,589212,589461,589703,589879,590120,590353,590578,591173,591648,592172,592433,593784,594259,595485,595955,597003,597455,597699,598156,599401,599884,600034,600589,601041,601441,601594,601739,601882,601952,602380,602668,603172,603681,603797,604699,604821,604933,605110,605376,605646,605912,606180,606436,606696,606952,607210,607462,607718,607970,608224,608456,608692,608944,609200,609452,609706,609938,610172,610284,610936,611391,611515,612607,613422,613618,613942,614331,614683,614924,615138,615437,615629,615944,616151,616497,616797,617198,617417,617830,618067,618437,619161,619516,619785,619925,620179,620323,620600,621592,622001,622633,622979,623347,624421,624784,625184,626692,627277,627595,630130,630324,630542,630768,630980,631179,631386,632590,632885,633442,633832,634464,634941,635186,635673,635919,637115,637512,638518,638740,639163,639354,639733,639821,639929,640037,640350,640675,640994,641325,644028,644216,644477,644726,647310,647502,647767,648020,648552,648960,649159,649743,649978,650102,650514,650728,651130,651233,651363,651538,651790,651986,652126,652320,653331,654400,654688,654818,655595,656252,656398,657104,657342,658882,659032,659449,659614,660300,660770,660966,661057,661141,661285,661519,661686,662614,662900,663060,663675,663834,664162,664389,664901,665263,665342,665681,665786,666151,666522,666883,668757,669386,670462,670886,671139,671291,672339,673076,673279,673525,673772,673990,674232,674553,674817,675122,675345,675656,675845,676560,676829,677323,677549,677989,678148,678432,679177,679542,679847,680005,680243,681562,681960,682188,682408,682550,683840,683946,684076,684214,684338,684626,684795,684895,685180,685294,686177,686932,687371,687495,687741,687934,688068,688259,689038,689256,689547,689826,690143,690365,690660,690943,691047,691388,692204,692520,693081,693587,693792,694578,694983,695644,695833,696384,696950,697070,697472,698006,848064,848157,848220,848302,848395,848488,848575,848673,848764,848855,848943,849027,849123,849223,849329,849432,849533,849637,849743,849842,849948,850050,850157,850266,850377,850508,850628,850744,850862,850961,851068,851184,851303,851431,851520,851615,851692,851781,851872,851965,852039,852136,852231,852329,852428,852532,852628,852730,852833,852933,853036,853121,853222,853320,853410,853505,853592,853698,853800,853894,853985,854079,854155,854247,854336,854439,854550,854633,854719,854814,854911,855007,855095,855196,855297,855400,855506,855604,855701,855796,855894,855997,856097,856200,856305,856423,856539,856634,856727,856812,856908,857002,857094,857177,857281,857386,857486,857587,857692,857792,857893,857992,858094,858188,858295,858397,858500,858593,858689,858791,858894,858990,859092,859195,859292,859395,859493,859597,859702,859799,859907,860021,860136,860244,860358,860473,860575,860680,860788,860898,861014,861131,861226,861323,861422,861527,861633,861732,861837,861943,862043,862149,862250,862357,862476,862575,862680,862782,862884,862984,863087,863182,863286,863371,863475,863579,863677,863781,863887,863985,864090,864188,864301,864395,864484,864573,864656,864747,864830,864928,865018,865114,865203,865297,865385,865481,865566,865674,865775,865876,865974,866080,866171,866270,866367,866465,866561,866654,866764,866862,866957,867067,867159,867259,867358,867445,867549,867654,867753,867860,867967,868066,868175,868267,868378,868489,868600,868704,868819,868935,869062,869182,869277,869372,869469,869568,869660,869759,869851,869950,870036,870130,870233,870329,870432,870528,870631,870728,870826,870929,871022,871112,871213,871296,871387,871472,871564,871667,871762,871858,871951,872045,872124,872231,872322,872421,872514,872617,872721,872822,872923,873027,873121,873225,873329,873442,873548,873654,873762,873879,873980,874088,874188,874291,874396,874503,874599,874678,874768,874852,874944,875017,875109,875198,875290,875375,875472,875565,875660,875759,875856,875947,876038,876130,876225,876332,876440,876542,876639,876736,876829,876916,877000,877097,877194,877287,877374,877465,877564,877663,877758,877847,877928,878027,878131,878228,878333,878430,878514,878613,878717,878814,878919,879016,879114,879215,879321,879420,879527,879626,879725,879816,879905,879994,880076,880169,880260,880371,880472,880572,880684,880797,880895,881003,881097,881197,881286,881378,881489,881599,881694,881810,881936,882062,882181,882309,882434,882559,882677,882804,882913,883022,883135,883258,883381,883497,883622,883719,883827,883949,884065,884181,884290,884378,884479,884568,884669,884756,884844,884941,885033,885139,885239,885315,885392"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,17,26,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,745,1242,-1", "endLines": "-1,-1,-1,-1,-1,-1,-1,24,29,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,12,12,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,1236,1421,-1"}, "to": {"startLines": "2859,2860,2861,2866,2871,2872,2880,2884,2892,6289", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "197340,197414,197504,197732,198005,198085,198572,198756,199252,436605", "endLines": "2859,2860,2865,2870,2871,2879,2883,2891,2895,6291", "endColumns": "73,89,12,12,79,12,12,12,12,12", "endOffsets": "197409,197499,197727,198000,198080,198567,198751,199247,199431,436728"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_banner_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "946", "startColumns": "4", "startOffsets": "46170", "endColumns": "54", "endOffsets": "46220"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "849,854,869,874,875,876,877,878,879,880,881,890,891,932,933,935,936,941,942,943,949,950,951,952,1193,1195,1196,1205,1210,1213,1223,1224,1227,1229,1230,1231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39747,40117,41177,41476,41522,41572,41616,41663,41710,41761,41814,42535,42594,45394,45442,45541,45592,45904,45956,45995,46326,46371,46416,46471,64677,64787,64835,65476,65726,65879,66563,66610,66780,66875,66928,66976", "endColumns": "50,38,49,45,49,43,46,46,50,52,49,58,45,47,53,50,45,51,38,39,44,44,54,56,40,47,52,40,37,54,46,48,47,52,47,39", "endOffsets": "39793,40151,41222,41517,41567,41611,41658,41705,41756,41809,41859,42589,42635,45437,45491,45587,45633,45951,45990,46030,46366,46411,46466,46523,64713,64830,64883,65512,65759,65929,66605,66654,66823,66923,66971,67011"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,59,83,101,106,113,161,171,2298,2299,2300,2301,2305,2306,2307,2309,2311,2347,2348,2349,2350,2351,2352,2353,2354,2355,2360,2361,2362,2363,2367,2369,2370,2371,2379,2380,2385,2387,2390,2391,2402,2403,2404,2405,2406,2407,2408,2409,2410,2427,2428,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2550,2551,2553,2554,2555,2556,2557,2563,2564,2569,2570,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2670,2672,2673,2674,2675,2676,2677,2678,2679,2680,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2784,2785,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2828", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1586,1980,2719,3329,3517,3764,5336,5640,144163,144228,144279,144339,144647,144712,144819,144934,145135,149525,149609,149676,149760,149849,149927,149997,150068,150129,150534,150598,150699,150739,150981,151113,151196,151269,152036,152093,152569,152748,153105,153188,154401,154448,154493,154533,154587,154671,154731,154773,154854,156838,156897,160087,160145,160205,160262,160316,160379,160446,160521,160612,160697,160783,160879,160967,161058,161148,161236,161324,161420,161505,161582,161644,161706,161758,161818,161895,161961,162031,162087,162159,162221,162283,172826,172902,173020,173088,173152,173198,173284,174160,174228,174517,174571,174772,174818,174882,174952,175015,175073,175153,175230,175332,175431,175537,175637,175763,175835,175930,176012,176100,176191,176267,176351,176452,176530,176585,176679,177308,177377,177432,177488,177556,177611,177688,177761,177816,177877,177926,177985,178030,178082,178144,178253,178330,178389,178452,178557,178612,178661,178727,178782,178855,178912,178970,179025,179086,179135,179185,179270,179349,179425,179485,179540,179597,179670,179742,179805,179872,179953,180024,180087,180156,180244,180304,180361,180419,180483,180539,180586,180642,180698,180789,180855,180914,181011,181072,181128,181198,181261,181336,181410,181484,181914,182053,182132,182200,182261,182331,182417,182506,182609,182665,182861,183020,183136,183324,183450,183650,183790,183850,183918,183976,184193,184331,184421,184556,184721,184792,184894,185043,185307,185405,185609,185665,185818,185922,185991,186145,186330,186400,186464,186523,186590,186649,186712,186758,186819,186895,186973,187047,187105,187189,187265,187365,187461,187557,187655,187734,187802,187876,187946,188025,188077,188125,188171,188215,188282,188342,188409,188494,188562,188644,188722,188834,188939,189034,189127,189214,189300,189376,189466,189538,189595,189667,189752,189833,189906,189979,190053,190122,190203,190291,190353,190447,190531,190598,190661,190753,190845,190920,190992,191059,191133,191220,191290,191364,191434,191498,191592,191652,191718,191784,191836,191915,191973,192046,192125,192187,192245,192324,192366,192433,192498,192558,192630,192724,192793,192859,192909,192981,193103,193161,193283,193344,193432,193516,193626,193712,193786,193850,193912,193986,194036,194110,194247,194329,194417,194486,194557,194633,194708,194758,194833,194885,195052", "endLines": "49,63,88,105,112,116,165,175,2298,2299,2300,2301,2305,2306,2307,2309,2311,2347,2348,2349,2350,2351,2352,2353,2354,2355,2360,2361,2362,2363,2367,2369,2370,2371,2379,2380,2385,2387,2390,2391,2402,2403,2404,2405,2406,2407,2408,2409,2410,2427,2428,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2550,2551,2553,2554,2555,2556,2557,2563,2564,2569,2570,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2670,2672,2673,2674,2675,2676,2677,2678,2679,2680,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2784,2785,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2828", "endColumns": "19,19,19,19,19,19,19,19,64,50,59,58,64,106,48,50,79,83,66,83,88,77,69,70,60,84,63,100,39,81,66,82,72,91,56,60,71,49,82,90,46,44,39,53,83,59,41,80,66,58,57,57,59,56,53,62,66,74,90,84,85,95,87,90,89,87,87,95,84,76,61,61,51,59,76,65,69,55,71,61,61,78,75,73,67,63,45,85,73,67,72,53,77,45,63,69,62,57,79,76,101,98,105,99,125,71,94,81,87,90,75,83,100,77,54,93,67,68,54,55,67,54,76,72,54,60,48,58,44,51,61,108,76,58,62,104,54,48,65,54,72,56,57,54,60,48,49,84,78,75,59,54,56,72,71,62,66,80,70,62,68,87,59,56,57,63,55,46,55,55,90,65,58,96,60,55,69,62,74,73,73,81,67,78,67,60,69,85,88,102,55,56,158,115,187,125,199,139,59,67,57,216,137,89,134,164,70,101,148,263,97,203,55,152,103,68,23,184,69,63,58,66,58,62,45,60,75,77,73,57,83,75,99,95,95,97,78,67,73,69,78,51,47,45,43,66,59,66,84,67,81,77,111,104,94,92,86,85,75,89,71,56,71,84,80,72,72,73,68,80,87,61,93,83,66,62,91,91,74,71,66,73,86,69,73,69,63,93,59,65,65,51,78,57,72,78,61,57,78,41,66,64,59,71,93,68,65,49,71,121,57,65,60,87,83,109,85,73,63,61,73,49,73,136,81,87,68,70,75,74,49,74,51,68,63", "endOffsets": "1697,2124,2914,3512,3759,3891,5479,5791,144223,144274,144334,144393,144707,144814,144863,144980,145210,149604,149671,149755,149844,149922,149992,150063,150124,150209,150593,150694,150734,150816,151043,151191,151264,151356,152088,152149,152636,152793,153183,153274,154443,154488,154528,154582,154666,154726,154768,154849,154916,156892,156950,160140,160200,160257,160311,160374,160441,160516,160607,160692,160778,160874,160962,161053,161143,161231,161319,161415,161500,161577,161639,161701,161753,161813,161890,161956,162026,162082,162154,162216,162278,162357,172897,172971,173083,173147,173193,173279,173353,174223,174296,174566,174644,174813,174877,174947,175010,175068,175148,175225,175327,175426,175532,175632,175758,175830,175925,176007,176095,176186,176262,176346,176447,176525,176580,176674,176742,177372,177427,177483,177551,177606,177683,177756,177811,177872,177921,177980,178025,178077,178139,178248,178325,178384,178447,178552,178607,178656,178722,178777,178850,178907,178965,179020,179081,179130,179180,179265,179344,179420,179480,179535,179592,179665,179737,179800,179867,179948,180019,180082,180151,180239,180299,180356,180414,180478,180534,180581,180637,180693,180784,180850,180909,181006,181067,181123,181193,181256,181331,181405,181479,181561,181977,182127,182195,182256,182326,182412,182501,182604,182660,182717,183015,183131,183319,183445,183645,183785,183845,183913,183971,184188,184326,184416,184551,184716,184787,184889,185038,185302,185400,185604,185660,185813,185917,185986,186140,186325,186395,186459,186518,186585,186644,186707,186753,186814,186890,186968,187042,187100,187184,187260,187360,187456,187552,187650,187729,187797,187871,187941,188020,188072,188120,188166,188210,188277,188337,188404,188489,188557,188639,188717,188829,188934,189029,189122,189209,189295,189371,189461,189533,189590,189662,189747,189828,189901,189974,190048,190117,190198,190286,190348,190442,190526,190593,190656,190748,190840,190915,190987,191054,191128,191215,191285,191359,191429,191493,191587,191647,191713,191779,191831,191910,191968,192041,192120,192182,192240,192319,192361,192428,192493,192553,192625,192719,192788,192854,192904,192976,193098,193156,193222,193339,193427,193511,193621,193707,193781,193845,193907,193981,194031,194105,194242,194324,194412,194481,194552,194628,194703,194753,194828,194880,194949,195111"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "947", "startColumns": "4", "startOffsets": "46225", "endColumns": "56", "endOffsets": "46277"}}]}]}