{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-73:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e09aa481a2483f3d3bc09954c2742862\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2162", "startColumns": "4", "startOffsets": "135638", "endColumns": "42", "endOffsets": "135676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3301c563da9d289aac88c6ca3529e20c\\transformed\\recyclerview-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "697,1369,1370,1371,1380,1381,1382,2091", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "31852,76451,76510,76558,77272,77347,77423,131930", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "31903,76505,76553,76609,77342,77418,77490,131991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\be0b7d272e566bfd154d1bf5d5ba0c43\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2086,2124,2167", "startColumns": "4,4,4", "startOffsets": "131698,133663,135888", "endColumns": "56,64,63", "endOffsets": "131750,133723,135947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\19bfc16374171ba7a26e435a1ff1fa3b\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "294,10117", "startColumns": "4,4", "startOffsets": "11766,694605", "endLines": "294,10119", "endColumns": "60,12", "endOffsets": "11822,694745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2279,2280", "startColumns": "4,4", "startOffsets": "143297,143379", "endColumns": "81,83", "endOffsets": "143374,143458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\cb2a86983ad6559594debb17aca21969\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2090,2111", "startColumns": "4,4", "startOffsets": "131876,133024", "endColumns": "53,66", "endOffsets": "131925,133086"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1379,2021,2022,2023,2039,2040,2041,2042", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "77225,127750,127802,127852,128788,128838,128886,128934", "endColumns": "46,51,49,49,49,47,47,47", "endOffsets": "77267,127797,127847,127897,128833,128881,128929,128977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\fbfe99676c41211a4bfd510557294d9b\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2117,2164", "startColumns": "4,4", "startOffsets": "133344,135724", "endColumns": "41,59", "endOffsets": "133381,135779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,849,850,851,852,853,854,855,856,857,858,863,864,924,925,926,927,931,932,933,934,936,937,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1186,1187,1188,1189,1190,1191,1192,1193,1198,1199,1201,1202,1203,1204,1206,1207,1208,1209,1210,1211,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1367,1368,1372,1373,1374,1375,1376,1377,1378,2031,2032,2033,2034,2035,2036,2037,2038,2080,2081,2082,2083,2089,2115,2116,2125,2159,2177,2178,2181,2182,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2571,2791,2792,2793,2794,2795,2840,2848,2849,2853,2857,2868,2873,2902,2909,2913,2917,2922,2926,2930,2934,2938,2942,2946,2952,2956,2962,2966,2972,2976,2981,2985,2988,2992,2998,3002,3008,3012,3018,3021,3025,3029,3033,3037,3041,3042,3043,3044,3047,3050,3053,3056,3060,3061,3062,3063,3104,3107,3109,3111,3113,3118,3119,3123,3129,3133,3134,3136,3148,3149,3153,3159,3163,3240,3241,3245,3272,3276,3277,3281,5083,5255,5281,5452,5478,5509,5517,5523,5539,5561,5566,5571,5581,5590,5599,5603,5610,5629,5636,5637,5646,5649,5652,5656,5660,5664,5667,5668,5673,5678,5688,5693,5700,5706,5707,5710,5714,5719,5721,5723,5726,5729,5731,5735,5738,5745,5748,5751,5755,5757,5761,5763,5765,5767,5771,5779,5787,5799,5805,5814,5817,5828,5831,5832,5837,5838,6338,6407,6481,6482,6492,6501,6657,6659,6663,6666,6669,6672,6675,6678,6681,6684,6688,6691,6694,6697,6701,6704,6708,6859,6860,6861,6862,6863,6864,6865,6866,6867,6868,6869,6870,6871,6872,6873,6874,6875,6876,6877,6878,6879,6881,6883,6884,6885,6886,6887,6888,6889,6890,6892,6893,6895,6896,6898,6900,6901,6903,6904,6905,6906,6907,6908,6910,6911,6912,6913,6914,7199,7201,7203,7205,7206,7207,7208,7209,7210,7211,7212,7213,7214,7215,7216,7217,7219,7220,7221,7222,7223,7224,7225,7227,7231,7414,7415,7416,7417,7418,7419,7423,7424,7425,7966,7968,7970,7972,7974,7976,7977,7978,7979,7981,7983,7985,7986,7987,7988,7989,7990,7991,7992,7993,7994,7995,7996,7999,8000,8001,8002,8004,8006,8007,8009,8010,8012,8014,8016,8017,8018,8019,8020,8021,8022,8023,8024,8025,8026,8027,8029,8030,8031,8032,8034,8035,8036,8037,8038,8040,8042,8044,8046,8047,8048,8049,8050,8051,8052,8053,8054,8055,8056,8057,8058,8059,8060", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12942,17487,17792,36391,38574,38629,39086,39150,39220,39281,39356,39432,39509,39747,39832,39914,39990,40066,40143,40221,40327,40433,40512,40841,40898,44919,44993,45068,45133,45346,45406,45467,45539,45664,45731,59160,59219,59278,59337,59396,59455,59509,59563,59616,59670,59724,59778,64386,64460,64539,64612,64686,64757,64829,64901,65183,65240,65353,65426,65500,65574,65696,65768,65841,65911,65982,66042,66756,66825,66894,66964,67038,67114,67178,67255,67331,67408,67473,67542,67619,67694,67763,67831,67908,67974,68035,68132,68197,68266,68365,68436,68495,68553,68610,68669,68733,68804,68876,68948,69020,69092,69159,69227,69295,69354,69417,69481,69571,69662,69722,69788,69855,69921,69991,70055,70108,70175,70236,70303,70416,70474,70537,70602,70667,70742,70815,70887,70931,70978,71024,71073,71134,71195,71256,71318,71382,71446,71510,71575,71638,71698,71759,71825,71884,71944,72006,72077,72137,76278,76364,76614,76704,76791,76879,76961,77044,77134,128335,128387,128445,128490,128556,128620,128677,128734,131382,131439,131487,131536,131842,133248,133295,133728,135513,136401,136465,136655,136715,141431,141505,141575,141653,141707,141777,141862,141910,141956,142017,142080,142146,142210,142281,142344,142409,142473,142534,142595,142647,142720,142794,142863,142938,143012,143086,143227,175475,192923,193001,193091,193179,193275,196030,196612,196701,196948,197229,197895,198180,199989,200466,200688,200910,201186,201413,201643,201873,202103,202333,202560,202979,203205,203630,203860,204288,204507,204790,204998,205129,205356,205782,206007,206434,206655,207080,207200,207476,207777,208101,208392,208706,208843,208974,209079,209321,209488,209692,209900,210171,210283,210395,210500,212593,212807,212953,213093,213179,213527,213615,213861,214279,214528,214610,214708,215365,215465,215717,216141,216396,222286,222375,222612,224636,224878,224980,225233,360802,371483,372999,383694,385222,386979,387605,388025,389286,390551,390807,391043,391590,392084,392689,392887,393467,394835,395210,395328,395866,396023,396219,396492,396748,396918,397059,397123,397488,397855,398531,398795,399133,399486,399580,399766,400072,400334,400459,400586,400825,401036,401155,401348,401525,401980,402161,402283,402542,402655,402842,402944,403051,403180,403455,403963,404459,405336,405630,406200,406349,407081,407253,407337,407673,407765,440069,445300,451015,451077,451655,452239,460437,460550,460779,460939,461091,461262,461428,461597,461764,461927,462170,462340,462513,462684,462958,463157,463362,472860,472944,473040,473136,473234,473334,473436,473538,473640,473742,473844,473944,474040,474152,474281,474404,474535,474666,474764,474878,474972,475112,475246,475342,475454,475554,475670,475766,475878,475978,476118,476254,476418,476548,476706,476856,476997,477141,477276,477388,477538,477666,477794,477930,478062,478192,478322,478434,496092,496238,496382,496520,496586,496676,496752,496856,496946,497048,497156,497264,497364,497444,497536,497634,497744,497796,497874,497980,498072,498176,498286,498408,498571,512279,512359,512459,512549,512659,512749,512990,513084,513190,553069,553169,553281,553395,553511,553627,553721,553835,553947,554049,554169,554291,554373,554477,554597,554723,554821,554915,555003,555115,555231,555353,555465,555640,555756,555842,555934,556046,556170,556237,556363,556431,556559,556703,556831,556900,556995,557110,557223,557322,557431,557542,557653,557754,557859,557959,558089,558180,558303,558397,558509,558595,558699,558795,558883,559001,559105,559209,559335,559423,559531,559631,559721,559831,559915,560017,560101,560155,560219,560325,560411,560521,560605", "endLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,849,850,851,852,853,854,855,856,857,858,863,864,924,925,926,927,931,932,933,934,936,937,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1186,1187,1188,1189,1190,1191,1192,1193,1198,1199,1201,1202,1203,1204,1206,1207,1208,1209,1210,1211,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1367,1368,1372,1373,1374,1375,1376,1377,1378,2031,2032,2033,2034,2035,2036,2037,2038,2080,2081,2082,2083,2089,2115,2116,2125,2159,2177,2178,2181,2182,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2571,2791,2792,2793,2794,2795,2847,2848,2852,2856,2860,2872,2878,2908,2912,2916,2921,2925,2929,2933,2937,2941,2945,2951,2955,2961,2965,2971,2975,2980,2984,2987,2991,2997,3001,3007,3011,3017,3020,3024,3028,3032,3036,3040,3041,3042,3043,3046,3049,3052,3055,3059,3060,3061,3062,3063,3106,3108,3110,3112,3117,3118,3122,3128,3132,3133,3135,3147,3148,3152,3158,3162,3163,3240,3244,3271,3275,3276,3280,3308,5254,5280,5451,5477,5508,5516,5522,5538,5560,5565,5570,5580,5589,5598,5602,5609,5628,5635,5636,5645,5648,5651,5655,5659,5663,5666,5667,5672,5677,5687,5692,5699,5705,5706,5709,5713,5718,5720,5722,5725,5728,5730,5734,5737,5744,5747,5750,5754,5756,5760,5762,5764,5766,5770,5778,5786,5798,5804,5813,5816,5827,5830,5831,5836,5837,5842,6406,6476,6481,6491,6500,6501,6658,6662,6665,6668,6671,6674,6677,6680,6683,6687,6690,6693,6696,6700,6703,6707,6711,6859,6860,6861,6862,6863,6864,6865,6866,6867,6868,6869,6870,6871,6872,6873,6874,6875,6876,6877,6878,6880,6882,6883,6884,6885,6886,6887,6888,6889,6891,6892,6894,6895,6897,6899,6900,6902,6903,6904,6905,6906,6907,6909,6910,6911,6912,6913,6914,7200,7202,7204,7205,7206,7207,7208,7209,7210,7211,7212,7213,7214,7215,7216,7218,7219,7220,7221,7222,7223,7224,7226,7230,7234,7414,7415,7416,7417,7418,7422,7423,7424,7425,7967,7969,7971,7973,7975,7976,7977,7978,7980,7982,7984,7985,7986,7987,7988,7989,7990,7991,7992,7993,7994,7995,7998,7999,8000,8001,8003,8005,8006,8008,8009,8011,8013,8015,8016,8017,8018,8019,8020,8021,8022,8023,8024,8025,8026,8028,8029,8030,8031,8033,8034,8035,8036,8037,8039,8041,8043,8045,8046,8047,8048,8049,8050,8051,8052,8053,8054,8055,8056,8057,8058,8059,8060", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "12992,17527,17836,36427,38624,38686,39145,39215,39276,39351,39427,39504,39582,39827,39909,39985,40061,40138,40216,40322,40428,40507,40587,40893,40951,44988,45063,45128,45194,45401,45462,45534,45607,45726,45794,59214,59273,59332,59391,59450,59504,59558,59611,59665,59719,59773,59827,64455,64534,64607,64681,64752,64824,64896,64969,65235,65293,65421,65495,65569,65644,65763,65836,65906,65977,66037,66098,66820,66889,66959,67033,67109,67173,67250,67326,67403,67468,67537,67614,67689,67758,67826,67903,67969,68030,68127,68192,68261,68360,68431,68490,68548,68605,68664,68728,68799,68871,68943,69015,69087,69154,69222,69290,69349,69412,69476,69566,69657,69717,69783,69850,69916,69986,70050,70103,70170,70231,70298,70411,70469,70532,70597,70662,70737,70810,70882,70926,70973,71019,71068,71129,71190,71251,71313,71377,71441,71505,71570,71633,71693,71754,71820,71879,71939,72001,72072,72132,72200,76359,76446,76699,76786,76874,76956,77039,77129,77220,128382,128440,128485,128551,128615,128672,128729,128783,131434,131482,131531,131582,131871,133290,133339,133769,135540,136460,136522,136710,136767,141500,141570,141648,141702,141772,141857,141905,141951,142012,142075,142141,142205,142276,142339,142404,142468,142529,142590,142642,142715,142789,142858,142933,143007,143081,143222,143292,175523,192996,193086,193174,193270,193360,196607,196696,196943,197224,197476,198175,198568,200461,200683,200905,201181,201408,201638,201868,202098,202328,202555,202974,203200,203625,203855,204283,204502,204785,204993,205124,205351,205777,206002,206429,206650,207075,207195,207471,207772,208096,208387,208701,208838,208969,209074,209316,209483,209687,209895,210166,210278,210390,210495,210612,212802,212948,213088,213174,213522,213610,213856,214274,214523,214605,214703,215360,215460,215712,216136,216391,216485,222370,222607,224631,224873,224975,225228,227384,371478,372994,383689,385217,386974,387600,388020,389281,390546,390802,391038,391585,392079,392684,392882,393462,394830,395205,395323,395861,396018,396214,396487,396743,396913,397054,397118,397483,397850,398526,398790,399128,399481,399575,399761,400067,400329,400454,400581,400820,401031,401150,401343,401520,401975,402156,402278,402537,402650,402837,402939,403046,403175,403450,403958,404454,405331,405625,406195,406344,407076,407248,407332,407668,407760,408038,445295,450666,451072,451650,452234,452325,460545,460774,460934,461086,461257,461423,461592,461759,461922,462165,462335,462508,462679,462953,463152,463357,463687,472939,473035,473131,473229,473329,473431,473533,473635,473737,473839,473939,474035,474147,474276,474399,474530,474661,474759,474873,474967,475107,475241,475337,475449,475549,475665,475761,475873,475973,476113,476249,476413,476543,476701,476851,476992,477136,477271,477383,477533,477661,477789,477925,478057,478187,478317,478429,478569,496233,496377,496515,496581,496671,496747,496851,496941,497043,497151,497259,497359,497439,497531,497629,497739,497791,497869,497975,498067,498171,498281,498403,498566,498723,512354,512454,512544,512654,512744,512985,513079,513185,513277,553164,553276,553390,553506,553622,553716,553830,553942,554044,554164,554286,554368,554472,554592,554718,554816,554910,554998,555110,555226,555348,555460,555635,555751,555837,555929,556041,556165,556232,556358,556426,556554,556698,556826,556895,556990,557105,557218,557317,557426,557537,557648,557749,557854,557954,558084,558175,558298,558392,558504,558590,558694,558790,558878,558996,559100,559204,559330,559418,559526,559626,559716,559826,559910,560012,560096,560150,560214,560320,560406,560516,560600,560720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "659,847,848,865,866,1181,1182,1311,1312,1313,1314,1315,1316,1317,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2092,2093,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2150,2247,2295,2296,2297,2298,2299,2300,2301,2640,6915,6916,6920,6921,6925,8061,8062", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30242,39587,39659,40956,41021,64084,64153,72638,72708,72776,72848,72918,72979,73053,126772,126833,126894,126956,127020,127082,127143,127211,127311,127371,127437,127510,127579,127636,127688,129259,129331,129407,129472,129531,129590,129650,129710,129770,129830,129890,129950,130010,130070,130130,130190,130249,130309,130369,130429,130489,130549,130609,130669,130729,130789,130849,130908,130968,131028,131087,131146,131205,131264,131323,131996,132031,133774,133829,133892,133947,134005,134061,134119,134180,134243,134300,134351,134409,134459,134520,134577,134643,134677,135059,141041,145203,145270,145342,145411,145480,145554,145626,180343,478574,478691,478892,479002,479203,560725,560797", "endLines": "659,847,848,865,866,1181,1182,1311,1312,1313,1314,1315,1316,1317,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2092,2093,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2150,2247,2295,2296,2297,2298,2299,2300,2301,2640,6915,6919,6920,6924,6925,8061,8062", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "30297,39654,39742,41016,41082,64148,64211,72703,72771,72843,72913,72974,73048,73121,126828,126889,126951,127015,127077,127138,127206,127306,127366,127432,127505,127574,127631,127683,127745,129326,129402,129467,129526,129585,129645,129705,129765,129825,129885,129945,130005,130065,130125,130185,130244,130304,130364,130424,130484,130544,130604,130664,130724,130784,130844,130903,130963,131023,131082,131141,131200,131259,131318,131377,132026,132061,133824,133887,133942,134000,134056,134114,134175,134238,134295,134346,134404,134454,134515,134572,134638,134672,134707,135089,141106,145265,145337,145406,145475,145549,145621,145709,180409,478686,478887,478997,479198,479327,560792,560859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2185,2314", "startColumns": "4,4", "startOffsets": "136907,147131", "endColumns": "67,166", "endOffsets": "136970,147293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\97daf22df7e5c6e3b5afccf9cf12b867\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,868,869,870,871,1307,1308,1309,2861,6236,6238,6241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7641,41137,41198,41260,41322,72413,72472,72529,197481,433327,433391,433517", "endLines": "220,868,869,870,871,1307,1308,1309,2867,6237,6240,6243", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "7688,41193,41255,41317,41381,72467,72524,72578,197890,433386,433512,433640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\700cabc0e517e16b3b1a2efd32cecc35\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "727,732,733,734,2084", "startColumns": "4,4,4,4,4", "startOffsets": "33364,33543,33603,33655,131587", "endLines": "731,732,733,734,2084", "endColumns": "11,59,51,44,59", "endOffsets": "33538,33598,33650,33695,131642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c5c1f58a58ca000404c5b280db793ba2\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2087,2088,2110,2119,2120,2151,2152,2153,2154,2155,2156,2157,2158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "131755,131795,132981,133429,133484,135094,135139,135193,135249,135301,135353,135402,135463", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "131790,131837,133019,133479,133526,135134,135188,135244,135296,135348,135397,135458,135508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b67b39daef2398884ed6af353040af9d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2281", "startColumns": "4", "startOffsets": "143463", "endColumns": "82", "endOffsets": "143541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\07e10290a8de075a2af92b4127d3fcd4\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "187,194,196,202,203,211,213,214,221,227,229,230,231,232,233,290,291,292,293,295,299,300,301,304,314,324,352,353,358,359,364,369,370,371,376,377,382,383,388,389,390,396,397,398,403,409,410,428,429,435,436,437,438,441,444,447,448,451,454,455,456,457,458,461,464,465,466,467,473,478,481,484,485,486,491,492,493,496,499,500,503,506,509,512,513,514,517,520,521,526,527,533,538,541,544,545,546,547,548,549,550,551,552,553,554,555,571,652,653,654,655,660,667,675,676,677,680,685,687,695,696,725,740,777,778,782,783,794,795,796,802,805,811,815,816,817,818,819,828,2097,2161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6151,6435,6547,6782,6843,7134,7241,7291,7693,8000,8120,8175,8235,8300,8359,11545,11597,11658,11720,11827,11960,12012,12062,12223,12630,13053,14995,15054,15251,15308,15503,15684,15738,15795,15987,16045,16241,16297,16491,16548,16599,16821,16873,16928,17118,17334,17384,18285,18341,18547,18608,18668,18738,18871,19002,19130,19198,19327,19453,19515,19578,19646,19713,19836,19961,20028,20093,20158,20447,20628,20749,20870,20936,21003,21213,21282,21348,21473,21599,21666,21792,21919,22044,22171,22227,22292,22418,22541,22606,22814,22881,23169,23349,23469,23589,23654,23716,23778,23842,23904,23963,24023,24084,24145,24204,24264,24924,29867,29918,29967,30015,30302,30594,30902,30949,31009,31115,31295,31407,31742,31796,33268,33951,36281,36332,36541,36593,37029,37088,37142,37380,37558,37760,37899,37945,38000,38045,38089,38437,132215,135593", "endLines": "193,194,200,202,210,211,213,214,221,227,229,230,231,232,233,290,291,292,293,298,299,300,301,313,321,324,352,357,358,363,368,369,370,375,376,381,382,387,388,389,395,396,397,402,408,409,410,428,434,435,436,437,440,443,446,447,450,453,454,455,456,457,460,463,464,465,466,472,477,480,483,484,485,490,491,492,495,498,499,502,505,508,511,512,513,516,519,520,525,526,532,537,540,543,544,545,546,547,548,549,550,551,552,553,554,570,576,652,653,654,655,666,674,675,676,679,684,685,694,695,696,725,740,777,778,782,791,794,795,801,802,810,814,815,816,817,818,827,831,2097,2161", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "6430,6486,6728,6838,7129,7181,7286,7339,7736,8046,8170,8230,8295,8354,8416,11592,11653,11715,11761,11955,12007,12057,12108,12625,12937,13093,15049,15246,15303,15498,15679,15733,15790,15982,16040,16236,16292,16486,16543,16594,16816,16868,16923,17113,17329,17379,17431,18336,18542,18603,18663,18733,18866,18997,19125,19193,19322,19448,19510,19573,19641,19708,19831,19956,20023,20088,20153,20442,20623,20744,20865,20931,20998,21208,21277,21343,21468,21594,21661,21787,21914,22039,22166,22222,22287,22413,22536,22601,22809,22876,23164,23344,23464,23584,23649,23711,23773,23837,23899,23958,24018,24079,24140,24199,24259,24919,25170,29913,29962,30010,30068,30589,30897,30944,31004,31110,31290,31344,31737,31791,31847,33309,33993,36327,36386,36588,36918,37083,37137,37375,37430,37755,37894,37940,37995,38040,38084,38432,38569,132251,135633"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\generated\\res\\processFdroidDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2335,2358,2359,2360,2361,2363,2543", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "149219,151053,151135,151239,151348,151540,173223", "endColumns": "143,81,103,108,119,106,75", "endOffsets": "149358,151130,151234,151343,151463,151642,173294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\59e516bb99fd3e6425d2c9627b07a4fa\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7406", "startColumns": "4", "startOffsets": "511869", "endLines": "7413", "endColumns": "8", "endOffsets": "512274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1194,1195,1196,1197,2545,2546,6653", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "64974,65032,65079,65135,173377,173438,460186", "endLines": "1194,1195,1196,1197,2545,2546,6656", "endColumns": "57,46,55,47,60,61,10", "endOffsets": "65027,65074,65130,65178,173433,173495,460432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ebcc5988bd502c901a17c4d3fc1eb859\\transformed\\zxing-android-embedded-4.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,2168,2169,2170,2171,2172,2173,2174,2175,2176,2285,2336,2370,2371,2372,2373,2374,2375,2376,2377,2378,2787,2788,2789,2790,10120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66150,66222,66283,66347,66412,66477,66531,66585,66639,66698,135952,135999,136048,136096,136138,136187,136239,136297,136347,143767,149363,152237,152334,152451,152547,152708,152821,152907,153054,153153,192560,192619,192666,192810,694750", "endLines": "1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,2168,2169,2170,2171,2172,2173,2174,2175,2176,2285,2336,2370,2371,2372,2373,2374,2375,2376,2377,2378,2787,2788,2789,2790,10121", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10", "endOffsets": "66217,66278,66342,66407,66472,66526,66580,66634,66693,66751,135994,136043,136091,136133,136182,136234,136292,136342,136396,143828,149428,152329,152446,152542,152703,152816,152902,153049,153148,153295,192614,192661,192805,192918,694845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f68ab7854e90816622d4e41b6bdf2b2c\\transformed\\camera-view-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "415,699", "startColumns": "4,4", "startOffsets": "17640,31954", "endLines": "418,706", "endColumns": "11,11", "endOffsets": "17787,32256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "834,1183,2024,2025,2026,2027,2028,2029,2030,2112,2113,2114,2244,2245,2333,2349,2527,2540,2650,2784,2785,6216,6502,6505,6511,6517,6520,6526,6530,6533,6540,6546,6549,6555,6560,6565,6572,6574,6580,6586,6594,6599,6606,6611,6617,6621,6628,6632,6638,6644,6647,6651,6652", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "38691,64216,127902,127966,128021,128089,128156,128221,128278,133091,133139,133187,140858,140921,149113,150260,171758,173083,181083,192398,192448,431761,452330,452435,452680,453018,453164,453504,453716,453879,454286,454624,454747,455086,455325,455582,455953,456013,456351,456637,457086,457378,457766,458071,458415,458660,458990,459197,459465,459738,459882,460083,460130", "endLines": "834,1183,2024,2025,2026,2027,2028,2029,2030,2112,2113,2114,2244,2245,2333,2349,2527,2542,2650,2784,2785,6232,6504,6510,6516,6519,6525,6529,6532,6539,6545,6548,6554,6559,6564,6571,6573,6579,6585,6593,6598,6605,6610,6616,6620,6627,6631,6637,6643,6646,6650,6651,6652", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "38759,64280,127961,128016,128084,128151,128216,128273,128330,133134,133182,133243,140916,140979,149146,150312,171797,173218,181217,192443,192491,433194,452430,452675,453013,453159,453499,453711,453874,454281,454619,454742,455081,455320,455577,455948,456008,456346,456632,457081,457373,457761,458066,458410,458655,458985,459192,459460,459733,459877,460078,460125,460181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3671adc343b59afebe99d3ec6265d6d7\\transformed\\Toasty-1.5.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "888,930,940,1180,1205,1212,2764", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "42445,45301,45911,64038,65649,66103,190897", "endColumns": "50,44,43,45,46,46,55", "endOffsets": "42491,45341,45950,64079,65691,66145,190948"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,7,14,19,25,34,38,42,50,55,64,69,78,89,94,117,130,139,152,156,166,176,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,301,517,689,878,1163,1298,1445,1702,1845,2129,2297,2557,2919,3102,3896,4404,4681,5058,5182,5484,5796,5953", "endLines": "6,13,18,24,33,37,41,45,54,58,68,77,82,93,100,129,138,151,155,160,170,180,186", "endColumns": "19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19", "endOffsets": "296,512,684,873,1158,1293,1440,1581,1840,1975,2292,2552,2714,3097,3324,4399,4676,5053,5177,5331,5635,5948,6146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d70395873c50a004c47266b87418baa0\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2085", "startColumns": "4", "startOffsets": "131647", "endColumns": "50", "endOffsets": "131693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\8db806c88c82d97fba3e067706ad47ec\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "835,836,837,839", "startColumns": "4,4,4,4", "startOffsets": "38764,38829,38899,39025", "endColumns": "64,69,63,60", "endOffsets": "38824,38894,38958,39081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\aaec5b52f0b87a27b87b977c567045d7\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2165", "startColumns": "4", "startOffsets": "135784", "endColumns": "53", "endOffsets": "135833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c588be67285252bb290c6b2c2549687a\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2166", "startColumns": "4", "startOffsets": "135838", "endColumns": "49", "endOffsets": "135883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f7eb1da5e42044f662b8e59e06978dfd\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7257", "startColumns": "4", "startOffsets": "499870", "endLines": "7260", "endColumns": "12", "endOffsets": "500088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\43f80c422e65d725a8b34fc24966139b\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "323,326,1318", "startColumns": "4,4,4", "startOffsets": "12997,13161,73126", "endColumns": "55,47,51", "endOffsets": "13048,13204,73173"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6856", "startColumns": "4", "startOffsets": "472728", "endLines": "6858", "endColumns": "12", "endOffsets": "472855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "859,860,861,862,1305,1306,2334,2353,2354,2355", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40592,40650,40716,40779,72270,72341,149151,150720,150787,150866", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "40645,40711,40774,40836,72336,72408,149214,150782,150861,150930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "880,881,882,883,884,885,886,887,2306,2307,2308,2309,2310,2311,2312,2313,2315,2316,2317,2318,2319,2320,2321,2322,2323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41774,41864,41944,42034,42124,42204,42285,42365,146091,146196,146377,146502,146609,146789,146912,147028,147298,147486,147591,147772,147897,148072,148220,148283,148345", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "41859,41939,42029,42119,42199,42280,42360,42440,146191,146372,146497,146604,146784,146907,147023,147126,147481,147586,147767,147892,148067,148215,148278,148340,148419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3fac676b4e1c0ee2b6233dfa300c1ec0\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2163", "startColumns": "4", "startOffsets": "135681", "endColumns": "42", "endOffsets": "135719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,717,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1304,1310,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2043,2044,2094,2095,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2118,2121,2122,2123,2143,2144,2145,2146,2147,2148,2149,2160,2179,2180,2183,2184,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2246,2248,2287,2289,2290,2291,2292,2293,2294,2302,2303,2304,2305,2341,2345,2350,2351,2352,2365,2366,2369,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2469,2472,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2533,2534,2535,2536,2537,2572,2575,2576,2636,2637,2796,2800,2804,2808,2812,2813,2879,2887,2894,3064,3067,3077,3086,3095,3164,3165,3166,3167,3173,3174,3175,3176,3177,3178,3184,3185,3186,3187,3188,3193,3194,3198,3199,3205,3209,3210,3211,3212,3222,3223,3224,3228,3229,3235,3239,3309,3312,3313,3318,3319,3322,3323,3324,3325,3589,3596,3857,3863,4127,4134,4395,4401,4464,4546,4598,4680,4742,4824,4888,4940,5022,5030,5036,5047,5051,5055,5068,5843,5859,5866,5872,5889,5902,5922,5939,5948,5953,5960,5980,5993,6010,6016,6022,6029,6033,6039,6053,6056,6066,6067,6068,6116,6120,6124,6128,6129,6130,6133,6149,6156,6170,6215,6244,6250,6254,6258,6263,6270,6276,6277,6280,6284,6289,6302,6306,6311,6316,6321,6324,6327,6330,6334,6477,6478,6479,6480,6712,6713,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6726,6727,6728,6729,6730,6734,6738,6742,6746,6750,6754,6758,6759,6760,6761,6762,6763,6764,6765,6769,6773,6774,6778,6779,6782,6786,6789,6792,6795,6799,6802,6805,6809,6813,6817,6821,6824,6825,6826,6827,6830,6834,6837,6840,6843,6846,6849,6852,6926,6929,6930,6933,6936,6937,6940,6941,6942,6946,6947,6952,6959,6966,6973,6980,6987,6994,7001,7008,7015,7024,7033,7042,7049,7058,7067,7070,7073,7074,7075,7076,7077,7078,7079,7080,7081,7082,7083,7084,7085,7089,7094,7099,7102,7103,7104,7105,7106,7114,7122,7123,7131,7135,7143,7151,7159,7167,7175,7176,7184,7192,7193,7196,7235,7237,7242,7244,7249,7253,7261,7262,7263,7264,7268,7272,7273,7277,7278,7279,7280,7281,7282,7283,7284,7285,7286,7287,7291,7292,7293,7294,7298,7299,7300,7301,7305,7309,7310,7314,7315,7316,7321,7322,7323,7324,7325,7326,7327,7328,7329,7330,7331,7332,7333,7334,7335,7336,7337,7338,7339,7340,7341,7345,7346,7347,7353,7354,7358,7360,7361,7366,7367,7368,7369,7370,7371,7375,7376,7377,7383,7384,7388,7390,7394,7398,7402,7426,7427,7428,7429,7432,7435,7438,7441,7444,7449,7453,7456,7457,7462,7466,7471,7477,7483,7488,7492,7497,7501,7505,7546,7547,7548,7549,7550,7554,7555,7556,7557,7561,7565,7569,7573,7577,7581,7585,7589,7595,7596,7637,7651,7656,7682,7689,7692,7703,7708,7711,7714,7769,7775,7776,7779,7782,7785,7788,7791,7794,7797,7801,7804,7805,7806,7814,7822,7825,7830,7835,7840,7845,7849,7853,7854,7862,7863,7864,7865,7866,7874,7879,7884,7885,7886,7887,7912,7918,7923,7926,7930,7933,7937,7947,7950,7955,7958,7962,8063,8071,8085,8098,8102,8117,8128,8131,8142,8147,8151,8186,8187,8188,8200,8208,8216,8224,8232,8252,8255,8282,8287,8307,8310,8313,8320,8333,8342,8345,8365,8375,8379,8383,8396,8400,8404,8408,8414,8418,8435,8443,8447,8451,8455,8458,8462,8466,8470,8480,8487,8494,8498,8524,8534,8559,8568,8588,8598,8602,8612,8637,8647,8650,8657,8664,8671,8672,8673,8674,8675,8682,8686,8692,8698,8699,8712,8713,8714,8717,8720,8723,8726,8729,8732,8735,8738,8741,8744,8747,8750,8753,8756,8759,8762,8765,8768,8771,8774,8777,8778,8786,8794,8795,8808,8818,8822,8827,8832,8836,8839,8843,8847,8850,8854,8857,8861,8866,8871,8874,8881,8885,8889,8898,8903,8908,8909,8913,8916,8920,8933,8938,8946,8950,8954,8971,8975,8980,8998,9005,9009,9039,9042,9045,9048,9051,9054,9057,9076,9082,9090,9097,9109,9117,9122,9130,9134,9152,9159,9175,9179,9187,9190,9195,9196,9197,9198,9202,9206,9210,9214,9249,9252,9256,9260,9294,9297,9301,9305,9314,9320,9323,9333,9337,9338,9345,9349,9356,9357,9358,9361,9366,9371,9372,9376,9391,9410,9414,9415,9427,9437,9438,9450,9455,9479,9482,9488,9491,9500,9508,9512,9515,9518,9521,9525,9528,9545,9549,9552,9567,9570,9578,9583,9590,9595,9596,9601,9602,9608,9614,9620,9652,9663,9680,9687,9691,9694,9707,9716,9720,9725,9729,9733,9737,9741,9745,9749,9753,9758,9761,9773,9778,9787,9790,9797,9798,9802,9811,9817,9821,9822,9826,9847,9853,9857,9861,9862,9880,9881,9882,9883,9884,9889,9892,9893,9899,9900,9912,9924,9931,9932,9937,9942,9943,9947,9961,9966,9972,9978,9984,9989,9995,10001,10002,10008,10023,10028,10037,10046,10049,10063,10068,10079,10083,10092,10101,10102,10109,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6491,6733,7186,7344,7400,7460,7521,7586,7741,7791,7841,7894,7952,8051,8421,8469,8540,8612,8684,8757,8824,8873,8927,8964,9015,9075,9122,9178,9227,9285,9339,9400,9456,9507,9567,9623,9686,9735,9791,9847,9897,9956,10011,10073,10120,10174,10230,10282,10337,10391,10445,10499,10548,10606,10660,10717,10773,10820,10873,10929,10989,11052,11111,11173,11223,11277,11331,11379,11436,11489,12113,12167,13098,13209,13271,13327,13387,13440,13501,13580,13661,13733,13812,13892,13968,14046,14115,14191,14268,14339,14412,14488,14566,14635,14711,14788,14852,14923,17436,17532,17585,17841,17908,17961,18013,18063,18121,18186,18234,25175,25242,25308,25366,25435,25493,25562,25632,25705,25779,25847,25914,25984,26050,26123,26183,26259,26319,26379,26454,26522,26588,26656,26716,26775,26832,26898,26960,27017,27085,27158,27228,27290,27351,27419,27481,27551,27620,27676,27735,27797,27859,27926,27983,28044,28105,28166,28227,28283,28339,28395,28451,28509,28567,28625,28683,28740,28797,28854,28911,28970,29029,29087,29170,29253,29326,29380,29449,29505,29586,29667,29738,30073,30126,30184,31349,31908,32261,32321,32375,32445,32515,32580,32646,32711,32779,32848,32916,33046,33099,33158,33216,33314,33700,33752,33798,33848,33904,33998,34056,34114,34176,34239,34301,34360,34420,34485,34551,34616,34678,34740,34802,34864,34926,34988,35054,35121,35187,35250,35314,35377,35445,35506,35568,35630,35693,35757,35820,35884,35962,36021,36087,36167,36228,36432,36490,36923,36968,37435,37499,38963,42496,42570,42641,42707,42781,42850,42921,42994,43065,43133,43206,43282,43352,43430,43498,43564,43625,43694,43758,43824,43892,43958,44021,44089,44160,44225,44298,44361,44442,44506,44572,44642,44712,44782,44852,46067,46124,46182,46241,46301,46360,46419,46478,46537,46596,46655,46714,46773,46832,46891,46951,47012,47074,47135,47196,47257,47318,47379,47440,47500,47561,47622,47682,47743,47804,47865,47926,47987,48048,48109,48170,48231,48292,48353,48421,48490,48560,48629,48698,48767,48836,48905,48974,49043,49112,49181,49250,49310,49371,49433,49494,49555,49616,49677,49738,49799,49860,49921,49982,50043,50105,50168,50232,50295,50358,50421,50484,50547,50610,50673,50736,50799,50862,50923,50985,51048,51110,51172,51234,51296,51358,51420,51482,51544,51606,51668,51725,51811,51891,51981,52076,52168,52260,52350,52433,52526,52613,52710,52801,52902,52989,53092,53181,53280,53372,53472,53556,53650,53738,53836,53919,54010,54104,54203,54305,54403,54503,54590,54690,54776,54872,54960,55041,55132,55228,55321,55414,55505,55590,55684,55773,55871,55964,56066,56154,56258,56349,56449,56542,56643,56728,56823,56912,57011,57096,57188,57283,57383,57486,57585,57688,57777,57878,57965,58062,58150,58246,58338,58438,58528,58626,58711,58800,58889,58982,59069,59832,59898,59974,60043,60122,60195,60275,60355,60432,60500,60578,60654,60725,60806,60879,60962,61037,61122,61195,61276,61357,61431,61515,61585,61663,61733,61813,61891,61963,62045,62115,62192,62272,62357,62445,62529,62616,62690,62768,62846,62917,62998,63089,63172,63268,63366,63473,63538,63604,63657,63733,63799,63886,63962,72205,72583,73178,73232,73311,73389,73462,73527,73590,73656,73727,73798,73868,73930,73999,74065,74125,74192,74259,74315,74366,74419,74471,74525,74596,74659,74718,74780,74839,74912,74979,75049,75109,75172,75247,75319,75415,75486,75542,75613,75670,75727,75793,75857,75928,75985,76038,76101,76153,76211,77495,77564,77630,77689,77772,77831,77888,77955,78025,78099,78161,78230,78300,78399,78496,78595,78681,78767,78848,78923,79012,79103,79187,79246,79292,79358,79415,79482,79539,79621,79686,79752,79875,79959,80080,80145,80207,80305,80379,80462,80551,80615,80694,80768,80830,80926,80991,81050,81106,81162,81222,81329,81376,81436,81497,81561,81622,81682,81740,81783,81832,81884,81935,81987,82036,82085,82150,82216,82276,82337,82393,82452,82501,82549,82607,82664,82766,82823,82898,82946,82997,83059,83124,83176,83250,83313,83376,83444,83494,83556,83616,83673,83733,83782,83850,83956,84058,84127,84198,84254,84303,84403,84474,84584,84675,84757,84855,84911,85012,85122,85221,85284,85390,85467,85579,85706,85818,85945,86015,86129,86260,86357,86425,86543,86646,86764,86825,86899,86966,87071,87193,87267,87334,87444,87543,87616,87713,87835,87953,88071,88132,88254,88371,88439,88545,88647,88727,88798,88894,88961,89035,89109,89195,89285,89363,89440,89540,89611,89732,89853,89917,90042,90116,90240,90364,90431,90540,90668,90780,90859,90937,91038,91109,91231,91353,91418,91544,91656,91762,91830,91929,92033,92096,92162,92246,92359,92472,92590,92668,92740,92876,93012,93097,93237,93375,93513,93655,93737,93823,93900,93973,94082,94193,94321,94449,94581,94711,94841,94975,95064,95126,95222,95289,95406,95527,95624,95706,95793,95880,96011,96142,96277,96354,96431,96542,96656,96730,96839,96951,97018,97091,97156,97258,97354,97458,97526,97591,97685,97757,97867,97973,98046,98137,98239,98342,98437,98544,98649,98771,98893,99019,99078,99136,99260,99384,99512,99630,99748,99870,99956,100053,100187,100321,100401,100539,100671,100803,100939,101014,101090,101193,101267,101380,101461,101518,101579,101638,101698,101756,101817,101875,101925,101974,102041,102100,102159,102208,102279,102363,102433,102504,102584,102653,102716,102784,102850,102918,102983,103049,103126,103204,103310,103416,103512,103641,103730,103857,103923,103993,104079,104145,104228,104302,104400,104496,104592,104690,104799,104894,104983,105045,105105,105170,105227,105308,105362,105419,105516,105626,105687,105802,105923,106018,106110,106203,106259,106318,106367,106459,106508,106562,106616,106670,106724,106778,106833,106943,107053,107161,107271,107381,107491,107601,107709,107815,107919,108023,108127,108222,108317,108410,108503,108607,108713,108817,108921,109014,109107,109200,109293,109401,109507,109613,109719,109816,109911,110006,110101,110207,110313,110419,110525,110623,110718,110814,110911,110976,111080,111138,111202,111263,111325,111385,111450,111512,111580,111638,111701,111764,111831,111906,111979,112045,112097,112150,112202,112259,112343,112438,112523,112604,112684,112761,112840,112917,112991,113065,113136,113216,113288,113363,113428,113489,113549,113624,113698,113771,113841,113913,113983,114056,114120,114190,114236,114305,114357,114442,114525,114582,114648,114715,114781,114862,114937,114993,115046,115107,115165,115215,115264,115313,115362,115424,115476,115521,115602,115653,115707,115760,115814,115865,115914,115980,116031,116092,116153,116215,116265,116306,116383,116442,116501,116560,116621,116677,116733,116800,116861,116926,116981,117046,117115,117183,117261,117330,117390,117461,117535,117600,117672,117742,117809,117893,117962,118029,118099,118162,118229,118297,118380,118459,118549,118626,118694,118761,118839,118896,118953,119021,119087,119143,119203,119262,119316,119366,119416,119464,119526,119577,119650,119730,119810,119874,119941,120012,120070,120131,120197,120256,120323,120383,120443,120506,120574,120635,120702,120780,120850,120899,120956,121025,121086,121174,121262,121350,121438,121525,121612,121699,121786,121844,121918,121988,122044,122115,122180,122242,122317,122390,122480,122546,122612,122673,122737,122799,122857,122928,123011,123070,123141,123207,123272,123333,123392,123463,123529,123594,123677,123753,123828,123909,123969,124038,124108,124177,124232,124288,124344,124405,124463,124519,124578,124632,124687,124749,124806,124900,124969,125070,125121,125191,125254,125310,125368,125427,125481,125567,125651,125721,125790,125860,125975,126096,126163,126230,126305,126372,126431,126485,126539,126593,126646,126698,128982,129119,132066,132115,132165,132256,132304,132360,132418,132480,132535,132593,132664,132728,132787,132849,132915,133386,133531,133575,133620,134712,134763,134810,134855,134906,134957,135008,135545,136527,136593,136772,136835,136975,137032,137086,137141,137199,137254,137313,137369,137438,137507,137576,137646,137709,137772,137835,137898,137963,138028,138093,138158,138221,138285,138349,138413,138464,138542,138620,138691,138763,138836,138908,138974,139040,139108,139176,139242,139309,139383,139446,139503,139563,139628,139695,139760,139817,139878,139936,140040,140150,140259,140363,140441,140506,140573,140639,140709,140756,140808,140984,141111,143884,144114,144245,144429,144607,144845,145034,145714,145812,145927,146012,149720,149947,150317,150406,150563,151697,151850,152178,153820,154007,154103,154193,154289,154379,154545,154668,154791,154961,155067,155182,155297,155399,155505,155622,155737,155819,155992,156160,156308,156467,156622,156795,156912,157029,157197,157309,157423,157595,157771,157929,158062,158174,158320,158472,158604,158747,161144,161322,161458,161554,161690,161785,161952,162045,162137,162324,162480,162658,162822,163004,163321,163503,163685,163875,164107,164297,164474,164636,164793,164903,165086,165223,165427,165611,165795,165955,166113,166297,166524,166727,166898,167118,167340,167495,167695,167879,167982,168172,168313,168478,168649,168849,169053,169255,169420,169625,169824,170023,170220,170311,170460,170610,170694,170843,170988,171140,171281,171447,172140,172218,172519,172685,172840,175528,175686,175850,179863,180086,193365,193642,193914,194192,194437,194499,198573,199024,199480,210617,210765,211279,211716,212150,216490,216575,216696,216795,217200,217297,217414,217501,217624,217725,218131,218230,218349,218442,218549,218892,218999,219244,219365,219774,220022,220122,220227,220346,220855,221002,221121,221372,221505,221920,222174,227389,227636,227761,228169,228290,228518,228639,228772,228919,249641,250133,270604,271028,291795,292289,312805,313231,318072,323489,327580,333011,337753,343130,347114,351106,356497,357044,357477,358233,358463,358706,359873,408043,408947,409531,410004,411434,412178,413371,414425,414903,415196,415579,417094,417859,419002,419443,419884,420480,420754,421165,422181,422359,423112,423249,423340,425534,425800,426122,426332,426441,426560,426744,427862,428332,429083,431666,433645,434021,434249,434505,434764,435340,435694,435816,435955,436247,436507,437435,437721,438124,438526,438869,439081,439282,439495,439784,450671,450744,450831,450916,463692,463804,463910,464033,464165,464288,464418,464542,464675,464806,464931,465048,465168,465300,465428,465542,465660,465773,465894,466082,466269,466450,466633,466817,466982,467164,467284,467404,467512,467622,467734,467842,467952,468117,468283,468435,468600,468701,468821,468992,469153,469316,469477,469644,469763,469880,470060,470242,470423,470606,470761,470906,471028,471163,471326,471519,471645,471797,471939,472109,472265,472437,479332,479527,479619,479792,479954,480049,480218,480312,480401,480644,480733,481026,481442,481862,482283,482709,483126,483542,483959,484377,484791,485261,485734,486206,486617,487088,487560,487750,487956,488062,488170,488276,488388,488502,488614,488728,488844,488958,489066,489176,489284,489546,489925,490329,490476,490584,490694,490802,490916,491325,491739,491855,492273,492514,492944,493379,493789,494211,494621,494743,495152,495568,495690,495908,498728,498796,499140,499220,499576,499726,500093,500169,500281,500371,500633,500898,501006,501158,501266,501342,501454,501544,501646,501754,501862,501962,502070,502155,502321,502425,502553,502640,502807,502885,502999,503091,503355,503622,503732,503885,503995,504079,504468,504566,504674,504768,504898,505006,505128,505264,505372,505492,505626,505748,505876,506018,506144,506284,506410,506528,506660,506758,506868,507168,507280,507398,507862,507978,508281,508407,508503,508904,509014,509138,509276,509386,509508,509820,509944,510074,510550,510678,510993,511131,511293,511509,511665,513282,513350,513434,513538,513741,513930,514131,514324,514529,514842,515054,515220,515336,515582,515798,516111,516537,516999,517236,517388,517648,517792,517934,521166,521280,521400,521516,521610,521931,522030,522148,522249,522528,522813,523092,523374,523627,523886,524139,524395,524819,524895,528145,529500,529944,531798,532373,532581,533591,533971,534137,534278,539298,539724,539836,539971,540124,540321,540492,540675,540850,541037,541309,541467,541551,541655,542142,542698,542856,543075,543306,543529,543764,543986,544252,544390,544989,545103,545241,545353,545477,546048,546543,547089,547234,547327,547419,549346,549916,550214,550403,550609,550802,551012,551896,552041,552433,552591,552808,560864,561296,562171,562791,562988,563936,564701,564824,565597,565818,566018,567995,568095,568185,568871,569624,570389,571152,571927,573140,573305,574918,575239,576302,576512,576682,577252,578147,578780,578946,580432,581048,581284,581505,582463,582728,582993,583240,583654,583890,585175,585624,585811,586060,586302,586478,586719,586952,587177,587772,588247,588771,589032,590383,590858,592084,592554,593602,594054,594298,594755,596000,596483,596633,597188,597640,598040,598193,598338,598481,598551,598979,599267,599771,600280,600396,601298,601420,601532,601709,601975,602245,602511,602779,603035,603295,603551,603809,604061,604317,604569,604823,605055,605291,605543,605799,606051,606305,606537,606771,606883,607535,607990,608114,609206,610021,610217,610541,610930,611282,611523,611737,612036,612228,612543,612750,613096,613396,613797,614016,614429,614666,615036,615760,616115,616384,616524,616778,616922,617199,618191,618600,619232,619578,619946,621020,621383,621783,623291,623876,624194,626729,626923,627141,627367,627579,627778,627985,629189,629484,630041,630431,631063,631540,631785,632272,632518,633714,634111,635117,635339,635762,635953,636332,636420,636528,636636,636949,637274,637593,637924,640627,640815,641076,641325,643909,644101,644366,644619,645151,645559,645758,646342,646577,646701,647113,647327,647729,647832,647962,648137,648389,648585,648725,648919,649930,650999,651287,651417,652194,652851,652997,653703,653941,655481,655631,656048,656213,656899,657369,657565,657656,657740,657884,658118,658285,659213,659499,659659,660274,660433,660761,660988,661500,661862,661941,662280,662385,662750,663121,663482,665356,665985,667061,667485,667738,667890,668938,669675,669878,670124,670371,670589,670831,671152,671416,671721,671944,672255,672444,673159,673428,673922,674148,674588,674747,675031,675776,676141,676446,676604,676842,678161,678559,678787,679007,679149,680439,680545,680675,680813,680937,681225,681394,681494,681779,681893,682776,683531,683970,684094,684340,684533,684667,684858,685637,685855,686146,686425,686742,686964,687259,687542,687646,687987,688803,689119,689680,690186,690391,691177,691582,692243,692432,692983,693549,693669,694071,844568,844663,844756,844819,844901,844994,845087,845174,845272,845363,845454,845542,845626,845722,845822,845928,846031,846132,846236,846342,846441,846547,846649,846756,846865,846976,847107,847227,847343,847461,847560,847667,847783,847902,848030,848119,848214,848291,848380,848471,848564,848638,848735,848830,848928,849027,849131,849227,849329,849432,849532,849635,849720,849821,849919,850009,850104,850191,850297,850399,850493,850584,850678,850754,850846,850935,851038,851149,851232,851318,851413,851510,851606,851694,851795,851896,851999,852105,852203,852300,852395,852493,852596,852696,852799,852904,853022,853138,853233,853326,853411,853507,853601,853693,853776,853880,853985,854085,854186,854291,854391,854492,854591,854693,854787,854894,854996,855099,855192,855288,855390,855493,855589,855691,855794,855891,855994,856092,856196,856301,856398,856506,856620,856735,856843,856957,857072,857174,857279,857387,857497,857613,857730,857825,857922,858021,858126,858232,858331,858436,858542,858642,858748,858849,858956,859075,859174,859279,859381,859483,859583,859686,859781,859885,859970,860074,860178,860276,860380,860486,860584,860689,860787,860900,860994,861083,861172,861255,861346,861429,861527,861617,861713,861802,861896,861984,862080,862165,862273,862374,862475,862573,862679,862770,862869,862966,863064,863160,863253,863363,863461,863556,863666,863758,863858,863957,864044,864148,864253,864352,864459,864566,864665,864774,864866,864977,865088,865199,865303,865418,865534,865661,865781,865876,865971,866068,866167,866259,866358,866450,866549,866635,866729,866832,866928,867031,867127,867230,867327,867425,867528,867621,867711,867812,867895,867986,868071,868163,868266,868361,868457,868550,868644,868723,868830,868921,869020,869113,869216,869320,869421,869522,869626,869720,869824,869928,870041,870147,870253,870361,870478,870579,870687,870787,870890,870995,871102,871198,871277,871367,871451,871543,871616,871708,871797,871889,871974,872071,872164,872259,872358,872455,872546,872637,872729,872824,872931,873039,873141,873238,873335,873428,873515,873599,873696,873793,873886,873973,874064,874163,874262,874357,874446,874527,874626,874730,874827,874932,875029,875113,875212,875316,875413,875518,875615,875713,875814,875920,876019,876126,876225,876324,876415,876504,876593,876675,876768,876859,876970,877071,877171,877283,877396,877494,877602,877696,877796,877885,877977,878088,878198,878293,878409,878535,878661,878780,878908,879033,879158,879276,879403,879512,879621,879734,879857,879980,880096,880221,880318,880426,880548,880664,880780,880889,880977,881078,881167,881268,881355,881443,881540,881632,881738,881838,881914", "endLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,651,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,720,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1304,1310,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2043,2044,2094,2095,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2118,2121,2122,2123,2143,2144,2145,2146,2147,2148,2149,2160,2179,2180,2183,2184,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2246,2251,2287,2289,2290,2291,2292,2293,2294,2302,2303,2304,2305,2343,2345,2350,2351,2352,2365,2366,2369,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2457,2458,2459,2460,2461,2462,2463,2464,2465,2468,2471,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2533,2534,2535,2536,2537,2574,2575,2576,2636,2637,2799,2803,2807,2811,2812,2816,2886,2893,2901,3066,3076,3085,3094,3103,3164,3165,3166,3172,3173,3174,3175,3176,3177,3183,3184,3185,3186,3187,3192,3193,3197,3198,3204,3208,3209,3210,3211,3221,3222,3223,3227,3228,3234,3238,3239,3311,3312,3317,3318,3321,3322,3323,3324,3588,3595,3856,3862,4126,4133,4394,4400,4463,4545,4597,4679,4741,4823,4887,4939,5021,5029,5035,5046,5050,5054,5067,5082,5858,5865,5871,5888,5901,5921,5938,5947,5952,5959,5979,5992,6009,6015,6021,6028,6032,6038,6052,6055,6065,6066,6067,6115,6119,6123,6127,6128,6129,6132,6148,6155,6169,6214,6215,6249,6253,6257,6262,6269,6275,6276,6279,6283,6288,6301,6305,6310,6315,6320,6323,6326,6329,6333,6337,6477,6478,6479,6480,6712,6713,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6726,6727,6728,6729,6733,6737,6741,6745,6749,6753,6757,6758,6759,6760,6761,6762,6763,6764,6768,6772,6773,6777,6778,6781,6785,6788,6791,6794,6798,6801,6804,6808,6812,6816,6820,6823,6824,6825,6826,6829,6833,6836,6839,6842,6845,6848,6851,6855,6928,6929,6932,6935,6936,6939,6940,6941,6945,6946,6951,6958,6965,6972,6979,6986,6993,7000,7007,7014,7023,7032,7041,7048,7057,7066,7069,7072,7073,7074,7075,7076,7077,7078,7079,7080,7081,7082,7083,7084,7088,7093,7098,7101,7102,7103,7104,7105,7113,7121,7122,7130,7134,7142,7150,7158,7166,7174,7175,7183,7191,7192,7195,7198,7236,7241,7243,7248,7252,7256,7261,7262,7263,7267,7271,7272,7276,7277,7278,7279,7280,7281,7282,7283,7284,7285,7286,7290,7291,7292,7293,7297,7298,7299,7300,7304,7308,7309,7313,7314,7315,7320,7321,7322,7323,7324,7325,7326,7327,7328,7329,7330,7331,7332,7333,7334,7335,7336,7337,7338,7339,7340,7344,7345,7346,7352,7353,7357,7359,7360,7365,7366,7367,7368,7369,7370,7374,7375,7376,7382,7383,7387,7389,7393,7397,7401,7405,7426,7427,7428,7431,7434,7437,7440,7443,7448,7452,7455,7456,7461,7465,7470,7476,7482,7487,7491,7496,7500,7504,7545,7546,7547,7548,7549,7553,7554,7555,7556,7560,7564,7568,7572,7576,7580,7584,7588,7594,7595,7636,7650,7655,7681,7688,7691,7702,7707,7710,7713,7768,7774,7775,7778,7781,7784,7787,7790,7793,7796,7800,7803,7804,7805,7813,7821,7824,7829,7834,7839,7844,7848,7852,7853,7861,7862,7863,7864,7865,7873,7878,7883,7884,7885,7886,7911,7917,7922,7925,7929,7932,7936,7946,7949,7954,7957,7961,7965,8070,8084,8097,8101,8116,8127,8130,8141,8146,8150,8185,8186,8187,8199,8207,8215,8223,8231,8251,8254,8281,8286,8306,8309,8312,8319,8332,8341,8344,8364,8374,8378,8382,8395,8399,8403,8407,8413,8417,8434,8442,8446,8450,8454,8457,8461,8465,8469,8479,8486,8493,8497,8523,8533,8558,8567,8587,8597,8601,8611,8636,8646,8649,8656,8663,8670,8671,8672,8673,8674,8681,8685,8691,8697,8698,8711,8712,8713,8716,8719,8722,8725,8728,8731,8734,8737,8740,8743,8746,8749,8752,8755,8758,8761,8764,8767,8770,8773,8776,8777,8785,8793,8794,8807,8817,8821,8826,8831,8835,8838,8842,8846,8849,8853,8856,8860,8865,8870,8873,8880,8884,8888,8897,8902,8907,8908,8912,8915,8919,8932,8937,8945,8949,8953,8970,8974,8979,8997,9004,9008,9038,9041,9044,9047,9050,9053,9056,9075,9081,9089,9096,9108,9116,9121,9129,9133,9151,9158,9174,9178,9186,9189,9194,9195,9196,9197,9201,9205,9209,9213,9248,9251,9255,9259,9293,9296,9300,9304,9313,9319,9322,9332,9336,9337,9344,9348,9355,9356,9357,9360,9365,9370,9371,9375,9390,9409,9413,9414,9426,9436,9437,9449,9454,9478,9481,9487,9490,9499,9507,9511,9514,9517,9520,9524,9527,9544,9548,9551,9566,9569,9577,9582,9589,9594,9595,9600,9601,9607,9613,9619,9651,9662,9679,9686,9690,9693,9706,9715,9719,9724,9728,9732,9736,9740,9744,9748,9752,9757,9760,9772,9777,9786,9789,9796,9797,9801,9810,9816,9820,9821,9825,9846,9852,9856,9860,9861,9879,9880,9881,9882,9883,9888,9891,9892,9898,9899,9911,9923,9930,9931,9936,9941,9942,9946,9960,9965,9971,9977,9983,9988,9994,10000,10001,10007,10022,10027,10036,10045,10048,10062,10067,10078,10082,10091,10100,10101,10108,10116,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "6542,6777,7236,7395,7455,7516,7581,7636,7786,7836,7889,7947,7995,8115,8464,8535,8607,8679,8752,8819,8868,8922,8959,9010,9070,9117,9173,9222,9280,9334,9395,9451,9502,9562,9618,9681,9730,9786,9842,9892,9951,10006,10068,10115,10169,10225,10277,10332,10386,10440,10494,10543,10601,10655,10712,10768,10815,10868,10924,10984,11047,11106,11168,11218,11272,11326,11374,11431,11484,11540,12162,12218,13156,13266,13322,13382,13435,13496,13575,13656,13728,13807,13887,13963,14041,14110,14186,14263,14334,14407,14483,14561,14630,14706,14783,14847,14918,14990,17482,17580,17635,17903,17956,18008,18058,18116,18181,18229,18280,25237,25303,25361,25430,25488,25557,25627,25700,25774,25842,25909,25979,26045,26118,26178,26254,26314,26374,26449,26517,26583,26651,26711,26770,26827,26893,26955,27012,27080,27153,27223,27285,27346,27414,27476,27546,27615,27671,27730,27792,27854,27921,27978,28039,28100,28161,28222,28278,28334,28390,28446,28504,28562,28620,28678,28735,28792,28849,28906,28965,29024,29082,29165,29248,29321,29375,29444,29500,29581,29662,29733,29862,30121,30179,30237,31402,31949,32316,32370,32440,32510,32575,32641,32706,32774,32843,32911,33041,33094,33153,33211,33263,33359,33747,33793,33843,33899,33946,34051,34109,34171,34234,34296,34355,34415,34480,34546,34611,34673,34735,34797,34859,34921,34983,35049,35116,35182,35245,35309,35372,35440,35501,35563,35625,35688,35752,35815,35879,35957,36016,36082,36162,36223,36276,36485,36536,36963,37024,37494,37553,39020,42565,42636,42702,42776,42845,42916,42989,43060,43128,43201,43277,43347,43425,43493,43559,43620,43689,43753,43819,43887,43953,44016,44084,44155,44220,44293,44356,44437,44501,44567,44637,44707,44777,44847,44914,46119,46177,46236,46296,46355,46414,46473,46532,46591,46650,46709,46768,46827,46886,46946,47007,47069,47130,47191,47252,47313,47374,47435,47495,47556,47617,47677,47738,47799,47860,47921,47982,48043,48104,48165,48226,48287,48348,48416,48485,48555,48624,48693,48762,48831,48900,48969,49038,49107,49176,49245,49305,49366,49428,49489,49550,49611,49672,49733,49794,49855,49916,49977,50038,50100,50163,50227,50290,50353,50416,50479,50542,50605,50668,50731,50794,50857,50918,50980,51043,51105,51167,51229,51291,51353,51415,51477,51539,51601,51663,51720,51806,51886,51976,52071,52163,52255,52345,52428,52521,52608,52705,52796,52897,52984,53087,53176,53275,53367,53467,53551,53645,53733,53831,53914,54005,54099,54198,54300,54398,54498,54585,54685,54771,54867,54955,55036,55127,55223,55316,55409,55500,55585,55679,55768,55866,55959,56061,56149,56253,56344,56444,56537,56638,56723,56818,56907,57006,57091,57183,57278,57378,57481,57580,57683,57772,57873,57960,58057,58145,58241,58333,58433,58523,58621,58706,58795,58884,58977,59064,59155,59893,59969,60038,60117,60190,60270,60350,60427,60495,60573,60649,60720,60801,60874,60957,61032,61117,61190,61271,61352,61426,61510,61580,61658,61728,61808,61886,61958,62040,62110,62187,62267,62352,62440,62524,62611,62685,62763,62841,62912,62993,63084,63167,63263,63361,63468,63533,63599,63652,63728,63794,63881,63957,64033,72265,72633,73227,73306,73384,73457,73522,73585,73651,73722,73793,73863,73925,73994,74060,74120,74187,74254,74310,74361,74414,74466,74520,74591,74654,74713,74775,74834,74907,74974,75044,75104,75167,75242,75314,75410,75481,75537,75608,75665,75722,75788,75852,75923,75980,76033,76096,76148,76206,76273,77559,77625,77684,77767,77826,77883,77950,78020,78094,78156,78225,78295,78394,78491,78590,78676,78762,78843,78918,79007,79098,79182,79241,79287,79353,79410,79477,79534,79616,79681,79747,79870,79954,80075,80140,80202,80300,80374,80457,80546,80610,80689,80763,80825,80921,80986,81045,81101,81157,81217,81324,81371,81431,81492,81556,81617,81677,81735,81778,81827,81879,81930,81982,82031,82080,82145,82211,82271,82332,82388,82447,82496,82544,82602,82659,82761,82818,82893,82941,82992,83054,83119,83171,83245,83308,83371,83439,83489,83551,83611,83668,83728,83777,83845,83951,84053,84122,84193,84249,84298,84398,84469,84579,84670,84752,84850,84906,85007,85117,85216,85279,85385,85462,85574,85701,85813,85940,86010,86124,86255,86352,86420,86538,86641,86759,86820,86894,86961,87066,87188,87262,87329,87439,87538,87611,87708,87830,87948,88066,88127,88249,88366,88434,88540,88642,88722,88793,88889,88956,89030,89104,89190,89280,89358,89435,89535,89606,89727,89848,89912,90037,90111,90235,90359,90426,90535,90663,90775,90854,90932,91033,91104,91226,91348,91413,91539,91651,91757,91825,91924,92028,92091,92157,92241,92354,92467,92585,92663,92735,92871,93007,93092,93232,93370,93508,93650,93732,93818,93895,93968,94077,94188,94316,94444,94576,94706,94836,94970,95059,95121,95217,95284,95401,95522,95619,95701,95788,95875,96006,96137,96272,96349,96426,96537,96651,96725,96834,96946,97013,97086,97151,97253,97349,97453,97521,97586,97680,97752,97862,97968,98041,98132,98234,98337,98432,98539,98644,98766,98888,99014,99073,99131,99255,99379,99507,99625,99743,99865,99951,100048,100182,100316,100396,100534,100666,100798,100934,101009,101085,101188,101262,101375,101456,101513,101574,101633,101693,101751,101812,101870,101920,101969,102036,102095,102154,102203,102274,102358,102428,102499,102579,102648,102711,102779,102845,102913,102978,103044,103121,103199,103305,103411,103507,103636,103725,103852,103918,103988,104074,104140,104223,104297,104395,104491,104587,104685,104794,104889,104978,105040,105100,105165,105222,105303,105357,105414,105511,105621,105682,105797,105918,106013,106105,106198,106254,106313,106362,106454,106503,106557,106611,106665,106719,106773,106828,106938,107048,107156,107266,107376,107486,107596,107704,107810,107914,108018,108122,108217,108312,108405,108498,108602,108708,108812,108916,109009,109102,109195,109288,109396,109502,109608,109714,109811,109906,110001,110096,110202,110308,110414,110520,110618,110713,110809,110906,110971,111075,111133,111197,111258,111320,111380,111445,111507,111575,111633,111696,111759,111826,111901,111974,112040,112092,112145,112197,112254,112338,112433,112518,112599,112679,112756,112835,112912,112986,113060,113131,113211,113283,113358,113423,113484,113544,113619,113693,113766,113836,113908,113978,114051,114115,114185,114231,114300,114352,114437,114520,114577,114643,114710,114776,114857,114932,114988,115041,115102,115160,115210,115259,115308,115357,115419,115471,115516,115597,115648,115702,115755,115809,115860,115909,115975,116026,116087,116148,116210,116260,116301,116378,116437,116496,116555,116616,116672,116728,116795,116856,116921,116976,117041,117110,117178,117256,117325,117385,117456,117530,117595,117667,117737,117804,117888,117957,118024,118094,118157,118224,118292,118375,118454,118544,118621,118689,118756,118834,118891,118948,119016,119082,119138,119198,119257,119311,119361,119411,119459,119521,119572,119645,119725,119805,119869,119936,120007,120065,120126,120192,120251,120318,120378,120438,120501,120569,120630,120697,120775,120845,120894,120951,121020,121081,121169,121257,121345,121433,121520,121607,121694,121781,121839,121913,121983,122039,122110,122175,122237,122312,122385,122475,122541,122607,122668,122732,122794,122852,122923,123006,123065,123136,123202,123267,123328,123387,123458,123524,123589,123672,123748,123823,123904,123964,124033,124103,124172,124227,124283,124339,124400,124458,124514,124573,124627,124682,124744,124801,124895,124964,125065,125116,125186,125249,125305,125363,125422,125476,125562,125646,125716,125785,125855,125970,126091,126158,126225,126300,126367,126426,126480,126534,126588,126641,126693,126767,129114,129254,132110,132160,132210,132299,132355,132413,132475,132530,132588,132659,132723,132782,132844,132910,132976,133424,133570,133615,133658,134758,134805,134850,134901,134952,135003,135054,135588,136588,136650,136830,136902,137027,137081,137136,137194,137249,137308,137364,137433,137502,137571,137641,137704,137767,137830,137893,137958,138023,138088,138153,138216,138280,138344,138408,138459,138537,138615,138686,138758,138831,138903,138969,139035,139103,139171,139237,139304,139378,139441,139498,139558,139623,139690,139755,139812,139873,139931,140035,140145,140254,140358,140436,140501,140568,140634,140704,140751,140803,140853,141036,141426,144029,144240,144424,144602,144840,145029,145198,145807,145922,146007,146086,149875,150007,150401,150558,150715,151845,151999,152232,154002,154098,154188,154284,154374,154540,154663,154786,154956,155062,155177,155292,155394,155500,155617,155732,155814,155987,156155,156303,156462,156617,156790,156907,157024,157192,157304,157418,157590,157766,157924,158057,158169,158315,158467,158599,158742,158864,161317,161453,161549,161685,161780,161947,162040,162132,162319,162475,162653,162817,162999,163316,163498,163680,163870,164102,164292,164469,164631,164788,164898,165081,165218,165422,165606,165790,165950,166108,166292,166519,166722,166893,167113,167335,167490,167690,167874,167977,168167,168308,168473,168644,168844,169048,169250,169415,169620,169819,170018,170215,170306,170455,170605,170689,170838,170983,171135,171276,171442,171603,172213,172514,172680,172835,172937,175681,175845,176031,180081,180206,193637,193909,194187,194432,194494,194779,199019,199475,199984,210760,211274,211711,212145,212588,216570,216691,216790,217195,217292,217409,217496,217619,217720,218126,218225,218344,218437,218544,218887,218994,219239,219360,219769,220017,220117,220222,220341,220850,220997,221116,221367,221500,221915,222169,222281,227631,227756,228164,228285,228513,228634,228767,228914,249636,250128,270599,271023,291790,292284,312800,313226,318067,323484,327575,333006,337748,343125,347109,351101,356492,357039,357472,358228,358458,358701,359868,360797,408942,409526,409999,411429,412173,413366,414420,414898,415191,415574,417089,417854,418997,419438,419879,420475,420749,421160,422176,422354,423107,423244,423335,425529,425795,426117,426327,426436,426555,426739,427857,428327,429078,431661,431756,434016,434244,434500,434759,435335,435689,435811,435950,436242,436502,437430,437716,438119,438521,438864,439076,439277,439490,439779,440064,450739,450826,450911,451010,463799,463905,464028,464160,464283,464413,464537,464670,464801,464926,465043,465163,465295,465423,465537,465655,465768,465889,466077,466264,466445,466628,466812,466977,467159,467279,467399,467507,467617,467729,467837,467947,468112,468278,468430,468595,468696,468816,468987,469148,469311,469472,469639,469758,469875,470055,470237,470418,470601,470756,470901,471023,471158,471321,471514,471640,471792,471934,472104,472260,472432,472723,479522,479614,479787,479949,480044,480213,480307,480396,480639,480728,481021,481437,481857,482278,482704,483121,483537,483954,484372,484786,485256,485729,486201,486612,487083,487555,487745,487951,488057,488165,488271,488383,488497,488609,488723,488839,488953,489061,489171,489279,489541,489920,490324,490471,490579,490689,490797,490911,491320,491734,491850,492268,492509,492939,493374,493784,494206,494616,494738,495147,495563,495685,495903,496087,498791,499135,499215,499571,499721,499865,500164,500276,500366,500628,500893,501001,501153,501261,501337,501449,501539,501641,501749,501857,501957,502065,502150,502316,502420,502548,502635,502802,502880,502994,503086,503350,503617,503727,503880,503990,504074,504463,504561,504669,504763,504893,505001,505123,505259,505367,505487,505621,505743,505871,506013,506139,506279,506405,506523,506655,506753,506863,507163,507275,507393,507857,507973,508276,508402,508498,508899,509009,509133,509271,509381,509503,509815,509939,510069,510545,510673,510988,511126,511288,511504,511660,511864,513345,513429,513533,513736,513925,514126,514319,514524,514837,515049,515215,515331,515577,515793,516106,516532,516994,517231,517383,517643,517787,517929,521161,521275,521395,521511,521605,521926,522025,522143,522244,522523,522808,523087,523369,523622,523881,524134,524390,524814,524890,528140,529495,529939,531793,532368,532576,533586,533966,534132,534273,539293,539719,539831,539966,540119,540316,540487,540670,540845,541032,541304,541462,541546,541650,542137,542693,542851,543070,543301,543524,543759,543981,544247,544385,544984,545098,545236,545348,545472,546043,546538,547084,547229,547322,547414,549341,549911,550209,550398,550604,550797,551007,551891,552036,552428,552586,552803,553064,561291,562166,562786,562983,563931,564696,564819,565592,565813,566013,567990,568090,568180,568866,569619,570384,571147,571922,573135,573300,574913,575234,576297,576507,576677,577247,578142,578775,578941,580427,581043,581279,581500,582458,582723,582988,583235,583649,583885,585170,585619,585806,586055,586297,586473,586714,586947,587172,587767,588242,588766,589027,590378,590853,592079,592549,593597,594049,594293,594750,595995,596478,596628,597183,597635,598035,598188,598333,598476,598546,598974,599262,599766,600275,600391,601293,601415,601527,601704,601970,602240,602506,602774,603030,603290,603546,603804,604056,604312,604564,604818,605050,605286,605538,605794,606046,606300,606532,606766,606878,607530,607985,608109,609201,610016,610212,610536,610925,611277,611518,611732,612031,612223,612538,612745,613091,613391,613792,614011,614424,614661,615031,615755,616110,616379,616519,616773,616917,617194,618186,618595,619227,619573,619941,621015,621378,621778,623286,623871,624189,626724,626918,627136,627362,627574,627773,627980,629184,629479,630036,630426,631058,631535,631780,632267,632513,633709,634106,635112,635334,635757,635948,636327,636415,636523,636631,636944,637269,637588,637919,640622,640810,641071,641320,643904,644096,644361,644614,645146,645554,645753,646337,646572,646696,647108,647322,647724,647827,647957,648132,648384,648580,648720,648914,649925,650994,651282,651412,652189,652846,652992,653698,653936,655476,655626,656043,656208,656894,657364,657560,657651,657735,657879,658113,658280,659208,659494,659654,660269,660428,660756,660983,661495,661857,661936,662275,662380,662745,663116,663477,665351,665980,667056,667480,667733,667885,668933,669670,669873,670119,670366,670584,670826,671147,671411,671716,671939,672250,672439,673154,673423,673917,674143,674583,674742,675026,675771,676136,676441,676599,676837,678156,678554,678782,679002,679144,680434,680540,680670,680808,680932,681220,681389,681489,681774,681888,682771,683526,683965,684089,684335,684528,684662,684853,685632,685850,686141,686420,686737,686959,687254,687537,687641,687982,688798,689114,689675,690181,690386,691172,691577,692238,692427,692978,693544,693664,694066,694600,844658,844751,844814,844896,844989,845082,845169,845267,845358,845449,845537,845621,845717,845817,845923,846026,846127,846231,846337,846436,846542,846644,846751,846860,846971,847102,847222,847338,847456,847555,847662,847778,847897,848025,848114,848209,848286,848375,848466,848559,848633,848730,848825,848923,849022,849126,849222,849324,849427,849527,849630,849715,849816,849914,850004,850099,850186,850292,850394,850488,850579,850673,850749,850841,850930,851033,851144,851227,851313,851408,851505,851601,851689,851790,851891,851994,852100,852198,852295,852390,852488,852591,852691,852794,852899,853017,853133,853228,853321,853406,853502,853596,853688,853771,853875,853980,854080,854181,854286,854386,854487,854586,854688,854782,854889,854991,855094,855187,855283,855385,855488,855584,855686,855789,855886,855989,856087,856191,856296,856393,856501,856615,856730,856838,856952,857067,857169,857274,857382,857492,857608,857725,857820,857917,858016,858121,858227,858326,858431,858537,858637,858743,858844,858951,859070,859169,859274,859376,859478,859578,859681,859776,859880,859965,860069,860173,860271,860375,860481,860579,860684,860782,860895,860989,861078,861167,861250,861341,861424,861522,861612,861708,861797,861891,861979,862075,862160,862268,862369,862470,862568,862674,862765,862864,862961,863059,863155,863248,863358,863456,863551,863661,863753,863853,863952,864039,864143,864248,864347,864454,864561,864660,864769,864861,864972,865083,865194,865298,865413,865529,865656,865776,865871,865966,866063,866162,866254,866353,866445,866544,866630,866724,866827,866923,867026,867122,867225,867322,867420,867523,867616,867706,867807,867890,867981,868066,868158,868261,868356,868452,868545,868639,868718,868825,868916,869015,869108,869211,869315,869416,869517,869621,869715,869819,869923,870036,870142,870248,870356,870473,870574,870682,870782,870885,870990,871097,871193,871272,871362,871446,871538,871611,871703,871792,871884,871969,872066,872159,872254,872353,872450,872541,872632,872724,872819,872926,873034,873136,873233,873330,873423,873510,873594,873691,873788,873881,873968,874059,874158,874257,874352,874441,874522,874621,874725,874822,874927,875024,875108,875207,875311,875408,875513,875610,875708,875809,875915,876014,876121,876220,876319,876410,876499,876588,876670,876763,876854,876965,877066,877166,877278,877391,877489,877597,877691,877791,877880,877972,878083,878193,878288,878404,878530,878656,878775,878903,879028,879153,879271,879398,879507,879616,879729,879852,879975,880091,880216,880313,880421,880543,880659,880775,880884,880972,881073,881162,881263,881350,881438,881535,881627,881733,881833,881909,881986"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2817,2818,2823,2828,2836,6233", "startColumns": "4,4,4,4,4,4", "startOffsets": "194784,194858,195086,195359,195846,433199", "endLines": "2817,2822,2827,2835,2839,6235", "endColumns": "73,12,12,12,12,12", "endOffsets": "194853,195081,195354,195841,196025,433322"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_banner_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "938", "startColumns": "4", "startOffsets": "45799", "endColumns": "54", "endOffsets": "45849"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "867,872,873,874,875,876,877,878,879,928,929,935,941,942,1184,1185,1200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41087,41386,41432,41482,41526,41573,41620,41671,41724,45199,45247,45612,45955,46010,64285,64333,65298", "endColumns": "49,45,49,43,46,46,50,52,49,47,53,51,54,56,47,52,54", "endOffsets": "41132,41427,41477,41521,41568,41615,41666,41719,41769,45242,45296,45659,46005,46062,64328,64381,65348"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,401,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,402,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25080,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25130,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,49,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25125,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25177,-1"}, "to": {"startLines": "46,59,83,101,106,113,161,171,2282,2283,2284,2286,2288,2324,2325,2326,2327,2328,2329,2330,2331,2332,2337,2338,2339,2340,2344,2346,2347,2348,2356,2357,2362,2364,2367,2368,2379,2380,2381,2382,2383,2384,2385,2386,2387,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2525,2526,2528,2529,2530,2531,2532,2538,2539,2544,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2638,2639,2641,2642,2643,2644,2645,2646,2647,2648,2649,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2786", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1586,1980,2719,3329,3517,3764,5336,5640,143546,143611,143718,143833,144034,148424,148508,148575,148659,148748,148826,148896,148967,149028,149433,149497,149598,149638,149880,150012,150095,150168,150935,150992,151468,151647,152004,152087,153300,153347,153392,153432,153486,153570,153630,153672,153753,158869,158927,158987,159044,159098,159161,159228,159303,159394,159479,159565,159661,159749,159840,159930,160018,160106,160202,160287,160364,160426,160488,160540,160600,160677,160743,160813,160869,160941,161003,161065,171608,171684,171802,171870,171934,171980,172066,172942,173010,173299,173500,173546,173610,173680,173743,173801,173881,173958,174060,174159,174265,174365,174491,174563,174658,174740,174828,174919,174995,175079,175180,175258,175313,175407,176036,176105,176160,176216,176284,176339,176416,176489,176544,176605,176654,176713,176758,176810,176872,176981,177058,177117,177180,177285,177340,177389,177455,177510,177583,177640,177698,177753,177814,177863,177913,177998,178077,178153,178213,178268,178325,178398,178470,178533,178600,178681,178752,178815,178884,178972,179032,179089,179147,179211,179267,179314,179370,179426,179517,179583,179642,179739,179800,180211,180279,180414,180493,180561,180622,180692,180778,180867,180970,181026,181222,181381,181497,181685,181885,182025,182085,182153,182211,182428,182566,182656,182791,182956,183027,183176,183440,183538,183742,183798,183951,184055,184124,184278,184463,184533,184597,184656,184719,184765,184826,184902,184980,185054,185112,185196,185272,185372,185468,185564,185662,185741,185809,185883,185953,186032,186084,186132,186178,186222,186289,186349,186416,186501,186569,186651,186756,186851,186944,187031,187117,187193,187283,187355,187412,187484,187569,187650,187723,187796,187870,187939,188027,188089,188183,188267,188334,188397,188489,188581,188656,188728,188795,188869,188956,189026,189100,189170,189234,189328,189388,189454,189506,189585,189643,189716,189795,189857,189915,189994,190036,190103,190168,190228,190300,190394,190463,190529,190579,190651,190773,190831,190953,191014,191102,191186,191296,191382,191456,191520,191582,191656,191706,191780,191917,192005,192074,192145,192221,192296,192346,192496", "endLines": "49,63,88,105,112,116,165,175,2282,2283,2284,2286,2288,2324,2325,2326,2327,2328,2329,2330,2331,2332,2337,2338,2339,2340,2344,2346,2347,2348,2356,2357,2362,2364,2367,2368,2379,2380,2381,2382,2383,2384,2385,2386,2387,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2525,2526,2528,2529,2530,2531,2532,2538,2539,2544,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2638,2639,2641,2642,2643,2644,2645,2646,2647,2648,2649,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2786", "endColumns": "19,19,19,19,19,19,19,19,64,106,48,50,79,83,66,83,88,77,69,70,60,84,63,100,39,81,66,82,72,91,56,60,71,49,82,90,46,44,39,53,83,59,41,80,66,57,59,56,53,62,66,74,90,84,85,95,87,90,89,87,87,95,84,76,61,61,51,59,76,65,69,55,71,61,61,78,75,73,67,63,45,85,73,67,72,77,45,63,69,62,57,79,76,101,98,105,99,125,71,94,81,87,90,75,83,100,77,54,93,67,68,54,55,67,54,76,72,54,60,48,58,44,51,61,108,76,58,62,104,54,48,65,54,72,56,57,54,60,48,49,84,78,75,59,54,56,72,71,62,66,80,70,62,68,87,59,56,57,63,55,46,55,55,90,65,58,96,60,62,67,63,78,67,60,69,85,88,102,55,56,158,115,187,199,139,59,67,57,216,137,89,134,164,70,148,263,97,203,55,152,103,68,23,184,69,63,58,62,45,60,75,77,73,57,83,75,99,95,95,97,78,67,73,69,78,51,47,45,43,66,59,66,84,67,81,104,94,92,86,85,75,89,71,56,71,84,80,72,72,73,68,87,61,93,83,66,62,91,91,74,71,66,73,86,69,73,69,63,93,59,65,51,78,57,72,78,61,57,78,41,66,64,59,71,93,68,65,49,71,121,57,65,60,87,83,109,85,73,63,61,73,49,73,136,87,68,70,75,74,49,51,63", "endOffsets": "1697,2124,2914,3512,3759,3891,5479,5791,143606,143713,143762,143879,144109,148503,148570,148654,148743,148821,148891,148962,149023,149108,149492,149593,149633,149715,149942,150090,150163,150255,150987,151048,151535,151692,152082,152173,153342,153387,153427,153481,153565,153625,153667,153748,153815,158922,158982,159039,159093,159156,159223,159298,159389,159474,159560,159656,159744,159835,159925,160013,160101,160197,160282,160359,160421,160483,160535,160595,160672,160738,160808,160864,160936,160998,161060,161139,171679,171753,171865,171929,171975,172061,172135,173005,173078,173372,173541,173605,173675,173738,173796,173876,173953,174055,174154,174260,174360,174486,174558,174653,174735,174823,174914,174990,175074,175175,175253,175308,175402,175470,176100,176155,176211,176279,176334,176411,176484,176539,176600,176649,176708,176753,176805,176867,176976,177053,177112,177175,177280,177335,177384,177450,177505,177578,177635,177693,177748,177809,177858,177908,177993,178072,178148,178208,178263,178320,178393,178465,178528,178595,178676,178747,178810,178879,178967,179027,179084,179142,179206,179262,179309,179365,179421,179512,179578,179637,179734,179795,179858,180274,180338,180488,180556,180617,180687,180773,180862,180965,181021,181078,181376,181492,181680,181880,182020,182080,182148,182206,182423,182561,182651,182786,182951,183022,183171,183435,183533,183737,183793,183946,184050,184119,184273,184458,184528,184592,184651,184714,184760,184821,184897,184975,185049,185107,185191,185267,185367,185463,185559,185657,185736,185804,185878,185948,186027,186079,186127,186173,186217,186284,186344,186411,186496,186564,186646,186751,186846,186939,187026,187112,187188,187278,187350,187407,187479,187564,187645,187718,187791,187865,187934,188022,188084,188178,188262,188329,188392,188484,188576,188651,188723,188790,188864,188951,189021,189095,189165,189229,189323,189383,189449,189501,189580,189638,189711,189790,189852,189910,189989,190031,190098,190163,190223,190295,190389,190458,190524,190574,190646,190768,190826,190892,191009,191097,191181,191291,191377,191451,191515,191577,191651,191701,191775,191912,192000,192069,192140,192216,192291,192341,192393,192555"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "939", "startColumns": "4", "startOffsets": "45854", "endColumns": "56", "endOffsets": "45906"}}]}, {"outputFile": "com.mohamedrady.v2hoor.app-mergeFdroidDebugResources-73:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\e09aa481a2483f3d3bc09954c2742862\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2162", "startColumns": "4", "startOffsets": "135638", "endColumns": "42", "endOffsets": "135676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3301c563da9d289aac88c6ca3529e20c\\transformed\\recyclerview-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "697,1369,1370,1371,1380,1381,1382,2091", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "31852,76451,76510,76558,77272,77347,77423,131930", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "31903,76505,76553,76609,77342,77418,77490,131991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\be0b7d272e566bfd154d1bf5d5ba0c43\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2086,2124,2167", "startColumns": "4,4,4", "startOffsets": "131698,133663,135888", "endColumns": "56,64,63", "endOffsets": "131750,133723,135947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\19bfc16374171ba7a26e435a1ff1fa3b\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "294,10117", "startColumns": "4,4", "startOffsets": "11766,694623", "endLines": "294,10119", "endColumns": "60,12", "endOffsets": "11822,694763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\004c9aef0bfe597de4245d1fd9384d65\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2279,2280", "startColumns": "4,4", "startOffsets": "143297,143379", "endColumns": "81,83", "endOffsets": "143374,143458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\cb2a86983ad6559594debb17aca21969\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2090,2111", "startColumns": "4,4", "startOffsets": "131876,133024", "endColumns": "53,66", "endOffsets": "131925,133086"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1379,2021,2022,2023,2039,2040,2041,2042", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "77225,127750,127802,127852,128788,128838,128886,128934", "endColumns": "46,51,49,49,49,47,47,47", "endOffsets": "77267,127797,127847,127897,128833,128881,128929,128977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\fbfe99676c41211a4bfd510557294d9b\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2117,2164", "startColumns": "4,4", "startOffsets": "133344,135724", "endColumns": "41,59", "endOffsets": "133381,135779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\0e9c4fffcc61ffb5e8382deeedde39b2\\transformed\\appcompat-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,849,850,851,852,853,854,855,856,857,858,863,864,924,925,926,927,931,932,933,934,936,937,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1186,1187,1188,1189,1190,1191,1192,1193,1198,1199,1201,1202,1203,1204,1206,1207,1208,1209,1210,1211,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1367,1368,1372,1373,1374,1375,1376,1377,1378,2031,2032,2033,2034,2035,2036,2037,2038,2080,2081,2082,2083,2089,2115,2116,2125,2159,2177,2178,2181,2182,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2571,2791,2792,2793,2794,2795,2840,2848,2849,2853,2857,2868,2873,2902,2909,2913,2917,2922,2926,2930,2934,2938,2942,2946,2952,2956,2962,2966,2972,2976,2981,2985,2988,2992,2998,3002,3008,3012,3018,3021,3025,3029,3033,3037,3041,3042,3043,3044,3047,3050,3053,3056,3060,3061,3062,3063,3104,3107,3109,3111,3113,3118,3119,3123,3129,3133,3134,3136,3148,3149,3153,3159,3163,3240,3241,3245,3272,3276,3277,3281,5083,5255,5281,5452,5478,5509,5517,5523,5539,5561,5566,5571,5581,5590,5599,5603,5610,5629,5636,5637,5646,5649,5652,5656,5660,5664,5667,5668,5673,5678,5688,5693,5700,5706,5707,5710,5714,5719,5721,5723,5726,5729,5731,5735,5738,5745,5748,5751,5755,5757,5761,5763,5765,5767,5771,5779,5787,5799,5805,5814,5817,5828,5831,5832,5837,5838,6338,6407,6481,6482,6492,6501,6657,6659,6663,6666,6669,6672,6675,6678,6681,6684,6688,6691,6694,6697,6701,6704,6708,6859,6860,6861,6862,6863,6864,6865,6866,6867,6868,6869,6870,6871,6872,6873,6874,6875,6876,6877,6878,6879,6881,6883,6884,6885,6886,6887,6888,6889,6890,6892,6893,6895,6896,6898,6900,6901,6903,6904,6905,6906,6907,6908,6910,6911,6912,6913,6914,7199,7201,7203,7205,7206,7207,7208,7209,7210,7211,7212,7213,7214,7215,7216,7217,7219,7220,7221,7222,7223,7224,7225,7227,7231,7414,7415,7416,7417,7418,7419,7423,7424,7425,7966,7968,7970,7972,7974,7976,7977,7978,7979,7981,7983,7985,7986,7987,7988,7989,7990,7991,7992,7993,7994,7995,7996,7999,8000,8001,8002,8004,8006,8007,8009,8010,8012,8014,8016,8017,8018,8019,8020,8021,8022,8023,8024,8025,8026,8027,8029,8030,8031,8032,8034,8035,8036,8037,8038,8040,8042,8044,8046,8047,8048,8049,8050,8051,8052,8053,8054,8055,8056,8057,8058,8059,8060", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12942,17487,17792,36391,38574,38629,39086,39150,39220,39281,39356,39432,39509,39747,39832,39914,39990,40066,40143,40221,40327,40433,40512,40841,40898,44919,44993,45068,45133,45346,45406,45467,45539,45664,45731,59160,59219,59278,59337,59396,59455,59509,59563,59616,59670,59724,59778,64386,64460,64539,64612,64686,64757,64829,64901,65183,65240,65353,65426,65500,65574,65696,65768,65841,65911,65982,66042,66756,66825,66894,66964,67038,67114,67178,67255,67331,67408,67473,67542,67619,67694,67763,67831,67908,67974,68035,68132,68197,68266,68365,68436,68495,68553,68610,68669,68733,68804,68876,68948,69020,69092,69159,69227,69295,69354,69417,69481,69571,69662,69722,69788,69855,69921,69991,70055,70108,70175,70236,70303,70416,70474,70537,70602,70667,70742,70815,70887,70931,70978,71024,71073,71134,71195,71256,71318,71382,71446,71510,71575,71638,71698,71759,71825,71884,71944,72006,72077,72137,76278,76364,76614,76704,76791,76879,76961,77044,77134,128335,128387,128445,128490,128556,128620,128677,128734,131382,131439,131487,131536,131842,133248,133295,133728,135513,136401,136465,136655,136715,141431,141505,141575,141653,141707,141777,141862,141910,141956,142017,142080,142146,142210,142281,142344,142409,142473,142534,142595,142647,142720,142794,142863,142938,143012,143086,143227,175475,192941,193019,193109,193197,193293,196048,196630,196719,196966,197247,197913,198198,200007,200484,200706,200928,201204,201431,201661,201891,202121,202351,202578,202997,203223,203648,203878,204306,204525,204808,205016,205147,205374,205800,206025,206452,206673,207098,207218,207494,207795,208119,208410,208724,208861,208992,209097,209339,209506,209710,209918,210189,210301,210413,210518,212611,212825,212971,213111,213197,213545,213633,213879,214297,214546,214628,214726,215383,215483,215735,216159,216414,222304,222393,222630,224654,224896,224998,225251,360820,371501,373017,383712,385240,386997,387623,388043,389304,390569,390825,391061,391608,392102,392707,392905,393485,394853,395228,395346,395884,396041,396237,396510,396766,396936,397077,397141,397506,397873,398549,398813,399151,399504,399598,399784,400090,400352,400477,400604,400843,401054,401173,401366,401543,401998,402179,402301,402560,402673,402860,402962,403069,403198,403473,403981,404477,405354,405648,406218,406367,407099,407271,407355,407691,407783,440087,445318,451033,451095,451673,452257,460455,460568,460797,460957,461109,461280,461446,461615,461782,461945,462188,462358,462531,462702,462976,463175,463380,472878,472962,473058,473154,473252,473352,473454,473556,473658,473760,473862,473962,474058,474170,474299,474422,474553,474684,474782,474896,474990,475130,475264,475360,475472,475572,475688,475784,475896,475996,476136,476272,476436,476566,476724,476874,477015,477159,477294,477406,477556,477684,477812,477948,478080,478210,478340,478452,496110,496256,496400,496538,496604,496694,496770,496874,496964,497066,497174,497282,497382,497462,497554,497652,497762,497814,497892,497998,498090,498194,498304,498426,498589,512297,512377,512477,512567,512677,512767,513008,513102,513208,553087,553187,553299,553413,553529,553645,553739,553853,553965,554067,554187,554309,554391,554495,554615,554741,554839,554933,555021,555133,555249,555371,555483,555658,555774,555860,555952,556064,556188,556255,556381,556449,556577,556721,556849,556918,557013,557128,557241,557340,557449,557560,557671,557772,557877,557977,558107,558198,558321,558415,558527,558613,558717,558813,558901,559019,559123,559227,559353,559441,559549,559649,559739,559849,559933,560035,560119,560173,560237,560343,560429,560539,560623", "endLines": "322,412,419,779,832,833,840,841,842,843,844,845,846,849,850,851,852,853,854,855,856,857,858,863,864,924,925,926,927,931,932,933,934,936,937,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1186,1187,1188,1189,1190,1191,1192,1193,1198,1199,1201,1202,1203,1204,1206,1207,1208,1209,1210,1211,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1367,1368,1372,1373,1374,1375,1376,1377,1378,2031,2032,2033,2034,2035,2036,2037,2038,2080,2081,2082,2083,2089,2115,2116,2125,2159,2177,2178,2181,2182,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2571,2791,2792,2793,2794,2795,2847,2848,2852,2856,2860,2872,2878,2908,2912,2916,2921,2925,2929,2933,2937,2941,2945,2951,2955,2961,2965,2971,2975,2980,2984,2987,2991,2997,3001,3007,3011,3017,3020,3024,3028,3032,3036,3040,3041,3042,3043,3046,3049,3052,3055,3059,3060,3061,3062,3063,3106,3108,3110,3112,3117,3118,3122,3128,3132,3133,3135,3147,3148,3152,3158,3162,3163,3240,3244,3271,3275,3276,3280,3308,5254,5280,5451,5477,5508,5516,5522,5538,5560,5565,5570,5580,5589,5598,5602,5609,5628,5635,5636,5645,5648,5651,5655,5659,5663,5666,5667,5672,5677,5687,5692,5699,5705,5706,5709,5713,5718,5720,5722,5725,5728,5730,5734,5737,5744,5747,5750,5754,5756,5760,5762,5764,5766,5770,5778,5786,5798,5804,5813,5816,5827,5830,5831,5836,5837,5842,6406,6476,6481,6491,6500,6501,6658,6662,6665,6668,6671,6674,6677,6680,6683,6687,6690,6693,6696,6700,6703,6707,6711,6859,6860,6861,6862,6863,6864,6865,6866,6867,6868,6869,6870,6871,6872,6873,6874,6875,6876,6877,6878,6880,6882,6883,6884,6885,6886,6887,6888,6889,6891,6892,6894,6895,6897,6899,6900,6902,6903,6904,6905,6906,6907,6909,6910,6911,6912,6913,6914,7200,7202,7204,7205,7206,7207,7208,7209,7210,7211,7212,7213,7214,7215,7216,7218,7219,7220,7221,7222,7223,7224,7226,7230,7234,7414,7415,7416,7417,7418,7422,7423,7424,7425,7967,7969,7971,7973,7975,7976,7977,7978,7980,7982,7984,7985,7986,7987,7988,7989,7990,7991,7992,7993,7994,7995,7998,7999,8000,8001,8003,8005,8006,8008,8009,8011,8013,8015,8016,8017,8018,8019,8020,8021,8022,8023,8024,8025,8026,8028,8029,8030,8031,8033,8034,8035,8036,8037,8039,8041,8043,8045,8046,8047,8048,8049,8050,8051,8052,8053,8054,8055,8056,8057,8058,8059,8060", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "12992,17527,17836,36427,38624,38686,39145,39215,39276,39351,39427,39504,39582,39827,39909,39985,40061,40138,40216,40322,40428,40507,40587,40893,40951,44988,45063,45128,45194,45401,45462,45534,45607,45726,45794,59214,59273,59332,59391,59450,59504,59558,59611,59665,59719,59773,59827,64455,64534,64607,64681,64752,64824,64896,64969,65235,65293,65421,65495,65569,65644,65763,65836,65906,65977,66037,66098,66820,66889,66959,67033,67109,67173,67250,67326,67403,67468,67537,67614,67689,67758,67826,67903,67969,68030,68127,68192,68261,68360,68431,68490,68548,68605,68664,68728,68799,68871,68943,69015,69087,69154,69222,69290,69349,69412,69476,69566,69657,69717,69783,69850,69916,69986,70050,70103,70170,70231,70298,70411,70469,70532,70597,70662,70737,70810,70882,70926,70973,71019,71068,71129,71190,71251,71313,71377,71441,71505,71570,71633,71693,71754,71820,71879,71939,72001,72072,72132,72200,76359,76446,76699,76786,76874,76956,77039,77129,77220,128382,128440,128485,128551,128615,128672,128729,128783,131434,131482,131531,131582,131871,133290,133339,133769,135540,136460,136522,136710,136767,141500,141570,141648,141702,141772,141857,141905,141951,142012,142075,142141,142205,142276,142339,142404,142468,142529,142590,142642,142715,142789,142858,142933,143007,143081,143222,143292,175523,193014,193104,193192,193288,193378,196625,196714,196961,197242,197494,198193,198586,200479,200701,200923,201199,201426,201656,201886,202116,202346,202573,202992,203218,203643,203873,204301,204520,204803,205011,205142,205369,205795,206020,206447,206668,207093,207213,207489,207790,208114,208405,208719,208856,208987,209092,209334,209501,209705,209913,210184,210296,210408,210513,210630,212820,212966,213106,213192,213540,213628,213874,214292,214541,214623,214721,215378,215478,215730,216154,216409,216503,222388,222625,224649,224891,224993,225246,227402,371496,373012,383707,385235,386992,387618,388038,389299,390564,390820,391056,391603,392097,392702,392900,393480,394848,395223,395341,395879,396036,396232,396505,396761,396931,397072,397136,397501,397868,398544,398808,399146,399499,399593,399779,400085,400347,400472,400599,400838,401049,401168,401361,401538,401993,402174,402296,402555,402668,402855,402957,403064,403193,403468,403976,404472,405349,405643,406213,406362,407094,407266,407350,407686,407778,408056,445313,450684,451090,451668,452252,452343,460563,460792,460952,461104,461275,461441,461610,461777,461940,462183,462353,462526,462697,462971,463170,463375,463705,472957,473053,473149,473247,473347,473449,473551,473653,473755,473857,473957,474053,474165,474294,474417,474548,474679,474777,474891,474985,475125,475259,475355,475467,475567,475683,475779,475891,475991,476131,476267,476431,476561,476719,476869,477010,477154,477289,477401,477551,477679,477807,477943,478075,478205,478335,478447,478587,496251,496395,496533,496599,496689,496765,496869,496959,497061,497169,497277,497377,497457,497549,497647,497757,497809,497887,497993,498085,498189,498299,498421,498584,498741,512372,512472,512562,512672,512762,513003,513097,513203,513295,553182,553294,553408,553524,553640,553734,553848,553960,554062,554182,554304,554386,554490,554610,554736,554834,554928,555016,555128,555244,555366,555478,555653,555769,555855,555947,556059,556183,556250,556376,556444,556572,556716,556844,556913,557008,557123,557236,557335,557444,557555,557666,557767,557872,557972,558102,558193,558316,558410,558522,558608,558712,558808,558896,559014,559118,559222,559348,559436,559544,559644,559734,559844,559928,560030,560114,560168,560232,560338,560424,560534,560618,560738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d304b09fc6c5965909d0ef57a0fa0ff6\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "659,847,848,865,866,1181,1182,1311,1312,1313,1314,1315,1316,1317,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2092,2093,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2150,2247,2295,2296,2297,2298,2299,2300,2301,2639,6915,6916,6920,6921,6925,8061,8062", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30242,39587,39659,40956,41021,64084,64153,72638,72708,72776,72848,72918,72979,73053,126772,126833,126894,126956,127020,127082,127143,127211,127311,127371,127437,127510,127579,127636,127688,129259,129331,129407,129472,129531,129590,129650,129710,129770,129830,129890,129950,130010,130070,130130,130190,130249,130309,130369,130429,130489,130549,130609,130669,130729,130789,130849,130908,130968,131028,131087,131146,131205,131264,131323,131996,132031,133774,133829,133892,133947,134005,134061,134119,134180,134243,134300,134351,134409,134459,134520,134577,134643,134677,135059,141041,145203,145270,145342,145411,145480,145554,145626,180279,478592,478709,478910,479020,479221,560743,560815", "endLines": "659,847,848,865,866,1181,1182,1311,1312,1313,1314,1315,1316,1317,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2092,2093,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2150,2247,2295,2296,2297,2298,2299,2300,2301,2639,6915,6919,6920,6924,6925,8061,8062", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "30297,39654,39742,41016,41082,64148,64211,72703,72771,72843,72913,72974,73048,73121,126828,126889,126951,127015,127077,127138,127206,127306,127366,127432,127505,127574,127631,127683,127745,129326,129402,129467,129526,129585,129645,129705,129765,129825,129885,129945,130005,130065,130125,130185,130244,130304,130364,130424,130484,130544,130604,130664,130724,130784,130844,130903,130963,131023,131082,131141,131200,131259,131318,131377,132026,132061,133824,133887,133942,134000,134056,134114,134175,134238,134295,134346,134404,134454,134515,134572,134638,134672,134707,135089,141106,145265,145337,145406,145475,145549,145621,145709,180345,478704,478905,479015,479216,479345,560810,560877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\4578899047b14a577ffdc3b874f24398\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2185,2314", "startColumns": "4,4", "startOffsets": "136907,147131", "endColumns": "67,166", "endOffsets": "136970,147293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\97daf22df7e5c6e3b5afccf9cf12b867\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,868,869,870,871,1307,1308,1309,2861,6236,6238,6241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7641,41137,41198,41260,41322,72413,72472,72529,197499,433345,433409,433535", "endLines": "220,868,869,870,871,1307,1308,1309,2867,6237,6240,6243", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "7688,41193,41255,41317,41381,72467,72524,72578,197908,433404,433530,433658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\700cabc0e517e16b3b1a2efd32cecc35\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "727,732,733,734,2084", "startColumns": "4,4,4,4,4", "startOffsets": "33364,33543,33603,33655,131587", "endLines": "731,732,733,734,2084", "endColumns": "11,59,51,44,59", "endOffsets": "33538,33598,33650,33695,131642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c5c1f58a58ca000404c5b280db793ba2\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2087,2088,2110,2119,2120,2151,2152,2153,2154,2155,2156,2157,2158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "131755,131795,132981,133429,133484,135094,135139,135193,135249,135301,135353,135402,135463", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "131790,131837,133019,133479,133526,135134,135188,135244,135296,135348,135397,135458,135508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b67b39daef2398884ed6af353040af9d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2281", "startColumns": "4", "startOffsets": "143463", "endColumns": "82", "endOffsets": "143541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\07e10290a8de075a2af92b4127d3fcd4\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "187,194,196,202,203,211,213,214,221,227,229,230,231,232,233,290,291,292,293,295,299,300,301,304,314,324,352,353,358,359,364,369,370,371,376,377,382,383,388,389,390,396,397,398,403,409,410,428,429,435,436,437,438,441,444,447,448,451,454,455,456,457,458,461,464,465,466,467,473,478,481,484,485,486,491,492,493,496,499,500,503,506,509,512,513,514,517,520,521,526,527,533,538,541,544,545,546,547,548,549,550,551,552,553,554,555,571,652,653,654,655,660,667,675,676,677,680,685,687,695,696,725,740,777,778,782,783,794,795,796,802,805,811,815,816,817,818,819,828,2097,2161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6151,6435,6547,6782,6843,7134,7241,7291,7693,8000,8120,8175,8235,8300,8359,11545,11597,11658,11720,11827,11960,12012,12062,12223,12630,13053,14995,15054,15251,15308,15503,15684,15738,15795,15987,16045,16241,16297,16491,16548,16599,16821,16873,16928,17118,17334,17384,18285,18341,18547,18608,18668,18738,18871,19002,19130,19198,19327,19453,19515,19578,19646,19713,19836,19961,20028,20093,20158,20447,20628,20749,20870,20936,21003,21213,21282,21348,21473,21599,21666,21792,21919,22044,22171,22227,22292,22418,22541,22606,22814,22881,23169,23349,23469,23589,23654,23716,23778,23842,23904,23963,24023,24084,24145,24204,24264,24924,29867,29918,29967,30015,30302,30594,30902,30949,31009,31115,31295,31407,31742,31796,33268,33951,36281,36332,36541,36593,37029,37088,37142,37380,37558,37760,37899,37945,38000,38045,38089,38437,132215,135593", "endLines": "193,194,200,202,210,211,213,214,221,227,229,230,231,232,233,290,291,292,293,298,299,300,301,313,321,324,352,357,358,363,368,369,370,375,376,381,382,387,388,389,395,396,397,402,408,409,410,428,434,435,436,437,440,443,446,447,450,453,454,455,456,457,460,463,464,465,466,472,477,480,483,484,485,490,491,492,495,498,499,502,505,508,511,512,513,516,519,520,525,526,532,537,540,543,544,545,546,547,548,549,550,551,552,553,554,570,576,652,653,654,655,666,674,675,676,679,684,685,694,695,696,725,740,777,778,782,791,794,795,801,802,810,814,815,816,817,818,827,831,2097,2161", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "6430,6486,6728,6838,7129,7181,7286,7339,7736,8046,8170,8230,8295,8354,8416,11592,11653,11715,11761,11955,12007,12057,12108,12625,12937,13093,15049,15246,15303,15498,15679,15733,15790,15982,16040,16236,16292,16486,16543,16594,16816,16868,16923,17113,17329,17379,17431,18336,18542,18603,18663,18733,18866,18997,19125,19193,19322,19448,19510,19573,19641,19708,19831,19956,20023,20088,20153,20442,20623,20744,20865,20931,20998,21208,21277,21343,21468,21594,21661,21787,21914,22039,22166,22222,22287,22413,22536,22601,22809,22876,23164,23344,23464,23584,23649,23711,23773,23837,23899,23958,24018,24079,24140,24199,24259,24919,25170,29913,29962,30010,30068,30589,30897,30944,31004,31110,31290,31344,31737,31791,31847,33309,33993,36327,36386,36588,36918,37083,37137,37375,37430,37755,37894,37940,37995,38040,38084,38432,38569,132251,135633"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\build\\generated\\res\\processFdroidDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2335,2358,2359,2360,2361,2363,2543", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "149219,151053,151135,151239,151348,151540,173223", "endColumns": "143,81,103,108,119,106,75", "endOffsets": "149358,151130,151234,151343,151463,151642,173294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\59e516bb99fd3e6425d2c9627b07a4fa\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7406", "startColumns": "4", "startOffsets": "511887", "endLines": "7413", "endColumns": "8", "endOffsets": "512292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\76702c707d43210cfa7dee4ec2c1641f\\transformed\\quickie-foss-1.14.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1194,1195,1196,1197,2545,2546,6653", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "64974,65032,65079,65135,173377,173438,460204", "endLines": "1194,1195,1196,1197,2545,2546,6656", "endColumns": "57,46,55,47,60,61,10", "endOffsets": "65027,65074,65130,65178,173433,173495,460450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ebcc5988bd502c901a17c4d3fc1eb859\\transformed\\zxing-android-embedded-4.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,2168,2169,2170,2171,2172,2173,2174,2175,2176,2285,2336,2370,2371,2372,2373,2374,2375,2376,2377,2378,2787,2788,2789,2790,10120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66150,66222,66283,66347,66412,66477,66531,66585,66639,66698,135952,135999,136048,136096,136138,136187,136239,136297,136347,143767,149363,152237,152334,152451,152547,152708,152821,152907,153054,153153,192578,192637,192684,192828,694768", "endLines": "1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,2168,2169,2170,2171,2172,2173,2174,2175,2176,2285,2336,2370,2371,2372,2373,2374,2375,2376,2377,2378,2787,2788,2789,2790,10121", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10", "endOffsets": "66217,66278,66342,66407,66472,66526,66580,66634,66693,66751,135994,136043,136091,136133,136182,136234,136292,136342,136396,143828,149428,152329,152446,152542,152703,152816,152902,153049,153148,153295,192632,192679,192823,192936,694863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f68ab7854e90816622d4e41b6bdf2b2c\\transformed\\camera-view-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "415,699", "startColumns": "4,4", "startOffsets": "17640,31954", "endLines": "418,706", "endColumns": "11,11", "endOffsets": "17787,32256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\65324aff12f16468e03fc5b512ae58fc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "834,1183,2024,2025,2026,2027,2028,2029,2030,2112,2113,2114,2244,2245,2333,2349,2527,2540,2649,2784,2785,6216,6502,6505,6511,6517,6520,6526,6530,6533,6540,6546,6549,6555,6560,6565,6572,6574,6580,6586,6594,6599,6606,6611,6617,6621,6628,6632,6638,6644,6647,6651,6652", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "38691,64216,127902,127966,128021,128089,128156,128221,128278,133091,133139,133187,140858,140921,149113,150260,171758,173083,181019,192416,192466,431779,452348,452453,452698,453036,453182,453522,453734,453897,454304,454642,454765,455104,455343,455600,455971,456031,456369,456655,457104,457396,457784,458089,458433,458678,459008,459215,459483,459756,459900,460101,460148", "endLines": "834,1183,2024,2025,2026,2027,2028,2029,2030,2112,2113,2114,2244,2245,2333,2349,2527,2542,2649,2784,2785,6232,6504,6510,6516,6519,6525,6529,6532,6539,6545,6548,6554,6559,6564,6571,6573,6579,6585,6593,6598,6605,6610,6616,6620,6627,6631,6637,6643,6646,6650,6651,6652", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "38759,64280,127961,128016,128084,128151,128216,128273,128330,133134,133182,133243,140916,140979,149146,150312,171797,173218,181153,192461,192509,433212,452448,452693,453031,453177,453517,453729,453892,454299,454637,454760,455099,455338,455595,455966,456026,456364,456650,457099,457391,457779,458084,458428,458673,459003,459210,459478,459751,459895,460096,460143,460199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3671adc343b59afebe99d3ec6265d6d7\\transformed\\Toasty-1.5.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "888,930,940,1180,1205,1212,2763", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "42445,45301,45911,64038,65649,66103,190833", "endColumns": "50,44,43,45,46,46,55", "endOffsets": "42491,45341,45950,64079,65691,66145,190884"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,7,14,19,25,34,38,42,50,55,64,69,78,89,94,117,130,139,152,156,166,176,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,301,517,689,878,1163,1298,1445,1702,1845,2129,2297,2557,2919,3102,3896,4404,4681,5058,5182,5484,5796,5953", "endLines": "6,13,18,24,33,37,41,45,54,58,68,77,82,93,100,129,138,151,155,160,170,180,186", "endColumns": "19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19,19", "endOffsets": "296,512,684,873,1158,1293,1440,1581,1840,1975,2292,2552,2714,3097,3324,4399,4676,5053,5177,5331,5635,5948,6146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\d70395873c50a004c47266b87418baa0\\transformed\\coil-core-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2085", "startColumns": "4", "startOffsets": "131647", "endColumns": "50", "endOffsets": "131693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\8db806c88c82d97fba3e067706ad47ec\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "835,836,837,839", "startColumns": "4,4,4,4", "startOffsets": "38764,38829,38899,39025", "endColumns": "64,69,63,60", "endOffsets": "38824,38894,38958,39081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\aaec5b52f0b87a27b87b977c567045d7\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2165", "startColumns": "4", "startOffsets": "135784", "endColumns": "53", "endOffsets": "135833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c588be67285252bb290c6b2c2549687a\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2166", "startColumns": "4", "startOffsets": "135838", "endColumns": "49", "endOffsets": "135883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\f7eb1da5e42044f662b8e59e06978dfd\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "7257", "startColumns": "4", "startOffsets": "499888", "endLines": "7260", "endColumns": "12", "endOffsets": "500106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\43f80c422e65d725a8b34fc24966139b\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "323,326,1318", "startColumns": "4,4,4", "startOffsets": "12997,13161,73126", "endColumns": "55,47,51", "endOffsets": "13048,13204,73173"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6856", "startColumns": "4", "startOffsets": "472746", "endLines": "6858", "endColumns": "12", "endOffsets": "472873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\ba00bb078231f50d815e7c2c79fbd77c\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "859,860,861,862,1305,1306,2334,2353,2354,2355", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40592,40650,40716,40779,72270,72341,149151,150720,150787,150866", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "40645,40711,40774,40836,72336,72408,149214,150782,150861,150930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\17d5b14458b73464e26d2134afde20b1\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "880,881,882,883,884,885,886,887,2306,2307,2308,2309,2310,2311,2312,2313,2315,2316,2317,2318,2319,2320,2321,2322,2323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41774,41864,41944,42034,42124,42204,42285,42365,146091,146196,146377,146502,146609,146789,146912,147028,147298,147486,147591,147772,147897,148072,148220,148283,148345", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "41859,41939,42029,42119,42199,42280,42360,42440,146191,146372,146497,146604,146784,146907,147023,147126,147481,147586,147767,147892,148067,148215,148278,148340,148419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\3fac676b4e1c0ee2b6233dfa300c1ec0\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2163", "startColumns": "4", "startOffsets": "135681", "endColumns": "42", "endOffsets": "135719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\881daab11df0f8fb6be0bbb2034fa98e\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,717,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1304,1310,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2043,2044,2094,2095,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2118,2121,2122,2123,2143,2144,2145,2146,2147,2148,2149,2160,2179,2180,2183,2184,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2246,2248,2287,2289,2290,2291,2292,2293,2294,2302,2303,2304,2305,2341,2345,2350,2351,2352,2365,2366,2369,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2469,2472,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2533,2534,2535,2536,2537,2572,2575,2576,2636,2637,2796,2800,2804,2808,2812,2813,2879,2887,2894,3064,3067,3077,3086,3095,3164,3165,3166,3167,3173,3174,3175,3176,3177,3178,3184,3185,3186,3187,3188,3193,3194,3198,3199,3205,3209,3210,3211,3212,3222,3223,3224,3228,3229,3235,3239,3309,3312,3313,3318,3319,3322,3323,3324,3325,3589,3596,3857,3863,4127,4134,4395,4401,4464,4546,4598,4680,4742,4824,4888,4940,5022,5030,5036,5047,5051,5055,5068,5843,5859,5866,5872,5889,5902,5922,5939,5948,5953,5960,5980,5993,6010,6016,6022,6029,6033,6039,6053,6056,6066,6067,6068,6116,6120,6124,6128,6129,6130,6133,6149,6156,6170,6215,6244,6250,6254,6258,6263,6270,6276,6277,6280,6284,6289,6302,6306,6311,6316,6321,6324,6327,6330,6334,6477,6478,6479,6480,6712,6713,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6726,6727,6728,6729,6730,6734,6738,6742,6746,6750,6754,6758,6759,6760,6761,6762,6763,6764,6765,6769,6773,6774,6778,6779,6782,6786,6789,6792,6795,6799,6802,6805,6809,6813,6817,6821,6824,6825,6826,6827,6830,6834,6837,6840,6843,6846,6849,6852,6926,6929,6930,6933,6936,6937,6940,6941,6942,6946,6947,6952,6959,6966,6973,6980,6987,6994,7001,7008,7015,7024,7033,7042,7049,7058,7067,7070,7073,7074,7075,7076,7077,7078,7079,7080,7081,7082,7083,7084,7085,7089,7094,7099,7102,7103,7104,7105,7106,7114,7122,7123,7131,7135,7143,7151,7159,7167,7175,7176,7184,7192,7193,7196,7235,7237,7242,7244,7249,7253,7261,7262,7263,7264,7268,7272,7273,7277,7278,7279,7280,7281,7282,7283,7284,7285,7286,7287,7291,7292,7293,7294,7298,7299,7300,7301,7305,7309,7310,7314,7315,7316,7321,7322,7323,7324,7325,7326,7327,7328,7329,7330,7331,7332,7333,7334,7335,7336,7337,7338,7339,7340,7341,7345,7346,7347,7353,7354,7358,7360,7361,7366,7367,7368,7369,7370,7371,7375,7376,7377,7383,7384,7388,7390,7394,7398,7402,7426,7427,7428,7429,7432,7435,7438,7441,7444,7449,7453,7456,7457,7462,7466,7471,7477,7483,7488,7492,7497,7501,7505,7546,7547,7548,7549,7550,7554,7555,7556,7557,7561,7565,7569,7573,7577,7581,7585,7589,7595,7596,7637,7651,7656,7682,7689,7692,7703,7708,7711,7714,7769,7775,7776,7779,7782,7785,7788,7791,7794,7797,7801,7804,7805,7806,7814,7822,7825,7830,7835,7840,7845,7849,7853,7854,7862,7863,7864,7865,7866,7874,7879,7884,7885,7886,7887,7912,7918,7923,7926,7930,7933,7937,7947,7950,7955,7958,7962,8063,8071,8085,8098,8102,8117,8128,8131,8142,8147,8151,8186,8187,8188,8200,8208,8216,8224,8232,8252,8255,8282,8287,8307,8310,8313,8320,8333,8342,8345,8365,8375,8379,8383,8396,8400,8404,8408,8414,8418,8435,8443,8447,8451,8455,8458,8462,8466,8470,8480,8487,8494,8498,8524,8534,8559,8568,8588,8598,8602,8612,8637,8647,8650,8657,8664,8671,8672,8673,8674,8675,8682,8686,8692,8698,8699,8712,8713,8714,8717,8720,8723,8726,8729,8732,8735,8738,8741,8744,8747,8750,8753,8756,8759,8762,8765,8768,8771,8774,8777,8778,8786,8794,8795,8808,8818,8822,8827,8832,8836,8839,8843,8847,8850,8854,8857,8861,8866,8871,8874,8881,8885,8889,8898,8903,8908,8909,8913,8916,8920,8933,8938,8946,8950,8954,8971,8975,8980,8998,9005,9009,9039,9042,9045,9048,9051,9054,9057,9076,9082,9090,9097,9109,9117,9122,9130,9134,9152,9159,9175,9179,9187,9190,9195,9196,9197,9198,9202,9206,9210,9214,9249,9252,9256,9260,9294,9297,9301,9305,9314,9320,9323,9333,9337,9338,9345,9349,9356,9357,9358,9361,9366,9371,9372,9376,9391,9410,9414,9415,9427,9437,9438,9450,9455,9479,9482,9488,9491,9500,9508,9512,9515,9518,9521,9525,9528,9545,9549,9552,9567,9570,9578,9583,9590,9595,9596,9601,9602,9608,9614,9620,9652,9663,9680,9687,9691,9694,9707,9716,9720,9725,9729,9733,9737,9741,9745,9749,9753,9758,9761,9773,9778,9787,9790,9797,9798,9802,9811,9817,9821,9822,9826,9847,9853,9857,9861,9862,9880,9881,9882,9883,9884,9889,9892,9893,9899,9900,9912,9924,9931,9932,9937,9942,9943,9947,9961,9966,9972,9978,9984,9989,9995,10001,10002,10008,10023,10028,10037,10046,10049,10063,10068,10079,10083,10092,10101,10102,10109,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6491,6733,7186,7344,7400,7460,7521,7586,7741,7791,7841,7894,7952,8051,8421,8469,8540,8612,8684,8757,8824,8873,8927,8964,9015,9075,9122,9178,9227,9285,9339,9400,9456,9507,9567,9623,9686,9735,9791,9847,9897,9956,10011,10073,10120,10174,10230,10282,10337,10391,10445,10499,10548,10606,10660,10717,10773,10820,10873,10929,10989,11052,11111,11173,11223,11277,11331,11379,11436,11489,12113,12167,13098,13209,13271,13327,13387,13440,13501,13580,13661,13733,13812,13892,13968,14046,14115,14191,14268,14339,14412,14488,14566,14635,14711,14788,14852,14923,17436,17532,17585,17841,17908,17961,18013,18063,18121,18186,18234,25175,25242,25308,25366,25435,25493,25562,25632,25705,25779,25847,25914,25984,26050,26123,26183,26259,26319,26379,26454,26522,26588,26656,26716,26775,26832,26898,26960,27017,27085,27158,27228,27290,27351,27419,27481,27551,27620,27676,27735,27797,27859,27926,27983,28044,28105,28166,28227,28283,28339,28395,28451,28509,28567,28625,28683,28740,28797,28854,28911,28970,29029,29087,29170,29253,29326,29380,29449,29505,29586,29667,29738,30073,30126,30184,31349,31908,32261,32321,32375,32445,32515,32580,32646,32711,32779,32848,32916,33046,33099,33158,33216,33314,33700,33752,33798,33848,33904,33998,34056,34114,34176,34239,34301,34360,34420,34485,34551,34616,34678,34740,34802,34864,34926,34988,35054,35121,35187,35250,35314,35377,35445,35506,35568,35630,35693,35757,35820,35884,35962,36021,36087,36167,36228,36432,36490,36923,36968,37435,37499,38963,42496,42570,42641,42707,42781,42850,42921,42994,43065,43133,43206,43282,43352,43430,43498,43564,43625,43694,43758,43824,43892,43958,44021,44089,44160,44225,44298,44361,44442,44506,44572,44642,44712,44782,44852,46067,46124,46182,46241,46301,46360,46419,46478,46537,46596,46655,46714,46773,46832,46891,46951,47012,47074,47135,47196,47257,47318,47379,47440,47500,47561,47622,47682,47743,47804,47865,47926,47987,48048,48109,48170,48231,48292,48353,48421,48490,48560,48629,48698,48767,48836,48905,48974,49043,49112,49181,49250,49310,49371,49433,49494,49555,49616,49677,49738,49799,49860,49921,49982,50043,50105,50168,50232,50295,50358,50421,50484,50547,50610,50673,50736,50799,50862,50923,50985,51048,51110,51172,51234,51296,51358,51420,51482,51544,51606,51668,51725,51811,51891,51981,52076,52168,52260,52350,52433,52526,52613,52710,52801,52902,52989,53092,53181,53280,53372,53472,53556,53650,53738,53836,53919,54010,54104,54203,54305,54403,54503,54590,54690,54776,54872,54960,55041,55132,55228,55321,55414,55505,55590,55684,55773,55871,55964,56066,56154,56258,56349,56449,56542,56643,56728,56823,56912,57011,57096,57188,57283,57383,57486,57585,57688,57777,57878,57965,58062,58150,58246,58338,58438,58528,58626,58711,58800,58889,58982,59069,59832,59898,59974,60043,60122,60195,60275,60355,60432,60500,60578,60654,60725,60806,60879,60962,61037,61122,61195,61276,61357,61431,61515,61585,61663,61733,61813,61891,61963,62045,62115,62192,62272,62357,62445,62529,62616,62690,62768,62846,62917,62998,63089,63172,63268,63366,63473,63538,63604,63657,63733,63799,63886,63962,72205,72583,73178,73232,73311,73389,73462,73527,73590,73656,73727,73798,73868,73930,73999,74065,74125,74192,74259,74315,74366,74419,74471,74525,74596,74659,74718,74780,74839,74912,74979,75049,75109,75172,75247,75319,75415,75486,75542,75613,75670,75727,75793,75857,75928,75985,76038,76101,76153,76211,77495,77564,77630,77689,77772,77831,77888,77955,78025,78099,78161,78230,78300,78399,78496,78595,78681,78767,78848,78923,79012,79103,79187,79246,79292,79358,79415,79482,79539,79621,79686,79752,79875,79959,80080,80145,80207,80305,80379,80462,80551,80615,80694,80768,80830,80926,80991,81050,81106,81162,81222,81329,81376,81436,81497,81561,81622,81682,81740,81783,81832,81884,81935,81987,82036,82085,82150,82216,82276,82337,82393,82452,82501,82549,82607,82664,82766,82823,82898,82946,82997,83059,83124,83176,83250,83313,83376,83444,83494,83556,83616,83673,83733,83782,83850,83956,84058,84127,84198,84254,84303,84403,84474,84584,84675,84757,84855,84911,85012,85122,85221,85284,85390,85467,85579,85706,85818,85945,86015,86129,86260,86357,86425,86543,86646,86764,86825,86899,86966,87071,87193,87267,87334,87444,87543,87616,87713,87835,87953,88071,88132,88254,88371,88439,88545,88647,88727,88798,88894,88961,89035,89109,89195,89285,89363,89440,89540,89611,89732,89853,89917,90042,90116,90240,90364,90431,90540,90668,90780,90859,90937,91038,91109,91231,91353,91418,91544,91656,91762,91830,91929,92033,92096,92162,92246,92359,92472,92590,92668,92740,92876,93012,93097,93237,93375,93513,93655,93737,93823,93900,93973,94082,94193,94321,94449,94581,94711,94841,94975,95064,95126,95222,95289,95406,95527,95624,95706,95793,95880,96011,96142,96277,96354,96431,96542,96656,96730,96839,96951,97018,97091,97156,97258,97354,97458,97526,97591,97685,97757,97867,97973,98046,98137,98239,98342,98437,98544,98649,98771,98893,99019,99078,99136,99260,99384,99512,99630,99748,99870,99956,100053,100187,100321,100401,100539,100671,100803,100939,101014,101090,101193,101267,101380,101461,101518,101579,101638,101698,101756,101817,101875,101925,101974,102041,102100,102159,102208,102279,102363,102433,102504,102584,102653,102716,102784,102850,102918,102983,103049,103126,103204,103310,103416,103512,103641,103730,103857,103923,103993,104079,104145,104228,104302,104400,104496,104592,104690,104799,104894,104983,105045,105105,105170,105227,105308,105362,105419,105516,105626,105687,105802,105923,106018,106110,106203,106259,106318,106367,106459,106508,106562,106616,106670,106724,106778,106833,106943,107053,107161,107271,107381,107491,107601,107709,107815,107919,108023,108127,108222,108317,108410,108503,108607,108713,108817,108921,109014,109107,109200,109293,109401,109507,109613,109719,109816,109911,110006,110101,110207,110313,110419,110525,110623,110718,110814,110911,110976,111080,111138,111202,111263,111325,111385,111450,111512,111580,111638,111701,111764,111831,111906,111979,112045,112097,112150,112202,112259,112343,112438,112523,112604,112684,112761,112840,112917,112991,113065,113136,113216,113288,113363,113428,113489,113549,113624,113698,113771,113841,113913,113983,114056,114120,114190,114236,114305,114357,114442,114525,114582,114648,114715,114781,114862,114937,114993,115046,115107,115165,115215,115264,115313,115362,115424,115476,115521,115602,115653,115707,115760,115814,115865,115914,115980,116031,116092,116153,116215,116265,116306,116383,116442,116501,116560,116621,116677,116733,116800,116861,116926,116981,117046,117115,117183,117261,117330,117390,117461,117535,117600,117672,117742,117809,117893,117962,118029,118099,118162,118229,118297,118380,118459,118549,118626,118694,118761,118839,118896,118953,119021,119087,119143,119203,119262,119316,119366,119416,119464,119526,119577,119650,119730,119810,119874,119941,120012,120070,120131,120197,120256,120323,120383,120443,120506,120574,120635,120702,120780,120850,120899,120956,121025,121086,121174,121262,121350,121438,121525,121612,121699,121786,121844,121918,121988,122044,122115,122180,122242,122317,122390,122480,122546,122612,122673,122737,122799,122857,122928,123011,123070,123141,123207,123272,123333,123392,123463,123529,123594,123677,123753,123828,123909,123969,124038,124108,124177,124232,124288,124344,124405,124463,124519,124578,124632,124687,124749,124806,124900,124969,125070,125121,125191,125254,125310,125368,125427,125481,125567,125651,125721,125790,125860,125975,126096,126163,126230,126305,126372,126431,126485,126539,126593,126646,126698,128982,129119,132066,132115,132165,132256,132304,132360,132418,132480,132535,132593,132664,132728,132787,132849,132915,133386,133531,133575,133620,134712,134763,134810,134855,134906,134957,135008,135545,136527,136593,136772,136835,136975,137032,137086,137141,137199,137254,137313,137369,137438,137507,137576,137646,137709,137772,137835,137898,137963,138028,138093,138158,138221,138285,138349,138413,138464,138542,138620,138691,138763,138836,138908,138974,139040,139108,139176,139242,139309,139383,139446,139503,139563,139628,139695,139760,139817,139878,139936,140040,140150,140259,140363,140441,140506,140573,140639,140709,140756,140808,140984,141111,143884,144114,144245,144429,144607,144845,145034,145714,145812,145927,146012,149720,149947,150317,150406,150563,151697,151850,152178,153820,154007,154103,154193,154289,154379,154545,154668,154791,154961,155067,155182,155297,155399,155505,155622,155737,155819,155992,156160,156308,156467,156622,156795,156912,157029,157197,157309,157423,157595,157771,157929,158062,158174,158320,158472,158604,158747,161144,161322,161458,161554,161690,161785,161952,162045,162137,162324,162480,162658,162822,163004,163321,163503,163685,163875,164107,164297,164474,164636,164793,164903,165086,165223,165427,165611,165795,165955,166113,166297,166524,166727,166898,167118,167340,167495,167695,167879,167982,168172,168313,168478,168649,168849,169053,169255,169420,169625,169824,170023,170220,170311,170460,170610,170694,170843,170988,171140,171281,171447,172140,172218,172519,172685,172840,175528,175686,175850,179863,180086,193383,193660,193932,194210,194455,194517,198591,199042,199498,210635,210783,211297,211734,212168,216508,216593,216714,216813,217218,217315,217432,217519,217642,217743,218149,218248,218367,218460,218567,218910,219017,219262,219383,219792,220040,220140,220245,220364,220873,221020,221139,221390,221523,221938,222192,227407,227654,227779,228187,228308,228536,228657,228790,228937,249659,250151,270622,271046,291813,292307,312823,313249,318090,323507,327598,333029,337771,343148,347132,351124,356515,357062,357495,358251,358481,358724,359891,408061,408965,409549,410022,411452,412196,413389,414443,414921,415214,415597,417112,417877,419020,419461,419902,420498,420772,421183,422199,422377,423130,423267,423358,425552,425818,426140,426350,426459,426578,426762,427880,428350,429101,431684,433663,434039,434267,434523,434782,435358,435712,435834,435973,436265,436525,437453,437739,438142,438544,438887,439099,439300,439513,439802,450689,450762,450849,450934,463710,463822,463928,464051,464183,464306,464436,464560,464693,464824,464949,465066,465186,465318,465446,465560,465678,465791,465912,466100,466287,466468,466651,466835,467000,467182,467302,467422,467530,467640,467752,467860,467970,468135,468301,468453,468618,468719,468839,469010,469171,469334,469495,469662,469781,469898,470078,470260,470441,470624,470779,470924,471046,471181,471344,471537,471663,471815,471957,472127,472283,472455,479350,479545,479637,479810,479972,480067,480236,480330,480419,480662,480751,481044,481460,481880,482301,482727,483144,483560,483977,484395,484809,485279,485752,486224,486635,487106,487578,487768,487974,488080,488188,488294,488406,488520,488632,488746,488862,488976,489084,489194,489302,489564,489943,490347,490494,490602,490712,490820,490934,491343,491757,491873,492291,492532,492962,493397,493807,494229,494639,494761,495170,495586,495708,495926,498746,498814,499158,499238,499594,499744,500111,500187,500299,500389,500651,500916,501024,501176,501284,501360,501472,501562,501664,501772,501880,501980,502088,502173,502339,502443,502571,502658,502825,502903,503017,503109,503373,503640,503750,503903,504013,504097,504486,504584,504692,504786,504916,505024,505146,505282,505390,505510,505644,505766,505894,506036,506162,506302,506428,506546,506678,506776,506886,507186,507298,507416,507880,507996,508299,508425,508521,508922,509032,509156,509294,509404,509526,509838,509962,510092,510568,510696,511011,511149,511311,511527,511683,513300,513368,513452,513556,513759,513948,514149,514342,514547,514860,515072,515238,515354,515600,515816,516129,516555,517017,517254,517406,517666,517810,517952,521184,521298,521418,521534,521628,521949,522048,522166,522267,522546,522831,523110,523392,523645,523904,524157,524413,524837,524913,528163,529518,529962,531816,532391,532599,533609,533989,534155,534296,539316,539742,539854,539989,540142,540339,540510,540693,540868,541055,541327,541485,541569,541673,542160,542716,542874,543093,543324,543547,543782,544004,544270,544408,545007,545121,545259,545371,545495,546066,546561,547107,547252,547345,547437,549364,549934,550232,550421,550627,550820,551030,551914,552059,552451,552609,552826,560882,561314,562189,562809,563006,563954,564719,564842,565615,565836,566036,568013,568113,568203,568889,569642,570407,571170,571945,573158,573323,574936,575257,576320,576530,576700,577270,578165,578798,578964,580450,581066,581302,581523,582481,582746,583011,583258,583672,583908,585193,585642,585829,586078,586320,586496,586737,586970,587195,587790,588265,588789,589050,590401,590876,592102,592572,593620,594072,594316,594773,596018,596501,596651,597206,597658,598058,598211,598356,598499,598569,598997,599285,599789,600298,600414,601316,601438,601550,601727,601993,602263,602529,602797,603053,603313,603569,603827,604079,604335,604587,604841,605073,605309,605561,605817,606069,606323,606555,606789,606901,607553,608008,608132,609224,610039,610235,610559,610948,611300,611541,611755,612054,612246,612561,612768,613114,613414,613815,614034,614447,614684,615054,615778,616133,616402,616542,616796,616940,617217,618209,618618,619250,619596,619964,621038,621401,621801,623309,623894,624212,626747,626941,627159,627385,627597,627796,628003,629207,629502,630059,630449,631081,631558,631803,632290,632536,633732,634129,635135,635357,635780,635971,636350,636438,636546,636654,636967,637292,637611,637942,640645,640833,641094,641343,643927,644119,644384,644637,645169,645577,645776,646360,646595,646719,647131,647345,647747,647850,647980,648155,648407,648603,648743,648937,649948,651017,651305,651435,652212,652869,653015,653721,653959,655499,655649,656066,656231,656917,657387,657583,657674,657758,657902,658136,658303,659231,659517,659677,660292,660451,660779,661006,661518,661880,661959,662298,662403,662768,663139,663500,665374,666003,667079,667503,667756,667908,668956,669693,669896,670142,670389,670607,670849,671170,671434,671739,671962,672273,672462,673177,673446,673940,674166,674606,674765,675049,675794,676159,676464,676622,676860,678179,678577,678805,679025,679167,680457,680563,680693,680831,680955,681243,681412,681512,681797,681911,682794,683549,683988,684112,684358,684551,684685,684876,685655,685873,686164,686443,686760,686982,687277,687560,687664,688005,688821,689137,689698,690204,690409,691195,691600,692261,692450,693001,693567,693687,694089,844586,844681,844774,844837,844919,845012,845105,845192,845290,845381,845472,845560,845644,845740,845840,845946,846049,846150,846254,846360,846459,846565,846667,846774,846883,846994,847125,847245,847361,847479,847578,847685,847801,847920,848048,848137,848232,848309,848398,848489,848582,848656,848753,848848,848946,849045,849149,849245,849347,849450,849550,849653,849738,849839,849937,850027,850122,850209,850315,850417,850511,850602,850696,850772,850864,850953,851056,851167,851250,851336,851431,851528,851624,851712,851813,851914,852017,852123,852221,852318,852413,852511,852614,852714,852817,852922,853040,853156,853251,853344,853429,853525,853619,853711,853794,853898,854003,854103,854204,854309,854409,854510,854609,854711,854805,854912,855014,855117,855210,855306,855408,855511,855607,855709,855812,855909,856012,856110,856214,856319,856416,856524,856638,856753,856861,856975,857090,857192,857297,857405,857515,857631,857748,857843,857940,858039,858144,858250,858349,858454,858560,858660,858766,858867,858974,859093,859192,859297,859399,859501,859601,859704,859799,859903,859988,860092,860196,860294,860398,860504,860602,860707,860805,860918,861012,861101,861190,861273,861364,861447,861545,861635,861731,861820,861914,862002,862098,862183,862291,862392,862493,862591,862697,862788,862887,862984,863082,863178,863271,863381,863479,863574,863684,863776,863876,863975,864062,864166,864271,864370,864477,864584,864683,864792,864884,864995,865106,865217,865321,865436,865552,865679,865799,865894,865989,866086,866185,866277,866376,866468,866567,866653,866747,866850,866946,867049,867145,867248,867345,867443,867546,867639,867729,867830,867913,868004,868089,868181,868284,868379,868475,868568,868662,868741,868848,868939,869038,869131,869234,869338,869439,869540,869644,869738,869842,869946,870059,870165,870271,870379,870496,870597,870705,870805,870908,871013,871120,871216,871295,871385,871469,871561,871634,871726,871815,871907,871992,872089,872182,872277,872376,872473,872564,872655,872747,872842,872949,873057,873159,873256,873353,873446,873533,873617,873714,873811,873904,873991,874082,874181,874280,874375,874464,874545,874644,874748,874845,874950,875047,875131,875230,875334,875431,875536,875633,875731,875832,875938,876037,876144,876243,876342,876433,876522,876611,876693,876786,876877,876988,877089,877189,877301,877414,877512,877620,877714,877814,877903,877995,878106,878216,878311,878427,878553,878679,878798,878926,879051,879176,879294,879421,879530,879639,879752,879875,879998,880114,880239,880336,880444,880566,880682,880798,880907,880995,881096,881185,881286,881373,881461,881558,881650,881756,881856,881932", "endLines": "195,201,212,215,216,217,218,219,222,223,224,225,226,228,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,303,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,411,413,414,420,421,422,423,424,425,426,427,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,651,656,657,658,686,698,707,708,709,710,711,712,713,714,715,716,720,721,722,723,724,726,735,736,737,738,739,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,780,781,792,793,803,804,838,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1304,1310,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2043,2044,2094,2095,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2118,2121,2122,2123,2143,2144,2145,2146,2147,2148,2149,2160,2179,2180,2183,2184,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2246,2251,2287,2289,2290,2291,2292,2293,2294,2302,2303,2304,2305,2343,2345,2350,2351,2352,2365,2366,2369,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2457,2458,2459,2460,2461,2462,2463,2464,2465,2468,2471,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2533,2534,2535,2536,2537,2574,2575,2576,2636,2637,2799,2803,2807,2811,2812,2816,2886,2893,2901,3066,3076,3085,3094,3103,3164,3165,3166,3172,3173,3174,3175,3176,3177,3183,3184,3185,3186,3187,3192,3193,3197,3198,3204,3208,3209,3210,3211,3221,3222,3223,3227,3228,3234,3238,3239,3311,3312,3317,3318,3321,3322,3323,3324,3588,3595,3856,3862,4126,4133,4394,4400,4463,4545,4597,4679,4741,4823,4887,4939,5021,5029,5035,5046,5050,5054,5067,5082,5858,5865,5871,5888,5901,5921,5938,5947,5952,5959,5979,5992,6009,6015,6021,6028,6032,6038,6052,6055,6065,6066,6067,6115,6119,6123,6127,6128,6129,6132,6148,6155,6169,6214,6215,6249,6253,6257,6262,6269,6275,6276,6279,6283,6288,6301,6305,6310,6315,6320,6323,6326,6329,6333,6337,6477,6478,6479,6480,6712,6713,6714,6715,6716,6717,6718,6719,6720,6721,6722,6723,6724,6725,6726,6727,6728,6729,6733,6737,6741,6745,6749,6753,6757,6758,6759,6760,6761,6762,6763,6764,6768,6772,6773,6777,6778,6781,6785,6788,6791,6794,6798,6801,6804,6808,6812,6816,6820,6823,6824,6825,6826,6829,6833,6836,6839,6842,6845,6848,6851,6855,6928,6929,6932,6935,6936,6939,6940,6941,6945,6946,6951,6958,6965,6972,6979,6986,6993,7000,7007,7014,7023,7032,7041,7048,7057,7066,7069,7072,7073,7074,7075,7076,7077,7078,7079,7080,7081,7082,7083,7084,7088,7093,7098,7101,7102,7103,7104,7105,7113,7121,7122,7130,7134,7142,7150,7158,7166,7174,7175,7183,7191,7192,7195,7198,7236,7241,7243,7248,7252,7256,7261,7262,7263,7267,7271,7272,7276,7277,7278,7279,7280,7281,7282,7283,7284,7285,7286,7290,7291,7292,7293,7297,7298,7299,7300,7304,7308,7309,7313,7314,7315,7320,7321,7322,7323,7324,7325,7326,7327,7328,7329,7330,7331,7332,7333,7334,7335,7336,7337,7338,7339,7340,7344,7345,7346,7352,7353,7357,7359,7360,7365,7366,7367,7368,7369,7370,7374,7375,7376,7382,7383,7387,7389,7393,7397,7401,7405,7426,7427,7428,7431,7434,7437,7440,7443,7448,7452,7455,7456,7461,7465,7470,7476,7482,7487,7491,7496,7500,7504,7545,7546,7547,7548,7549,7553,7554,7555,7556,7560,7564,7568,7572,7576,7580,7584,7588,7594,7595,7636,7650,7655,7681,7688,7691,7702,7707,7710,7713,7768,7774,7775,7778,7781,7784,7787,7790,7793,7796,7800,7803,7804,7805,7813,7821,7824,7829,7834,7839,7844,7848,7852,7853,7861,7862,7863,7864,7865,7873,7878,7883,7884,7885,7886,7911,7917,7922,7925,7929,7932,7936,7946,7949,7954,7957,7961,7965,8070,8084,8097,8101,8116,8127,8130,8141,8146,8150,8185,8186,8187,8199,8207,8215,8223,8231,8251,8254,8281,8286,8306,8309,8312,8319,8332,8341,8344,8364,8374,8378,8382,8395,8399,8403,8407,8413,8417,8434,8442,8446,8450,8454,8457,8461,8465,8469,8479,8486,8493,8497,8523,8533,8558,8567,8587,8597,8601,8611,8636,8646,8649,8656,8663,8670,8671,8672,8673,8674,8681,8685,8691,8697,8698,8711,8712,8713,8716,8719,8722,8725,8728,8731,8734,8737,8740,8743,8746,8749,8752,8755,8758,8761,8764,8767,8770,8773,8776,8777,8785,8793,8794,8807,8817,8821,8826,8831,8835,8838,8842,8846,8849,8853,8856,8860,8865,8870,8873,8880,8884,8888,8897,8902,8907,8908,8912,8915,8919,8932,8937,8945,8949,8953,8970,8974,8979,8997,9004,9008,9038,9041,9044,9047,9050,9053,9056,9075,9081,9089,9096,9108,9116,9121,9129,9133,9151,9158,9174,9178,9186,9189,9194,9195,9196,9197,9201,9205,9209,9213,9248,9251,9255,9259,9293,9296,9300,9304,9313,9319,9322,9332,9336,9337,9344,9348,9355,9356,9357,9360,9365,9370,9371,9375,9390,9409,9413,9414,9426,9436,9437,9449,9454,9478,9481,9487,9490,9499,9507,9511,9514,9517,9520,9524,9527,9544,9548,9551,9566,9569,9577,9582,9589,9594,9595,9600,9601,9607,9613,9619,9651,9662,9679,9686,9690,9693,9706,9715,9719,9724,9728,9732,9736,9740,9744,9748,9752,9757,9760,9772,9777,9786,9789,9796,9797,9801,9810,9816,9820,9821,9825,9846,9852,9856,9860,9861,9879,9880,9881,9882,9883,9888,9891,9892,9898,9899,9911,9923,9930,9931,9936,9941,9942,9946,9960,9965,9971,9977,9983,9988,9994,10000,10001,10007,10022,10027,10036,10045,10048,10062,10067,10078,10082,10091,10100,10101,10108,10116,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191,15192,15193,15194,15195,15196,15197", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "6542,6777,7236,7395,7455,7516,7581,7636,7786,7836,7889,7947,7995,8115,8464,8535,8607,8679,8752,8819,8868,8922,8959,9010,9070,9117,9173,9222,9280,9334,9395,9451,9502,9562,9618,9681,9730,9786,9842,9892,9951,10006,10068,10115,10169,10225,10277,10332,10386,10440,10494,10543,10601,10655,10712,10768,10815,10868,10924,10984,11047,11106,11168,11218,11272,11326,11374,11431,11484,11540,12162,12218,13156,13266,13322,13382,13435,13496,13575,13656,13728,13807,13887,13963,14041,14110,14186,14263,14334,14407,14483,14561,14630,14706,14783,14847,14918,14990,17482,17580,17635,17903,17956,18008,18058,18116,18181,18229,18280,25237,25303,25361,25430,25488,25557,25627,25700,25774,25842,25909,25979,26045,26118,26178,26254,26314,26374,26449,26517,26583,26651,26711,26770,26827,26893,26955,27012,27080,27153,27223,27285,27346,27414,27476,27546,27615,27671,27730,27792,27854,27921,27978,28039,28100,28161,28222,28278,28334,28390,28446,28504,28562,28620,28678,28735,28792,28849,28906,28965,29024,29082,29165,29248,29321,29375,29444,29500,29581,29662,29733,29862,30121,30179,30237,31402,31949,32316,32370,32440,32510,32575,32641,32706,32774,32843,32911,33041,33094,33153,33211,33263,33359,33747,33793,33843,33899,33946,34051,34109,34171,34234,34296,34355,34415,34480,34546,34611,34673,34735,34797,34859,34921,34983,35049,35116,35182,35245,35309,35372,35440,35501,35563,35625,35688,35752,35815,35879,35957,36016,36082,36162,36223,36276,36485,36536,36963,37024,37494,37553,39020,42565,42636,42702,42776,42845,42916,42989,43060,43128,43201,43277,43347,43425,43493,43559,43620,43689,43753,43819,43887,43953,44016,44084,44155,44220,44293,44356,44437,44501,44567,44637,44707,44777,44847,44914,46119,46177,46236,46296,46355,46414,46473,46532,46591,46650,46709,46768,46827,46886,46946,47007,47069,47130,47191,47252,47313,47374,47435,47495,47556,47617,47677,47738,47799,47860,47921,47982,48043,48104,48165,48226,48287,48348,48416,48485,48555,48624,48693,48762,48831,48900,48969,49038,49107,49176,49245,49305,49366,49428,49489,49550,49611,49672,49733,49794,49855,49916,49977,50038,50100,50163,50227,50290,50353,50416,50479,50542,50605,50668,50731,50794,50857,50918,50980,51043,51105,51167,51229,51291,51353,51415,51477,51539,51601,51663,51720,51806,51886,51976,52071,52163,52255,52345,52428,52521,52608,52705,52796,52897,52984,53087,53176,53275,53367,53467,53551,53645,53733,53831,53914,54005,54099,54198,54300,54398,54498,54585,54685,54771,54867,54955,55036,55127,55223,55316,55409,55500,55585,55679,55768,55866,55959,56061,56149,56253,56344,56444,56537,56638,56723,56818,56907,57006,57091,57183,57278,57378,57481,57580,57683,57772,57873,57960,58057,58145,58241,58333,58433,58523,58621,58706,58795,58884,58977,59064,59155,59893,59969,60038,60117,60190,60270,60350,60427,60495,60573,60649,60720,60801,60874,60957,61032,61117,61190,61271,61352,61426,61510,61580,61658,61728,61808,61886,61958,62040,62110,62187,62267,62352,62440,62524,62611,62685,62763,62841,62912,62993,63084,63167,63263,63361,63468,63533,63599,63652,63728,63794,63881,63957,64033,72265,72633,73227,73306,73384,73457,73522,73585,73651,73722,73793,73863,73925,73994,74060,74120,74187,74254,74310,74361,74414,74466,74520,74591,74654,74713,74775,74834,74907,74974,75044,75104,75167,75242,75314,75410,75481,75537,75608,75665,75722,75788,75852,75923,75980,76033,76096,76148,76206,76273,77559,77625,77684,77767,77826,77883,77950,78020,78094,78156,78225,78295,78394,78491,78590,78676,78762,78843,78918,79007,79098,79182,79241,79287,79353,79410,79477,79534,79616,79681,79747,79870,79954,80075,80140,80202,80300,80374,80457,80546,80610,80689,80763,80825,80921,80986,81045,81101,81157,81217,81324,81371,81431,81492,81556,81617,81677,81735,81778,81827,81879,81930,81982,82031,82080,82145,82211,82271,82332,82388,82447,82496,82544,82602,82659,82761,82818,82893,82941,82992,83054,83119,83171,83245,83308,83371,83439,83489,83551,83611,83668,83728,83777,83845,83951,84053,84122,84193,84249,84298,84398,84469,84579,84670,84752,84850,84906,85007,85117,85216,85279,85385,85462,85574,85701,85813,85940,86010,86124,86255,86352,86420,86538,86641,86759,86820,86894,86961,87066,87188,87262,87329,87439,87538,87611,87708,87830,87948,88066,88127,88249,88366,88434,88540,88642,88722,88793,88889,88956,89030,89104,89190,89280,89358,89435,89535,89606,89727,89848,89912,90037,90111,90235,90359,90426,90535,90663,90775,90854,90932,91033,91104,91226,91348,91413,91539,91651,91757,91825,91924,92028,92091,92157,92241,92354,92467,92585,92663,92735,92871,93007,93092,93232,93370,93508,93650,93732,93818,93895,93968,94077,94188,94316,94444,94576,94706,94836,94970,95059,95121,95217,95284,95401,95522,95619,95701,95788,95875,96006,96137,96272,96349,96426,96537,96651,96725,96834,96946,97013,97086,97151,97253,97349,97453,97521,97586,97680,97752,97862,97968,98041,98132,98234,98337,98432,98539,98644,98766,98888,99014,99073,99131,99255,99379,99507,99625,99743,99865,99951,100048,100182,100316,100396,100534,100666,100798,100934,101009,101085,101188,101262,101375,101456,101513,101574,101633,101693,101751,101812,101870,101920,101969,102036,102095,102154,102203,102274,102358,102428,102499,102579,102648,102711,102779,102845,102913,102978,103044,103121,103199,103305,103411,103507,103636,103725,103852,103918,103988,104074,104140,104223,104297,104395,104491,104587,104685,104794,104889,104978,105040,105100,105165,105222,105303,105357,105414,105511,105621,105682,105797,105918,106013,106105,106198,106254,106313,106362,106454,106503,106557,106611,106665,106719,106773,106828,106938,107048,107156,107266,107376,107486,107596,107704,107810,107914,108018,108122,108217,108312,108405,108498,108602,108708,108812,108916,109009,109102,109195,109288,109396,109502,109608,109714,109811,109906,110001,110096,110202,110308,110414,110520,110618,110713,110809,110906,110971,111075,111133,111197,111258,111320,111380,111445,111507,111575,111633,111696,111759,111826,111901,111974,112040,112092,112145,112197,112254,112338,112433,112518,112599,112679,112756,112835,112912,112986,113060,113131,113211,113283,113358,113423,113484,113544,113619,113693,113766,113836,113908,113978,114051,114115,114185,114231,114300,114352,114437,114520,114577,114643,114710,114776,114857,114932,114988,115041,115102,115160,115210,115259,115308,115357,115419,115471,115516,115597,115648,115702,115755,115809,115860,115909,115975,116026,116087,116148,116210,116260,116301,116378,116437,116496,116555,116616,116672,116728,116795,116856,116921,116976,117041,117110,117178,117256,117325,117385,117456,117530,117595,117667,117737,117804,117888,117957,118024,118094,118157,118224,118292,118375,118454,118544,118621,118689,118756,118834,118891,118948,119016,119082,119138,119198,119257,119311,119361,119411,119459,119521,119572,119645,119725,119805,119869,119936,120007,120065,120126,120192,120251,120318,120378,120438,120501,120569,120630,120697,120775,120845,120894,120951,121020,121081,121169,121257,121345,121433,121520,121607,121694,121781,121839,121913,121983,122039,122110,122175,122237,122312,122385,122475,122541,122607,122668,122732,122794,122852,122923,123006,123065,123136,123202,123267,123328,123387,123458,123524,123589,123672,123748,123823,123904,123964,124033,124103,124172,124227,124283,124339,124400,124458,124514,124573,124627,124682,124744,124801,124895,124964,125065,125116,125186,125249,125305,125363,125422,125476,125562,125646,125716,125785,125855,125970,126091,126158,126225,126300,126367,126426,126480,126534,126588,126641,126693,126767,129114,129254,132110,132160,132210,132299,132355,132413,132475,132530,132588,132659,132723,132782,132844,132910,132976,133424,133570,133615,133658,134758,134805,134850,134901,134952,135003,135054,135588,136588,136650,136830,136902,137027,137081,137136,137194,137249,137308,137364,137433,137502,137571,137641,137704,137767,137830,137893,137958,138023,138088,138153,138216,138280,138344,138408,138459,138537,138615,138686,138758,138831,138903,138969,139035,139103,139171,139237,139304,139378,139441,139498,139558,139623,139690,139755,139812,139873,139931,140035,140145,140254,140358,140436,140501,140568,140634,140704,140751,140803,140853,141036,141426,144029,144240,144424,144602,144840,145029,145198,145807,145922,146007,146086,149875,150007,150401,150558,150715,151845,151999,152232,154002,154098,154188,154284,154374,154540,154663,154786,154956,155062,155177,155292,155394,155500,155617,155732,155814,155987,156155,156303,156462,156617,156790,156907,157024,157192,157304,157418,157590,157766,157924,158057,158169,158315,158467,158599,158742,158864,161317,161453,161549,161685,161780,161947,162040,162132,162319,162475,162653,162817,162999,163316,163498,163680,163870,164102,164292,164469,164631,164788,164898,165081,165218,165422,165606,165790,165950,166108,166292,166519,166722,166893,167113,167335,167490,167690,167874,167977,168167,168308,168473,168644,168844,169048,169250,169415,169620,169819,170018,170215,170306,170455,170605,170689,170838,170983,171135,171276,171442,171603,172213,172514,172680,172835,172937,175681,175845,176031,180081,180206,193655,193927,194205,194450,194512,194797,199037,199493,200002,210778,211292,211729,212163,212606,216588,216709,216808,217213,217310,217427,217514,217637,217738,218144,218243,218362,218455,218562,218905,219012,219257,219378,219787,220035,220135,220240,220359,220868,221015,221134,221385,221518,221933,222187,222299,227649,227774,228182,228303,228531,228652,228785,228932,249654,250146,270617,271041,291808,292302,312818,313244,318085,323502,327593,333024,337766,343143,347127,351119,356510,357057,357490,358246,358476,358719,359886,360815,408960,409544,410017,411447,412191,413384,414438,414916,415209,415592,417107,417872,419015,419456,419897,420493,420767,421178,422194,422372,423125,423262,423353,425547,425813,426135,426345,426454,426573,426757,427875,428345,429096,431679,431774,434034,434262,434518,434777,435353,435707,435829,435968,436260,436520,437448,437734,438137,438539,438882,439094,439295,439508,439797,440082,450757,450844,450929,451028,463817,463923,464046,464178,464301,464431,464555,464688,464819,464944,465061,465181,465313,465441,465555,465673,465786,465907,466095,466282,466463,466646,466830,466995,467177,467297,467417,467525,467635,467747,467855,467965,468130,468296,468448,468613,468714,468834,469005,469166,469329,469490,469657,469776,469893,470073,470255,470436,470619,470774,470919,471041,471176,471339,471532,471658,471810,471952,472122,472278,472450,472741,479540,479632,479805,479967,480062,480231,480325,480414,480657,480746,481039,481455,481875,482296,482722,483139,483555,483972,484390,484804,485274,485747,486219,486630,487101,487573,487763,487969,488075,488183,488289,488401,488515,488627,488741,488857,488971,489079,489189,489297,489559,489938,490342,490489,490597,490707,490815,490929,491338,491752,491868,492286,492527,492957,493392,493802,494224,494634,494756,495165,495581,495703,495921,496105,498809,499153,499233,499589,499739,499883,500182,500294,500384,500646,500911,501019,501171,501279,501355,501467,501557,501659,501767,501875,501975,502083,502168,502334,502438,502566,502653,502820,502898,503012,503104,503368,503635,503745,503898,504008,504092,504481,504579,504687,504781,504911,505019,505141,505277,505385,505505,505639,505761,505889,506031,506157,506297,506423,506541,506673,506771,506881,507181,507293,507411,507875,507991,508294,508420,508516,508917,509027,509151,509289,509399,509521,509833,509957,510087,510563,510691,511006,511144,511306,511522,511678,511882,513363,513447,513551,513754,513943,514144,514337,514542,514855,515067,515233,515349,515595,515811,516124,516550,517012,517249,517401,517661,517805,517947,521179,521293,521413,521529,521623,521944,522043,522161,522262,522541,522826,523105,523387,523640,523899,524152,524408,524832,524908,528158,529513,529957,531811,532386,532594,533604,533984,534150,534291,539311,539737,539849,539984,540137,540334,540505,540688,540863,541050,541322,541480,541564,541668,542155,542711,542869,543088,543319,543542,543777,543999,544265,544403,545002,545116,545254,545366,545490,546061,546556,547102,547247,547340,547432,549359,549929,550227,550416,550622,550815,551025,551909,552054,552446,552604,552821,553082,561309,562184,562804,563001,563949,564714,564837,565610,565831,566031,568008,568108,568198,568884,569637,570402,571165,571940,573153,573318,574931,575252,576315,576525,576695,577265,578160,578793,578959,580445,581061,581297,581518,582476,582741,583006,583253,583667,583903,585188,585637,585824,586073,586315,586491,586732,586965,587190,587785,588260,588784,589045,590396,590871,592097,592567,593615,594067,594311,594768,596013,596496,596646,597201,597653,598053,598206,598351,598494,598564,598992,599280,599784,600293,600409,601311,601433,601545,601722,601988,602258,602524,602792,603048,603308,603564,603822,604074,604330,604582,604836,605068,605304,605556,605812,606064,606318,606550,606784,606896,607548,608003,608127,609219,610034,610230,610554,610943,611295,611536,611750,612049,612241,612556,612763,613109,613409,613810,614029,614442,614679,615049,615773,616128,616397,616537,616791,616935,617212,618204,618613,619245,619591,619959,621033,621396,621796,623304,623889,624207,626742,626936,627154,627380,627592,627791,627998,629202,629497,630054,630444,631076,631553,631798,632285,632531,633727,634124,635130,635352,635775,635966,636345,636433,636541,636649,636962,637287,637606,637937,640640,640828,641089,641338,643922,644114,644379,644632,645164,645572,645771,646355,646590,646714,647126,647340,647742,647845,647975,648150,648402,648598,648738,648932,649943,651012,651300,651430,652207,652864,653010,653716,653954,655494,655644,656061,656226,656912,657382,657578,657669,657753,657897,658131,658298,659226,659512,659672,660287,660446,660774,661001,661513,661875,661954,662293,662398,662763,663134,663495,665369,665998,667074,667498,667751,667903,668951,669688,669891,670137,670384,670602,670844,671165,671429,671734,671957,672268,672457,673172,673441,673935,674161,674601,674760,675044,675789,676154,676459,676617,676855,678174,678572,678800,679020,679162,680452,680558,680688,680826,680950,681238,681407,681507,681792,681906,682789,683544,683983,684107,684353,684546,684680,684871,685650,685868,686159,686438,686755,686977,687272,687555,687659,688000,688816,689132,689693,690199,690404,691190,691595,692256,692445,692996,693562,693682,694084,694618,844676,844769,844832,844914,845007,845100,845187,845285,845376,845467,845555,845639,845735,845835,845941,846044,846145,846249,846355,846454,846560,846662,846769,846878,846989,847120,847240,847356,847474,847573,847680,847796,847915,848043,848132,848227,848304,848393,848484,848577,848651,848748,848843,848941,849040,849144,849240,849342,849445,849545,849648,849733,849834,849932,850022,850117,850204,850310,850412,850506,850597,850691,850767,850859,850948,851051,851162,851245,851331,851426,851523,851619,851707,851808,851909,852012,852118,852216,852313,852408,852506,852609,852709,852812,852917,853035,853151,853246,853339,853424,853520,853614,853706,853789,853893,853998,854098,854199,854304,854404,854505,854604,854706,854800,854907,855009,855112,855205,855301,855403,855506,855602,855704,855807,855904,856007,856105,856209,856314,856411,856519,856633,856748,856856,856970,857085,857187,857292,857400,857510,857626,857743,857838,857935,858034,858139,858245,858344,858449,858555,858655,858761,858862,858969,859088,859187,859292,859394,859496,859596,859699,859794,859898,859983,860087,860191,860289,860393,860499,860597,860702,860800,860913,861007,861096,861185,861268,861359,861442,861540,861630,861726,861815,861909,861997,862093,862178,862286,862387,862488,862586,862692,862783,862882,862979,863077,863173,863266,863376,863474,863569,863679,863771,863871,863970,864057,864161,864266,864365,864472,864579,864678,864787,864879,864990,865101,865212,865316,865431,865547,865674,865794,865889,865984,866081,866180,866272,866371,866463,866562,866648,866742,866845,866941,867044,867140,867243,867340,867438,867541,867634,867724,867825,867908,867999,868084,868176,868279,868374,868470,868563,868657,868736,868843,868934,869033,869126,869229,869333,869434,869535,869639,869733,869837,869941,870054,870160,870266,870374,870491,870592,870700,870800,870903,871008,871115,871211,871290,871380,871464,871556,871629,871721,871810,871902,871987,872084,872177,872272,872371,872468,872559,872650,872742,872837,872944,873052,873154,873251,873348,873441,873528,873612,873709,873806,873899,873986,874077,874176,874275,874370,874459,874540,874639,874743,874840,874945,875042,875126,875225,875329,875426,875531,875628,875726,875827,875933,876032,876139,876238,876337,876428,876517,876606,876688,876781,876872,876983,877084,877184,877296,877409,877507,877615,877709,877809,877898,877990,878101,878211,878306,878422,878548,878674,878793,878921,879046,879171,879289,879416,879525,879634,879747,879870,879993,880109,880234,880331,880439,880561,880677,880793,880902,880990,881091,881180,881281,881368,881456,881553,881645,881751,881851,881927,882004"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2817,2818,2823,2828,2836,6233", "startColumns": "4,4,4,4,4,4", "startOffsets": "194802,194876,195104,195377,195864,433217", "endLines": "2817,2822,2827,2835,2839,6235", "endColumns": "73,12,12,12,12,12", "endOffsets": "194871,195099,195372,195859,196043,433340"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_banner_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "938", "startColumns": "4", "startOffsets": "45799", "endColumns": "54", "endOffsets": "45849"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "867,872,873,874,875,876,877,878,879,928,929,935,941,942,1184,1185,1200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41087,41386,41432,41482,41526,41573,41620,41671,41724,45199,45247,45612,45955,46010,64285,64333,65298", "endColumns": "49,45,49,43,46,46,50,52,49,47,53,51,54,56,47,52,54", "endOffsets": "41132,41427,41477,41521,41568,41615,41666,41719,41769,45242,45296,45659,46005,46062,64328,64381,65348"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,379,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23711,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,81,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23788,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,59,83,101,106,113,161,171,2282,2283,2284,2286,2288,2324,2325,2326,2327,2328,2329,2330,2331,2332,2337,2338,2339,2340,2344,2346,2347,2348,2356,2357,2362,2364,2367,2368,2379,2380,2381,2382,2383,2384,2385,2386,2387,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2525,2526,2528,2529,2530,2531,2532,2538,2539,2544,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2638,2640,2641,2642,2643,2644,2645,2646,2647,2648,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2786", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1586,1980,2719,3329,3517,3764,5336,5640,143546,143611,143718,143833,144034,148424,148508,148575,148659,148748,148826,148896,148967,149028,149433,149497,149598,149638,149880,150012,150095,150168,150935,150992,151468,151647,152004,152087,153300,153347,153392,153432,153486,153570,153630,153672,153753,158869,158927,158987,159044,159098,159161,159228,159303,159394,159479,159565,159661,159749,159840,159930,160018,160106,160202,160287,160364,160426,160488,160540,160600,160677,160743,160813,160869,160941,161003,161065,171608,171684,171802,171870,171934,171980,172066,172942,173010,173299,173500,173546,173610,173680,173743,173801,173881,173958,174060,174159,174265,174365,174491,174563,174658,174740,174828,174919,174995,175079,175180,175258,175313,175407,176036,176105,176160,176216,176284,176339,176416,176489,176544,176605,176654,176713,176758,176810,176872,176981,177058,177117,177180,177285,177340,177389,177455,177510,177583,177640,177698,177753,177814,177863,177913,177998,178077,178153,178213,178268,178325,178398,178470,178533,178600,178681,178752,178815,178884,178972,179032,179089,179147,179211,179267,179314,179370,179426,179517,179583,179642,179739,179800,180211,180350,180429,180497,180558,180628,180714,180803,180906,180962,181158,181317,181433,181621,181821,181961,182021,182089,182147,182364,182502,182592,182727,182892,182963,183112,183376,183474,183678,183734,183887,183991,184060,184214,184399,184469,184533,184592,184655,184701,184762,184838,184916,184990,185048,185132,185208,185308,185404,185500,185598,185677,185745,185819,185889,185968,186020,186068,186114,186158,186225,186285,186352,186437,186505,186587,186692,186787,186880,186967,187053,187129,187219,187291,187348,187420,187505,187586,187659,187732,187806,187875,187963,188025,188119,188203,188270,188333,188425,188517,188592,188664,188731,188805,188892,188962,189036,189106,189170,189264,189324,189390,189442,189521,189579,189652,189731,189793,189851,189930,189972,190039,190104,190164,190236,190330,190399,190465,190515,190587,190709,190767,190889,190950,191038,191122,191232,191318,191392,191456,191518,191592,191642,191716,191853,191935,192023,192092,192163,192239,192314,192364,192514", "endLines": "49,63,88,105,112,116,165,175,2282,2283,2284,2286,2288,2324,2325,2326,2327,2328,2329,2330,2331,2332,2337,2338,2339,2340,2344,2346,2347,2348,2356,2357,2362,2364,2367,2368,2379,2380,2381,2382,2383,2384,2385,2386,2387,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2525,2526,2528,2529,2530,2531,2532,2538,2539,2544,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2638,2640,2641,2642,2643,2644,2645,2646,2647,2648,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2781,2782,2783,2786", "endColumns": "19,19,19,19,19,19,19,19,64,106,48,50,79,83,66,83,88,77,69,70,60,84,63,100,39,81,66,82,72,91,56,60,71,49,82,90,46,44,39,53,83,59,41,80,66,57,59,56,53,62,66,74,90,84,85,95,87,90,89,87,87,95,84,76,61,61,51,59,76,65,69,55,71,61,61,78,75,73,67,63,45,85,73,67,72,77,45,63,69,62,57,79,76,101,98,105,99,125,71,94,81,87,90,75,83,100,77,54,93,67,68,54,55,67,54,76,72,54,60,48,58,44,51,61,108,76,58,62,104,54,48,65,54,72,56,57,54,60,48,49,84,78,75,59,54,56,72,71,62,66,80,70,62,68,87,59,56,57,63,55,46,55,55,90,65,58,96,60,62,67,78,67,60,69,85,88,102,55,56,158,115,187,199,139,59,67,57,216,137,89,134,164,70,148,263,97,203,55,152,103,68,23,184,69,63,58,62,45,60,75,77,73,57,83,75,99,95,95,97,78,67,73,69,78,51,47,45,43,66,59,66,84,67,81,104,94,92,86,85,75,89,71,56,71,84,80,72,72,73,68,87,61,93,83,66,62,91,91,74,71,66,73,86,69,73,69,63,93,59,65,51,78,57,72,78,61,57,78,41,66,64,59,71,93,68,65,49,71,121,57,65,60,87,83,109,85,73,63,61,73,49,73,136,81,87,68,70,75,74,49,51,63", "endOffsets": "1697,2124,2914,3512,3759,3891,5479,5791,143606,143713,143762,143879,144109,148503,148570,148654,148743,148821,148891,148962,149023,149108,149492,149593,149633,149715,149942,150090,150163,150255,150987,151048,151535,151692,152082,152173,153342,153387,153427,153481,153565,153625,153667,153748,153815,158922,158982,159039,159093,159156,159223,159298,159389,159474,159560,159656,159744,159835,159925,160013,160101,160197,160282,160359,160421,160483,160535,160595,160672,160738,160808,160864,160936,160998,161060,161139,171679,171753,171865,171929,171975,172061,172135,173005,173078,173372,173541,173605,173675,173738,173796,173876,173953,174055,174154,174260,174360,174486,174558,174653,174735,174823,174914,174990,175074,175175,175253,175308,175402,175470,176100,176155,176211,176279,176334,176411,176484,176539,176600,176649,176708,176753,176805,176867,176976,177053,177112,177175,177280,177335,177384,177450,177505,177578,177635,177693,177748,177809,177858,177908,177993,178072,178148,178208,178263,178320,178393,178465,178528,178595,178676,178747,178810,178879,178967,179027,179084,179142,179206,179262,179309,179365,179421,179512,179578,179637,179734,179795,179858,180274,180424,180492,180553,180623,180709,180798,180901,180957,181014,181312,181428,181616,181816,181956,182016,182084,182142,182359,182497,182587,182722,182887,182958,183107,183371,183469,183673,183729,183882,183986,184055,184209,184394,184464,184528,184587,184650,184696,184757,184833,184911,184985,185043,185127,185203,185303,185399,185495,185593,185672,185740,185814,185884,185963,186015,186063,186109,186153,186220,186280,186347,186432,186500,186582,186687,186782,186875,186962,187048,187124,187214,187286,187343,187415,187500,187581,187654,187727,187801,187870,187958,188020,188114,188198,188265,188328,188420,188512,188587,188659,188726,188800,188887,188957,189031,189101,189165,189259,189319,189385,189437,189516,189574,189647,189726,189788,189846,189925,189967,190034,190099,190159,190231,190325,190394,190460,190510,190582,190704,190762,190828,190945,191033,191117,191227,191313,191387,191451,191513,191587,191637,191711,191848,191930,192018,192087,192158,192234,192309,192359,192411,192573"}}, {"source": "D:\\work\\v2rayNG-1.10.7\\V2HoorVPN\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "939", "startColumns": "4", "startOffsets": "45854", "endColumns": "56", "endOffsets": "45906"}}]}]}