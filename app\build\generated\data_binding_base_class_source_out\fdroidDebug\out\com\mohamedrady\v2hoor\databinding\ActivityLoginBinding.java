// Generated by view binder compiler. Do not edit!
package com.mohamedrady.v2hoor.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.mohamedrady.v2hoor.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button btnGoogleSignIn;

  @NonNull
  public final Button btnLogin;

  @NonNull
  public final Button btnSkipLogin;

  @NonNull
  public final EditText etEmail;

  @NonNull
  public final EditText etPassword;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvForgotPassword;

  private ActivityLoginBinding(@NonNull CoordinatorLayout rootView, @NonNull Button btnGoogleSignIn,
      @NonNull Button btnLogin, @NonNull Button btnSkipLogin, @NonNull EditText etEmail,
      @NonNull EditText etPassword, @NonNull ProgressBar progressBar, @NonNull Toolbar toolbar,
      @NonNull TextView tvForgotPassword) {
    this.rootView = rootView;
    this.btnGoogleSignIn = btnGoogleSignIn;
    this.btnLogin = btnLogin;
    this.btnSkipLogin = btnSkipLogin;
    this.etEmail = etEmail;
    this.etPassword = etPassword;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
    this.tvForgotPassword = tvForgotPassword;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_google_sign_in;
      Button btnGoogleSignIn = ViewBindings.findChildViewById(rootView, id);
      if (btnGoogleSignIn == null) {
        break missingId;
      }

      id = R.id.btn_login;
      Button btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.btn_skip_login;
      Button btnSkipLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnSkipLogin == null) {
        break missingId;
      }

      id = R.id.et_email;
      EditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.et_password;
      EditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_forgot_password;
      TextView tvForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPassword == null) {
        break missingId;
      }

      return new ActivityLoginBinding((CoordinatorLayout) rootView, btnGoogleSignIn, btnLogin,
          btnSkipLogin, etEmail, etPassword, progressBar, toolbar, tvForgotPassword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
