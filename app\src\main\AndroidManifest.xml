<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingLeanbackLauncher">

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <uses-sdk
        android:minSdkVersion="21"
        tools:overrideLibrary="com.blacksquircle.ui.editorkit" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <!-- https://developer.android.com/about/versions/11/privacy/package-visibility -->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"
        android:minSdkVersion="34" />
    <!-- <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:name=".AngApplication"
        android:allowBackup="true"
        android:banner="@mipmap/ic_banner"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppThemeDayNight"
        android:usesCleartextTraffic="true"
        tools:targetApi="m">

        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeDayNight.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />
            </intent-filter>

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>
        <activity
            android:name=".ui.ServerActivity"
            android:exported="false"
            android:windowSoftInputMode="stateUnchanged" />
        <activity
            android:name=".ui.ServerCustomConfigActivity"
            android:exported="false"
            android:windowSoftInputMode="stateUnchanged" />
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false" />
        <activity
            android:name=".ui.PerAppProxyActivity"
            android:exported="false" />
        <activity
            android:name=".ui.ScannerActivity"
            android:exported="false" />
        <activity
            android:name=".ui.LogcatActivity"
            android:exported="false" />
        <activity
            android:name=".ui.RoutingSettingActivity"
            android:exported="false" />
        <activity
            android:name=".ui.RoutingEditActivity"
            android:exported="false" />
        <activity
            android:name=".ui.SubSettingActivity"
            android:exported="false" />
        <activity
            android:name=".ui.UserAssetActivity"
            android:exported="false" />
        <activity
            android:name=".ui.UserAssetUrlActivity"
            android:exported="false" />

        <activity
            android:name=".ui.SubEditActivity"
            android:exported="false" />
        <activity
            android:name=".ui.ScScannerActivity"
            android:exported="false" />
        <activity
            android:name=".ui.ScSwitchActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:process=":RunSoLibV2RayDaemon"
            android:theme="@style/AppTheme.NoActionBar.Translucent" />

        <activity
            android:name=".ui.UrlSchemeActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="v2rayng" />
                <data android:host="install-config" />
                <data android:host="install-sub" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.CheckUpdateActivity"
            android:exported="false" />
        <activity
            android:name=".ui.AboutActivity"
            android:exported="false" />

        <service
            android:name=".service.V2RayVpnService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_VPN_SERVICE"
            android:process=":RunSoLibV2RayDaemon">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
            <meta-data
                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
                android:value="true" />
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="vpn" />
        </service>

        <service
            android:name=".service.V2RayProxyOnlyService"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:label="@string/app_name"
            android:process=":RunSoLibV2RayDaemon">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="proxy" />
        </service>

        <service
            android:name=".service.V2RayTestService"
            android:exported="false"
            android:process=":RunSoLibV2RayDaemon" />

        <receiver
            android:name=".receiver.WidgetProvider"
            android:exported="true"
            android:process=":RunSoLibV2RayDaemon">
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/app_widget_provider" />
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.mohamedrady.v2hoor.action.widget.click" />
                <action android:name="com.mohamedrady.v2hoor.action.activity" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receiver.BootReceiver"
            android:exported="true"
            android:label="BootReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name=".service.QSTileService"
            android:exported="true"
            android:foregroundServiceType="specialUse"
            android:icon="@drawable/ic_stat_name"
            android:label="@string/app_tile_name"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:process=":RunSoLibV2RayDaemon"
            tools:targetApi="24">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="tile" />
        </service>
        <!-- =====================Tasker===================== -->
        <activity
            android:name=".ui.TaskerActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher">
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".receiver.TaskerReceiver"
            android:exported="true"
            android:process=":RunSoLibV2RayDaemon"
            tools:ignore="ExportedReceiver">
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
            </intent-filter>
        </receiver>
        <!-- =====================Tasker===================== -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">

            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />

        </provider>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.cache"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/cache_paths" />
        </provider>

    </application>

</manifest>
