<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".ui.UserDetailsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- User Basic Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المعلومات الأساسية"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="12dp"
                        android:background="@color/divider_color" />

                    <TextView
                        android:id="@+id/textViewUserName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="محمد أحمد" />

                    <TextView
                        android:id="@+id/textViewUserEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="16sp"
                        tools:text="<EMAIL>" />

                    <TextView
                        android:id="@+id/textViewUserId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        tools:text="معرف المستخدم: abc123" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/textViewUserStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_status_badge"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="6dp"
                            android:text="نشط"
                            android:textColor="@color/green"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/textViewRole"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textColor="@color/blue"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="الدور: مستخدم" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Subscription Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات الاشتراك"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="12dp"
                        android:background="@color/divider_color" />

                    <TextView
                        android:id="@+id/textViewSubscriptionType"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/orange"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="نوع الاشتراك: مميز" />

                    <TextView
                        android:id="@+id/textViewServerCount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="عدد السيرفرات: 3" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Dates Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="التواريخ المهمة"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="12dp"
                        android:background="@color/divider_color" />

                    <TextView
                        android:id="@+id/textViewCreationDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="تاريخ الانضمام: 15/01/2024" />

                    <TextView
                        android:id="@+id/textViewLastLogin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="آخر تسجيل دخول: منذ ساعة" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Contact Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات الاتصال"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="12dp"
                        android:background="@color/divider_color" />

                    <TextView
                        android:id="@+id/textViewPhoneNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="رقم الهاتف: +20123456789" />

                    <TextView
                        android:id="@+id/textViewCountry"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="البلد: مصر" />

                    <TextView
                        android:id="@+id/textViewCity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="المدينة: القاهرة" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <Button
                    android:id="@+id/buttonToggleStatus"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/bg_button_primary"
                    android:text="تفعيل"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/buttonManageServers"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/bg_button_secondary"
                    android:text="إدارة السيرفرات"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/buttonEditProfile"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/bg_button_secondary"
                    android:text="تعديل الملف الشخصي"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/buttonDeleteUser"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/bg_button_danger"
                    android:text="حذف المستخدم"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
