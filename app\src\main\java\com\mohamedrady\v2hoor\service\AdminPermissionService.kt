package com.mohamedrady.v2hoor.service

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.handler.MmkvManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

/**
 * Service for managing admin permissions and roles
 */
class AdminPermissionService private constructor() {

    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()

    companion object {
        @Volatile
        private var INSTANCE: AdminPermissionService? = null

        // Super admin email
        private const val SUPER_ADMIN_EMAIL = "<EMAIL>"
        
        // Collections
        private const val ADMINS_COLLECTION = "admins"
        private const val USER_ROLES_COLLECTION = "user_roles"
        
        // Local storage keys
        private const val KEY_IS_ADMIN = "is_admin"
        private const val KEY_IS_SUPER_ADMIN = "is_super_admin"
        private const val KEY_ADMIN_PERMISSIONS = "admin_permissions"

        fun getInstance(): AdminPermissionService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AdminPermissionService().also { INSTANCE = it }
            }
        }
    }

    /**
     * Admin permission levels
     */
    enum class AdminLevel {
        NONE,           // عادي
        MODERATOR,      // مشرف
        ADMIN,          // مدير
        SUPER_ADMIN     // مدير عام
    }

    /**
     * Admin permissions
     */
    data class AdminPermissions(
        val canManageServers: Boolean = false,      // إدارة السيرفرات
        val canManageUsers: Boolean = false,        // إدارة المستخدمين
        val canViewLogs: Boolean = false,           // عرض السجلات
        val canManageSettings: Boolean = false,     // إدارة الإعدادات
        val canPromoteUsers: Boolean = false,       // ترقية المستخدمين
        val canDeleteServers: Boolean = false,      // حذف السيرفرات
        val canAddServers: Boolean = false,         // إضافة السيرفرات
        val canViewServerDetails: Boolean = false,  // عرض تفاصيل السيرفرات
        val canEditServerDetails: Boolean = false   // تعديل تفاصيل السيرفرات
    )

    /**
     * Check if current user is super admin
     */
    fun isSuperAdmin(): Boolean {
        val user = auth.currentUser
        if (user?.email == SUPER_ADMIN_EMAIL) {
            return true
        }
        return MmkvManager.decodeSettingsBool(KEY_IS_SUPER_ADMIN, false)
    }

    /**
     * Check if current user is admin (any level)
     */
    fun isAdmin(): Boolean {
        return try {
            if (isSuperAdmin()) return true
            MmkvManager.decodeSettingsBool(KEY_IS_ADMIN, false)
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Error checking admin status", e)
            false
        }
    }

    /**
     * Get current user admin level
     */
    suspend fun getCurrentUserAdminLevel(): AdminLevel {
        return withContext(Dispatchers.IO) {
            try {
                val user = auth.currentUser ?: return@withContext AdminLevel.NONE

                // Check if super admin first
                if (user.email == SUPER_ADMIN_EMAIL) {
                    MmkvManager.encodeSettings(KEY_IS_SUPER_ADMIN, true)
                    MmkvManager.encodeSettings(KEY_IS_ADMIN, true)

                    // Ensure super admin exists in Firestore
                    ensureSuperAdminInFirestore(user.uid, user.email ?: SUPER_ADMIN_EMAIL)

                    return@withContext AdminLevel.SUPER_ADMIN
                }

                // Check in Firestore
                val doc = firestore.collection(USER_ROLES_COLLECTION)
                    .document(user.uid)
                    .get()
                    .await()

                val level = when (doc.getString("role")) {
                    "super_admin" -> AdminLevel.SUPER_ADMIN
                    "admin" -> AdminLevel.ADMIN
                    "moderator" -> AdminLevel.MODERATOR
                    else -> AdminLevel.NONE
                }

                // Cache locally
                val isAdmin = level != AdminLevel.NONE
                val isSuperAdmin = level == AdminLevel.SUPER_ADMIN
                MmkvManager.encodeSettings(KEY_IS_ADMIN, isAdmin)
                MmkvManager.encodeSettings(KEY_IS_SUPER_ADMIN, isSuperAdmin)

                level
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get user admin level", e)

                // Fallback: check if super admin by email
                val user = auth.currentUser
                if (user?.email == SUPER_ADMIN_EMAIL) {
                    MmkvManager.encodeSettings(KEY_IS_SUPER_ADMIN, true)
                    MmkvManager.encodeSettings(KEY_IS_ADMIN, true)
                    return@withContext AdminLevel.SUPER_ADMIN
                }

                AdminLevel.NONE
            }
        }
    }

    /**
     * Ensure super admin exists in Firestore
     */
    private suspend fun ensureSuperAdminInFirestore(uid: String, email: String) {
        try {
            val userData = mapOf(
                "email" to email,
                "role" to "super_admin",
                "displayName" to "المدير العام",
                "createdAt" to System.currentTimeMillis(),
                "lastLogin" to System.currentTimeMillis(),
                "isActive" to true
            )

            // Set user role
            firestore.collection(USER_ROLES_COLLECTION)
                .document(uid)
                .set(userData)
                .await()

            // Also add to users collection
            firestore.collection("users")
                .document(uid)
                .set(userData)
                .await()

            Log.i(AppConfig.TAG, "Super admin ensured in Firestore")
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to ensure super admin in Firestore", e)
        }
    }

    /**
     * Get admin permissions for current user
     */
    suspend fun getCurrentUserPermissions(): AdminPermissions {
        return withContext(Dispatchers.IO) {
            try {
                val level = getCurrentUserAdminLevel()
                
                when (level) {
                    AdminLevel.SUPER_ADMIN -> AdminPermissions(
                        canManageServers = true,
                        canManageUsers = true,
                        canViewLogs = true,
                        canManageSettings = true,
                        canPromoteUsers = true,
                        canDeleteServers = true,
                        canAddServers = true,
                        canViewServerDetails = true,
                        canEditServerDetails = true
                    )
                    AdminLevel.ADMIN -> AdminPermissions(
                        canManageServers = true,
                        canManageUsers = false,
                        canViewLogs = true,
                        canManageSettings = false,
                        canPromoteUsers = false,
                        canDeleteServers = true,
                        canAddServers = true,
                        canViewServerDetails = true,
                        canEditServerDetails = true
                    )
                    AdminLevel.MODERATOR -> AdminPermissions(
                        canManageServers = false,
                        canManageUsers = false,
                        canViewLogs = true,
                        canManageSettings = false,
                        canPromoteUsers = false,
                        canDeleteServers = false,
                        canAddServers = false,
                        canViewServerDetails = true,
                        canEditServerDetails = false
                    )
                    AdminLevel.NONE -> AdminPermissions()
                }
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to get user permissions", e)
                AdminPermissions()
            }
        }
    }

    /**
     * Promote user to admin
     */
    suspend fun promoteUserToAdmin(userEmail: String, level: AdminLevel): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Only super admin can promote users
                if (!isSuperAdmin()) {
                    return@withContext Result.failure(Exception("Only super admin can promote users"))
                }

                // Find user by email
                val usersQuery = firestore.collection("users")
                    .whereEqualTo("email", userEmail)
                    .get()
                    .await()

                if (usersQuery.documents.isEmpty()) {
                    return@withContext Result.failure(Exception("User not found"))
                }

                val userDoc = usersQuery.documents.first()
                val userId = userDoc.id

                // Set role
                val roleData = mapOf(
                    "role" to when (level) {
                        AdminLevel.SUPER_ADMIN -> "super_admin"
                        AdminLevel.ADMIN -> "admin"
                        AdminLevel.MODERATOR -> "moderator"
                        AdminLevel.NONE -> "user"
                    },
                    "promotedBy" to auth.currentUser?.email,
                    "promotedAt" to System.currentTimeMillis()
                )

                firestore.collection(USER_ROLES_COLLECTION)
                    .document(userId)
                    .set(roleData)
                    .await()

                Log.i(AppConfig.TAG, "User $userEmail promoted to ${level.name}")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(AppConfig.TAG, "Failed to promote user", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Check if user can view server details
     */
    suspend fun canViewServerDetails(): Boolean {
        val permissions = getCurrentUserPermissions()
        return permissions.canViewServerDetails
    }

    /**
     * Check if user can edit server details
     */
    suspend fun canEditServerDetails(): Boolean {
        val permissions = getCurrentUserPermissions()
        return permissions.canEditServerDetails
    }

    /**
     * Check if user can manage servers
     */
    suspend fun canManageServers(): Boolean {
        val permissions = getCurrentUserPermissions()
        return permissions.canManageServers
    }

    /**
     * Initialize admin permissions on app start
     */
    suspend fun initializePermissions() {
        try {
            getCurrentUserAdminLevel()
            Log.i(AppConfig.TAG, "Admin permissions initialized")
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to initialize admin permissions", e)
        }
    }

    /**
     * Clear cached permissions (on logout)
     */
    fun clearCachedPermissions() {
        MmkvManager.encodeSettings(KEY_IS_ADMIN, false)
        MmkvManager.encodeSettings(KEY_IS_SUPER_ADMIN, false)
    }
}
