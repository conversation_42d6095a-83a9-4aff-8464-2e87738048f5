package com.mohamedrady.v2hoor.service

import android.content.Context
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.model.UserRole

/**
 * Role-Based Access Control (RBAC) Service
 * Handles permission checks and access control based on user roles
 */
class RoleBasedAccessControl private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: RoleBasedAccessControl? = null
        
        fun getInstance(context: Context): RoleBasedAccessControl {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RoleBasedAccessControl(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val userRoleService = UserRoleService.getInstance(context)
    
    /**
     * Permission enumeration
     */
    enum class Permission {
        // User management permissions
        VIEW_ALL_USERS,
        CREATE_USER,
        EDIT_USER,
        DELETE_USER,
        CHANGE_USER_ROLE,
        
        // Server management permissions
        VIEW_ALL_SERVERS,
        CREATE_SERVER,
        EDIT_SERVER,
        DELETE_SERVER,
        ASSIGN_SERVER_TO_USER,
        
        // System permissions
        VIEW_SYSTEM_LOGS,
        MANAGE_SYSTEM_SETTINGS,
        VIEW_ANALYTICS,
        EXPORT_DATA,
        
        // Subscription permissions
        MANAGE_SUBSCRIPTIONS,
        EXTEND_SUBSCRIPTION,
        VIEW_USAGE_STATS,
        
        // Admin panel access
        ACCESS_ADMIN_PANEL,
        ACCESS_USER_MANAGEMENT,
        ACCESS_SERVER_MANAGEMENT,
        ACCESS_REAL_TIME_LOGS
    }
    
    /**
     * Check if current user has specific permission
     */
    fun hasPermission(permission: Permission): Boolean {
        val currentRole = userRoleService.getCurrentUserRole()
        return hasPermission(currentRole, permission)
    }
    
    /**
     * Check if specific role has permission
     */
    fun hasPermission(role: UserRole, permission: Permission): Boolean {
        return when (role) {
            UserRole.ADMIN -> hasAdminPermission(permission)
            UserRole.USER -> hasUserPermission(permission)
        }
    }
    
    /**
     * Admin permissions
     */
    private fun hasAdminPermission(permission: Permission): Boolean {
        return when (permission) {
            // Admins have all permissions
            Permission.VIEW_ALL_USERS,
            Permission.CREATE_USER,
            Permission.EDIT_USER,
            Permission.DELETE_USER,
            Permission.CHANGE_USER_ROLE,
            Permission.VIEW_ALL_SERVERS,
            Permission.CREATE_SERVER,
            Permission.EDIT_SERVER,
            Permission.DELETE_SERVER,
            Permission.ASSIGN_SERVER_TO_USER,
            Permission.VIEW_SYSTEM_LOGS,
            Permission.MANAGE_SYSTEM_SETTINGS,
            Permission.VIEW_ANALYTICS,
            Permission.EXPORT_DATA,
            Permission.MANAGE_SUBSCRIPTIONS,
            Permission.EXTEND_SUBSCRIPTION,
            Permission.VIEW_USAGE_STATS,
            Permission.ACCESS_ADMIN_PANEL,
            Permission.ACCESS_USER_MANAGEMENT,
            Permission.ACCESS_SERVER_MANAGEMENT,
            Permission.ACCESS_REAL_TIME_LOGS -> true
        }
    }
    
    /**
     * User permissions
     */
    private fun hasUserPermission(permission: Permission): Boolean {
        return when (permission) {
            // Users have limited permissions
            Permission.VIEW_USAGE_STATS -> true
            
            // Users cannot access admin features
            Permission.VIEW_ALL_USERS,
            Permission.CREATE_USER,
            Permission.EDIT_USER,
            Permission.DELETE_USER,
            Permission.CHANGE_USER_ROLE,
            Permission.VIEW_ALL_SERVERS,
            Permission.CREATE_SERVER,
            Permission.EDIT_SERVER,
            Permission.DELETE_SERVER,
            Permission.ASSIGN_SERVER_TO_USER,
            Permission.VIEW_SYSTEM_LOGS,
            Permission.MANAGE_SYSTEM_SETTINGS,
            Permission.VIEW_ANALYTICS,
            Permission.EXPORT_DATA,
            Permission.MANAGE_SUBSCRIPTIONS,
            Permission.EXTEND_SUBSCRIPTION,
            Permission.ACCESS_ADMIN_PANEL,
            Permission.ACCESS_USER_MANAGEMENT,
            Permission.ACCESS_SERVER_MANAGEMENT,
            Permission.ACCESS_REAL_TIME_LOGS -> false
        }
    }
    
    /**
     * Require permission or throw exception
     */
    @Throws(SecurityException::class)
    fun requirePermission(permission: Permission) {
        if (!hasPermission(permission)) {
            val currentRole = userRoleService.getCurrentUserRole()
            val message = "Access denied: ${permission.name} requires higher privileges. Current role: ${currentRole.displayName}"
            android.util.Log.w(AppConfig.TAG, message)
            throw SecurityException(message)
        }
    }
    
    /**
     * Require admin role or throw exception
     */
    @Throws(SecurityException::class)
    fun requireAdmin() {
        if (!userRoleService.isCurrentUserAdmin()) {
            val currentRole = userRoleService.getCurrentUserRole()
            val message = "Access denied: Admin role required. Current role: ${currentRole.displayName}"
            android.util.Log.w(AppConfig.TAG, message)
            throw SecurityException(message)
        }
    }
    
    /**
     * Check if user can access admin panel
     */
    fun canAccessAdminPanel(): Boolean {
        return hasPermission(Permission.ACCESS_ADMIN_PANEL)
    }
    
    /**
     * Check if user can manage other users
     */
    fun canManageUsers(): Boolean {
        return hasPermission(Permission.ACCESS_USER_MANAGEMENT)
    }
    
    /**
     * Check if user can manage servers
     */
    fun canManageServers(): Boolean {
        return hasPermission(Permission.ACCESS_SERVER_MANAGEMENT)
    }
    
    /**
     * Check if user can view system logs
     */
    fun canViewSystemLogs(): Boolean {
        return hasPermission(Permission.ACCESS_REAL_TIME_LOGS)
    }
    
    /**
     * Check if user can create other users
     */
    fun canCreateUsers(): Boolean {
        return hasPermission(Permission.CREATE_USER)
    }
    
    /**
     * Check if user can edit other users
     */
    fun canEditUsers(): Boolean {
        return hasPermission(Permission.EDIT_USER)
    }
    
    /**
     * Check if user can delete other users
     */
    fun canDeleteUsers(): Boolean {
        return hasPermission(Permission.DELETE_USER)
    }
    
    /**
     * Check if user can change user roles
     */
    fun canChangeUserRoles(): Boolean {
        return hasPermission(Permission.CHANGE_USER_ROLE)
    }
    
    /**
     * Check if user can view all servers
     */
    fun canViewAllServers(): Boolean {
        return hasPermission(Permission.VIEW_ALL_SERVERS)
    }
    
    /**
     * Check if user can create servers
     */
    fun canCreateServers(): Boolean {
        return hasPermission(Permission.CREATE_SERVER)
    }
    
    /**
     * Check if user can edit servers
     */
    fun canEditServers(): Boolean {
        return hasPermission(Permission.EDIT_SERVER)
    }
    
    /**
     * Check if user can delete servers
     */
    fun canDeleteServers(): Boolean {
        return hasPermission(Permission.DELETE_SERVER)
    }
    
    /**
     * Check if user can assign servers to other users
     */
    fun canAssignServers(): Boolean {
        return hasPermission(Permission.ASSIGN_SERVER_TO_USER)
    }
    
    /**
     * Check if user can manage subscriptions
     */
    fun canManageSubscriptions(): Boolean {
        return hasPermission(Permission.MANAGE_SUBSCRIPTIONS)
    }
    
    /**
     * Check if user can extend subscriptions
     */
    fun canExtendSubscriptions(): Boolean {
        return hasPermission(Permission.EXTEND_SUBSCRIPTION)
    }
    
    /**
     * Check if user can view analytics
     */
    fun canViewAnalytics(): Boolean {
        return hasPermission(Permission.VIEW_ANALYTICS)
    }
    
    /**
     * Check if user can export data
     */
    fun canExportData(): Boolean {
        return hasPermission(Permission.EXPORT_DATA)
    }
    
    /**
     * Get list of permissions for current user
     */
    fun getCurrentUserPermissions(): List<Permission> {
        val currentRole = userRoleService.getCurrentUserRole()
        return Permission.values().filter { hasPermission(currentRole, it) }
    }
    
    /**
     * Get list of permissions for specific role
     */
    fun getPermissionsForRole(role: UserRole): List<Permission> {
        return Permission.values().filter { hasPermission(role, it) }
    }
    
    /**
     * Log access attempt
     */
    fun logAccessAttempt(permission: Permission, granted: Boolean) {
        val currentRole = userRoleService.getCurrentUserRole()
        val currentUser = userRoleService.getCurrentUserProfile()
        val userEmail = currentUser?.email ?: "unknown"
        
        val logMessage = if (granted) {
            "Access granted: $userEmail (${currentRole.displayName}) -> ${permission.name}"
        } else {
            "Access denied: $userEmail (${currentRole.displayName}) -> ${permission.name}"
        }
        
        if (granted) {
            android.util.Log.i(AppConfig.TAG, logMessage)
        } else {
            android.util.Log.w(AppConfig.TAG, logMessage)
        }
    }
    
    /**
     * Check permission with logging
     */
    fun checkPermissionWithLogging(permission: Permission): Boolean {
        val hasPermission = hasPermission(permission)
        logAccessAttempt(permission, hasPermission)
        return hasPermission
    }
    
    /**
     * Require permission with logging
     */
    @Throws(SecurityException::class)
    fun requirePermissionWithLogging(permission: Permission) {
        val hasPermission = hasPermission(permission)
        logAccessAttempt(permission, hasPermission)
        
        if (!hasPermission) {
            val currentRole = userRoleService.getCurrentUserRole()
            val message = "Access denied: ${permission.name} requires higher privileges. Current role: ${currentRole.displayName}"
            throw SecurityException(message)
        }
    }
}
